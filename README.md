module-base	公共的内容和能力
module-dashboard	首页列表界面	
module-handwritingText	手写文本混合	
module-handwritingToText	全篇手写转文	
module-handwritingEnhance	字迹美化	
module-voiceToText	录音转文本
module-summary	AI智能概要
module-enhance	AI润色
module-writingAssistant	AI帮写
==========================================================
组件化开发方案：
在gradle.properties里有配置各feature单独编译的宏，单独将某个宏打开时，可只构建单独的module
通过在module里moduleManifest下配置不同的AndroidManifest.xml，生成独立的apk供测试的调试
fullMode=false // 此值设置为true为整编模式，下面的单编宏失效，只有当此值为false时，下面的单编才生效。
standaloneModuleDashboard=true //单module编译模式
standaloneHandwritingText=false
standaloneHandwritingToText=false
standaloneHandwritingEnhance=false
standaloneVoiceToText=false
standaloneSummary=false
standaloneEnhance=false
standaloneWritingAssistant=false

