package com.tcl.ai.note.journalhome.vm.state

import com.tcl.ai.note.journalhome.entity.HomeCategoryItemEntity

data class HomeCategoryUiState(
    val selectedCategoryId: String = "",
    val categories: List<HomeCategoryItemEntity> = emptyList(),
    //文件夹 先保留为空，在后续需要时添加
    val folders: List<HomeCategoryItemEntity> = emptyList(),
    // 是否显示新建分类对话框
    val isShowDeleteCategoryDialog: Boolean = false
)