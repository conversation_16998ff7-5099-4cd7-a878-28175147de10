package com.tcl.ai.note.journalhome.data

import com.tcl.ai.note.base.R
import com.tcl.ai.note.journalhome.entity.CategoryIcon
import com.tcl.ai.note.journalhome.entity.HomeCategoryItemEntity

// 模拟数据
val demoCategories = listOf(
    HomeCategoryItemEntity(
        name = "All notes",
        noteCounts = 30,
        categoryIcon = CategoryIcon(R.drawable.ic_all_notes),
        id = "all_notes"
    ),
    HomeCategoryItemEntity(
        name = "Uncategorised",
        noteCounts = 26,
        categoryIcon = CategoryIcon(R.drawable.ic_category_uncategorised),
        id = "uncategorised"
    ),
    HomeCategoryItemEntity(
        name = "Recently Deleted",
        noteCounts = 60,
        categoryIcon = CategoryIcon(R.drawable.ic_category_uncategorised),
        id = "recently_deleted"
    ),
)

