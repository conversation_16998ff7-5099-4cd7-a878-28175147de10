package com.tcl.ai.note.journalhome.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.database.repository.HomeRepository
import com.tcl.ai.note.journalbase.copyAssetDirToExternal
import com.tcl.ai.note.journaldashboard.track.JournalUsageReportWorker
import com.tcl.ai.note.journalhome.components.categorydialog.CategoryDialogCallBackEvent
import com.tcl.ai.note.journalhome.components.categorydialog.CategoryDialogEvent
import com.tcl.ai.note.journalhome.components.categorydialog.CategoryDialogEventManager
import com.tcl.ai.note.journalhome.components.categorydialog.CategoryDialogType
import com.tcl.ai.note.journalhome.components.categorydialog.DialogNote
import com.tcl.ai.note.journalhome.entity.HomeCategoryItemEntity
import com.tcl.ai.note.journalhome.data.HomeDataManager
import com.tcl.ai.note.journalhome.entity.HomeNoteItemEntity
import com.tcl.ai.note.journalhome.utils.HighlightUtils
import com.tcl.ai.note.journalhome.utils.TYPE_UN_CATEGORISED_ID
import com.tcl.ai.note.journalhome.utils.formatDate
//import com.tcl.ai.note.journalhome.utils.getHomeNoteType
import com.tcl.ai.note.journalhome.vm.state.HomeNoteUiState
import com.tcl.ai.note.journalhome.vm.state.NoteListAction
import com.tcl.ai.note.journalhome.vm.state.HomeContentListNotesUiState
import com.tcl.ai.note.journalhome.vm.state.HomeContentListState
import com.tcl.ai.note.journalhome.vm.state.HomeTitleMode
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import com.tcl.ai.note.journalhome.utils.FlowPerformanceTracker.measureTime
import com.tcl.ai.note.journalhome.utils.FlowPerformanceTracker.measureTransform
import com.tcl.ai.note.journalhome.utils.mapToHomeNoteItems
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.isSupportJournal
import com.tcl.ai.note.utils.launchIO
import java.io.File

/**
 * 主要是处理主页的状态和用户交互
 * 负责获取所有笔记数据,笔记的增删改查等操作
 */
class HomeNoteViewModel() : ViewModel() {
    private val TAG = "JournalHomeNoteViewModel"
    private val TAG_PERFORMANCE = "JournalHomeNoteViewModel_Performance"

    // _allNotes 只会因为数据库变化而变化
    private val _mAllNotesState = MutableStateFlow<List<HomeNoteItemEntity>>(emptyList())

    // UI状态
    private val _homeNoteUiState =
        MutableStateFlow(HomeNoteUiState(isShowBottomNav = isSupportJournal))
    val homeNoteUiState: StateFlow<HomeNoteUiState> = _homeNoteUiState.asStateFlow()

    // 记录过滤和排序状态 中间类 因为 homeNoteUiState 会受用户操作影响，导致状态变化，
    private val _filterState = MutableStateFlow(HomeNoteUiState())

    // 用于管理数据库监听的 Job
    private var observeNotesJob: Job? = null
    private var isFirstTimeLoading = true // 标记是否为首次加载
    private var isHasLoadedNoteList = false // 标记是否为首次加载

    companion object { //Add for Journal
        const val JOURNAL_LIST_VIEW_TYPE = "journal_list_view_type"
        const val JOURNAL_IS_CREATE_TIME_SORT = "journal_is_create_time_sort"
    }

    init {
        initStateData() //Modified for journal
        JournalUsageReportWorker.scheduleTask() //Modified for journal
        copyTemplateImageToExternal() //Modified for journal
        observeCategoryDialogEvents()
//        observeNotesFromDatabase() //Modified for journal
    }

    private fun initStateData() {
        viewModelScope.launch {
            val viewType = AppDataStore.getStringData(JOURNAL_LIST_VIEW_TYPE, DataStoreParam.VIEW_TYPE_GRID)
            val isCreateTimeSort = AppDataStore.getBoolean(JOURNAL_IS_CREATE_TIME_SORT, true)
            _filterState.update {
                it.copy(isCreateTimeSort = isCreateTimeSort)
            }
            _homeNoteUiState.update {
                it.copy(viewType = viewType, isCreateTimeSort = isCreateTimeSort)
            }
            observeNotesFromDatabase()
        }
    }
    
    private fun observeCategoryDialogEvents() {
        viewModelScope.launch {
            CategoryDialogEventManager.categoryDialogSuccessEvents.collect { event ->
                if (event is CategoryDialogCallBackEvent.OnCategoryCreatedCallBack || event is CategoryDialogCallBackEvent.OnCategoryNoteMoved) {
                    Logger.d(TAG, "Received new category callback: categoryId=${event.categoryId}")
                    _homeNoteUiState.update {
                        it.copy(titleMode = HomeTitleMode.Normal)
                    }
                } else if(event is CategoryDialogCallBackEvent.OnCategoryDialogDismiss){
                    Logger.d(TAG, "Received category pop window dismiss callback: categoryId=${event.categoryId}")
                }
            }
        }
    }
    /**
     * 监听数据库变化 - 只有数据库数据变化时才更新
     */
    @OptIn(FlowPreview::class)
    fun observeNotesFromDatabase() {
        // 先取消之前的监听
        observeNotesJob?.cancel()

        observeNotesJob = HomeRepository.getAllJournals()
            .measureTime(TAG_PERFORMANCE, "Database Query")
            .measureTransform(TAG_PERFORMANCE, "Data Transform") { notes ->
                Logger.d(TAG_PERFORMANCE, "Starting data transformation, original data size: ${notes.size}")
                val isCreateTimeSort = _filterState.value.isCreateTimeSort
                mapToHomeNoteItems(notes, isCreateTimeSort)
            }
            .catch { e ->
                Logger.e(TAG, "Flow execution error: ${e.message}")
                _homeNoteUiState.update {
                    it.copy(
                        listNotesUiState = HomeContentListNotesUiState.Error(
                            e.message ?: "Unknown error"
                        )
                    )
                }
                emit(emptyList()) // Emit empty list to continue flow
            }
            .flowOn(Dispatchers.IO)
            .onEach { homeNoteItems ->
                Logger.d(TAG, "Flow execution completed: ${System.currentTimeMillis()}, result count: ${homeNoteItems.size}")
                _mAllNotesState.value = homeNoteItems
                isHasLoadedNoteList=true
                // 平板首次启动时延迟笔记列表UI渲染，减少和分类列表一起渲染时的压力 错峰渲染
                if (isFirstTimeLoading&& isTablet) {
                    Logger.d(TAG, "First time loading, delaying UI rendering for 500ms")
                    delay(500)
                    isFirstTimeLoading = false
                }
                updateDisplayNotes()
            }
            .launchIn(viewModelScope)
    }

    /**
     * 停止数据库监听
     */
    fun stopObservingNotes() {
        observeNotesJob?.let { job ->
            job.cancel()
        }
        observeNotesJob = null
    }

    /**
     * 分类变更 - 本地处理，不访问数据库
     */
    fun onCategoryChanged(category: HomeCategoryItemEntity) {
        Logger.d(TAG, "onCategoryChanged: ${category.name}, category.id:${category.id}")

        // 更新分类选择状态
        updateCategorySelection(category.id, category.name ?: "")

        // 更新其他状态
        _filterState.update {
            it.copy(
                currentCategoryIndex = category.colorIndex,
                searchText = ""
            )
        }
        _homeNoteUiState.update {
            it.copy(
                currentCategoryIndex = category.colorIndex,
                titleMode = HomeTitleMode.Normal,
                searchText = ""
            )
        }

        updateDisplayNotes()
    }


    /**
     * 统一更新分类选择状态，避免重复代码
     *  优化：添加去重逻辑，避免重复更新导致的滚动问题
     */
    private fun updateCategorySelection(categoryId: String, categoryName: String) {
        val currentCategoryId = _homeNoteUiState.value.selectedCategoryId
        val selectedCategoryName = _homeNoteUiState.value.selectedCategoryName
        if (currentCategoryId == categoryId && selectedCategoryName == categoryName) {
            Logger.d(TAG, "Skip updateCategorySelection - categoryId unchanged: $categoryId")
            return
        }

        val updateCategoryName = HomeDataManager.globalCachedCategoryItems?.find { it.id == categoryId }?.let {
          it.name ?: ""
        }
        Logger.d(TAG, "updateCategorySelection, categoryId: $currentCategoryId -> $categoryId, localCategoryName:$updateCategoryName")

        _filterState.update {
            it.copy(
                selectedCategoryId = categoryId,
                selectedCategoryName = updateCategoryName
            )
        }
        _homeNoteUiState.update {
            it.copy(
                selectedCategoryId = categoryId,
                selectedCategoryName = updateCategoryName
            )
        }
    }

    /**
     * 统一更新排序类型，避免重复代码
     */
    private fun updateSortType(isCreateTimeSort: Boolean) {
        _filterState.update {
            it.copy(isCreateTimeSort = isCreateTimeSort)
        }
        _homeNoteUiState.update {
            it.copy(isCreateTimeSort = isCreateTimeSort)
        }
        viewModelScope.launch {
            AppDataStore.putBoolean(JOURNAL_IS_CREATE_TIME_SORT, isCreateTimeSort)
        }
    }

    /**
     * 更新显示的笔记列表 - 统一的过滤和排序逻辑
     * 更新显示的笔记列表 - 包含高亮逻辑
     * 在 IO 线程中执行以避免阻塞主线程
     */
    private fun updateDisplayNotes() {
        viewModelScope.launch(Dispatchers.IO) {
            val allNoteList = _mAllNotesState.value
            if (allNoteList.isEmpty() && isHasLoadedNoteList) {
                Logger.d(TAG, "No notes found empty")
                _homeNoteUiState.update {
                    it.copy(
                        listNotesUiState = HomeContentListNotesUiState.Empty,
                        selectedCount = 0,
                        titleMode = if (it.titleMode == HomeTitleMode.Search)  it.titleMode else HomeTitleMode.Normal
                    )
                }
                return@launch
            }

            val filterState = _filterState.value
            val selectedCategoryId = filterState.selectedCategoryId

            // 1. 按分类过滤
            val filteredByCategory = if (selectedCategoryId.isEmpty()) {
                allNoteList
            } else {
                allNoteList.filter { it.categoryId == selectedCategoryId }
            }

            // 2. 按搜索文本过滤并添加高亮信息
            val filteredBySearch =
                getFilterSearchList(filterState.searchText, filteredByCategory, allNoteList)

            // 3. 排序和日期格式化
            val sortedNotes = filteredBySearch
                .sortedWith( //Modified by Journal
                    compareByDescending<HomeNoteItemEntity> { it.id.toLong() == 1L }
                        .thenByDescending { note ->
                            if (filterState.isCreateTimeSort) note.createTime else note.modifyTime
                        }
                )
                .map { note ->
                    note.copy(date = formatDate(note.createTime, note.modifyTime, filterState.isCreateTimeSort))
                }

            // 5. 更新UI状态
            val selectedCount = sortedNotes.count { it.isChecked }
            val isSelectedAll = sortedNotes.isNotEmpty() && sortedNotes.size == selectedCount

            _homeNoteUiState.update {
                it.copy(
                    listNotesUiState = HomeContentListNotesUiState.Success(
                        HomeContentListState(sortedNotes)
                    ),
                    selectedCount = selectedCount,
                    isSelectedAll = isSelectedAll
                )
            }
        }
    }

    private fun getFilterSearchList(
        searchText: String,
        filteredByCategory: List<HomeNoteItemEntity>,
        allNoteList: List<HomeNoteItemEntity>
    ) = if (searchText.isEmpty()) {
        // 无搜索时，清除高亮信息
        filteredByCategory.map {
            it.copy(highlightInfo = null)
        }
    } else {
        // 有搜索时，是搜所有的笔记
        allNoteList.mapNotNull { note ->
            // 获取实际显示的标题：优先使用 titleResId 对应的字符串，否则使用 title
            val displayTitle = /*note.titleResId?.let {
                try {
                    GlobalContext.instance.getString(it)
                } catch (e: Exception) {
                    Logger.w(TAG, "Failed to get string resource for titleResId: $it, error: ${e.message}")
                    note.title // 如果获取资源失败，回退到原标题
                }
            } ?: */note.title

            val highlightInfo = HighlightUtils.createHighlightInfo(
                title = displayTitle,
                subtitle = "",//note.summary,
                searchKeyword = searchText
            )

            // 只保留有匹配的笔记
            if (highlightInfo != null) {
                Logger.d(TAG, "Search match found in note ${note.id}: title='$displayTitle'}")
                note.copy(highlightInfo = highlightInfo)
            } else null
        }
    }


    /**
     * 获取当前选中的笔记ID列表
     */
    private fun getCurrentSelectedNoteIds(): Set<String> {
        return getCurrentSelectedNote().map { it.id }.toSet()
    }

    private fun getCurrentSelectedNote(): List<HomeNoteItemEntity> {
        val currentState = _homeNoteUiState.value.listNotesUiState
        return if (currentState is HomeContentListNotesUiState.Success) {
            currentState.homeContentListState.notes
                .filter { it.isChecked }
        } else {
            arrayListOf()
        }
    }

    /**
     * 处理用户操作
     */
    fun onHomeAction(action: NoteListAction) {
        when (action) {
            is NoteListAction.OnItemCheckedChange -> {
                updateNoteCheckState(action.noteId.toString(), checked = action.checked, isSingleSelection = false)
            }

            is NoteListAction.OnItemLongClick -> {
                //搜索模式下不处理长按事件
                if (!_homeNoteUiState.value.isSearchMode) {
                    _homeNoteUiState.update {
                        it.copy(
                            titleMode = HomeTitleMode.Edit,
                        )
                    }
                    //当前的item 应该是选中的笔记 其他Item如果也是选中态 则继续选中
                    updateNoteCheckState(action.noteId.toString(), checked = true, isSingleSelection = true)
                }
            }

            is NoteListAction.OnChangeViewType -> {
                changeListShowType(action.viewType)
            }

            is NoteListAction.OnSelectAllClick -> {
                selectAllNotes(action.isSelectAll)
            }

            is NoteListAction.OnChangeTitleMode -> {
                _homeNoteUiState.update {
                    //搜索模式隐藏底部导航
                    it.copy(titleMode = action.titleMode,isShowBottomNav = action.titleMode != HomeTitleMode.Search && isSupportJournal)
                }

                if (action.titleMode == HomeTitleMode.Normal) {
                    // 退出编辑模式时 还原当前分类的列表数据
                    clearAllSelections()
                }else if (action.titleMode == HomeTitleMode.Search) {
                    clearAllNoteList()
                }
            }

            is NoteListAction.OnClickSort -> {
                viewModelScope.launch {
//                    delay(250)//延时是为了让上一次的点击效果先消失，再弹出
                    _homeNoteUiState.update {
                        it.copy(isClickedSort = action.clickSorted)
                    }
                }
            }

            is NoteListAction.OnChangeSortType -> {
                val newSortType = action.isCreateTimeSort ?: _filterState.value.isCreateTimeSort
                updateSortType(newSortType)
                updateDisplayNotes()
            }

            is NoteListAction.OnSearchTextChange -> {
                val searchText = action.searchText.trim()
                _homeNoteUiState.update {
                    it.copy(
                        searchText = searchText,
                        isSearchInputFocused = action.isSearchInputFocused
                    )
                }
                if (searchText.isEmpty()){
                    clearAllNoteList()
                }else {
                    _filterState.update {
                        it.copy(searchText = searchText)
                    }
                    updateDisplayNotes()
                }
            }

            is NoteListAction.OnDelete -> {
                deleteSelectedNotes()
            }

            is NoteListAction.OnMoveToCategory -> {
                moveNoteToCategory(getCurrentSelectedNote()) //Modified by Journal
            }

            is NoteListAction.OnShowDeleteDialog -> {
                _homeNoteUiState.update {
                    it.copy(
                        isShowDeleteDialog = action.isShowDialog,
                        selectedCount = getCurrentSelectedNoteIds().size
                    )
                }
            }

            is NoteListAction.OnClickView -> {
                viewModelScope.launch {
                    delay(250)//延时是为了让上一次的点击效果先消失，再弹出
                    _homeNoteUiState.update {
                        it.copy(isClickedView = action.clickView)
                    }
                }
            }

            is NoteListAction.OnShowDeleteItemDialog -> {
                _homeNoteUiState.update {
                    it.copy(
                        isShowDeleteDialog = action.isShowDialog,
                        selectedCount = 1,
                        selectedItem = action.deleteItem
                    )
                }
            }

            is NoteListAction.OnDeleteItem -> {
                // 在查看状态删除指定笔记
                val noteToDelete = action.journal?.copy(
                    isChecked = true, // 确保删除时不影响选中状态
                )
                noteToDelete?.let {
                    viewModelScope.launch {
                        deleteNoteFromDB(listOf(noteToDelete))
                        deleteNoteFromState(_mAllNotesState.value.filter { it.id != noteToDelete.id })
                    }
                }
            }

            is NoteListAction.OnMoveItem -> {
                // 在查看状态移动指定笔记
                moveNoteToCategory(listOf(action.journal))
            }

            is NoteListAction.ShowOrDismissPopScreen -> {
                _homeNoteUiState.update {
                    it.copy(
                        showPopupScreen = action.isShow
                    )
                }
            }
	    
	    is NoteListAction.OnTimeFormatChanged -> {
                updateDisplayNotes()
            }

            else -> {}
        }
    }

    /**
     * 切换列表显示类型
     */
    private fun changeListShowType(newViewType: String) {
        _homeNoteUiState.update {
            it.copy(viewType = newViewType)
        }
        viewModelScope.launch {
            AppDataStore.putStringData(JOURNAL_LIST_VIEW_TYPE, newViewType)
        }
    }

    // 进入搜索模式时，列表清空
    private fun clearAllNoteList() {
        _homeNoteUiState.update {
            it.copy(
                listNotesUiState = HomeContentListNotesUiState.SearchMode
            )
        }
    }

    /**
     * 移动笔记到指定分类
     */
    private fun moveNoteToCategory(currentSelectedNote: List<HomeNoteItemEntity>) {
        //val currentSelectedNote = getCurrentSelectedNote()
        if (currentSelectedNote.isNotEmpty()) {
            // 将选中的Notes移至指定的分类
            viewModelScope.launch {
                CategoryDialogEventManager.sendCategoryDialogEvent(
                    CategoryDialogEvent(
                        type = CategoryDialogType.MOVE_NOTE_TO_CATEGORY,
                        dialogNotes = currentSelectedNote.map { note ->
                            DialogNote(
                                id = note.id.toLongOrNull() ?: 0L,
                                name = note.title,
                                content = /*note.summary ?:*/ "",
                                categoryId = note.categoryId.toLongOrNull()
                                    ?: TYPE_UN_CATEGORISED_ID,
                            )
                        },
                        categoryId = _homeNoteUiState.value.selectedCategoryId.toLongOrNull()
                            ?: TYPE_UN_CATEGORISED_ID,
                        isPreviewMode = false
                    )
                )
            }
        }
    }

    /**
     * 更新笔记选中状态
     * @param noteId 指定笔记ID，为null时表示操作所有笔记
     * @param checked 选中状态
     * @param isSingleSelection 是否为单选模式（单选时会取消其他笔记的选中状态）
     */
    private fun updateNoteCheckState(noteId: String? = null, checked: Boolean, isSingleSelection: Boolean = true) {
        val currentState = _homeNoteUiState.value.listNotesUiState
        if (currentState is HomeContentListNotesUiState.Success) {
            val updatedNotes = currentState.homeContentListState.notes.map { note ->
                when {
                    // 全选/取消全选操作
                    noteId == null -> note.copy(isChecked = checked)
                    // 单个笔记操作
                    note.id == noteId -> note.copy(isChecked = checked)
                    // 单选模式下，取消其他笔记的选中状态
                    isSingleSelection -> note.copy(isChecked = false)
                    // 多选模式下，保持其他笔记的状态不变
                    else -> note
                }
            }
            updateNoteListState(updatedNotes)
        }
    }

    /**
     * 全选/取消全选
     */
    private fun selectAllNotes(selectAll: Boolean) {
        updateNoteCheckState(noteId = null, checked = selectAll, isSingleSelection = false)
    }


    /**
     * 清除所有选中状态、过滤信息和高亮信息，回到正常状态
     */
    private fun clearAllSelections() {
        // 清除搜索文本和过滤状态
        _filterState.update {
            it.copy(searchText = "")
        }

        // 清除UI状态中的搜索文本
        _homeNoteUiState.update {
            it.copy(searchText = "",)
        }

        // 重新刷新显示列表，这会自动清除高亮信息和选中状态
        updateDisplayNotes()
    }

    /**
     * 删除选中的笔记
     */
    private fun deleteSelectedNotes() {
        viewModelScope.launch {
            val currentState = _homeNoteUiState.value.listNotesUiState
            if (currentState is HomeContentListNotesUiState.Success) {
                val currentNotes = currentState.homeContentListState.notes
                val notesToDelete = currentNotes.filter { it.isChecked }

                if (notesToDelete.isNotEmpty()) {
                    Logger.d(TAG, "Starting to delete ${notesToDelete.size} notes")
                    deleteNoteFromState(currentNotes)
                    deleteNoteFromDB(notesToDelete)
                }
            }
        }
    }
    /**
     * 异步执行数据库删除操作
     */
    private suspend fun deleteNoteFromDB(notesToDelete: List<HomeNoteItemEntity>) {
        try {
            val noteIdsToDelete = notesToDelete.map { it.id.toLong() }
            HomeRepository.deleteNotes(noteIdsToDelete)
            Logger.d(TAG, "Successfully deleted ${noteIdsToDelete.size} notes from database")
            JournalUsageReportWorker.recordDeleteJournal(notesToDelete.size)
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to delete notes from database: ${e.message}")
            // 6. 显示错误信息
            _homeNoteUiState.update {
                it.copy(listNotesUiState = HomeContentListNotesUiState.Error("Delete failed: ${e.message}"))
            }
        }
    }

    /**
     * 直接先从UI状态中删除选中的笔记，快速响应用户删除操作，乐观删除
     */
    private fun deleteNoteFromState(currentNotes: List<HomeNoteItemEntity>) {
        val remainingNotes = currentNotes.filterNot { it.isChecked }

        _homeNoteUiState.update {
            it.copy(
                isShowDeleteDialog = false,
                listNotesUiState = if (remainingNotes.isEmpty()) {
                    HomeContentListNotesUiState.Empty
                } else {
                    HomeContentListNotesUiState.Success(HomeContentListState(remainingNotes))
                },
                selectedCount = 0,
                isSelectedAll = false
            )
        }
    }

    /**
     * 更新笔记列表状态
     */
    private fun updateNoteListState(updatedNotes: List<HomeNoteItemEntity>) {
        val selectedCount = updatedNotes.count { it.isChecked }
        val isSelectedAll = updatedNotes.isNotEmpty() && updatedNotes.size == selectedCount

        _homeNoteUiState.update {
            it.copy(
                listNotesUiState = HomeContentListNotesUiState.Success(
                    HomeContentListState(updatedNotes)
                ),
                selectedCount = selectedCount,
                isSelectedAll = isSelectedAll
            )
        }
    }

    /**
     * 将模板图片复制到External目录
     */
    private fun copyTemplateImageToExternal() {
        viewModelScope.launchIO {
            val outDir = File(GlobalContext.instance.filesDir, "template")
            if (outDir.exists()) {
                return@launchIO
            }
            copyAssetDirToExternal("image", outDir)
        }
    }

    override fun onCleared() {
        super.onCleared()
        // ViewModel 销毁时停止数据库监听
        stopObservingNotes()
    }
}
