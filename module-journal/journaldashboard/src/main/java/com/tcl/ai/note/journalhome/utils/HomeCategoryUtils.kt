package com.tcl.ai.note.journalhome.utils

import com.tcl.ai.note.base.R
import com.tcl.ai.note.database.entity.JournalCategory
import com.tcl.ai.note.journalhome.components.categorydialog.DialogCategory
import com.tcl.ai.note.journalhome.entity.CategoryIcon
import com.tcl.ai.note.journalhome.entity.HomeCategoryItemEntity
import com.tcl.ai.note.utils.getCategoryIconColor
import com.tcl.ai.note.utils.getCategoryIconResId


/**
 * 未分类的分类ID
 */
const val TYPE_UN_CATEGORISED_ID = 1L

/**
 * 获取"全部"分类项
 */
fun allCategoryItem(totalNoteCount: Int): HomeCategoryItemEntity {
    // 在开头添加"全部"分类
    val elementAllItem = HomeCategoryItemEntity(
        categoryIcon = CategoryIcon(R.drawable.ic_all_notes, getCategoryIconColor(-1)),
        noteCounts = totalNoteCount,
        id = "", // 全部分类的ID为空字符串,
    )
    return elementAllItem
}
/**
 * 将 HomeCategoryItem 转换为 NoteCategory
 */
fun translateToCategory(
    categoryItem: HomeCategoryItemEntity,
): JournalCategory {
    val noteCategory = JournalCategory(
        categoryId = categoryItem.id.toLong(),
        colorIndex = categoryItem.colorIndex,
        name = categoryItem.name ?: ""
    )
    return noteCategory
}

/**
 * 获取分类图标
 */
 fun getCategoryIcon(colorIndex: Int): CategoryIcon {
    return CategoryIcon(getCategoryIconResId(colorIndex), getCategoryIconColor(colorIndex))
}

fun isExistCategory(items:List<DialogCategory>, categoryName:String, categoryId: Long):Boolean{
    return items.any { item ->
        item.name.equals(categoryName, ignoreCase = true) && item.name.isNotEmpty() && (item.categoryId != categoryId)
    }
}