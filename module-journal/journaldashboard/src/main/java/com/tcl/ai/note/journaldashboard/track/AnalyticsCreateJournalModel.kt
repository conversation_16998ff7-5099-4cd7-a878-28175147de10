package com.tcl.ai.note.journaldashboard.track

import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.journaldashboard.vm.CreateJournalViewModel
import com.tcl.ai.note.track.AbsAnalyticsSubModel
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.judge


/**
 * 分析AI概要使用情况
 *
 * 用于上报埋点
 */
object AnalyticsCreateJournalModel : AbsAnalyticsSubModel() {
    fun loadCreateJournalViewModel(createJournalViewModel: CreateJournalViewModel) {
        reportCreateJournalState(createJournalViewModel)
    }

    /**
     * 上报AI概要获取获取状态
     */
    private fun reportCreateJournalState(createJournalViewModel: CreateJournalViewModel) {
        Logger.d("AnalyticsCreateJournalModel", "reportCreateJournalState: reportJournal =${createJournalViewModel.reportJournal}, viewModelScope=${createJournalViewModel.viewModelScope}")
        createJournalViewModel.reportJournal.collectWithScope(createJournalViewModel.viewModelScope) { result ->
            result?.let {
                val lastEditItem = createJournalViewModel.lastEditItem.value
                TclAnalytics.reportJournalCreation(
                    title = it.title,
                    cover = it.coverId.toString(),
                    type = it.categoryId.toString(),
                    opType = (it.createTime == it.modifyTime).judge("0", "1"),
                    savedSuccess = (it.journalId > 0L).judge("0", "1"),
                    lastEditedItem = lastEditItem,
                    useDuration = ((System.currentTimeMillis() - createJournalViewModel.startTime) / 1000L).toString()
                )
            }
        }
    }
}