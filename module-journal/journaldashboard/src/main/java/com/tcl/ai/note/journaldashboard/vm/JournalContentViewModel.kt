package com.tcl.ai.note.journaldashboard.vm

import android.content.ClipData
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.pdf.PdfDocument
import android.net.Uri
import android.widget.Toast
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.database.repository.JournalContentRepository
import com.tcl.ai.note.database.entity.JournalContent
import com.tcl.ai.note.database.entity.PageInfo
import com.tcl.ai.note.database.repository.JournalRepository
import com.tcl.ai.note.template.bean.ElementType
import com.tcl.ai.note.template.bean.TemplateData
import com.tcl.ai.note.handwritingtext.utils.FileUtils.getJournalContentEntPath
import com.tcl.ai.note.handwritingtext.utils.FileUtils.getJournalContentThumbnailPath
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.journaldashboard.R
import com.tcl.ai.note.journaldashboard.intent.JournalContentIntent
import com.tcl.ai.note.journaldashboard.track.AnalyticsJournalContentModel
import com.tcl.ai.note.journaldashboard.track.ReportJournalContentInfo
import com.tcl.ai.note.journaldashboard.ui.AIGenerateState
import com.tcl.ai.note.picturetotext.NetworkUtils
import com.tcl.ai.note.picturetotext.ToastUtil
import com.tcl.ai.note.picturetotext.TravelDiaryRepository
import com.tcl.ai.note.picturetotext.bean.ErrorMessage
import com.tcl.ai.note.picturetotext.bean.ServerCode
import com.tcl.ai.note.picturetotext.bean.Status
import com.tcl.ai.note.picturetotext.bean.TravelDiaryGenerateResponse
import com.tcl.ai.note.picturetotext.bean.TravelDiaryGenerateResult
import com.tcl.ai.note.picturetotext.bean.TravelDiaryImageGroup
import com.tcl.ai.note.picturetotext.bean.TravelReportData
import com.tcl.ai.note.template.bean.JournalContentInfo
import com.tcl.ai.note.template.bean.PageConfigInfo
import com.tcl.ai.note.template.utils.TemplateUtils
import com.tcl.ai.note.template.utils.createCenterCropAndScaleBitmapFromPath
import com.tcl.ai.note.template.utils.getNewImagePath
import com.tcl.ai.note.template.utils.loadJsonFromAssets
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.net.URLEncoder
import javax.inject.Inject

@HiltViewModel
class JournalContentViewModel @Inject constructor(
    private val journalRepository: JournalRepository,
    private val repository: JournalContentRepository,
) : SuniaDrawViewModel() {

    private val _content = MutableStateFlow(JournalContent())
    val content: StateFlow<JournalContent> = _content.asStateFlow()
    private val _aiGenerateState = MutableStateFlow<AIGenerateState>(AIGenerateState.Content)
    val aiGenerateState: StateFlow<AIGenerateState> = _aiGenerateState.asStateFlow()
    private val _drawFinish = MutableStateFlow(false)
    val drawFinish: StateFlow<Boolean> = _drawFinish.asStateFlow()
    private val _showFloatingButton = MutableStateFlow(false)
    val showFloatingButton: StateFlow<Boolean> = _showFloatingButton.asStateFlow()
    private val _templateData = MutableStateFlow<TemplateData?>(null)
    val templateData: StateFlow<TemplateData?> = _templateData.asStateFlow()
    private var lastConfigInfo: PageConfigInfo? = null
    var configInfoJson = ""
    var imageGroups = listOf<TravelDiaryImageGroup>()
    // 是否初始化
    var hasInit = false
    var curConfigInfo: PageConfigInfo? = null
    // 自定义日记名称
    var customJournalName by mutableStateOf("")

    private var journalContentInfoJob: Job? = null
    private var curJournalContentInfo: JournalContentInfo? = null
    // 回忆集内容信息
    private val _journalContentInfo = MutableSharedFlow<JournalContentInfo?>(replay = 1)
    private val journalContentInfo = _journalContentInfo.asSharedFlow()
    // 替换文本信息
    private val _textList = MutableSharedFlow<List<String>>(replay = 1)
    private val textList = _textList.asSharedFlow()

    private val _showSensitiveImageDialog = MutableStateFlow(false)
    val showSensitiveImageDialog: StateFlow<Boolean> = _showSensitiveImageDialog.asStateFlow()
    fun dismissSensitiveImageDialog() {
        _showSensitiveImageDialog.value = false
    }
    private val _sensitiveImagePath = MutableStateFlow<String?>(null)
    val sensitiveImagePath: StateFlow<String?> = _sensitiveImagePath.asStateFlow()

    //用于埋点上报 begin
    private var _reportContent = ReportJournalContentInfo()
    private val _reportJournalContent = MutableStateFlow(ReportJournalContentInfo())
    val reportJournalContent = _reportJournalContent.asStateFlow()
    private var _reportAiWritingSettingsData: TravelReportData? = null
    private var _reportAiWritingData = MutableStateFlow<TravelReportData?>(null)
    val reportAiWritingData = _reportAiWritingData.asStateFlow()
    private var resultText = listOf<String>()
    //用于埋点上报 end

    init {
        AnalyticsJournalContentModel.loadJournalContentViewModel(this)
        _reportContent.pageStartTime = System.currentTimeMillis()
        viewModelScope.launchIO {
            TravelDiaryRepository.instance.travelReportData.collectLatest { data ->
                _reportAiWritingSettingsData = data
            }
        }
        viewModelScope.launchIO {
            TravelDiaryRepository.instance.generateResult.collectLatest { value ->
                handleGenerateResult(value)
            }
        }
        viewModelScope.launchIO {
            engineInitComplete.collect {
                if (it && curJournalContentInfo != null) {
                    curJournalContentInfo?.let { info ->
                        Logger.d(TAG, "engineInitComplete journalContentInfo: ${Gson().toJson(info)}")
                        setJournalContentInfo(info)
                    }
                }
            }
        }
        viewModelScope.launchIO {
            journalContentInfo.collect {
                journalContentInfoJob?.cancel()
                journalContentInfoJob = launch {
                    Logger.d(TAG, "journalContentInfo:$it, engineInitComplete: ${engineInitComplete.value}")
                    curJournalContentInfo = it
                    if (engineInitComplete.value && it != null) {
                        handleContentInfo(it) {
                            collectTextList(it)
                        }
                    }
                }
            }
        }
    }

    private fun collectTextList(journalContentInfo: JournalContentInfo) {
        textListJob?.cancel()
        textListJob = viewModelScope.launchIO {
            textList.collect { textList ->
                Logger.d(TAG, "textList:$textList")
                super.replaceText(textList, journalContentInfo)
            }
        }
    }

    fun replaceText(list: List<String>) {
        viewModelScope.launchIO {
            Logger.d(TAG, "replaceText: ${Gson().toJson(list)}")
            _textList.emit(list)
        }
    }

    fun setJournalContentInfo(journalContentInfo: JournalContentInfo?) {
        viewModelScope.launchIO {
            Logger.d(TAG, "setJournalContentInfo: ${Gson().toJson(journalContentInfo)}")
            _journalContentInfo.emit(journalContentInfo)
        }
    }

    override fun autoSaveJournalContent() {
        curJournalContentInfo?.let {
            Logger.d(TAG, "saveJournalContent journalId:${it.journalId} pageIndex:${it.pageIndex}")
            saveJournalContent(it.journalId, it.pageIndex)
        }
    }

    private suspend fun handleGenerateResult(value: TravelDiaryGenerateResult?) {
        value?.let {
            Logger.d(
                TAG,
                "generateResult value:${value.status} _templateData.value:${_templateData.value}"
            )
            if (_templateData.value != null) {
                when (value.status) {
                    is Status.Start -> {
                        _reportAiWritingSettingsData = _reportAiWritingSettingsData?.copy(generateStartTime = System.currentTimeMillis())
                        setAIGenerateState(AIGenerateState.Loading)
                        closeFloatingButton(false)
                    }

                    is Status.Success,
                    is Status.Failure -> {
                        _reportAiWritingSettingsData = _reportAiWritingSettingsData?.copy(generateEndTime = System.currentTimeMillis())

                        if (value.status is Status.Failure
                            && (value.status as Status.Failure).errorCode == ServerCode.SENSITIVE) {

                            if ((value.status as Status.Failure).errorMessage == ErrorMessage.SENSITIVETEXT) {
                                withContext(Dispatchers.Main) {
                                    Toast.makeText(
                                        GlobalContext.instance,
                                        GlobalContext.instance.getString(com.tcl.ai.note.resources.R.string.sensitive_words_filtering),
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            } else {
                                // 获取错误信息
                                val errorJson = (value.status as Status.Failure).errorMessage
                                try {
                                    Logger.d("errorJson: $errorJson")
                                    val response = Gson().fromJson(
                                        errorJson,
                                        TravelDiaryGenerateResponse::class.java
                                    )
                                    // 获取敏感图片检测结果列表
                                    val moderateList =
                                        response.data?.imageModerationResult
                                    Logger.d("moderateList: $moderateList")
                                    if (moderateList!=null && moderateList.isNotEmpty()) {
                                        // 获取第一个敏感图片的检测结果
                                        val first = moderateList.first()
                                        val groupIndex = first.groupIndex
                                        val imageIndex = first.imageIndex
                                        Logger.d("groupIndex: $groupIndex, imageIndex: $imageIndex")
                                        // 根据索引获取敏感图片的路径
                                        val imagePath = imageGroups
                                            .getOrNull(groupIndex)
                                            ?.images
                                            ?.getOrNull(imageIndex)
                                            ?.uri
                                            ?.path
                                        Logger.d("imageGroups: $imageGroups")
                                        Logger.d("imagePath: $imagePath")
                                        withContext(Dispatchers.Main) {
                                            _sensitiveImagePath.value = imagePath
                                            _showSensitiveImageDialog.value = true
                                        }
                                    }
                                } catch (e: Exception) {
                                    withContext(Dispatchers.Main) {
                                        _sensitiveImagePath.value = null
                                        _showSensitiveImageDialog.value = true
                                        Logger.d("_sensitiveImagePath: $_sensitiveImagePath")
                                    }
                                }
                            }
                        }
                        if (value.status is Status.Success) {
                            setAIGenerateState(AIGenerateState.MagicEffect)
                        } else {
                            setAIGenerateState(AIGenerateState.Content)
                        }
                        if (value.status is Status.Failure && !NetworkUtils.isNetworkAvailable(GlobalContext.instance)) {
                            withContext(Dispatchers.Main) {
                                val message =
                                    GlobalContext.instance.resources.getString(com.tcl.ai.note.resources.R.string.network_exception)
                                ToastUtil.show(message = message)
                            }
                        }
                        resultText = value.content
                            ?: List(TravelDiaryRepository.instance.imageGroups.size) {
                                GlobalContext.instance.getString(
                                    com.tcl.ai.note.resources.R.string.generation_failed
                                )
                            }
                        if (value.status is Status.Failure) {
                            _showFloatingButton.update { true }
                            replaceText(resultText)
                        }
                    }

                    Status.Stop -> {
                        _reportAiWritingSettingsData = _reportAiWritingSettingsData?.copy(isAdopted = false, generateEndTime = System.currentTimeMillis())
                        setAIGenerateState(AIGenerateState.Content)
                        closeFloatingButton(closeByUser = true)
                    }
                }
                if (value.status != Status.Start) {
                    _reportContent.pageEndTime = System.currentTimeMillis()
                    if (value.status == Status.Stop) {
                        _reportJournalContent.update {
                            _reportContent.copy()
                        }
                    } else if (value.status is Status.Success) {
                        _reportContent.wordCount = (value.content?.sumOf {item -> item.length }).toString()
                    }

                } else {
                    _reportContent.aiRewriteCount += 1
                }
            }
        }
    }

    fun initData(context: Context, journalId: Long, configInfo: PageConfigInfo?, operationType: String = DataStoreParam.OPERATION_TYPE_ADD) {
        viewModelScope.launchIO {
            curConfigInfo = configInfo

            handleConfigInfo(context, configInfo)
            parseData(configInfo)
            if (operationType == DataStoreParam.OPERATION_TYPE_ADD) {
                handleEvent(JournalContentIntent.LoadContent(journalId, configInfo))
            }
        }
    }

    private fun handleConfigInfo(context: Context, configInfo: PageConfigInfo?) {
        Logger.w(TAG, "handleConfigInfo configInfo: $configInfo")
        if (configInfo == null) {
            setAIGenerateState(AIGenerateState.Content)
            _drawFinish.update { true }
            return
        }
        val jsonStr = loadJsonFromAssets(context, configInfo.templateJson)   // 读取assets下json文件
        val data = Gson().fromJson(jsonStr, TemplateData::class.java).apply {
            var imageIndex = 0
            var handleImageResult: Boolean
            var newImagePath: String
            elements.forEach {
                val imagePath = configInfo.imagePaths.getOrNull(imageIndex).orEmpty()
                if (it.type == ElementType.IMAGE) {
                    newImagePath = getNewImagePath(
                        srcPath = imagePath,
                        journalId = _content.value.journalId
                    )
                    handleImageResult = createCenterCropAndScaleBitmapFromPath(
                        srcPath = imagePath,
                        dstPath = newImagePath,
                        rectW = it.size[0].toInt(),
                        rectH = it.size[1].toInt(),
                        cornerRadius = it.cornerRadius,
                    )
                    it.content = if (handleImageResult) {
                        newImagePath
                    } else {
                        imagePath
                    }
                    imageIndex++
                }
            }
            _reportContent.createType = "0"
            _reportContent.template = id
            _reportContent.isAIUsed = "0"
            _reportContent.photoCount = configInfo.imagePaths.size.toString()
        }
        _templateData.update {
            data
        }
    }

    private fun parseData(configInfo: PageConfigInfo?) {
        Logger.w(TAG, "parseData configInfo: $configInfo")
        configInfo?.let {
            val json = Gson().toJson(configInfo)
            val encoded = URLEncoder.encode(json, "UTF-8")
            configInfoJson = encoded
        }
        imageGroups = TravelDiaryRepository.instance.imageGroups
    }

    fun handleEvent(intent: JournalContentIntent) {
        viewModelScope.launchIO {
            when (intent) {
                is JournalContentIntent.LoadContent -> {
                    loadNoteContents(intent.journalId, intent.configInfo)
                }

                is JournalContentIntent.InsertContent -> {
                    repository.insertJournalContent(intent.content)
                    journalRepository.updateJournalModifyTime(intent.content.journalId)
                }

                is JournalContentIntent.UpdateContent -> {
                    repository.updateJournalContent(intent.content)
                    _content.update {
                        intent.content
                    }
                    intent.callback.invoke()
                }

                is JournalContentIntent.UpdateJournalTitle -> {
                    journalRepository.updateJournalTitle(intent.journalId, intent.journalTitle)
                }

                is JournalContentIntent.UpdateJournalModifyTime -> {
                    viewModelScope.launchIO {
                        journalRepository.updateJournalModifyTime(intent.journalId)
                    }
                }
            }
        }
    }

    private suspend fun loadNoteContents(journalId: Long, configInfo: PageConfigInfo? = null) {
        var journalContent =
            repository.getJournalContentById(journalId) ?: JournalContent()
        Logger.d(
            TAG,
            "loadNoteContents journalId:$journalId, content: ${Gson().toJson(journalContent)}"
        )
        if (configInfo != null && lastConfigInfo != configInfo) {
            val curIndex = journalContent.totalPageIndex
            val pageInfo = PageInfo(
                pageId = curIndex,
                bgImageResId = TemplateUtils.getTemplateBg(configInfo.templateJson),
                bitmapFilePath = getJournalContentThumbnailPath(
                    journalId,
                    curIndex
                ),
                entFilePath = getJournalContentEntPath(
                    journalId,
                    curIndex
                )
            )
            journalContent = journalContent.copy(
                journalId = journalId,
                lastPageIndex = journalContent.pageInfos.size,
                totalPageIndex = journalContent.totalPageIndex + 1,
                pageInfos = journalContent.pageInfos + pageInfo
            )
            lastConfigInfo = configInfo
        }
        _content.update {
            journalContent
        }
        Logger.d(
            TAG,
            "loadNoteContents _content: ${Gson().toJson(journalContent)}"
        )
    }

    fun setAIGenerateState(state: AIGenerateState) {
        Logger.w(TAG, "setAIGenerateState:$state")
        _aiGenerateState.update {
            state
        }
    }

    fun aiGenerateCompleted() {
        setAIGenerateState(AIGenerateState.Content)
        _showFloatingButton.update { true }
        replaceText(resultText)
    }

    fun clearTemplateData() {
        Logger.w(TAG, "clearTemplateData")
        if (_templateData.value != null) {
            _templateData.update { null }
        }
    }

    fun stopTravelDiaryGeneration(context: Context) {
        TravelDiaryRepository.instance.stopTravelDiaryGeneration()
        Toast.makeText(
            context,
            context.getString(com.tcl.ai.note.resources.R.string.ai_generation_has_been_stopped),
            Toast.LENGTH_SHORT
        ).show()
    }

    /**
     * 分享图片
     */
    fun shareImages(context: Context, list: List<PageInfo>) {
        if (list.isEmpty()) {
            return
        }
        viewModelScope.launchIO {
            try {
                // 生成分享URI
                val uriList = generateMultipleImage(
                    context,
                    list,
                    GlobalContext.screenWidth,
                    GlobalContext.screenHeight
                )

                val clipData = ClipData.newRawUri("", uriList[0])
                for (i in 1 until uriList.size) {
                    clipData.addItem(ClipData.Item(uriList[i]))
                }
                // 构建分享Intent
                if (uriList.size == 1) {
                    Intent(Intent.ACTION_SEND).apply {
                        putExtra(Intent.EXTRA_STREAM, uriList[0])
                    }
                } else {
                    Intent(Intent.ACTION_SEND_MULTIPLE).apply {
                        putParcelableArrayListExtra(Intent.EXTRA_STREAM, uriList)
                    }
                }.apply {
                    type = "image/png"
                    this.clipData = clipData
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }.also { intent ->
                    context.startActivity(
                        Intent.createChooser(
                            intent,
                            context.getString(com.tcl.ai.note.base.R.string.show_content_share)
                        )
                    )
                }
            } catch (e: Exception) {
                Logger.w(TAG, "Sharing failed:${e.stackTraceToString()}")
            }
        }
    }

    private fun generateMultipleImage(
        context: Context,
        list: List<PageInfo>,
        pageWidth: Int,
        pageHeight: Int
    ): ArrayList<Uri> {
        val uriList = arrayListOf<Uri>()
        list.forEachIndexed { index, info ->
            val canvasBitmap = Bitmap.createBitmap(
                pageWidth,
                pageHeight,
                Bitmap.Config.RGB_565
            )
            val canvas = Canvas(canvasBitmap)
            drawContent(context, canvas, info)

            // 创建缓存目录
            val cacheDir = File(context.cacheDir, "shared_images").apply { mkdirs() }
            val outputFile = File(cacheDir, "page_${index + 1}.png")

            // 保存Bitmap到文件
            FileOutputStream(outputFile).use { out ->
                canvasBitmap.compress(Bitmap.CompressFormat.WEBP_LOSSY, 90, out)
            }
            // 生成分享URI
            uriList.add(
                FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    outputFile
                )
            )
        }
        return uriList
    }

    private fun drawContent(context: Context, canvas: Canvas, info: PageInfo) {
        canvas.drawColor(
            ContextCompat.getColor(
                context,
                info.bgColorResId ?: R.color.white
            )
        )
        info.bgImageResId?.let {
            val bg = BitmapFactory.decodeResource(context.resources, it)
            canvas.drawBitmap(
                bg,
                Rect(0, 0, bg.width, bg.height),     // 要绘制的图片全图
                RectF(
                    0f,
                    0f,
                    canvas.width.toFloat(),
                    canvas.height.toFloat()
                ),  // 拉伸/缩放目标区域
                null
            )
        }
        val bitmap = BitmapFactory.decodeFile(info.bitmapFilePath)
        canvas.drawBitmap(
            bitmap,
            Rect(0, 0, bitmap.width, bitmap.height),     // 要绘制的图片全图
            RectF(
                0f,
                0f,
                canvas.width.toFloat(),
                canvas.height.toFloat()
            ),  // 拉伸/缩放目标区域
            null
        )
    }

    /**
     * 分享pdf
     */
    fun sharePdfs(context: Context, title: String, list: List<PageInfo>) {
        if (list.isEmpty()) {
            return
        }
        viewModelScope.launchIO {
            try {
                // 生成分享URI
                val uriList = generateMultiplePdf(
                    context,
                    list,
                    GlobalContext.screenWidth,
                    GlobalContext.screenHeight,
                    title
                )

                // 构建分享Intent
                if (uriList.size == 1) {
                    Intent(Intent.ACTION_SEND).apply {
                        putExtra(Intent.EXTRA_STREAM, uriList[0])
                    }
                } else {
                    Intent(Intent.ACTION_SEND_MULTIPLE).apply {
                        putParcelableArrayListExtra(Intent.EXTRA_STREAM, uriList)
                    }
                }.apply {
                    type = "application/pdf"
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }.also { intent ->
                    context.startActivity(
                        Intent.createChooser(
                            intent,
                            context.getString(com.tcl.ai.note.base.R.string.show_content_share)
                        )
                    )
                }
            } catch (e: Exception) {
                Logger.w(TAG, "Sharing failed:${e.stackTraceToString()}")
            }
        }
    }

    /**
     * 生成pdf文件
     */
    private fun generateMultiplePdf(
        context: Context,
        list: List<PageInfo>,
        pageWidth: Int,
        pageHeight: Int,
        title: String
    ): ArrayList<Uri> {
        val uriList = arrayListOf<Uri>()
        list.forEachIndexed { index, info ->
            // 初始化PDF文档
            val pdfDocument = PdfDocument()
            val pageInfo =
                PdfDocument.PageInfo.Builder(pageWidth, pageHeight, 1).create()
            val page = pdfDocument.startPage(pageInfo)
            val canvas = page.canvas
            drawContent(context, canvas, info)
            pdfDocument.finishPage(page)

            // 缓存生成文件
            val pdfDir = File(context.cacheDir, "shared_pdfs").apply { mkdirs() }
            val file = File(pdfDir, "${title}(${index + 1}).pdf")
            pdfDocument.writeTo(FileOutputStream(file))
            pdfDocument.close()

            uriList.add(
                FileProvider.getUriForFile(
                    context,
                    "${context.packageName}.fileprovider",
                    file
                )
            )
        }
        return uriList
    }

    fun triggerReport() {
        _reportJournalContent.update {
            _reportContent.copy()
        }
    }

    fun triggerAiWritingReport() {
        _reportAiWritingData.update {
            _reportAiWritingSettingsData?.copy(isAdopted = false)
        }
    }

    fun closeFloatingButton(clearData: Boolean = true, closeByUser: Boolean = false) {
        Logger.w(TAG, "closeFloatingButton clearData:$clearData, closeByUser: $closeByUser")
        _showFloatingButton.update { false }
        if (clearData) {
            _drawFinish.update { true }
            TravelDiaryRepository.instance.imageGroups.clear()
        }
        if (closeByUser) {
            Logger.d(TAG, "report ai settings: $_reportAiWritingSettingsData")
            _reportAiWritingData.update {
                _reportAiWritingSettingsData
            }
        }
    }

    fun resetData() {
        _content.update { JournalContent() }
        setAIGenerateState(AIGenerateState.Content)
        _drawFinish.update { false }
        _showFloatingButton.update { false }
        _templateData.update { null }
        lastConfigInfo = null
        _reportContent = ReportJournalContentInfo()
        _reportAiWritingSettingsData = null
    }

    override fun onCleared() {
        Logger.w(TAG, "onCleared")
        super.onCleared()
    }

    fun addText() {
        addText("")
    }

    companion object {
        const val TAG = "JournalContentViewModel"
    }
}