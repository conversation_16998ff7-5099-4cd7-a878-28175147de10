package com.tcl.ai.note.journaldashboard.states

import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.database.entity.JournalCategory
import com.tcl.ai.note.database.entity.Journal


/**
 * 页面状态
 */
data class ConfigState(
    /**
     * 首页列表界面，初始默认状态， LIST
     * */
    val viewType: String = DataStoreParam.VIEW_TYPE_LIST,

)

/**
 * 笔记列表数据状态
 */
sealed class ListNotesUiState {
    data object Loading : ListNotesUiState()
    data class Success(val items: List<Journal>, val hasMore: Boolean) : ListNotesUiState()
    data class Error(val message: String) : ListNotesUiState()
}

/**
 * 笔记分类列表数据状态
 */
sealed class ListNoteCategoryState {
    data object Loading : ListNoteCategoryState()
    data class Success(val items: List<JournalCategory>) : ListNoteCategoryState()
    data class Error(val message: String) : ListNoteCategoryState()
}

/**
 * 笔记分类数据状态
 */
sealed class NoteCategoryState {
    data object Loading : NoteCategoryState()
    data class Success(val item: JournalCategory) : NoteCategoryState()
    data class Error(val message: String) : NoteCategoryState()
}

/**
 * 当前显示的Note数据状态
 */
data class NoteState(
    val note: Journal? = null, // 当前笔记
    val noteId : Long =-1L, // 当前预览noteId
    //val contents: List<EditorContent> = emptyList(), // 内容块列表
    val newCategory: JournalCategory? = null, // 预览新建的分类
    val currentNoteCategory:JournalCategory? = null, // 当前预览note的分类
    val searchText:String = "", // 搜索框中输入的内容
    val isSearching: Boolean = false // 是否处于搜索状态
)

data class CoverState(
    val coverId: Int = 1, // 封面ID
)


//data class OtherConfigState(
//
//)





