package com.tcl.ai.note.journaldashboard.ui

import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.google.gson.Gson
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.navigation.NavigationUtils.ROUTE_MAIN_SCREEN
import com.tcl.ai.note.template.bean.PageConfigInfo
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.utils.getCurCoverRect
import java.net.URLDecoder
import java.nio.charset.StandardCharsets

const val ROUTE_ADD_PAGE_SCREEN = "add_page_screen"
const val ROUTE_JOURNAL_CONTENT_SCREEN = "journal_content_screen"
const val ROUTE_JOURNAL_CONTENT_NO_ANIM_SCREEN = "journal_content_no_anim_screen"
const val ROUTE_WRITE_ASSISTANT_SCREEN = "writing_assistant_screen"
const val ROUTE_SETTINGS_SCREEN = "settings_screen"

fun NavGraphBuilder.addPageScreen(navController: NavController, easing: CubicBezierEasing) {
    var previousRoute: String? = null
    composable(
        route = "$ROUTE_ADD_PAGE_SCREEN?journalId={journalId}&journalTitle={journalTitle}&coverId={coverId}",
        arguments = listOf(
            navArgument("journalId") {
                type = NavType.LongType
            },
            navArgument("journalTitle") {
                type = NavType.StringType
            },
            navArgument("coverId") {
                type = NavType.LongType
            },
        ),
        enterTransition = {
            previousRoute = initialState.destination.route
            slideInHorizontally(
                initialOffsetX = { it }, // it: 屏幕宽度，新页面从右侧x=it进来
                animationSpec = tween(400, easing = easing)
            )
        },
        exitTransition = {
            slideOutHorizontally(
                targetOffsetX = { -it }, // 向左x=-it退出
                animationSpec = tween(400, easing = easing)
            )
        },
        popEnterTransition = {
            slideInHorizontally(
                initialOffsetX = { -it }, // 新页面从左侧x=-it滑入
                animationSpec = tween(400, easing = easing)
            )
        },
        popExitTransition = {
            if (getCurCoverRect() != Rect.Zero && targetState.destination.route == ROUTE_MAIN_SCREEN) {
                fadeOut(targetAlpha = 0.99f, animationSpec = tween(600))
            } else {
                slideOutHorizontally(
                    targetOffsetX = { it }, // 当前页面向右x=it滑出
                    animationSpec = tween(400, easing = easing)
                )
            }
        },
    ) { backStackEntry ->
        val journalId = backStackEntry.arguments?.getLong("journalId") ?: 0
        val journalTitle = URLDecoder.decode(backStackEntry.arguments?.getString("journalTitle").orEmpty(), StandardCharsets.UTF_8.toString())
        val coverId = backStackEntry.arguments?.getLong("coverId") ?: 0
        CoverTurnPageScreen(
            navController = navController,
            journal = Journal(journalId = journalId, title = journalTitle, coverId = coverId),
            bounds = getCurCoverRect(),
            previousRoute = previousRoute,
        ) { _, viewModel, exitBack ->
            /*AddPageScreen(navController, journalId, journalTitle, coverId) {
                exitBack()
                navController.navigateUp()
            }*/
            JournalContentScreen(
                navController = navController,
                journalId = journalId,
                journalTitle = journalTitle,
                coverId = coverId,
                modifier = Modifier.fillMaxSize(),
                isFullMode = true,
                viewModel = viewModel
            ) {
                exitBack()
                navController.previousBackStackEntry?.savedStateHandle?.set("refresh_journalId", journalId)
                navController.navigateUp()
            }
        }
    }
}

fun NavGraphBuilder.journalContentScreen(navController: NavController, easing: CubicBezierEasing) {
    var isEnter = true
    var previousRoute: String? = null
    composable(
        route = "$ROUTE_JOURNAL_CONTENT_SCREEN?journalId={journalId}&journalTitle={journalTitle}&coverId={coverId}&configInfoJson={configInfoJson}",
        arguments = listOf(
            navArgument("journalId") {
                type = NavType.LongType
            },
            navArgument("journalTitle") {
                type = NavType.StringType
            },
            navArgument("coverId") {
                type = NavType.LongType
            },
            navArgument("configInfoJson") {
                type = NavType.StringType
                defaultValue = null  // 设置为可空参数
                nullable = true
            },
        ),
        enterTransition = {
            previousRoute = initialState.destination.route
            if (initialState.destination.route == ROUTE_MAIN_SCREEN) {
                isEnter = true
                fadeIn(initialAlpha = 0.99f, animationSpec = tween(600))
            } else {
                isEnter = false
                slideInHorizontally(
                    initialOffsetX = { it }, // it: 屏幕宽度，新页面从右侧x=it进来
                    animationSpec = tween(400, easing = easing)
                )
            }
        },
        exitTransition = {
            slideOutHorizontally(
                targetOffsetX = { -it }, // 向左x=-it退出
                animationSpec = tween(400, easing = easing)
            )
        },
        popEnterTransition = {
            isEnter = false
            slideInHorizontally(
                initialOffsetX = { -it }, // 新页面从左侧x=-it滑入
                animationSpec = tween(400, easing = easing)
            )
        },
        popExitTransition = {
            if (getCurCoverRect() != Rect.Zero && targetState.destination.route == ROUTE_MAIN_SCREEN) {
                fadeOut(targetAlpha = 0.99f, animationSpec = tween(600))
            } else {
                slideOutHorizontally(
                    targetOffsetX = { it }, // 当前页面向右x=it滑出
                    animationSpec = tween(400, easing = easing)
                )
            }
        },
    ){ backStackEntry ->
        val journalId = backStackEntry.arguments?.getLong("journalId") ?: 0
        val journalTitle = URLDecoder.decode(backStackEntry.arguments?.getString("journalTitle").orEmpty(), StandardCharsets.UTF_8.toString())
        val coverId = backStackEntry.arguments?.getLong("coverId") ?: 0
        val configInfoJson = backStackEntry.arguments?.getString("configInfoJson")
        val configInfo = configInfoJson?.let {
            val json = URLDecoder.decode(configInfoJson, "UTF-8")
            Gson().fromJson(json, PageConfigInfo::class.java)
        }
        NoteTclTheme {
            CoverTurnPageScreen(
                navController = navController,
                journal = Journal(journalId = journalId, title = journalTitle, coverId = coverId),
                bounds = getCurCoverRect(),
                isEnter = isEnter,
                previousRoute = previousRoute,
                configInfo = configInfo,
            ) { isFullMode, viewModel, exitBack ->
                JournalContentScreen(
                    navController = navController,
                    journalId = journalId,
                    journalTitle = journalTitle,
                    coverId = coverId,
                    modifier = Modifier.fillMaxSize(),
                    isFullMode = isFullMode,
                    configInfo = configInfo,
                    viewModel = viewModel
                ) {
                    exitBack()
                    navController.previousBackStackEntry?.savedStateHandle?.set("refresh_journalId", journalId)
                    navController.navigateUp()
                }
            }
        }
    }
}

fun NavGraphBuilder.journalContentNoAnimScreen(navController: NavController) {
    composable(
        route = "$ROUTE_JOURNAL_CONTENT_NO_ANIM_SCREEN?journalId={journalId}&journalTitle={journalTitle}&coverId={coverId}&configInfoJson={configInfoJson}",
        arguments = listOf(
            navArgument("journalId") {
                type = NavType.LongType
            },
            navArgument("journalTitle") {
                type = NavType.StringType
            },
            navArgument("coverId") {
                type = NavType.LongType
            },
            navArgument("configInfoJson") {
                type = NavType.StringType
                defaultValue = null  // 设置为可空参数
                nullable = true
            },
        ),
    ){ backStackEntry ->
        val journalId = backStackEntry.arguments?.getLong("journalId") ?: 0
        val journalTitle = URLDecoder.decode(backStackEntry.arguments?.getString("journalTitle").orEmpty(), StandardCharsets.UTF_8.toString())
        val coverId = backStackEntry.arguments?.getLong("coverId") ?: 0
        val configInfoJson = backStackEntry.arguments?.getString("configInfoJson")
        val configInfo = configInfoJson?.let {
            val json = URLDecoder.decode(configInfoJson, "UTF-8")
            Gson().fromJson(json, PageConfigInfo::class.java)
        }
        NoteTclTheme {
            JournalContentScreen(
                navController = navController,
                journalId = journalId,
                journalTitle = journalTitle,
                coverId = coverId,
                modifier = Modifier.fillMaxSize(),
                isFullMode = true,
                configInfo = configInfo,
            ) {
                navController.previousBackStackEntry?.savedStateHandle?.set("refresh_journalId", journalId)
                navController.navigateUp()
            }
        }
    }
}

fun NavGraphBuilder.settingsScreen(navController: NavController) {
    composable(
        route = ROUTE_SETTINGS_SCREEN,
    ) {
        SettingsScreen(navController = navController)
    }
}

fun NavGraphBuilder.aiWritingAssistantScreen(navController: NavController) {
//    composable(
//        route = "${ROUTE_WRITE_ASSISTANT_SCREEN}?travelImageGroup={travelImageGroup}",
//        arguments = listOf(
//            navArgument("travelImageGroup") {
//                navArgument("travelDiaryImageGroup") {
//                    type = TravelDiaryImageGroupListNavType()
//                    nullable = false
//                }
//            }
//        )
//    ) { backStackEntry ->
//        val imageGroups = backStackEntry.arguments
//            ?.getString("travelDiaryImageGroup")
//            ?.let { TravelDiaryImageGroupListNavType().parseValue(it) }
//            ?: emptyList()
//        AiWritingAssistantScreen(navController = navController, imageGroups = imageGroups)
//    }
}