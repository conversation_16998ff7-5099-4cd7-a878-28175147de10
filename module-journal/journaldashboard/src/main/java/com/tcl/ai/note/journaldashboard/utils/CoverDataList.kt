package com.tcl.ai.note.journaldashboard.utils

import com.tcl.ai.note.resources.R
import com.tcl.ai.note.journaldashboard.bean.Cover

object CoverDataList {
    val coverList = mapOf(
        1 to Cover(1, R.drawable.cover_1),
        2 to Cover(2, <PERSON><PERSON>drawable.cover_2),
        3 to Cover(3, <PERSON><PERSON>drawable.cover_3),
        4 to Cover(4, <PERSON>.drawable.cover_4),
        5 to Cover(5, R.drawable.cover_5),
        6 to Cover(6, <PERSON><PERSON>drawable.cover_6),
        7 to Cover(7, <PERSON>.drawable.cover_7),
        8 to Cover(8, R.drawable.cover_8),
        9 to Cover(9, <PERSON><PERSON>drawable.cover_9),
        10 to Cover(10, R.drawable.cover_10),
        11 to Cover(11, <PERSON><PERSON>drawable.cover_11),
        12 to Cover(12, R.drawable.cover_12),
        13 to Cover(13, <PERSON>.drawable.cover_13),
        14 to Cover(14, <PERSON>.drawable.cover_14),
        15 to Cover(15, <PERSON><PERSON>drawable.cover_15),
        16 to Cover(16, <PERSON>.drawable.cover_16),
        17 to Cover(17, <PERSON><PERSON>drawable.cover_17),
        18 to Cover(18, <PERSON><PERSON>drawable.cover_18),
        19 to Cover(19, <PERSON>.drawable.cover_19),
        20 to Cover(20, R.drawable.cover_20),
        21 to Cover(21, R.drawable.cover_21),
        22 to Cover(22, R.drawable.cover_22),
        23 to Cover(23, R.drawable.cover_23),
        24 to Cover(24, R.drawable.cover_24),
        25 to Cover(25, R.drawable.cover_25),
        26 to Cover(26, R.drawable.cover_26),
        27 to Cover(27, R.drawable.cover_27),
    )
}
