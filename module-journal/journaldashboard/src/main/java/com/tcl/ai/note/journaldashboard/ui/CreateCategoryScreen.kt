package com.tcl.ai.note.journaldashboard.ui

import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.database.entity.JournalCategory
import com.tcl.ai.note.journaldashboard.intent.ConfigIntent
import com.tcl.ai.note.journaldashboard.states.ListNoteCategoryState
import com.tcl.ai.note.journaldashboard.vm.DashboardModel
import com.tcl.ai.note.utils.ToastUtils


@Deprecated("使用新的创建分类弹框代替")
@Composable
fun CreateCategoryScreen(
    viewModel: DashboardModel = hiltViewModel(),
    isPreviewMode: Boolean,
    isAddCategoryMode: Boolean,
    showToast: Boolean = true,
    onDismissRequest: () -> Unit,
    onConfirmClick: (JournalCategory) -> Unit,
) {
    // Get category states from ViewModel
    val listNoteCategoryState by viewModel.listNoteCategoryState.collectAsState()
    val selectedCategory by viewModel.selectedCategory.collectAsState()

    // 分类列表数据
    var items by remember { mutableStateOf(listOf<JournalCategory>()) }
    if (listNoteCategoryState is ListNoteCategoryState.Success) {
        items = (listNoteCategoryState as ListNoteCategoryState.Success).items
    }

    // Hoisted state for the dialog
    var categoryName by remember(selectedCategory) {
        mutableStateOf(
            if (!isAddCategoryMode) selectedCategory.name else ""
        )
    }

    var selectedColorIndex by remember(selectedCategory) {
        mutableIntStateOf(
            selectedCategory.colorIndex
        )
    }

    var originalColorIndex by remember(selectedCategory) {
        mutableIntStateOf(
            selectedCategory.colorIndex
        )
    }

    var isOnChanged by remember { mutableStateOf(false) }

    // Compute dialog states for validation
    val isNotEmpty = selectedCategory.name.trim().isNotEmpty()
    var isExistCategory by remember {
        mutableStateOf(
            isExistCategory(items, categoryName, selectedCategory, isAddCategoryMode)
        )
    }


    val toastText = stringResource(R.string.category_add_success)
    InputDialog(
        title = if (isAddCategoryMode)
            stringResource(R.string.dialog_category_name_title)
        else
            stringResource(R.string.dialog_category_rename_title),
        text = categoryName,
        placeholder = stringResource(id = com.tcl.ai.note.resources.R.string.hint_input_category_name),
        error = stringResource(R.string.category_name_exists),
        isError = isExistCategory,
        maxLength = 20,
        onDismissRequest = onDismissRequest,
        onValueChange = { newName ->
            isExistCategory = isExistCategory(items, newName, selectedCategory, isAddCategoryMode)
            categoryName = newName
            isOnChanged = true
        },
        onConfirm = {
            onDismissRequest()
            if (isPreviewMode) {
                // Preview mode
                val category = JournalCategory(
                    name = categoryName,
                    colorIndex = selectedColorIndex,
                    createTime = System.currentTimeMillis(),
                    modifyTime = System.currentTimeMillis()
                )
                viewModel.handleIntent(ConfigIntent.AddCategory(category, true))
            } else {
                if (isAddCategoryMode) {
                    // Add new category
                    val category = JournalCategory(
                        name = categoryName,
                        colorIndex = selectedColorIndex,
                        createTime = System.currentTimeMillis(),
                        modifyTime = System.currentTimeMillis(),
                        isRename = true
                    )
                    viewModel.handleIntent(ConfigIntent.AddCategory(category, false))
                    if (showToast) {
                        ToastUtils.makeWithCancel(toastText, Toast.LENGTH_SHORT)
                    }
                    onConfirmClick(category)
                } else {
                    // Rename category
                    val newCategory = JournalCategory(
                        categoryId = selectedCategory.categoryId,
                        name = selectedCategory.name
                    )
                    newCategory.modifyTime = System.currentTimeMillis()
                    newCategory.name = categoryName
                    newCategory.colorIndex = selectedColorIndex
                    newCategory.isRename = true
                    viewModel.handleIntent(ConfigIntent.RenameCategory(newCategory))
                    onConfirmClick(newCategory)
                }
            }
        })
}

fun isExistCategory(items:List<JournalCategory>, categoryName:String, selectedCategory: JournalCategory, isAddCategoryMode: Boolean):Boolean{
    return items.any { item ->
        item.name.equals(categoryName, ignoreCase = true) && item.name.isNotEmpty() && (item.categoryId != selectedCategory.categoryId)
    }
}


