/*
package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.LocalActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.items
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.FloatingActionButtonDefaults
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.FabPosition
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.blur
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.navigation.NavController
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.dashboard.ui.DisplayModeTopAppBar
import com.tcl.ai.note.dashboard.ui.EditModeTopAppBar
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.journaldashboard.intent.ConfigIntent
import com.tcl.ai.note.journaldashboard.states.ListNotesUiState
import com.tcl.ai.note.journaldashboard.utils.PhotosPermissionHelper
import com.tcl.ai.note.journaldashboard.vm.DashboardModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.isJournalFastClick
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.setCurCoverRect
import com.tcl.ai.note.widget.SearchBox
import kotlinx.coroutines.delay
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

*/
/**
 * 旅行日记首页
 * *//*

@SuppressLint("DesignSystem", "MutableCollectionMutableState")
@Composable
fun JournalDashboard(
    navController: NavController,
    showBlur: Boolean = false,
    dashboardModel: DashboardModel = hiltViewModel(),
    onTabClick: (Int) -> Unit = {},
    pageIndex: Int = 0,
    onCreateJournalClick: () -> Unit = {},
    onResumeRefresh: (() -> Unit) -> Unit = {}
) {
    val dashboardState by dashboardModel.configState.collectAsState()
    val noteState by dashboardModel.noteState.collectAsState()
    val lifecycleOwner = LocalLifecycleOwner.current
    onResumeRefresh {
        Logger.d("JournalDashboard", "onResumeRefresh")
        dashboardModel.onResumeRefresh()
    }
    // 添加生命周期监听
//    DisposableEffect(lifecycleOwner) {
//        val lifecycleObserver = LifecycleEventObserver { _, event ->
//            if (event == Lifecycle.Event.ON_RESUME) {
//                dashboardModel.onResumeRefresh()
//            }
//        }
//
//        lifecycleOwner.lifecycle.addObserver(lifecycleObserver)
//        onDispose {
//            lifecycleOwner.lifecycle.removeObserver(lifecycleObserver)
//        }
//    }
    val viewType = dashboardState.viewType
    val isFabVisible = dashboardModel.isFabVisible
    val listNotesUiState by dashboardModel.listNotesUiState.collectAsState()
    val searchText = noteState.searchText
    val isSearching = noteState.isSearching
    var editMode by remember { mutableStateOf(false) }
    // 选中的item数据在items中的下标值集合
    var selectedItemCounts by remember { mutableStateOf(setOf<Int>()) }
    // 是否全选模式
    var isSelectedAllMode by remember { mutableStateOf(false) }
    // 是否显示列表数据删除提示框
    var isDeleteDialogShown by remember { mutableStateOf(false) }
    // 是否显示分类列表用于迁移已选中的数据至指定的分类中
    var showCategories by remember { mutableStateOf(false) }
    // 是否显示分类删除提示框
    var isDeleteCategoryDialog by remember { mutableStateOf(false) }
    // 是否删除当前分类下的Note
    var isDeleteSelectedCategoryNotes by remember { mutableStateOf(false) }

    // 是否显示排序方式选择提示框
    var isSortOrderDialogShown by remember { mutableStateOf(false) }

    // 当前分类
    val selectedCategory by dashboardModel.selectedCategory.collectAsState()

    val allJournals by dashboardModel.allJournals.collectAsState()

    val photoPermissionHelper = PhotosPermissionHelper {
        onCreateJournalClick()
    }

    // 监听返回时的刷新标志
    val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle
    LaunchedEffect(savedStateHandle) {
        savedStateHandle?.get<Boolean>("refresh")?.let { refresh ->
            Logger.d("refresh:", refresh.toString())
            if (isSearching) {
                dashboardModel.handleIntent(ConfigIntent.SearchNotes(searchText))
            } else if (refresh) {
                dashboardModel.loadInitialNotes() // 重新加载数据
                dashboardModel.handleIntent(ConfigIntent.GetCategories)
                savedStateHandle.remove<Boolean>("refresh") // 清除标志
            }
        }
        savedStateHandle?.get<Long>("refresh_journalId")?.let { journalId ->
            Logger.d("refresh_journalId:", journalId.toString())
            if (!isSearching) {
                dashboardModel.updateJournalById(journalId)
                savedStateHandle.remove<Long>("refresh_journalId") // 清除标志
            }
        }
    }

    // 是否按照创建时间排序
    val isCreateTimeSort = dashboardModel.isCreateTimeSort

    // 列表数据
    var items by remember { mutableStateOf(listOf<Journal>()) }
    if (listNotesUiState is ListNotesUiState.Success) {
        items = (listNotesUiState as ListNotesUiState.Success).items
    }

    // 选中的notes数据
    val selectedNotes by dashboardModel.selectedNotes.collectAsState()
    val staggeredGridCells = 2

    //是否需要高斯模糊
    var showBlurState by remember { mutableStateOf(showBlur) }
    val modifier = remember(showBlurState) {
        if (showBlurState) {
            Modifier
                .fillMaxSize()
                .blur(20.dp)
        } else {
            Modifier.fillMaxSize()
        }
    }

    LaunchedEffect(showBlurState) {
        if (showBlurState) {
            delay(600)
            showBlurState = false
        }
    }

    LaunchedEffect(key1 = selectedCategory) {
        // 当选中的分类发生变化时，重新加载数据
        showCategories = false
        editMode = false
        selectedItemCounts = emptySet()
        dashboardModel.updateSelectedNotes(emptyMap())
        isSelectedAllMode = false
        if (!isSearching) {
            dashboardModel.updateFabVisibleState(true)
        }
    }

    Box(modifier = modifier) {
        Scaffold(
            contentWindowInsets = ScaffoldDefaults
                .contentWindowInsets
                .exclude(WindowInsets.navigationBars),
            topBar = {
                // 显示模式
                if (!editMode) {
                    DisplayModeTopAppBar(
                        (selectedCategory.categoryId == -1L).judge("", selectedCategory.name),
                        viewType,
                        onSearchClicked = {
                            // 搜索
                            dashboardModel.updateSearchState(true)
                        },
                        onMoreActionsClicked = {
                            // 显示模式 列表or网格
                            val newViewType = if (viewType == DataStoreParam.VIEW_TYPE_LIST) {
                                DataStoreParam.VIEW_TYPE_GRID
                            } else {
                                DataStoreParam.VIEW_TYPE_LIST
                            }
                            dashboardModel.handleIntent(ConfigIntent.ChangeViewType(newViewType))
                        },
                        onSortClicked = {
                            // 排序
                            isSortOrderDialogShown = true
                        },
                        onRenameCategory = {
                            // 重命名分类
                            dashboardModel.updateNewCategoryVisibleState(true)
                            dashboardModel.updateNewCategoryModeState(false)
                        },
                        onDeleteCategory = {
                            // 删除分类
                            isDeleteCategoryDialog = true
                        },
                        onTabClick = {
                            onTabClick(it)
                        },
                        pageIndex = pageIndex,
                        onTitleClicked = {

                        }
                    )
                } else {
                    // 编辑模式
                    EditModeTopAppBar(
                        selectedItemCount = selectedItemCounts,
                        isSelectedAllMode = isSelectedAllMode,
                        onCancel = {
                            // 取消已选中的回到显示模式
                            editMode = false
                            selectedItemCounts = emptySet()
                            dashboardModel.updateSelectedNotes(emptyMap())
                            isSelectedAllMode = false
                            dashboardModel.updateFabVisibleState(true)
                        },
                        onSelectedAll = {
                            // 全选
                            isSelectedAllMode = !isSelectedAllMode
                            selectedItemCounts = emptySet()
                            val tempSelectedNotes = mutableMapOf<Long, Journal>()
                            if (isSelectedAllMode) {
                                allJournals.forEach { item ->
                                    tempSelectedNotes[item.journalId] = item
                                    val index = allJournals.indexOf(item)
                                    selectedItemCounts = selectedItemCounts + index
                                }
                            }
                            dashboardModel.updateSelectedNotes(tempSelectedNotes)
                        },
                        onDeleteSelected = {
                            // 删除选中的item
                            isDeleteDialogShown = true
                        },
                        onMoveTo = {
                            // 移至某一个分类
                            showCategories = true
                        },
                        onTabClick = {
                            onTabClick(it)
                        },
                        pageIndex = pageIndex,
                    )
                }

            },
            floatingActionButton = {
                if (isFabVisible) {
                    FloatingActionButton(
                        onClick = {
                            photoPermissionHelper.invoke {  }
                        },
                        elevation = FloatingActionButtonDefaults.elevation(0.dp), // 移除投影
                        backgroundColor = com.tct.theme.core.designsystem.theme.TclTheme.colorScheme.tctStanderAccentPrimary, // 设置FloatingActionButton的背景颜色
                        contentColor = Color.White, // 设置图标的颜色
                        modifier = Modifier
                            .navigationBarsPadding()
                            .padding(end = 12.dp, bottom = 12.dp)
                            .size(
                                isDensity440.judge(61.dp, 56.dp),
                                isDensity440.judge(61.dp, 56.dp)
                            ),
                    ) {
                        Icon(Icons.Filled.Add, contentDescription = "FAB Icon", tint = Color.White)
                    }
                }
            },
            floatingActionButtonPosition = FabPosition.End,
            content = { innerPadding ->
                // Your screen content here
                Box(modifier = Modifier.padding(innerPadding)) {
                    // Content goes here
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(color = TclTheme.colorScheme.tctGlobalBgColor)
                            .padding(
                                bottom = WindowInsets.navigationBars
                                    .asPaddingValues()
                                    .calculateBottomPadding()
                            ),

                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        //分类选择
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .zIndex(1f) //让分类选择框在最上面
                        ) {
                            // Use your original composable
                            NoteCategoryScreenPlaceholder(
                                editMode = editMode,
                                isSearching = isSearching
                            )
                        }

                        //列表数据加载
                        when (listNotesUiState) {
                            is ListNotesUiState.Loading -> {
                                CircularProgressIndicator(modifier = Modifier.padding(16.dp))
                            }

                            is ListNotesUiState.Success -> {
                                //数据加载成功
                                items = (listNotesUiState as ListNotesUiState.Success).items
                                if (items.isEmpty() && !dashboardModel.isLoading) {
                                    //没有笔记数据
                                    NoJournalScreen(isSearching && searchText.isNotEmpty())
                                } else {
                                    // 正常列表渲染

                                    // 自动加载更多
                                    val lazyListState = rememberLazyListState() // 为列表布局添加滚动状态跟踪

                                    LaunchedEffect(Unit) {
                                        dashboardModel.refreshData.collect {
                                            Logger.d("JournalDashboard", "refreshData")
                                            lazyListState.scrollToItem(0)
                                        }
                                    }
                                    val listShouldLoadMore = remember {
                                        derivedStateOf {
                                            // 检测是否滚动到底部最后3个元素
                                            val layoutInfo = lazyListState.layoutInfo
                                            val totalItems = layoutInfo.totalItemsCount
                                            val lastVisibleItem =
                                                layoutInfo.visibleItemsInfo.lastOrNull()

                                            lastVisibleItem?.index != null &&
                                                    lastVisibleItem.index >= totalItems - 3 &&
                                                    !dashboardModel.isLoading &&
                                                    dashboardModel.hasMore
                                        }
                                    }

                                    LaunchedEffect(listShouldLoadMore) {
                                        snapshotFlow { listShouldLoadMore.value }
                                            .collect {
                                                if (it && !dashboardModel.isLoading && dashboardModel.hasMore) {
                                                    dashboardModel.loadMoreNotes()
                                                }
                                            }
                                    }
                                    if (viewType == DataStoreParam.VIEW_TYPE_LIST) {
                                        FlatViewList(
                                            items,
                                            lazyListState,
                                            editMode,
                                            selectedNotes,
                                            searchQuery = searchText,
                                            onClick = { index, boundsRect ->
                                                Logger.d("zcl", "onClick index: $index")
                                                val item = items[index]
                                                if (editMode) {
                                                    selectedItemCounts =
                                                        if (selectedItemCounts.contains(index)) {
                                                            selectedItemCounts - index
                                                        } else {
                                                            selectedItemCounts + index
                                                        }
                                                    isSelectedAllMode =
                                                        selectedItemCounts.size == allJournals.size
                                                    val tempSelectedNotes =
                                                        mutableMapOf<Long, Journal>()
                                                    tempSelectedNotes.putAll(selectedNotes)
                                                    if (tempSelectedNotes.contains(item.journalId)) {
                                                        tempSelectedNotes.remove(item.journalId)
                                                    } else {
                                                        tempSelectedNotes[item.journalId] = item
                                                    }
                                                    dashboardModel.updateSelectedNotes(tempSelectedNotes)
                                                } else {
                                                    //跳转到详情页
                                                    if (isJournalFastClick()) return@FlatViewList
                                                    if (boundsRect != null) {
                                                        showBlurState = true
                                                        setCurCoverRect(boundsRect)
                                                        val encodedTitle = URLEncoder.encode(item.title, StandardCharsets.UTF_8.toString())
                                                        val route = "$ROUTE_JOURNAL_CONTENT_SCREEN?journalId=${item.journalId}&journalTitle=${encodedTitle}&coverId=${item.coverId}"
                                                        navController.navigate(route)
                                                    }
                                                }
                                            },
                                            onLongClick = { index ->
                                                if (!isSearching && !editMode) {
                                                    editMode = true

                                                    val item = items[index]
                                                    dashboardModel.updateFabVisibleState(false)
                                                    if (!selectedItemCounts.contains(index)) {
                                                        selectedItemCounts =
                                                            selectedItemCounts + index
                                                    }
                                                    val tempSelectedNotes =
                                                        mutableMapOf<Long, Journal>()
                                                    tempSelectedNotes[item.journalId] = item
                                                    dashboardModel.updateSelectedNotes(tempSelectedNotes)
                                                }
                                            })
                                    } else {
                                        //瀑布流展示
                                        // 自动加载更多
                                        val staggeredGridState =
                                            rememberLazyStaggeredGridState() // 为瀑布流布局添加滚动状态跟踪
                                        val gridShouldLoadMore = remember {
                                            derivedStateOf {
                                                // 检测是否滚动到底部最后3个元素
                                                val layoutInfo = staggeredGridState.layoutInfo
                                                val totalItems = layoutInfo.totalItemsCount
                                                val lastVisibleItem =
                                                    layoutInfo.visibleItemsInfo.lastOrNull()

                                                lastVisibleItem?.index != null &&
                                                        lastVisibleItem.index >= totalItems - 3 &&
                                                        !dashboardModel.isLoading &&
                                                        dashboardModel.hasMore
                                            }
                                        }

                                        LaunchedEffect(gridShouldLoadMore) {
                                            snapshotFlow { gridShouldLoadMore.value }
                                                .collect {
                                                    if (it && !dashboardModel.isLoading && dashboardModel.hasMore) {
                                                        dashboardModel.loadMoreNotes()
                                                    }
                                                }
                                        }
                                        var paddingSize = 12.dp
                                        LazyVerticalStaggeredGrid(
                                            state = staggeredGridState,
                                            columns = StaggeredGridCells.Fixed(staggeredGridCells), // Change 2 to the number of columns you want
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .padding(
                                                    start = paddingSize,
                                                    end = paddingSize
                                                )
                                        ) {
                                            items(items) { item ->
                                                val index = items.indexOf(item)
                                                // 瀑布流item展示
                                                */
/*GridViewItem(
                                                    dashboardModel,
                                                    item,
                                                    editMode,
                                                    isSelected = selectedItemCounts.contains(index),
                                                    isActive = false,
                                                    searchQuery = searchText,
                                                    onClick = {
                                                        if(editMode){
                                                            selectedItemCounts = if(selectedItemCounts.contains(index)){
                                                                selectedItemCounts - index
                                                            }else{
                                                                selectedItemCounts + index
                                                            }
                                                            isSelectedAllMode = selectedItemCounts.size == items.size
                                                            selectedNotes = if (selectedNotes.contains(item)) {
                                                                selectedNotes - item
                                                            } else {
                                                                selectedNotes + item
                                                            }
                                                        }else{
                                                            //跳转到详情页
                                                            navController.navigate("edit_screen?noteId=${item.collectionId}")
//                                                            Toast.makeText(context, "跳转到详情页", Toast.LENGTH_SHORT).show()
                                                        }
                                                    },
                                                    onLongClick = {
                                                        dashboardModel.updateFabVisibleState(false)
                                                        editMode = true
                                                        if(!selectedItemCounts.contains(index)){
                                                            selectedItemCounts = selectedItemCounts + index
                                                        }
                                                        if(!selectedNotes.contains(item)){
                                                            selectedNotes = selectedNotes + item
                                                        }
                                                    }
                                                )*//*

                                            }

                                        }
                                    }
                                }
                            }

                            is ListNotesUiState.Error -> {
                                //数据加载失败
                                NoJournalScreen(false)
                            }
                        }

                    }
                }
            }
        )
        // 如果 isSearching 为 true，监听返回键进行处理
        BackHandler(isSearching || editMode || showCategories) {
            if (showCategories) {
                showCategories = false
            } else if (isSearching) {
                dashboardModel.handleIntent(ConfigIntent.SearchNotes(""))
                dashboardModel.updateSearchState(false)
                dashboardModel.loadInitialNotes()
            } else {
                editMode = false
                selectedItemCounts = emptySet()
                dashboardModel.updateSelectedNotes(emptyMap())
                isSelectedAllMode = false
            }
            dashboardModel.updateFabVisibleState(true)
        }
        if (isSearching) {
            // 搜索框内容为空则显示半透明遮罩层
            if (searchText.trim().isEmpty()) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(
                            top = 56.dp + WindowInsets.statusBars
                                .asPaddingValues()
                                .calculateTopPadding()
                        )
                        .background(Color.Black.copy(alpha = 0.5f))
                        .clickable { dashboardModel.updateSearchState(false) } // 点击遮罩层取消搜索
                        .zIndex(0.5f) // 将遮罩层置于搜索框下方，但在内容上方
                )
            }

            // 搜索框
            SearchBox(
                text = searchText,
                onTextChange = {
                    val isAllSpaces = it.text.isNotEmpty() && it.text.trim().isEmpty()
                    if (!isAllSpaces) {
                        dashboardModel.updateFabVisibleState(it.text.isEmpty())
                        dashboardModel.handleIntent(ConfigIntent.SearchNotes(it.text))
                    } else {
                        dashboardModel.updateFabVisibleState(true)
                    }

                },
                onClear = {
                    dashboardModel.updateFabVisibleState(true)
                    dashboardModel.handleIntent(ConfigIntent.SearchNotes(""))
                },
                onBack = {
                    dashboardModel.updateFabVisibleState(true)
                    dashboardModel.updateSearchState(false)
                    dashboardModel.handleIntent(ConfigIntent.SearchNotes(""))
                    dashboardModel.loadInitialNotes()
                },
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(
                        top = WindowInsets.statusBars
                            .asPaddingValues()
                            .calculateTopPadding(), start = 0.dp, end = 0.dp
                    )
                    .zIndex(1f) // Ensure the search box is on top
            )
        }
        if (isDeleteDialogShown) {
            DeleteDataDialog(
                text = if (selectedItemCounts.size == 1) stringResource(com.tcl.ai.note.resources.R.string.dialog_title_delete_one_journal) else String.format(
                    stringResource(com.tcl.ai.note.resources.R.string.dialog_title_delete_multiple_journals),
                    selectedItemCounts.size
                ),
                onDelete = {
                    isDeleteDialogShown = false
                    // 删除选中的items
                    val noteIds = selectedNotes.keys.toList()
                    dashboardModel.handleIntent(ConfigIntent.DeleteNotes(noteIds))
                    editMode = false
                    dashboardModel.updateFabVisibleState(true)
                    selectedItemCounts = emptySet()
                    dashboardModel.updateSelectedNotes(emptyMap())
                    isSelectedAllMode = false
                },
                onDismiss = {
                    isDeleteDialogShown = false
                }
            )
        }

        if (showCategories) {
            MoveToNoteCategoryScreen(
                onBack = {
                    showCategories = false
                },
                onMoveToCategory = { categoryId ->
                    // 将选中的Notes移至指定的分类
                    val noteIds = selectedNotes.keys.toList()
                    dashboardModel.handleIntent(
                        ConfigIntent.UpdateNotesCategoryId(
                            categoryId,
                            noteIds
                        )
                    )
                    dashboardModel.updateFabVisibleState(true)
                    showCategories = false
                    editMode = false
                    selectedItemCounts = emptySet()
                    dashboardModel.updateSelectedNotes(emptyMap())
                    isSelectedAllMode = false
                },
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(
                        top = WindowInsets.statusBars
                            .asPaddingValues()
                            .calculateTopPadding(), start = 0.dp, end = 0.dp
                    )
                    .zIndex(1f)
            )
        }
        if (isDeleteCategoryDialog) {
            BottomDeleteCategoryDialog(
                onCancel = {
                    isDeleteCategoryDialog = false
                    isDeleteSelectedCategoryNotes = false
                },
                onDelete = {
                    dashboardModel.handleIntent(
                        ConfigIntent.DeleteCategory(
                            isDeleteSelectedCategoryNotes,
                            selectedCategory
                        )
                    )
                    isDeleteCategoryDialog = false
                },
                isDeleteNotesSelected = {
                    isDeleteSelectedCategoryNotes = it
                },
                isShowDeleteNotes = items.isNotEmpty()
            )
        }

        if (isSortOrderDialogShown) {
            BottomSortOrderDialog(
                onCancel = {
                    isSortOrderDialogShown = false
                },
                onSelected = {
                    dashboardModel.updateSortModeState(it)
                    isSortOrderDialogShown = false
                    // 执行排序查询操作
                    dashboardModel.loadInitialNotes()
                },
                isCreateDate = isCreateTimeSort
            )
        }
    }
}


@Composable
private fun NoteCategoryScreenPlaceholder(editMode: Boolean, isSearching: Boolean = false) {
    var measuredHeight by remember { mutableStateOf(0.dp) }
    SubcomposeLayout { constraints ->
        if (measuredHeight == 0.dp) {
            // 1. 先测量 NoteCategoryScreen 的高度
            val noteCategoryPlaceable = subcompose("NoteCategoryScreen") {
                NoteCategoryScreen()
            }.first().measure(constraints)
            measuredHeight = noteCategoryPlaceable.height.toDp()
        }

        // 2. 根据 editMode 决定渲染内容
        val contentPlaceable = subcompose("Content") {
            if (!editMode && !isSearching) {
                NoteCategoryScreen()
            } else {
                // 渲染一个相同高度的空 Box
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(measuredHeight)
                )
            }
        }.first().measure(constraints)
        // 3. 布局
        layout(contentPlaceable.width, contentPlaceable.height) {
            contentPlaceable.place(0, 0)
        }
    }
}*/
