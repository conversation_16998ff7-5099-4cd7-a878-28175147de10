package com.tcl.ai.note.journalhome.components.categorydialog

import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.mapSaver
import androidx.compose.runtime.saveable.rememberSaveable
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update

/**
 * 分类弹窗控制器 */
class CategoryDialogController(
    private val dialogState: MutableStateFlow<CategoryDialogState>
) {
    fun showNewCategoryDialog(isMoveToNewCategory:Boolean) {
        Logger.v("CategoryDialogController","showNewCategoryDialog, isMoveToNewCategory:$isMoveToNewCategory")
        dialogState.update { state ->
            state.copy(
                isVisible = true,
                isMoveToNewCategory = isMoveToNewCategory,
                type = CategoryDialogType.NEW_CATEGORY
            )
        }
    }

    fun showEditCategoryDialog(dialogCategory: DialogCategory) {
        dialogState.update { state ->
            state.copy(
                isVisible = true,
                isMoveToNewCategory = false,
                type = CategoryDialogType.EDIT_CATEGORY,
                editingDialogCategory = dialogCategory
            )
        }
    }

    fun showMoveNoteDialog(dialogNote: DialogNote) {
        dialogState.update { state ->
            state.copy(
                isVisible = true,
                type = CategoryDialogType.MOVE_NOTE_TO_CATEGORY,
                dialogNoteToMove = dialogNote,
            )
        }
    }

    fun showMoveNotesDialog(dialogNotes: List<DialogNote>,categoryId: Long, isPreviewMode:Boolean) {
        Logger.v("CategoryDialogController","showMoveNotesDialog, categoryId:$categoryId,isPreviewMode:$isPreviewMode, dialogNotes.size:${dialogNotes.size}")
        dialogState.update { state ->
            state.copy(
                isVisible = true,
                isPreviewMode = isPreviewMode,
                type = CategoryDialogType.MOVE_NOTE_TO_CATEGORY,
                notesToMove = dialogNotes,
                selectedCategoryId = categoryId
            )
        }
    }

    fun dismiss() {
        dialogState.value = dialogState.value.copy(isVisible = false)
    }

    val state: MutableStateFlow<CategoryDialogState> = dialogState
}


/**
 * 为CategoryDialogState创建一个Saver
 */
val CategoryDialogStateSaver = mapSaver(
    save = { state ->
        mapOf(
            "isVisible" to state.isVisible,
            "type" to state.type.name
            // 注意：这里没有保存editingCategory、noteToMove和notesToMove
            // 因为它们可能包含复杂数据结构，需要更复杂的序列化
        )
    },
    restore = { savedState ->
        CategoryDialogState(
            isVisible = savedState["isVisible"] as Boolean,
            isPreviewMode = savedState["isPreviewMode"] as Boolean,
            type = CategoryDialogType.valueOf(savedState["type"] as String)
            // 恢复时，其他字段使用默认值null和emptyList()
        )
    }
)


/**
 * 创建并记住一个CategoryDialogState实例 */
@Composable
fun rememberCategoryDialogState(): MutableState<CategoryDialogState> {
    return rememberSaveable(
        stateSaver = CategoryDialogStateSaver
    ) {
        mutableStateOf(CategoryDialogState())
    }
}

/**
 * 创建并记住一个CategoryDialogController实例
 * 简化版本：每次都创建新的Controller，但使用可保存的State
 */
//@Composable
//fun rememberCategoryDialogController(): CategoryDialogController {
//    val dialogState = rememberCategoryDialogState()
//
//    // 使用remember，但依赖于dialogState，这样当dialogState恢复时，Controller也会重新创建
//    return remember(dialogState) {
//        CategoryDialogController(dialogState)
//    }
//}