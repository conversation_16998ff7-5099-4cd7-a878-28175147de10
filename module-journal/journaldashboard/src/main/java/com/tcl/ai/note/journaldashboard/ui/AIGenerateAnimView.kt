package com.tcl.ai.note.journaldashboard.ui

import android.graphics.RectF
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import com.airbnb.lottie.LottieComposition
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.judge

sealed class AIGenerateState {
    data object Loading : AIGenerateState()           // 数据加载中，显示loading动画
    data object MagicEffect : AIGenerateState()      // 数据已经返回，展示临时动效
    data object Content : AIGenerateState()    // 展示数据内容
}

@Composable
fun AIGenerateAnimView(
    textRectFList: List<RectF>,
    state: AIGenerateState,
    lightComposition: LottieComposition?,
    loadingComposition: LottieComposition?,
    magicComposition: LottieComposition?,
    modifier: Modifier = Modifier,
    onAnimEnd: () -> Unit = {}
) {
    val rectList by rememberUpdatedState(textRectFList)
    val density = LocalDensity.current

    val scale = remember {
        if (GlobalContext.screenWidth / 360f < GlobalContext.screenHeight / 780f) {
            Offset(
                x = 1f,
                y = GlobalContext.screenHeight / (GlobalContext.screenWidth / 360f * 780f)
            )
        } else {
            Offset(
                x = GlobalContext.screenWidth / (GlobalContext.screenHeight / 780f * 360f),
                y = 1f
            )
        }
    }

    Box(modifier = modifier) {
        val progress by animateLottieCompositionAsState(
            lightComposition,
            iterations = LottieConstants.IterateForever,
        )

        LottieAnimation(
            composition = lightComposition,
            progress = progress,
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer(
                    scaleX = scale.x,
                    scaleY = scale.y,
                ),
        )

        if (state == AIGenerateState.Loading) {
            TextLottieAnimation(
                composition = loadingComposition,
                state = state,
                density = density,
                rectList = rectList,
                onAnimEnd = onAnimEnd,
            )
        } else if (state == AIGenerateState.MagicEffect) {
            TextLottieAnimation(
                composition = magicComposition,
                state = state,
                density = density,
                rectList = rectList,
                onAnimEnd = onAnimEnd,
            )
        }
    }
}

@Composable
fun TextLottieAnimation(
    composition: LottieComposition?,
    state: AIGenerateState,
    density: Density,
    rectList: List<RectF>,
    onAnimEnd: () -> Unit = {}
) {
    val heightRatio = remember { GlobalContext.screenHeight / 780f }
    // 原始动画高度
    val originalAnimHeight = remember { 64f * heightRatio }

    rectList.forEachIndexed { index, rectF ->
        val progress by animateLottieCompositionAsState(
            composition,
            iterations = (state == AIGenerateState.MagicEffect).judge(
                1,
                LottieConstants.IterateForever
            )
        )
        if (index == 0 && state == AIGenerateState.MagicEffect) {
            LaunchedEffect(progress, state) {
                if (progress == 1f) {
                    onAnimEnd()
                }
            }
        }

        //实际动画高度
        val actualAnimHeight = remember(rectF.width()) { rectF.width() / 180f * 64f }
        // 缩放比例
        val scale = remember(rectF.width()) { originalAnimHeight / actualAnimHeight }
        // 文本框内容留白高度
        val contentSpace = remember(rectF.width()) { (actualAnimHeight * scale / 64f * 12f) }
        // 缩放造成的留白高度
        val scaleSpace = remember(rectF.width()) { (actualAnimHeight - originalAnimHeight) / 2f }

        with(density) {
            LottieAnimation(
                composition = composition,
                progress = progress,
                modifier = Modifier
                    .offset(x = rectF.left.toDp(), y = rectF.top.toDp())
                    .width(rectF.width().toDp())
                    .aspectRatio(180f / 64f)
                    .graphicsLayer {
                        translationY = -(contentSpace + scaleSpace)
                        scaleY = scale
                    },
            )
        }
    }
}