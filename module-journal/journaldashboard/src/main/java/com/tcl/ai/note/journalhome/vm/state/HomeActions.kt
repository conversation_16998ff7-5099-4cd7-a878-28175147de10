package com.tcl.ai.note.journalhome.vm.state

import com.tcl.ai.note.journalhome.components.notelist.NavigationTab
import com.tcl.ai.note.journalhome.entity.HomeCategoryItemEntity
import com.tcl.ai.note.journalhome.entity.HomeNoteItemEntity
import java.util.Locale

/**
 * 右侧笔记相关操作
 */
sealed class NoteListAction {
    data class OnItemCheckedChange(val noteId: Long, val checked: Boolean) : NoteListAction()
    //长按选中
    data class OnItemLongClick(val noteId: Long) : NoteListAction()
    //新建笔记和跳转笔记
    data class OnAddNoteClick(val noteId: String, val isPen: Boolean = false) : NoteListAction()
    data class OnChangeViewType(val viewType: String) : NoteListAction()
    //标题模式
    data class OnChangeTitleMode(val titleMode: HomeTitleMode) : NoteListAction()
    //排序
    data class OnChangeSortType(val isCreateTimeSort: Boolean?=null) : NoteListAction()
    //点击了排序
    data class OnClickSort(val clickSorted:Boolean) : NoteListAction()
    //全选
    data class OnSelectAllClick(val isSelectAll: Boolean) : NoteListAction()
    //搜索
    data class OnSearchTextChange(val searchText: String,val isSearchInputFocused: Boolean = false) : NoteListAction()
    //删除
    data object OnDelete : NoteListAction()
    // 移动到分类
    data object OnMoveToCategory : NoteListAction()
    // show dialog 删除笔记弹窗
    data class OnShowDeleteDialog(val isShowDialog: Boolean=false) : NoteListAction()

    data class OnNavigateTo(val tab: NavigationTab) : NoteListAction()
    // 打开抽屉
    data object OnOpenDrawer : NoteListAction()
    // 时间格式变化，需要重新加载数据以更新时间显示
    data class OnTimeFormatChanged(val is24Hour: Boolean) : NoteListAction()
    //点击了展示方式
    data class OnClickView(val clickView: Boolean) : NoteListAction()
    //跳转设置页
    data object GoToSettings : NoteListAction()


    data class OnShowDeleteItemDialog(val isShowDialog: Boolean = false, val deleteItem: HomeNoteItemEntity) : NoteListAction()
    data class OnDeleteItem(val journal: HomeNoteItemEntity?) : NoteListAction()
    // 移动到分类
    data class OnMoveItem(val journal: HomeNoteItemEntity) : NoteListAction()
    data class ShowOrDismissPopScreen(val isShow: Boolean) : NoteListAction() //是否有显示半个弹层
}

/**
 * 左侧分类相关操作
 */
sealed class CategoryAction {
    //分类选中
    data class OnCategorySelected(val category: HomeCategoryItemEntity) : CategoryAction()
    //新建分类
    data class OnCreateNewCategoryClick(val isShowDialog: Boolean=false) : CategoryAction()
    // 重命名分类
    data class OnRenameCategoryClick(val isShowDialog: Boolean=false, val category: HomeCategoryItemEntity) : CategoryAction()
    //删除分类
    data class OnDeleteCategoryClick(
        val isDeleteNotesSelected: Boolean,
        val category: HomeCategoryItemEntity
    ) : CategoryAction()
    //长按选中 效果
    data class OnLongClickSelected(
        val selectedCategory: HomeCategoryItemEntity,
        val isLongClickSelected: Boolean =true) : CategoryAction()
    // show dialog 删除分类弹窗
    data class OnShowDeleteCategoryNotesDialog(val isShowDialog: Boolean) : CategoryAction()

    data class OnLocaleChange(val newLocale: Locale) : CategoryAction()
}