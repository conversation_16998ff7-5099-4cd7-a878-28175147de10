package com.tcl.ai.note.journalhome.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.delay

/**
 *  author : junze.liu
 *  date : 2025-07-07 13:56
 *  description :
 */

@Composable
internal fun HomePopItem(
    text: String,
    showCheckIcons:Boolean = false,
    onClick: () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val delayMillis = 200L
    Logger.w("PopItem", "isPressed:$isPressed")
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(40.dp)
            .clip(RoundedCornerShape(16.dp)) // 圆角
            .background(
                if (isPressed) colorResource(R.color.bottom_sheet_dialog_drag_bar_color) else Color.Transparent
            )
            .padding(horizontal = 16.dp)
            .clickable(
                interactionSource = interactionSource,
                indication = null, // 禁用默认水波
                onClick = onClick
            ),

        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = text,
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal,
            fontFamily = FontFamily.Default,
            color = colorResource(R.color.home_title_color),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.weight(1f)
        )

        val shouldShowIcon by rememberDelayedVisibility(showCheckIcons, delayMillis)

        if (shouldShowIcon) {
            Icon(
                painter = painterResource(id = R.drawable.ic_home_note_sort_check),
                contentDescription = null,
                tint = colorResource(R.color.bg_float_action_button),
                modifier = Modifier.padding(horizontal = 4.dp).size(24.dp)
            )
        }
    }
}

@Composable
fun rememberDelayedVisibility(
    visible: Boolean,
    delayMillis: Long = 200
): State<Boolean> {
    val internalVisible = remember { mutableStateOf(false) }

    LaunchedEffect(visible) {
        if (visible) {
            // 当需要显示时，延时指定时间
            delay(delayMillis)
            internalVisible.value = true
        } else {
            // 当需要隐藏时，立即执行
            internalVisible.value = false
        }
    }

    return internalVisible
}
