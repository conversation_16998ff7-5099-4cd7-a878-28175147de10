package com.tcl.ai.note.journaldashboard.track

import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.journaldashboard.vm.JournalContentViewModel
import com.tcl.ai.note.journaldashboard.vm.SettingsViewModel.Companion.SETTINGS_INSPIRATION_SUGGESTION
import com.tcl.ai.note.track.AbsAnalyticsSubModel
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.judge
import kotlinx.coroutines.launch


/**
 * 分析AI概要使用情况
 *
 * 用于上报埋点
 */
object AnalyticsJournalContentModel : AbsAnalyticsSubModel() {
    fun loadJournalContentViewModel(journalContentViewModel: JournalContentViewModel) {
        reportCreateJournalState(journalContentViewModel)
    }

    /**
     * 上报AI概要获取获取状态
     */
    private fun reportCreateJournalState(journalContentViewModel: JournalContentViewModel) {
        journalContentViewModel.reportJournalContent.collectWithScope(journalContentViewModel.viewModelScope) { result ->
            if (result.createType.isNotEmpty()) {
                journalContentViewModel.viewModelScope.launch {
                    val recommendOn = AppDataStore.getBoolean(SETTINGS_INSPIRATION_SUGGESTION, true)
                    TclAnalytics.reportJournalContent(
                        createType = result.createType,
                        recommendOn = recommendOn,
                        template = result.template,
                        isAIUsed = result.isAIUsed,
                        aiRewriteCount = result.aiRewriteCount.toString(),
                        photoCount = result.photoCount,
                        wordCount = result.wordCount,
                        pageStayDuration = ((result.pageEndTime - result.pageStartTime) / 1000L).toString(),
                    )
                }
            }
        }

        journalContentViewModel.reportAiWritingData.collectWithScope(journalContentViewModel.viewModelScope) { result ->
            result?.let {
                TclAnalytics.reportJournalAiWrite(
                    createType = it.createType.toString(),
                    style = it.style,
                    companion = it.companion,
                    wordCount = it.wordCount,
                    specialEventFilled = it.specialEventFilled,
                    specialEventStringCount = it.specialEventFilled.length.toString(),
                    operationDuration = ((System.currentTimeMillis() - it.newPageStartTime) / 1000).toString(),
                    generationDuration = ((it.generateEndTime - it.generateStartTime) / 1000).toString(),
                    isAdopted = it.isAdopted,
                    isRewrite = it.operationType == 1
                )
            }
        }
    }
}