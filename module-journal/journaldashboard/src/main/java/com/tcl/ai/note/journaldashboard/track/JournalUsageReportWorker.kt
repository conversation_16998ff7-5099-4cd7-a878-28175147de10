package com.tcl.ai.note.journaldashboard.track

import android.content.Context
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.database.JournalEntryPoint
import com.tcl.ai.note.database.repository.JournalRepository
import com.tcl.ai.note.journaldashboard.vm.SettingsViewModel.Companion.SETTINGS_INSPIRATION_SUGGESTION
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.SPUtils
import dagger.hilt.android.EntryPointAccessors
import java.util.Calendar
import java.util.concurrent.TimeUnit
import javax.inject.Inject

class JournalUsageReportWorker(private val context: Context, private val workerParams: WorkerParameters): CoroutineWorker(context, workerParams) {
    @Inject
    lateinit var journalRepository: JournalRepository

    init {
        initializeDependencies()
    }

    private fun initializeDependencies() {
        val entryPoint =
            EntryPointAccessors.fromApplication(applicationContext, JournalEntryPoint::class.java)
        journalRepository = entryPoint.journalRepository()
    }
    override suspend fun doWork(): Result {
        Logger.i(TAG, "doWork")
        val journalCount = journalRepository.getNoteCount()
        val createCount = AppDataStore.getData("createJournalCount", "0")
        val deleteCount = AppDataStore.getData("deleteJournalCount", "0")
        val viewType = SPUtils.getString(key = DataStoreParam.KEY_VIEW_TYPE, defValue = DataStoreParam.VIEW_TYPE_LIST)
        val recommendOn = AppDataStore.getBoolean(SETTINGS_INSPIRATION_SUGGESTION, true)
        TclAnalytics.reportJournalUsage(
            journalCount = journalCount,
            createCount = createCount,
            deleteCount = deleteCount,
            viewMode = viewType!!,
            recommendOn = recommendOn
        )
        resetReportData()
        return Result.success()
    }

    companion object {
        private const val TAG = "JournalUsageReportWorker"

        fun scheduleTask() {
            Logger.i(TAG, "schedule report Task")
            // 计算到次日凌晨2点的初始延迟
            val calendar = Calendar.getInstance()
            val now = calendar.timeInMillis

            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)

            var initialDelay = calendar.timeInMillis - now
            if (initialDelay < 0) {
                // 如果已经过了今天的 2 点，就加一天
                initialDelay += 24 * 60 * 60 * 1000
            }

            // 构建约束（可空，根据需求添加其他约束）
            val constraints = Constraints.Builder()
                .setRequiresBatteryNotLow(false) // 不依赖系统低电量判断
                .setRequiresDeviceIdle(false)  // 设置设备不需要闲置状态
                .setRequiresCharging(false)    // 设置任务不需要设备充电
                .build()

            Logger.i(TAG, "initialDelay:$initialDelay")
            // 创建每日重复的WorkRequest
            val workRequest = PeriodicWorkRequestBuilder<JournalUsageReportWorker>(
                24, // 间隔24小时
                TimeUnit.HOURS
            )
                .setInitialDelay(initialDelay, TimeUnit.MILLISECONDS)
                .setConstraints(constraints)
                .setBackoffCriteria(
                    backoffPolicy = BackoffPolicy.EXPONENTIAL,
                    backoffDelay = 30,
                    timeUnit = TimeUnit.MINUTES
                )
                .build()

            // 提交唯一任务（避免重复）
            WorkManager.getInstance(GlobalContext.instance.applicationContext).enqueueUniquePeriodicWork(
                "Report Journal Usage Task",
                ExistingPeriodicWorkPolicy.KEEP,
                workRequest
            )
        }

        suspend fun recordCreateJournal() {
            // 记录创建笔记的逻辑
            val createCount = AppDataStore.getData("createJournalCount", "0").toInt()
            AppDataStore.putData("createJournalCount", (createCount + 1).toString())
        }

        suspend fun recordDeleteJournal(count: Int) {
            if (count > 0) {
                // 记录删除笔记的逻辑
                val deleteCount = AppDataStore.getData("deleteJournalCount", "0").toInt()
                AppDataStore.putData("deleteJournalCount", (deleteCount + count).toString())
            }
        }

        suspend fun resetReportData() {
            // 重置创建和删除笔记的计数
            AppDataStore.putData("createJournalCount", "0")
            AppDataStore.putData("deleteJournalCount", "0")
        }
    }
}