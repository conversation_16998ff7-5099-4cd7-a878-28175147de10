package com.tcl.ai.note.journalhome.components.categorydialog

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.database.entity.JournalCategory
import com.tcl.ai.note.database.repository.HomeRepository
import com.tcl.ai.note.journalhome.utils.getCategoryIcon
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

/**
 *分类弹窗的Dialog ViewModel
 */
class CategoryDialogViewModel() : ViewModel() {


    private val TAG = "CategoryDialogViewModel"

    private val _categories = MutableStateFlow<List<DialogCategory>>(emptyList())
    val categories: StateFlow<List<DialogCategory>> = _categories.asStateFlow()

    private val _categoryDialogState = MutableStateFlow(
        CategoryDialogState()
    )
    val categoryDialogState: StateFlow<CategoryDialogState> = _categoryDialogState.asStateFlow()

    val categoryDialogController = CategoryDialogController(_categoryDialogState)

    fun observeCategoryListAndDialogState() {
        observeCategoryDialogEvents()
        observeCategoryList()
    }
    var categoryListJob: Job? = null
    fun observeCategoryList() {
            categoryListJob?.cancel()
         categoryListJob = viewModelScope.launch {
            // 直接监听分类列表变化，已经内置了笔记数量监听
             HomeRepository.getAllCategoriesList()
                .catch { emit(Pair(emptyList(), 0)) }
                .flowOn(Dispatchers.IO)
                .collect { categories ->
                    _categoryDialogState.update {
                        it.copy(noteCategories = mapToCategoriesList(categories))
                    }
                }
        }
    }


    private fun mapToCategoriesList(savedNoteValue: Pair<List<JournalCategory>, Int>): List<DialogCategory> {
        val totalNoteCount = savedNoteValue.second
        val savedCategories = savedNoteValue.first

        Logger.d(TAG, "loadCategoryList: totalNoteCount: $totalNoteCount  savedCategories: ${savedCategories.map { it.noteCounts }} " + Thread.currentThread().name)
        val categoryItems = savedCategories.toMutableList().apply {
            val unCategorisedItem = last()
            removeAt(lastIndex)
            add(0, unCategorisedItem)
        }
        return categoryItems.map { category ->
            DialogCategory(
                icon = getCategoryIcon(category.colorIndex).icon,
                categoryId = category.categoryId,
                name = category.name,
                colorIndex = category.colorIndex,
                color = null,
                noteCounts = category.noteCounts,
            )
        }
    }
    var eventJob: Job? = null

     fun observeCategoryDialogEvents() {
        // 取消之前的Job
        eventJob?.cancel()
        eventJob = viewModelScope.launch {
            CategoryDialogEventManager.categoryDialogEvents.collect { event ->
                when (event.type) {
                    CategoryDialogType.NEW_CATEGORY -> {
                        categoryDialogController.showNewCategoryDialog(false)
                    }

                    CategoryDialogType.EDIT_CATEGORY -> {
                        event.dialogCategory?.let {
                            categoryDialogController.showEditCategoryDialog(
                                it
                            )
                        }
                    }

                    CategoryDialogType.MOVE_NOTE_TO_CATEGORY -> {
                        categoryDialogController.showMoveNotesDialog(event.dialogNotes,event.categoryId,event.isPreviewMode)
                    }
                }
            }
        }
    }


    fun addCategory(newCategory: JournalCategory,noteIds: List<Long>, isPreviewMode: Boolean) = viewModelScope.launch {
        Logger.i(TAG,"addCategory, newCategory:$newCategory, isPreviewMode:$isPreviewMode, noteIds.size:${noteIds.size}")
        val categoryId = HomeRepository.addCategory(newCategory)
        if (categoryId > 0) {
            if (isPreviewMode) {
                updateNotesCategoryId(noteIds, categoryId,true)
            }
            CategoryDialogEventManager.sendCategoryDialogCallBackEvent(CategoryDialogCallBackEvent.OnCategoryCreatedCallBack(categoryId.toString(), newCategory.name))
        }
    }

    /**
     * 重命名一个Category
     */
    fun renameCategory(noteCategory: JournalCategory) = viewModelScope.launch {
        Logger.i(TAG,"renameCategory, rename, noteCategory:$noteCategory")
        HomeRepository.updateCategory(noteCategory)
    }


    /**
     * 更新指定 Note 的 categoryId
     */
    fun updateNotesCategoryId(listNote: List<Long>, categoryId: Long, isPreviewMode:Boolean) =
        viewModelScope.launch {
            Logger.i(TAG,"updateNotesCategoryId, categoryId:$categoryId,isPreviewMode:$isPreviewMode, listNote.size:${listNote.size}")
            HomeRepository.updateNotesCategoryId(listNote, categoryId)
            // 将缓存中的当前分类信息设置为移动后的分类
            val category = HomeRepository.getCategory(categoryId)
            category?.let {
                //AppDataStore.putStringData("selectedCategory", Gson().toJson(it))
                if (!isPreviewMode){
                    CategoryDialogEventManager.sendCategoryDialogCallBackEvent(CategoryDialogCallBackEvent.OnCategoryNoteMoved(categoryId.toString()))
                }else{
                    CategoryDialogEventManager.sendCategoryDialogCallBackEvent(CategoryDialogCallBackEvent.OnNoteMovedSuccessful(categoryId.toString()))
                }
            }
        }


    fun onCategoryDialogDismiss() {
        _categoryDialogState.update { it.copy(isVisible = false) }
        viewModelScope.launch {
            CategoryDialogEventManager.sendCategoryDialogCallBackEvent(CategoryDialogCallBackEvent.OnCategoryDialogDismiss())
        }
    }

    /**
     * 重置弹窗状态
     */
    fun resetDialogState() {
        Logger.i(TAG,"resetDialogState")
        eventJob?.cancel()
//        categoryListJob?.cancel()//保持分类列表的监听
        _categoryDialogState.update {
           it.copy(isVisible = false)
        }
    }

}