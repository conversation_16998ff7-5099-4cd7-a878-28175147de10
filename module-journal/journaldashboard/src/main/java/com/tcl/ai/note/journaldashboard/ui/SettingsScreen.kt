package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBackIosNew
import androidx.compose.material.icons.filled.ArrowForwardIos
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Switch
import androidx.compose.ui.graphics.Color
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.tcl.ai.note.base.R
import com.tcl.ai.note.journaldashboard.vm.SettingsViewModel
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import com.tct.theme.core.designsystem.component.TclSwitch
import com.tct.theme.core.designsystem.component.TclTopAppBar

@SuppressLint("DesignSystem")
@Composable
fun SettingsScreen(
    navController: NavController,
    darkTheme: Boolean = isSystemInDarkTheme(),
    viewModel: SettingsViewModel = hiltViewModel()
) {
    NoteTclTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(TclTheme.tclColorScheme.tctStanderBgBasic)
        ) {
            TitleBar {
                navController.navigateUp()
            }

            Row(
                modifier = Modifier
                    .padding(horizontal = 12.dp, vertical = 24.dp)
                    .fillMaxWidth()
                    .background(
                        TclTheme.tclColorScheme.tctStanderBgLayout,
                        RoundedCornerShape(20.dp)
                    )
                    .padding(horizontal = 12.dp, vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        modifier = Modifier
                            .fillMaxWidth(),
                        text = stringResource(com.tcl.ai.note.resources.R.string.inspiration_suggestion_title),
                        fontSize = 16.sp,
                        lineHeight = 22.sp,
                        color = TclTheme.tclColorScheme.tctStanderTextPrimary,
                        textAlign = TextAlign.Start,
                    )

                    Text(
                        modifier = Modifier
                            .fillMaxWidth(),
                        text = stringResource(com.tcl.ai.note.resources.R.string.inspiration_suggestions_setting_desc),
                        fontSize = 14.sp,
                        lineHeight = 18.sp,
                        color = darkTheme.judge(
                            R.color.white.colorRes(),
                            R.color.black.colorRes()
                        ).copy(alpha = 0.5f),
                        textAlign = TextAlign.Start,
                    )
                }

                Switch(
                    modifier = Modifier
                        .padding(start = 16.dp)
                        .width(46.dp)
                        .height(28.dp),
                    checked = viewModel.openInspirationSuggestion,
                    onCheckedChange = {
                        viewModel.setInspirationSuggestionState(it)
                    },
                    colors = SwitchDefaults.colors(
                        checkedThumbColor = darkTheme.judge(
                            Color(0xFF121212),
                            Color.White
                        ),
                        checkedBorderColor = Color.Transparent,
                        checkedTrackColor = Color(0xFFFF9E00),
                        uncheckedThumbColor = darkTheme.judge(
                            Color(0xFF121212),
                            Color.White
                        ),
                        uncheckedBorderColor = Color.Transparent,
                        uncheckedTrackColor = com.tct.theme.core.designsystem.theme.TclTheme.colorScheme.tctStanderSwitchSecondary,
                        checkedIconColor = Color.Transparent,
                        uncheckedIconColor = Color.Transparent,
                    ),
                    thumbContent = { Box(modifier = Modifier.size(20.dp)) }
                )
            }
        }
    }
}

@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TitleBar(
    backClick: () -> Unit,
) {
    TclTopAppBar(
        title = stringResource(R.string.settings),
        modifier = Modifier.fillMaxWidth(),
        navigationIcon = {
            IconButton(onClick = backClick) {
                Icon(
                    imageVector = when (LocalLayoutDirection.current) {
                        LayoutDirection.Ltr -> Icons.Filled.ArrowBackIosNew
                        LayoutDirection.Rtl -> Icons.Filled.ArrowForwardIos
                    },
                    contentDescription = stringResource(com.tct.theme.core.designsystem.R.string.back),
                    tint = TclTheme.tclColorScheme.tctStanderTextPrimary
                )
            }
        },
        isBackIcon = true,
    )
}