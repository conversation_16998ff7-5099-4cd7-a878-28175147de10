package com.tcl.ai.note.journaldashboard.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.setCreateCoverRect

/**
 * 笔记列表数据为空时UI
 */
@Composable
internal fun NoJournalScreen(isSearchNoResult: Boolean){
    Box{
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = 41.dp)
                .onGloballyPositioned { coordinates ->
                    val boundsRect = coordinates.boundsInWindow()
                    if (boundsRect.width.toInt() != GlobalContext.screenWidth) {
                        return@onGloballyPositioned
                    }
                    setCreateCoverRect(boundsRect)
                }
        ) {
            //Spacer(modifier = Modifier.height(26.dp))
            Image(
                painter = painterResource(id = R.drawable.ic_no_journals),
                contentDescription = stringResource(id = isSearchNoResult.judge(com.tcl.ai.note.base.R.string.no_results_found, R.string.text_empty_journal)),
            )
            //Spacer(modifier = Modifier.height(26.dp))
            Text(stringResource(id = isSearchNoResult.judge(com.tcl.ai.note.base.R.string.no_results_found, R.string.text_empty_journal)) ,
                color = colorResource(com.tcl.ai.note.base.R.color.text_summary),
                fontSize = 14.sp,
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .align(Alignment.CenterHorizontally),
                textAlign = TextAlign.Center)
        }
    }
}