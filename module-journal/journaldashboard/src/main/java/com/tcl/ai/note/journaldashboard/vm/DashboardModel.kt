package com.tcl.ai.note.journaldashboard.vm

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.database.repository.CategoryRepository
import com.tcl.ai.note.database.repository.JournalContentRepository
import com.tcl.ai.note.database.repository.JournalRepository
import com.tcl.ai.note.database.entity.JournalCategory
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.handwritingtext.utils.FileUtils.deleteJournalContent
import com.tcl.ai.note.journalbase.copyAssetDirToExternal
import com.tcl.ai.note.journaldashboard.intent.ConfigIntent
import com.tcl.ai.note.journaldashboard.states.ConfigState
import com.tcl.ai.note.journaldashboard.states.ListNoteCategoryState
import com.tcl.ai.note.journaldashboard.states.ListNotesUiState
import com.tcl.ai.note.journaldashboard.states.NoteState
import com.tcl.ai.note.journaldashboard.track.JournalUsageReportWorker
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.io.File
import javax.inject.Inject

@Deprecated("分别使用HomeNoteViewModel和HomeCategoryViewModel代替")
@HiltViewModel
class DashboardModel @Inject constructor(
    private val noteRepository: JournalRepository,
    private val categoryRepository: CategoryRepository,
    private val journalContentRepository: JournalContentRepository,
) : ViewModel() {

    private val _configState = MutableStateFlow(ConfigState())
    val configState = _configState.asStateFlow()

    // 首页笔记数据状态的流
    private val _listNotesUiState = MutableStateFlow<ListNotesUiState>(ListNotesUiState.Loading)
    val listNotesUiState: StateFlow<ListNotesUiState> = _listNotesUiState.asStateFlow()

    // 分类列表数据状态的流
    private val _listNoteCategoryState =
        MutableStateFlow<ListNoteCategoryState>(ListNoteCategoryState.Loading)
    val listNoteCategoryState: StateFlow<ListNoteCategoryState> =
        _listNoteCategoryState.asStateFlow()

    // 首页列表数据分页相关状态
    //当前页
    var currentPage by mutableIntStateOf(1)
    // 每页Note数据量
    private val itemsPerPage = 10
    // 是否还有下一页数据
    var hasMore by mutableStateOf(true)
        private set
    // 是否正在加载
    var isLoading by mutableStateOf(false)
        private set

    // 是否显示FloatingActionButton
    var isFabVisible by mutableStateOf(true)
        private set

    // 是否显示添加分类弹窗
    var isNewCategoryVisible by mutableStateOf(false)
        private set

    // 是否添加模式（有添加/修改两种模式，默认添加模式）
    var isAddCategoryMode by mutableStateOf(true)
        private set

    // 是否按照创建时间排序
    var isCreateTimeSort by mutableStateOf(true)
        private set

    //当前选择的分类
    /*var currentCategoryId = mutableStateOf("")
        private set
    var currentCategoryName = mutableStateOf("")
        private set
    var currentColorIndex = mutableStateOf("")
        private set*/

    // 当前Note
    private val _note = MutableStateFlow<Journal?>(null)
    val note:MutableStateFlow<Journal?> = _note
    private val _noteState = MutableStateFlow(NoteState())
    val noteState: StateFlow<NoteState> = _noteState.asStateFlow()

/*    private val defaultCategoryId = 6L // 预置的Travel类型的id是5
    private val defaultCategoryTypeName = GlobalContext.instance.getString(com.tcl.ai.note.resources.R.string.title_category_travel) // 预置的Travel类型的id是5
    val defaultJournalCategory = JournalCategory(
        categoryId = defaultCategoryId,
        name = defaultCategoryTypeName,
        colorIndex = CategoryColors.YELLOW_COLOR
    )*/
    private val _selectedCategory = MutableStateFlow(JournalCategory())
    val selectedCategory: StateFlow<JournalCategory> = _selectedCategory.asStateFlow()

    private val _selectedNotes = MutableStateFlow<Map<Long, Journal>>(emptyMap())
    val selectedNotes: StateFlow<Map<Long, Journal>> = _selectedNotes.asStateFlow()

    private val _allJournals = MutableStateFlow<List<Journal>>(emptyList())
    val allJournals = _allJournals.asStateFlow()

    private val _refreshData = MutableSharedFlow<Long>()
    val refreshData = _refreshData.asSharedFlow()

    init {
        // 初始化时加载所有日记
        JournalUsageReportWorker.scheduleTask()
        viewModelScope.launch {
            noteRepository.getAllJournalsFlow().collect { journals ->
                _allJournals.update { journals }
            }
        }
    }

    fun updateSelectedNotes(selectedNotes: Map<Long, Journal>) {
        _selectedNotes.update { current ->
            current.toMutableMap().apply {
                // 清除之前的选中状态
                clear()
                // 添加新的选中状态
                putAll(selectedNotes)
            }
        }
    }

    /**
     * 首页 FloatingActionButton 显示隐藏状态更新
     * @param newIsFabVisible 是否显示
     */
    fun updateFabVisibleState(newIsFabVisible: Boolean) {
        isFabVisible = newIsFabVisible
    }

    /**
     * 更新分类弹窗显示隐藏状态
     * @param isVisible 是否显示
     */
    fun updateNewCategoryVisibleState(isVisible: Boolean) {
        isNewCategoryVisible = isVisible
    }

    /**
     * 更新新建分类弹窗添加/修改状态
     * @param isAdd 是否为添加模式
     */
    fun updateNewCategoryModeState(isAdd: Boolean) {
        isAddCategoryMode = isAdd
    }

    /**
     * 更新列表排序方式（创建时间-默认/更新时间）
     * @param isCreateTime 是否按照创建时间排序
     */
    fun updateSortModeState(isCreateTime: Boolean) {
        isCreateTimeSort = isCreateTime
    }

    /**
     * 更新选中的分类
     * @param noteCategory 要更新的分类
     */
    private fun updateSelectedCategory(noteCategory: JournalCategory) {
        viewModelScope.launch {
            // 重置分页状态
            currentPage = 1
            hasMore = false
            if (noteCategory.categoryId == -1L) {
                // 如果是全部分类
                val categoryAll = JournalCategory(
                    categoryId = -1L,
                    name = GlobalContext.instance.getString(com.tcl.ai.note.resources.R.string.all_journals),
                    colorIndex = CategoryColors.NONE_COLOR
                )
                AppDataStore.putStringData("selectedCategory", Gson().toJson(categoryAll))
                _selectedCategory.value = categoryAll
            }else if(noteCategory.categoryId >= 1L){
                AppDataStore.putStringData("selectedCategory", Gson().toJson(noteCategory))
                _selectedCategory.value = noteCategory
            }else{
                val categoryUncategory = JournalCategory(
                    categoryId = 1L,
                    name = GlobalContext.instance.getString(R.string.database_preset_category_none),
                    colorIndex = CategoryColors.NONE_COLOR
                )
                AppDataStore.putStringData("selectedCategory", Gson().toJson(categoryUncategory))
                _selectedCategory.value = categoryUncategory
            }
            // 重置之前显示的内容
            _noteState.update {
                it.copy(note = null, noteId = -1)
            }
            Logger.d(TAG, "updateSelectedCategory: selectedCategory=${_selectedCategory.value}")
            loadInitialNotes()
        }
    }


    init {
        copyTemplateImageToExternal()
        loadDefaultConfigState() // 加载默认配置状态
        handleIntent(ConfigIntent.GetCategories) // 处理获取分类的意图
    }

    private fun handleSelectedCategory() {
        viewModelScope.launch {
            // 初始化当前分类信息
            _selectedCategory.value = AppDataStore.getStringData("selectedCategory", "")
                .let { json ->
                    if (json.isNotEmpty()) {
                        Gson().fromJson(json, JournalCategory::class.java)
                    } else {
                        getDefaultJournalCategory()
                    }
                }
            Logger.d(TAG, "handleSelectedCategory: selectedCategory=${_selectedCategory.value}")
            handleIntent(ConfigIntent.GetNotes) // 处理获取笔记的意图
        }
    }

    suspend fun getDefaultJournalCategory(): JournalCategory {
        for (categoryId in 6L downTo 2L) {
            categoryRepository.getCategory(categoryId)?.let { category ->
                return category
            }
        }
        return JournalCategory(categoryId = 1L, name = GlobalContext.instance.getString(R.string.database_preset_category_none), colorIndex = CategoryColors.NONE_COLOR)
    }

    /**
     * 加载默认配置状态
     */
    private fun loadDefaultConfigState() {
        viewModelScope.launchIO {
            // 从DataStore中获取最新ViewType
            val savedViewType = AppDataStore.getStringData("view_type", DataStoreParam.VIEW_TYPE_LIST)
            launch {
                _configState.update {
                    it.copy(viewType = savedViewType)
                }
            }
        }
    }

    /**
     * 初始化加载列表数据
     */
    fun loadInitialNotes() {
        currentPage = 1
        hasMore = true
        loadNotes(currentPage)
    }

    fun updateJournalById(journalId: Long) {
        viewModelScope.launch {
            val journal = noteRepository.getNote(journalId)
            journal?.let {
                val uiState = _listNotesUiState.value
                if (uiState is ListNotesUiState.Success) {
                    val items = uiState.items.toMutableList()
                    val index = items.indexOfFirst { it.journalId == journalId }
                    if (index >= 0) {
                        items[index] = journal
                        _listNotesUiState.value = ListNotesUiState.Success(items, hasMore)
                    }
                }
            }
        }
    }

    /**
     * 加载Note列表数据
     */
    private fun loadNotes(page: Int) {
        viewModelScope.launch {
            if (isLoading) return@launch

            isLoading = true
            val currentItems = when (val current = _listNotesUiState.value) {
                is ListNotesUiState.Success -> current.items
                else -> emptyList()
            }
            try {
                val category = _selectedCategory.value
                Logger.d(TAG, "loadNotes: page=$page, category=$category, isCreateTimeSort=$isCreateTimeSort")
                val result = if (category.categoryId > 0) {
                    if (isCreateTimeSort) {
                        // 按照创建时间排序
                        noteRepository.getNotesByCategoryIdPaged(
                            categoryId = category.categoryId,
                            page,
                            itemsPerPage
                        )
                    } else {
                        // 按照更新时间排序
                        noteRepository.getNotesByCategoryIdByModifyTimePaged(
                            categoryId = category.categoryId,
                            page,
                            itemsPerPage
                        )
                    }
                } else {
                    // 查询全部分类数据
                    if (isCreateTimeSort) {
                        // 按照创建时间排序
                        noteRepository.getAllNotesPaged(page, itemsPerPage)
                    } else {
                        // 按照更新时间排序
                        noteRepository.getAllNotesByModifyTimePaged(page, itemsPerPage)
                    }
                }
                val newItems = result.first
                hasMore = result.second
                Logger.d("refresh:", "load success")
                _listNotesUiState.value = when {
                    page == 1 && newItems.isEmpty() -> {
                        ListNotesUiState.Success(emptyList(), hasMore = false)
                    }
                    page == 1 -> {
                        ListNotesUiState.Success(newItems, hasMore)
                    }
                    else -> {
                        val combined = currentItems + newItems
                        ListNotesUiState.Success(combined, hasMore)
                    }
                }
                if (page == 1) {
                    _refreshData.emit(System.currentTimeMillis())
                }
                if(page==1 && newItems.isNotEmpty()){
                    if(_noteState.value.noteId>0){
                        loadNote(_noteState.value.noteId)
                    }else{
                        loadNote(newItems[0].journalId)
                    }
                }else if(page==1){
                    _noteState.update {
                        it.copy(note = null, noteId = -1)
                    }
                }
                currentPage = if (newItems.isNotEmpty()) page else page - 1
            } catch (e: Exception) {
                _listNotesUiState.value = ListNotesUiState.Error(e.message ?: "Unknown error")
            } finally {
                isLoading = false
            }
        }
    }

    // 加载需要显示的Note
    private fun loadNote(noteId:Long)=viewModelScope.launch{
        // 初始化第一条需要显示的数据
        noteRepository.getNote(noteId)?.let { note->
            _noteState.update {
                it.copy(
                    note = note,
                    noteId = note.journalId,
                )
            }
            _noteState.update { it.copy(currentNoteCategory = categoryRepository.getCategory(note.categoryId)) }
        }
    }

    /**
     * 加载下一页Note数据
     */
    fun loadMoreNotes() {
        if (hasMore && !isLoading) {
            loadNotes(currentPage + 1)
        }
    }

    /**
     * 修改搜索状态
     */
    fun updateSearchState(searching: Boolean) {
        _noteState.update {
            it.copy(
                isSearching = searching
            )
        }
    }


    /**
     * 搜索笔记数据
     */
    private fun searchNotes(text: String) {
        viewModelScope.launch {
            _noteState.update {
                it.copy(searchText = text, isSearching = if (text.isNotEmpty()) true else it.isSearching)
            }
            if (text.isNotEmpty()) {
                try {
                    val newItems = if (_selectedCategory.value.categoryId > 0) {
                        noteRepository.searchJournalsByCategoryAndQuery(
                            text,
                            _selectedCategory.value.categoryId
                        )
                    } else {
                        noteRepository.searchJournals(text)
                    }
                    _listNotesUiState.value = when {
                        newItems.isEmpty() -> {
                            ListNotesUiState.Success(emptyList(), hasMore = false)
                        }
                        else -> {
                            ListNotesUiState.Success(newItems, hasMore = false)
                        }
                    }
                    if(newItems.isNotEmpty()){
                        loadNote(newItems[0].journalId)
                    }else{
                        _noteState.update {
                            it.copy(note = null, noteId = -1)
                        }
                    }
                } catch (e: Exception) {
                    _listNotesUiState.value = ListNotesUiState.Error(e.message ?: "Unknown error")
                    _noteState.update {
                        it.copy(note = null, noteId = -1)
                    }
                } finally {
                    isLoading = false
                }
            } else {
                loadInitialNotes()
            }

        }
    }


    /**
     * 获取初始化的分类数据
     */
    private fun getCategories() {
        viewModelScope.launch {
            _listNoteCategoryState.value = ListNoteCategoryState.Loading
            try {
                val categories = categoryRepository.getAllCategories()
                if (categories.isNotEmpty()) {
                    val allCategories = JournalCategory(categoryId = -1,
                            name = "",
                            colorIndex = 0
                        )
                    allCategories.noteCounts = noteRepository.getNoteCount()
                    val listAllCategories = listOf(allCategories)+categories
                    _listNoteCategoryState.value = ListNoteCategoryState.Success(listAllCategories)
                    handleSelectedCategory()
                }else{
                    _listNoteCategoryState.value = ListNoteCategoryState.Error("无分类数据")
                }
            }catch (e: Exception) {
                _listNoteCategoryState.value = ListNoteCategoryState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 刷新分类列表
     */
    private fun refreshCategories() {
        viewModelScope.launch {
            _listNoteCategoryState.value = ListNoteCategoryState.Loading
            try {
                val categories = categoryRepository.getAllCategories()
                if (categories.isNotEmpty()) {
                    val allCategories = JournalCategory(categoryId = -1,
                        name = "",
                        colorIndex = 0
                    )
                    allCategories.noteCounts = noteRepository.getNoteCount()
                    val listAllCategories = listOf(allCategories)+categories
                    _listNoteCategoryState.value = ListNoteCategoryState.Success(listAllCategories)
                }else{
                    _listNoteCategoryState.value = ListNoteCategoryState.Error("无分类数据")
                }
            }catch (e: Exception) {
                _listNoteCategoryState.value = ListNoteCategoryState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 新增category
     */
    private fun addCategory(noteCategory: JournalCategory, isPreviewMode:Boolean) = viewModelScope.launch {
        val categoryId = categoryRepository.addCategory(noteCategory)
        if (categoryId > 0) {
            if(_selectedNotes.value.isNotEmpty()){
                _noteState.update { it.copy(newCategory = categoryRepository.getCategory(categoryId)) }
                // 将选中的notes移动到新增的分组中
                val journalIds = _selectedNotes.value.map { it.key }
                updateNotesCategoryId(journalIds, categoryId)
            } else {
                if(isPreviewMode){
                    // 将当前Note移之新增分类下
                    noteState.value.note?.let { updateNoteCategoryId(it.journalId, categoryId) }
                    _noteState.update { it.copy(newCategory = categoryRepository.getCategory(categoryId)) }
                    // 刷新分类列表
                    refreshCategories()
                    AppDataStore.putStringData("selectedCategory", Gson().toJson(noteCategory.copy(categoryId = categoryId)))
                    _selectedCategory.value = noteCategory.copy(categoryId = categoryId)
                }else{
                    // 刷新分类列表及当前选中分类信息
                    getCategories()
                    // 更新当前选中的分类
                    AppDataStore.putStringData("selectedCategory", Gson().toJson(noteCategory.copy(categoryId = categoryId)))
                    _selectedCategory.value = noteCategory.copy(categoryId = categoryId)
                    // 刷新列表数据
                    loadInitialNotes()
                }
            }
        }

    }

    /**
     * 确定分类icon
     */
    /*private fun determineIcon(category: JournalCategory): Int {
        // 这里是确定图标的逻辑，可以根据分类的某些属性或预置逻辑设置图标。
        return when (category.colorIndex) {
            CategoryColors.NONE_COLOR -> R.drawable.ic_category_gray // 未分类
            CategoryColors.YELLOW_COLOR -> R.drawable.ic_category_yellow
            CategoryColors.ORANGE_COLOR -> R.drawable.ic_category_orange
            CategoryColors.RED_COLOR -> R.drawable.ic_category_red
            CategoryColors.PURPLE_COLOR -> R.drawable.ic_category_purple
            CategoryColors.BLUE_COLOR -> R.drawable.ic_category_blue
            CategoryColors.BLACK_COLOR -> R.drawable.ic_category_black
            CategoryColors.GREEN_COLOR -> R.drawable.ic_category_green
            CategoryColors.GRAY_COLOR -> R.drawable.ic_category_gray
            else -> R.drawable.ic_all_notes
        }
    }*/

    /**
     * 删除一个Category
     * @param isDeleteSelectedCategoryNotes 是否删除分类下的Note数据
     */
    private fun deleteCategory(isDeleteSelectedCategoryNotes: Boolean, noteCategory: JournalCategory) =
        viewModelScope.launch {
            val res = categoryRepository.deleteCategory(noteCategory)
            if (res > 0) {
                if (isDeleteSelectedCategoryNotes) {
                    // 删除分类下的note数据
                    deleteNotesByCategoryId(noteCategory.categoryId)
                } else {
                    // 将该分类下的数据迁移到未分类下面
                    updateCategoryId(noteCategory.categoryId, 1)
                }
            }
            val categoryAll = JournalCategory(
                categoryId = -1L,
                name = GlobalContext.instance.getString(com.tcl.ai.note.resources.R.string.all_journals),
                colorIndex = CategoryColors.NONE_COLOR
            )
            AppDataStore.putStringData("selectedCategory", Gson().toJson(categoryAll))
            // 刷新分类列表
            getCategories()
            // 重新刷新数据
            loadInitialNotes()
        }

    /**
     * 重命名一个Category
     */
    private fun renameCategory(noteCategory: JournalCategory) = viewModelScope.launch {
        categoryRepository.updateCategory(noteCategory)
        val newCategory = categoryRepository.getCategory(noteCategory.categoryId)
        AppDataStore.putStringData("selectedCategory", Gson().toJson(newCategory))
        _selectedCategory.value = newCategory!!

        // 刷新分类列表
        getCategories()
    }


    /**
     * 删除一个Category下的notes
     */
    private fun deleteNotesByCategoryId(categoryId: Long) = viewModelScope.launch {
        noteRepository.deleteNotesByCategoryId(categoryId)
        // 刷新分类列表
        getCategories()
        // 刷新列表
        loadInitialNotes()
    }

    /**
     * 删除选中的notes
     */
    private fun deleteNotes(listNote: List<Long>) = viewModelScope.launch {
        noteRepository.deleteNotes(listNote)
        journalContentRepository.deleteJournalContents(listNote)
        listNote.forEach {
            deleteJournalContent(it)
        }
        // 刷新分类列表
        getCategories()
        // 刷新列表
        loadInitialNotes()
    }

    /**
     * 删除一条Note
     */
    private fun deleteOneNote(noteId:Long) = viewModelScope.launch {
        noteRepository.deleteNote(noteId)
        journalContentRepository.deleteJournalContent(noteId)
        deleteJournalContent(noteId)
        // 刷新分类列表
        getCategories()
        // 刷新列表
        loadInitialNotes()
    }

    /**
     * 更新单条指定 Note 的 categoryId
     */
    private fun updateNoteCategoryId(noteId: Long, categoryId: Long) = viewModelScope.launch{
        noteRepository.updateNoteCategoryId(noteId,categoryId)
        // 刷新分类列表
        getCategories()
        // 刷新列表
        loadInitialNotes()
    }


    /**
     * 更新指定 Note 的 categoryId
     */
    private fun updateNotesCategoryId(listNote: List<Long>, categoryId: Long) =
        viewModelScope.launch {
            noteRepository.updateNotesCategoryId(listNote, categoryId)
            // 将缓存中的当前分类信息设置为移动后的分类
            val category = categoryRepository.getCategory(categoryId)
            AppDataStore.putStringData("selectedCategory", Gson().toJson(category))
            _selectedCategory.value = category!!
            // 刷新分类列表
            getCategories()
            // 刷新列表
            loadInitialNotes()
        }

    /**
     *  更新Notes的分类信息
     */
    private fun updateCategoryId(categoryId: Long, newCategoryId: Long) = viewModelScope.launch {
        noteRepository.updateCategoryId(categoryId, newCategoryId)
        // 刷新分类列表
        getCategories()
    }

    /**
     * onResume状态下刷新数据
     */
    fun onResumeRefresh() {
        // 只在非搜索状态刷新
        if (noteState.value.searchText.isEmpty()) {
            handleIntent(ConfigIntent.GetCategories)
            if (_selectedCategory.value.categoryId != 0L) {
                loadInitialNotes() // 重新加载数据
            }
        }
    }


    fun handleIntent(intent: ConfigIntent) {
        Logger.d(TAG, "intent : $intent")
        when (intent) {
            is ConfigIntent.ChangeViewType -> {
                viewModelScope.launch {
                    // 将viewType类型保存到DataStore以免每次切换分类列表刷新时viewType数据状态重置
                    AppDataStore.putStringData("view_type", intent.viewType)
                }
                _configState.update { it.copy(viewType = intent.viewType) }
            }

            is ConfigIntent.GetNotes -> {
                loadInitialNotes()
            }

            is ConfigIntent.GetCategories -> {
                getCategories()
            }

            is ConfigIntent.SearchNotes -> {
                searchNotes(intent.text)
            }

            is ConfigIntent.AddCategory -> {
                addCategory(intent.category,intent.isPreviewMode)
            }

            is ConfigIntent.DeleteCategory -> {
                deleteCategory(intent.isDeleteSelectedCategoryNotes, intent.category)
            }

            is ConfigIntent.DeleteNotesByCategoryId -> {
                deleteNotesByCategoryId(intent.categoryId)
            }

            is ConfigIntent.RenameCategory -> {
                renameCategory(intent.category)
            }

            is ConfigIntent.UpdateSelectedCategory -> {
                updateSelectedCategory(intent.noteCategory)
            }

            is ConfigIntent.DeleteNotes -> {
                deleteNotes(intent.listNotes)
            }

            is ConfigIntent.DeleteOneNote -> {
                deleteOneNote(intent.noteId)
            }

            is ConfigIntent.UpdateNotesCategoryId -> {
                updateNotesCategoryId(intent.listNotes, intent.categoryId)
            }

            is ConfigIntent.UpdatePreviewNote -> {
                loadNote(intent.noteId)
            }

            is ConfigIntent.UpdateNoteCategoryId -> {
                updateNoteCategoryId(intent.noteId, intent.categoryId)
            }
        }
    }

    /**
     * 将模板图片复制到External目录
     */
    private fun copyTemplateImageToExternal() {
        viewModelScope.launchIO {
            val outDir = File(GlobalContext.instance.filesDir, "template")
            if (outDir.exists()) {
                return@launchIO
            }
            copyAssetDirToExternal("image", outDir)
        }
    }

    companion object {
        private const val TAG = "DashboardModel"
    }
}