package com.tcl.ai.note.journaldashboard.vm

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel

class TemplateCreateViewModel : ViewModel() {

    var currentStep by mutableIntStateOf(0)
    var activeStep by mutableIntStateOf(currentStep)

    var albumImagePaths by mutableStateOf(emptyList<String>())
    var selectedImages by mutableStateOf(emptyList<String>())

    fun gotoStep(step: Int) {
        currentStep = step
    }

    fun gotoNextStep() {
        currentStep += 1
        activeStep = currentStep
    }

    fun resetData() {
        currentStep = 0
        activeStep = currentStep
        albumImagePaths = emptyList()
        selectedImages = emptyList()
    }
}