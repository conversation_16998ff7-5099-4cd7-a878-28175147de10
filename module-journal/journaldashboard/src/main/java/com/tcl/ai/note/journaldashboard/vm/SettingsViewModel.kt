package com.tcl.ai.note.journaldashboard.vm

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.utils.AppDataStore
import kotlinx.coroutines.launch

class SettingsViewModel : ViewModel() {
    companion object {
        const val SETTINGS_INSPIRATION_SUGGESTION = "InspirationSuggestion"
    }

    var openInspirationSuggestion by mutableStateOf(true)

    init {
        viewModelScope.launch {
            openInspirationSuggestion = AppDataStore.getBoolean(SETTINGS_INSPIRATION_SUGGESTION, true)
        }
    }

    fun setInspirationSuggestionState(state: <PERSON>olean) {
        openInspirationSuggestion = state
        viewModelScope.launch {
            AppDataStore.putBoolean(SETTINGS_INSPIRATION_SUGGESTION, state)
        }
    }
}