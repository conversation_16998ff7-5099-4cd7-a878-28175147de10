package com.tcl.ai.note.journalhome.components

import android.view.MotionEvent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInteropFilter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.journalhome.entity.HighlightRange
import com.tcl.ai.note.journalhome.vm.state.HomeNoteUiState
import com.tcl.ai.note.journalhome.vm.state.HomeTitleMode
import com.tcl.ai.note.journalhome.vm.state.NoteListAction
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.widget.drawColoredShadow

/**
 * 往下消失，往上出现 动画
 */
@Composable
internal fun HomeAnimatedVisibility(
    visible: Boolean,
    modifier: Modifier = Modifier,
    enableFadeEffect: Boolean = true,
    isHorizontal: Boolean = false,//是否是水平方向的动画
    content: @Composable AnimatedVisibilityScope.() -> Unit
) {
    val slideEnter = slideInVertically(
        initialOffsetY = { it }, // 从下方滑入
        animationSpec = tween(300)
    )

    val slideExit = slideOutVertically(
        targetOffsetY = { it }, // 向下方滑出
        animationSpec = tween(300)
    )
    val slideEnterHorizontal = slideInHorizontally(
        initialOffsetX = { it }, // 从右侧滑入
        animationSpec = tween(300)
    )
    val slideExitHorizontal = slideOutHorizontally(
        targetOffsetX = { it }, // 向右侧滑出
        animationSpec = tween(300)
    )

    val enterAnimation = if (enableFadeEffect) {
        slideEnter + fadeIn(animationSpec = tween(300))
    } else {
        slideEnter
    }
    val enterAnimationHorizontal = if (enableFadeEffect) {
        slideEnterHorizontal + fadeIn(animationSpec = tween(300))
    } else {
        slideEnterHorizontal
    }
    val exitAnimationHorizontal = if (enableFadeEffect) {
        slideExitHorizontal + fadeOut(animationSpec = tween(300))
    } else {
        slideExitHorizontal
    }

    val exitAnimation = if (enableFadeEffect) {
        slideExit + fadeOut(animationSpec = tween(300))
    } else {
        slideExit
    }
    if (isHorizontal) {
        AnimatedVisibility(
            modifier = modifier,
            visible = visible,
            enter = enterAnimationHorizontal,
            exit = exitAnimationHorizontal
        ) {
            content()
        }
    }else {
        AnimatedVisibility(
            modifier = modifier,
            visible = visible,
            enter = enterAnimation,
            exit = exitAnimation
        ) {
            content()
        }
    }
}

@Composable
internal fun FloatAddNoteButton(
    modifier: Modifier = Modifier,
    onClick: (isPen: Boolean) -> Unit = {}
) {
    val isDarkTheme = isSystemInDarkTheme()

    //底部添加Note按钮
    val baseModifier = modifier
        .size(56.dp)

    val buttonModifier = if (isDarkTheme) {
        baseModifier
    } else {
        baseModifier.drawColoredShadow(
            color = Color(0xFFE58E00),
            alpha = 0.3f,
            borderRadius = 28.dp,
            shadowRadius = 8.dp,
            offsetY = 8.dp,
            offsetX = 0.dp
        )
    }

    var isPen by remember {
        mutableStateOf(false)
    }

    Box(modifier = buttonModifier) {
        FloatingActionButton(
            onClick = {
                onClick.invoke(isPen)
            },
            containerColor = Color(0xFFFF9E00),
            shape = CircleShape,
            elevation = FloatingActionButtonDefaults.elevation(0.dp),
            modifier = Modifier
                .fillMaxSize()
                .pointerInteropFilter { event->
                    isPen = event.getToolType(0) == MotionEvent.TOOL_TYPE_STYLUS
                    false
                }
        ) {
            Icon(
                painter = painterResource(R.drawable.ic_note_home_add),
                contentDescription = R.string.btn_add_sticky_note.stringRes(),
                tint = Color.White
            )
        }
    }
}

/**
 * 添加笔记按钮
 */
@Composable
fun AddNoteFloatingBtn(
    modifier: Modifier = Modifier,
    homeNoteUiState: HomeNoteUiState,
    onAction: (NoteListAction) -> Unit
) {


    HomeAnimatedVisibility(
        modifier = modifier,
        visible = homeNoteUiState.titleMode == HomeTitleMode.Normal,
//        isHorizontal = true
    ) {
        FloatAddNoteButton(onClick = { isPen ->
            onAction(NoteListAction.OnAddNoteClick("", isPen))
        })
    }
}


// 6. Compose UI 组件 - 高亮文本显示
@Composable
fun HighlightText(
    modifier: Modifier = Modifier,
    text: String,
    highlights: List<HighlightRange>,
    normalStyle: TextStyle = LocalTextStyle.current,
    highlightStyle: TextStyle = normalStyle.copy(
        color = colorResource(R.color.search_highlight),
        fontWeight = FontWeight.Medium
    ),
    maxLines: Int = 1,
    overflow: TextOverflow = TextOverflow.Ellipsis,
) {
    val density = LocalDensity.current
    if (highlights.isEmpty()) {
        Text(
            modifier = modifier,
            text = text,
            color = colorResource(R.color.home_title_color),
            style = normalStyle,
            maxLines = maxLines,
            overflow = overflow,
            lineHeight = with(density) { 16.dp.toSp() },
        )
        return
    }

    val annotatedString = buildAnnotatedString {
        var lastIndex = 0

        highlights.forEach { range ->
            // 添加高亮前的普通文本
            if (range.start > lastIndex) {
                append(text.substring(lastIndex, range.start))
            }

            // 添加高亮文本 - 使用文字颜色而非背景色
            withStyle(
                SpanStyle(
                    color = highlightStyle.color,
                    fontWeight = highlightStyle.fontWeight
                )
            ) {
                append(text.substring(range.start, range.end))
            }

            lastIndex = range.end
        }

        // 添加最后剩余的普通文本
        if (lastIndex < text.length) {
            append(text.substring(lastIndex))
        }
    }

    Text(
        text = annotatedString,
        style = normalStyle,
        color = colorResource(R.color.home_title_color),
        modifier = modifier,
        maxLines = maxLines,
        overflow = overflow
    )
}