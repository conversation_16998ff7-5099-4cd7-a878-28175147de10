package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.NoteTclTheme
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton
import com.tct.theme.core.designsystem.component.TclWarningButton
import com.tct.theme.core.designsystem.theme.TclTheme

/**
 * 数据删除对话框组件
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onDelete 当用户选择删除时执行的操作
 */
@Composable
fun DeleteDataDialog (
    text: String,
    onDismiss: () -> Unit,
    onDelete: () -> Unit
) {
    TclTheme(dynamicColor = false) {
        TclDialog(
            show = true,
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
            content = {
                Text(
                    text = text
                )
            },
            actions = {
                TclTextButton(onClick = { onDismiss.invoke() }) { Text( stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel)) }
                TclWarningButton( onClick = { onDelete.invoke() }) { Text(stringResource(id = R.string.menu_delete_recording)) }
            },
        )
    }
}


/**
 * 列表数据删除提示框
 * @param isDeleteNotes 是否显示删除分类里面的note数据
 * @param isDeleteNotesSelected 是否删除分类里面的note数据
 */
@SuppressLint("DesignSystem")
@Composable
internal fun BottomDeleteCategoryDialog(onDelete: () -> Unit,
                                        onCancel: () -> Unit,
                                        isDeleteNotesSelected:(Boolean)-> Unit,
                                        isShowDeleteNotes: Boolean) {
    // 复选框图标是否被选中
    var isSelected by remember { mutableStateOf(false) }
    TclDialog(
        show = true,
        onDismissRequest = { onCancel() },
        properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
        title = {
            Text(text = stringResource(R.string.dialog_category_delete_title),color = colorResource(R.color.text_title), fontSize = 16.sp, fontWeight = FontWeight.Normal)
        },
        content = {
            if(isShowDeleteNotes){
                Row(verticalAlignment = Alignment.Top,
                    modifier = Modifier.fillMaxWidth()) {
                    if(isSelected){
                        Image(
                            modifier = Modifier.clickable
                            {
                                isSelected = !isSelected
                                isDeleteNotesSelected(isSelected)
                            }
                                .align(Alignment.Top),
                            painter = painterResource(id = R.drawable.ic_item_checked),
                            contentDescription = null
                        )
                    }else{
                        Image(
                            modifier = Modifier.clickable
                            {
                                isSelected = !isSelected
                                isDeleteNotesSelected(isSelected)
                            }
                                .align(Alignment.Top),
                            painter = painterResource(id = R.drawable.ic_item_unchecked),
                            contentDescription = null
                        )
                    }

                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = stringResource(com.tcl.ai.note.resources.R.string.dialog_category_delete_journal),
                        fontSize = 16.sp,
                        lineHeight = 20.sp,
                        fontWeight = FontWeight.Normal,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.padding(0.dp)
                            .weight(1f)
                            .align(Alignment.Top),
                        color = colorResource(R.color.text_summary)
                    )
                }
            }
        },
        actions = {
            TclTextButton(onClick = { onCancel.invoke() }) { Text( stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel)) }
            TclWarningButton( onClick = { onDelete.invoke() }) { Text(stringResource(id = R.string.menu_delete_recording)) }
        },
    )
}


/**
 * 排序方式选择提示框
 */
@SuppressLint("DesignSystem")
@Composable
internal fun BottomSortOrderDialog(onSelected: (isCreateDate:Boolean) -> Unit, onCancel: () -> Unit,isCreateDate:Boolean) {
    // 复选框图标是否被选中
    Dialog(
        onDismissRequest = onCancel,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = 64.dp, start = 16.dp, end = 16.dp)
                .clickable(onClick = onCancel),
            contentAlignment = Alignment.BottomCenter
        ) {
            Surface(
                color = colorResource(R.color.bg_dialog),
                shape = RoundedCornerShape(16.dp),
                modifier = Modifier
                    .wrapContentWidth()
                    .fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(start = 24.dp, top = 24.dp, end = 24.dp, bottom = 8.dp),
                    horizontalAlignment = Alignment.Start,
                    verticalArrangement = Arrangement.Center
                ) {
                    Row(
                        modifier = Modifier
                            .clickable(onClick = { onSelected(true) })
                            .fillMaxWidth()
                    ) {
                        if(isCreateDate){
                            Image(
                                painter = painterResource(id = R.drawable.ic_item_checked),
                                contentDescription = null
                            )
                        }else{
                            Image(
                                painter = painterResource(id = R.drawable.ic_item_unchecked),
                                contentDescription = null
                            )
                        }

                        Spacer(modifier = Modifier.width(24.dp))
                        Text(
                            text = stringResource(R.string.create_date),
                            fontSize = 16.sp,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.padding(0.dp),
                            color = colorResource(R.color.text_title)
                        )
                    }
                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier
                            .clickable(onClick = { onSelected(false) })
                            .fillMaxWidth()
                    ) {
                        if(!isCreateDate){
                            Image(
                                painter = painterResource(id = R.drawable.ic_item_checked),
                                contentDescription = null
                            )
                        }else{
                            Image(
                                painter = painterResource(id = R.drawable.ic_item_unchecked),
                                contentDescription = null
                            )
                        }

                        Spacer(modifier = Modifier.width(24.dp))
                        Text(
                            text = stringResource(R.string.modify_date),
                            fontSize = 16.sp,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.padding(0.dp),
                            color = colorResource(R.color.text_title)
                        )
                    }
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }
    }
}

/**
 * 显示理由对话框
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onDelete 当用户选择删除时执行的操作
 */
@Composable
fun ShowPhotoRationaleDialog (
    onDismiss: () -> Unit,
    onPositiveClick: () -> Unit
) {
    NoteTclTheme {
        TclDialog(
            show = true,
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
            content = {
                Text(
                    text = stringResource(id = com.tcl.ai.note.resources.R.string.permission_description)
                )
            },
            actions = {
                TclTextButton(onClick = { onPositiveClick.invoke() }) { Text( stringResource(id = com.tcl.ai.note.resources.R.string.btn_text_i_know)) }
            },
        )
    }
}