package com.tcl.ai.note.journalhome.vm

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.journalhome.entity.HomeCategoryItemEntity
import com.tcl.ai.note.journalhome.vm.state.CategoryAction
import com.tcl.ai.note.journalhome.vm.state.HomeCategoryEffect
import com.tcl.ai.note.journalhome.vm.state.HomeTitleMode
import com.tcl.ai.note.journalhome.vm.state.NoteListAction
import kotlinx.coroutines.launch

/**
协调UI和ViewModel之间的交互
homeNoteViewModel  负责处理主页笔记相关的逻辑
homeCategoryViewModel 负责处理主页分类相关的逻辑
 */
class HomeCoordinator(
    val homeCategoryViewModel: HomeCategoryViewModel,
    val homeNoteViewModel: HomeNoteViewModel
) {
    val homeUiState = homeNoteViewModel.homeNoteUiState
    val categoryUiState = homeCategoryViewModel.homeCategoryUiState
    val categoryEffect = homeCategoryViewModel.effect
    init {
        homeCategoryViewModel.viewModelScope.launch {
            homeCategoryViewModel.effect.collect { effect ->
                when (effect) {
                    is HomeCategoryEffect.OnCategoryChange -> {
                        onCategoryChangedToNoteList(effect.changeCategoryItem)
                    }

                    else -> {}
                }
            }
        }
    }

    fun handleHomeAction(action: NoteListAction) {
        homeNoteViewModel.onHomeAction(action)
        if(action is NoteListAction.OnChangeTitleMode){
            // 根据标题模式变化调用分类状态变更
            val isSearchMode = action.titleMode == HomeTitleMode.Search
            homeCategoryViewModel.onChangeCategoryState(isSearchMode)
        }
    }

    fun handleCategoryAction(action: CategoryAction) {
        homeCategoryViewModel.onCategoryAction(action)
        if (action is CategoryAction.OnCategorySelected) {
            onCategoryChangedToNoteList(action.category)
        }
    }

    /**
     * 分类变更
     */
    private fun onCategoryChangedToNoteList(categoryItem: HomeCategoryItemEntity) {
        homeNoteViewModel.onCategoryChanged(categoryItem)
    }

    /**
     * 停止数据库监听
     */
    fun stopObservingCategoryAndNotesList() {
//        homeCategoryViewModel.stopObservingCategoryList()
//        homeNoteViewModel.stopObservingNotes()
    }

    fun observeCategoryAndNotesList() {
//        homeCategoryViewModel.observeCategoryList()
//        homeNoteViewModel.observeNotesFromDatabase()
    }

}

@Composable
fun rememberHomeCoordinator(
    categoryViewModel: HomeCategoryViewModel = hiltViewModel(),
    homeNoteViewModel: HomeNoteViewModel = hiltViewModel()
): HomeCoordinator {
    return remember(homeNoteViewModel, categoryViewModel) {
        HomeCoordinator(
            homeNoteViewModel = homeNoteViewModel,
            homeCategoryViewModel = categoryViewModel
        )
    }
}