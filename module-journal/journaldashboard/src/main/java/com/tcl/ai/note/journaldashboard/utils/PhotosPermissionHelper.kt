package com.tcl.ai.note.journaldashboard.utils

import android.Manifest
import android.content.pm.PackageManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.LocalActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.core.app.ActivityCompat
import com.tcl.ai.note.journaldashboard.ui.ShowPhotoRationaleDialog
import com.tcl.ai.note.utils.AppDataStore

@Composable
fun PhotosPermissionHelper(onPermissionResult: () -> Unit): (action: () -> Unit) -> Unit {
    // 是否首次请求Photos权限
    val coroutineScope = rememberCoroutineScope()
    var isFirstCheckPhotosPermission by remember { mutableStateOf(true) }
    var showStoragePermissionDialog by remember { mutableStateOf(false) }

    val activity = LocalActivity.current as ComponentActivity
    val storagePermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) {
        onPermissionResult()
    }

    LaunchedEffect(Unit) {
        isFirstCheckPhotosPermission = AppDataStore.getData("isFirstCheckPhotosPermission", true)
    }

    LaunchedEffect(key1 = showStoragePermissionDialog) {
        if (showStoragePermissionDialog) {
            isFirstCheckPhotosPermission = false
            AppDataStore.putData("isFirstCheckPhotosPermission", false)
        }
    }

    if (showStoragePermissionDialog) {
        // 显示权限请求对话框
        ShowPhotoRationaleDialog(
            onDismiss = {
                showStoragePermissionDialog = false
            },
            onPositiveClick = {
                showStoragePermissionDialog = false
                // 请求权限
                storagePermissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES)
            },
        )
    }

    return {
        if (ActivityCompat.checkSelfPermission(
                activity,
                Manifest.permission.READ_MEDIA_IMAGES
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            if (isFirstCheckPhotosPermission) {
                // 显示权限请求对话框
                isFirstCheckPhotosPermission = false
                showStoragePermissionDialog = true
            } else {
                storagePermissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES)
            }
        } else {
            storagePermissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES)
        }
    }
}