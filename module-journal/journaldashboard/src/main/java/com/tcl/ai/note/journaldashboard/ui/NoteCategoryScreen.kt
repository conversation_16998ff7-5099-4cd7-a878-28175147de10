package com.tcl.ai.note.journaldashboard.ui

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.journaldashboard.intent.ConfigIntent
import com.tcl.ai.note.journaldashboard.states.ListNoteCategoryState
import com.tcl.ai.note.journaldashboard.vm.DashboardModel
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toPx


/**
 * 笔记分类（分类列表，新增分类）
 */
@Deprecated("使用首页分类侧边栏代替")
@Composable
internal fun NoteCategoryScreen(dashboardModel: DashboardModel = hiltViewModel()) {
    var showPopup by remember { mutableStateOf(false) }
    //笔记分类默认分类为“全部便签”
    val initSelectedCategory = stringResource(com.tcl.ai.note.resources.R.string.all_journals)
    // 当前分类名称
    val selectedCategory by dashboardModel.selectedCategory.collectAsState()
    var rowPosition by remember { mutableStateOf(Offset.Zero) }
    var rowSize by remember { mutableStateOf(Size.Zero) }
    val isNewCategoryVisible = dashboardModel.isNewCategoryVisible
    val isAddCategoryMode = dashboardModel.isAddCategoryMode
    val listNoteCategoryState by dashboardModel.listNoteCategoryState.collectAsState()

    Box(modifier = Modifier.fillMaxWidth()){
        Column {
            Spacer(modifier = Modifier.height(8.dp))
            Row(
                modifier = Modifier
                    .padding(horizontal = isDensity440.judge(24.dp, 24.dp))
                    .clickable {
                        showPopup = true
                    }
                    .onGloballyPositioned { coordinates ->
                        rowPosition = coordinates.positionInWindow()
                        rowSize = coordinates.size.toSize()
                    },
                verticalAlignment = Alignment.CenterVertically

            ) {
                val textString = (selectedCategory.categoryId == -1L).judge(
                    initSelectedCategory,
                    selectedCategory.name
                )
                Text(
                    modifier = Modifier.widthIn(max = 260.dp),
                    text = textString,
                    fontSize = isDensity440.judge(16.sp, 14.sp),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    color = colorResource(R.color.text_category_title))
                Spacer(modifier = Modifier.width(8.dp))
                Image(
                    painter = painterResource(id = if(showPopup)R.drawable.ic_arrow_drop_up_title else R.drawable.ic_arrow_drop_down_title),
                    contentDescription = null,
                )
            }
            Spacer(modifier = Modifier.height(8.dp))

            if (showPopup && listNoteCategoryState is ListNoteCategoryState.Success) {
                val items = (listNoteCategoryState as ListNoteCategoryState.Success).items
                Popup(
                    alignment = Alignment.TopStart,
                    offset = IntOffset(
                        24.dp.toPx.toInt(),
                        (rowPosition.y.toInt()/3)
                    ),
                    onDismissRequest = { showPopup = false },
                    properties = PopupProperties(focusable = true),
                ) {
                    Surface(
                        elevation = 8.dp,
                        shape = RoundedCornerShape(isDensity440.judge(22.dp, 20.dp)),
                        modifier = Modifier.width(226.dp)
                    ) {
                        Box(modifier = Modifier
                            .width(186.dp)
                            .clip(RoundedCornerShape(isDensity440.judge(22.dp, 20.dp)))
                            .background(
                                colorResource(R.color.bg_dialog)
                            )) {
                            Column(modifier = Modifier) {
                                //Category list
                                LazyColumn(
                                    modifier = Modifier
                                        .heightIn(max = 292.dp)// 限制弹出菜单高度，使其可以滚动
                                        .padding(16.dp)
                                ) {
                                    items(items) { item ->
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(vertical = isDensity440.judge(10.dp, 10.dp))
                                                .clickable {
                                                    dashboardModel.handleIntent(
                                                        ConfigIntent.UpdateSelectedCategory(
                                                            item
                                                        )
                                                    )
                                                    showPopup = false
                                                }
                                        ) {
                                            var  textColor = colorResource(R.color.text_title)
                                            var name = item.name
                                            if(item.name.isEmpty()){
                                                name = stringResource(com.tcl.ai.note.resources.R.string.all_journals)
                                            }
                                            if(item.categoryId == selectedCategory.categoryId){
                                                textColor = colorResource(R.color.text_category_list_selected)
                                            }
                                            Text(
                                                text = name+"(${item.noteCounts})",
                                                color = textColor,
                                                fontSize = 14.sp
                                            )
                                        }
                                    }
                                }

                                Divider(color = colorResource(R.color.bg_outline_10), thickness = 1.dp, modifier = Modifier.padding(horizontal = 16.dp))

                                //New Category
//                                val context = LocalContext.current
                                Box(
                                    modifier = Modifier
                                        .padding(16.dp)
                                        .fillMaxWidth()
                                        .clickable {
//                                            Toast.makeText(context, "成功创建分类", Toast.LENGTH_SHORT).show()
                                            showPopup = false
                                            dashboardModel.updateNewCategoryVisibleState(true)
                                            dashboardModel.updateNewCategoryModeState(true)
                                            dashboardModel.updateFabVisibleState(false)
                                        }
                                ) {
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Image(
                                            painter = painterResource(id = R.drawable.ic_category_add),
                                            contentDescription = null,
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(
                                            text = stringResource(R.string.dialog_category_name_title),
                                            color = colorResource(R.color.text_title),
                                            fontSize = 14.sp
                                        )
                                    }
                                }
                            }
                        }

                    }
                }
            }

            //添加笔记分类对话框
            if (isNewCategoryVisible) {
                CreateCategoryScreen(
                    isPreviewMode = false,
                    isAddCategoryMode = isAddCategoryMode,
                    onDismissRequest = {
                        dashboardModel.updateNewCategoryVisibleState(false)
                        dashboardModel.updateNewCategoryModeState(true)
                        dashboardModel.updateFabVisibleState(true)
                    },
                    onConfirmClick = { },
                )
            }
        }

    }
}


/**
 * 分类列表（用于迁移已选中的Note数据至指定分类中）
 */
@Composable
internal fun MoveToNoteCategoryScreen(
    dashboardModel: DashboardModel = hiltViewModel(),
    onBack: () -> Unit,
    onMoveToCategory: (Long) -> Unit,
    modifier: Modifier = Modifier) {
    // 当前分类名称
    val selectedCategory by dashboardModel.selectedCategory.collectAsState()

    val listNoteCategoryState by dashboardModel.listNoteCategoryState.collectAsState()
    val isNewCategoryVisible = dashboardModel.isNewCategoryVisible
    val isAddCategoryMode = dashboardModel.isAddCategoryMode

    Box(modifier = modifier.fillMaxWidth()){
        Column {
            Box(modifier = Modifier.background(colorResource(R.color.bg_dialog))) {
                Column(modifier = Modifier) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .height(56.dp)
                            .fillMaxWidth()
                            .padding(start = 16.dp)){
                        Image(
                            painter = painterResource(id = R.drawable.ic_menu_back),
                            contentDescription = null,
                            modifier = Modifier.clickable { onBack() }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        androidx.compose.material3.Text(
                            text = stringResource(R.string.choose_category),
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold,
                            color = colorResource(R.color.text_title),
                        )
                    }
                    val context = LocalContext.current
                    //Category list
                    LazyColumn(
                        modifier = Modifier
                            .heightIn(max = 336.dp)// 限制弹出菜单高度，使其可以滚动
                            .padding(start = 16.dp, end = 20.dp, top = 0.dp, bottom = 0.dp)
                    ) {
                        when (listNoteCategoryState) {
                            is ListNoteCategoryState.Loading -> {
                                item {
                                    CircularProgressIndicator(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(16.dp)
                                    )
                                }
                            }
                            is ListNoteCategoryState.Success -> {
                                items((listNoteCategoryState as ListNoteCategoryState.Success).items) { item ->
                                    if (item.categoryId == -1L) {
                                        return@items // 跳过默认分类
                                    }
                                    var tip = stringResource(R.string.category_moveto_already)
                                    if(item.categoryId.toString() != selectedCategory.categoryId.toString()){
                                        tip = String.format(stringResource(R.string.category_moveto_success), item.name)
                                    }
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 16.dp)
                                            .clickable {
                                                if (item.categoryId.toString() == selectedCategory.categoryId.toString()) {
                                                    Toast
                                                        .makeText(context, tip, Toast.LENGTH_SHORT)
                                                        .show()
                                                } else {
                                                    onMoveToCategory(item.categoryId)
                                                    Toast
                                                        .makeText(context, tip, Toast.LENGTH_SHORT)
                                                        .show()
                                                }

                                            }
                                    ) {
                                        var  textColor = colorResource(R.color.text_title)
                                        var name = item.name
                                        if(item.name.isEmpty()){
                                            name = stringResource(com.tcl.ai.note.resources.R.string.all_journals)
                                        }
                                        if(item.name == selectedCategory.name){
                                            textColor = colorResource(R.color.text_category_list_selected)
                                        }
                                        Text(
                                            text = name,
                                            color = textColor,
                                            fontSize = 14.sp,
                                        )
                                        Spacer(modifier = Modifier.weight(1f))
                                        Text(
                                            text = "${item.noteCounts}",
                                            color = textColor,
                                            fontSize = 14.sp
                                        )
                                    }
                                }
                            }

                            is ListNoteCategoryState.Error -> {}
                        }
                    }

                    Divider(color = colorResource(R.color.bg_outline_10), thickness = 1.dp)

                    //New Category
                    Box(
                        modifier = Modifier
                            .padding(16.dp)
                            .fillMaxWidth()
                            .clickable {
                                dashboardModel.updateNewCategoryVisibleState(true)
                                dashboardModel.updateNewCategoryModeState(true)
                                dashboardModel.updateFabVisibleState(false)
                            }
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_category_add),
                                contentDescription = null,
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = stringResource(R.string.dialog_category_name_title),
                                color = colorResource(R.color.text_title),
                                fontSize = 14.sp
                            )
                        }
                    }
                }
            }
            if (isNewCategoryVisible) {
                CreateCategoryScreen(
                    isPreviewMode = false,
                    isAddCategoryMode = isAddCategoryMode,
                    onDismissRequest = {
                        dashboardModel.updateNewCategoryVisibleState(false)
                        dashboardModel.updateNewCategoryModeState(true)
                        dashboardModel.updateFabVisibleState(true)
                    },
                    onConfirmClick = {

                    },
                )
            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable { onBack() } // 点击遮罩层取消搜索
                    .zIndex(0.5f) // 将遮罩层置于列表下方，但在内容上方
            )
        }

    }
}