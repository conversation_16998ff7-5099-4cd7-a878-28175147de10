package com.tcl.ai.note.journalhome.components.categorydialog

import android.util.Log
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toHex

/**
 *  author : junze.liu
 *  date : 2025-06-16 17:14
 *  description :
 */

@Composable
fun CustomRadioButton(
    isDarkTheme: Boolean = isSystemInDarkTheme(),
    selected: Boolean,
    onClick: () -> Unit,
    color: Color = MaterialTheme.colorScheme.primary,
    modifier: Modifier = Modifier,
    outerSize: Dp = 20.dp,
    innerSize: Dp = 10.dp,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() }
) {
    val strokeWidth = 2.dp

    val animatedSelection by animateFloatAsState(
        targetValue = if (selected) 1f else 0f,
        animationSpec = tween(durationMillis = 200)
    )
    val darkThemeCircleColor = R.color.dark_theme_circle.colorRes()
    val radioSelColor =R.color.radio_sel.colorRes()
    Box(
        modifier = modifier
            .size(outerSize)
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                onClick = onClick
            )
            .drawWithContent {
                if(color == Color.White||(color == Color.Black && isDarkTheme)){

                    if(selected){
                        drawCircle(
                            color =radioSelColor,
                            radius = (outerSize-strokeWidth).toPx() / 2,
                            style = Stroke(width = strokeWidth.toPx())
                        )
                        drawCircle(
                            color = color.copy(alpha = animatedSelection),
                            radius = innerSize.toPx() / 2 * animatedSelection,
                            center = center
                        )
                    }else{
                        drawCircle(
                            color = darkThemeCircleColor,
                            radius = (outerSize-1.5.dp).toPx() / 2,
                            style =  Stroke(width = 1.5.dp.toPx())
                        )
                        drawCircle(
                            color = color,
                            radius =  (outerSize-1.5.dp).toPx() / 2,
                            center = center
                        )
                    }

                }else{
                    // 外圆绘制
                    drawCircle(
                        color = color,
                        radius = selected.judge(outerSize-strokeWidth,outerSize).toPx() / 2,
                        style = if (selected) Stroke(width = strokeWidth.toPx()) else Fill,
                    )
                    // 内圆动画过渡
                    drawCircle(
                        color = color.copy(alpha = animatedSelection),
                        radius = innerSize.toPx() / 2 * animatedSelection,
                        center = center
                    )
                }



            }
    )
}
