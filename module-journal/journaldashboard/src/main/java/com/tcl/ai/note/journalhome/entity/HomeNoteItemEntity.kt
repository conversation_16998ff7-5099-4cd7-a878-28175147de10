package com.tcl.ai.note.journalhome.entity

// 数据类定义
data class HomeNoteItemEntity(
    val id: String,
    val title: String,
    val coverId: Long,
    //val titleResId: Int? = null,
    //val summary: String? = null,
    //val image: String? = null,
    //val type: HomeNoteType,
    val date: String,
    val isChecked: Boolean = false,
    val categoryId: String = "", // 分类ID
    val createTime: Long?=null,
    val modifyTime: Long?=null,
    val searchKey: String = "", // 新增字段，表示搜索关键字
    var lastViewPage: Int = 0,
    // 新增：高亮信息
    val highlightInfo: HighlightInfo? = null,
    val categoryIcon: CategoryIcon? = null
)
// 2. 高亮信息数据类
data class HighlightInfo(
    val searchKeyword: String,
    val titleHighlights: List<HighlightRange> = emptyList(),
    val subtitleHighlights: List<HighlightRange> = emptyList()
)

// 3. 高亮范围数据类
data class HighlightRange(
    val start: Int,
    val end: Int
)
enum class HomeNoteType {
    TEXT_ONLY, AUDIO_ONLY, IMAGE_ONLY, MIX,
}

