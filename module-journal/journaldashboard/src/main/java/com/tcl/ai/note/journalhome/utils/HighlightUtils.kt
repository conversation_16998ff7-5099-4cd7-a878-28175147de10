package com.tcl.ai.note.journalhome.utils

import com.tcl.ai.note.journalhome.entity.HighlightInfo
import com.tcl.ai.note.journalhome.entity.HighlightRange

// 4. 高亮工具类
object HighlightUtils {
    
    /**
     * 查找文本中所有匹配的关键词位置
     */
    private fun findHighlightRanges(text: String, keyword: String): List<HighlightRange> {
        if (keyword.isEmpty() || text.isEmpty()) return emptyList()
        
        val ranges = mutableListOf<HighlightRange>()
        val lowerText = text.lowercase()
        val lowerKeyword = keyword.lowercase()
        
        var startIndex = 0
        while (startIndex < lowerText.length) {
            val index = lowerText.indexOf(lowerKeyword, startIndex)
            if (index == -1) break
            
            ranges.add(HighlightRange(index, index + keyword.length))
            startIndex = index + keyword.length
        }
        
        return ranges
    }
    
    /**
     * 为笔记项创建高亮信息
     * @param isNeedHighlightSubtitle 是否需要包含搜索副标题 进行高亮
     */
    fun createHighlightInfo(
        title: String,
        subtitle: String?,
        searchKeyword: String,
        isNeedHighlightSubtitle: Boolean = false
    ): HighlightInfo? {
        if (searchKeyword.isEmpty()) return null
        
        val titleHighlights = findHighlightRanges(title, searchKeyword)
        if(!isNeedHighlightSubtitle){
            return if (titleHighlights.isNotEmpty()) {
                HighlightInfo(
                    searchKeyword = searchKeyword,
                    titleHighlights = titleHighlights,
                )
            } else null
        }
        val subtitleHighlights = subtitle?.let {
            findHighlightRanges(it, searchKeyword)
        } ?: emptyList()
        
        // 只有找到匹配时才返回高亮信息
        return if (titleHighlights.isNotEmpty() || subtitleHighlights.isNotEmpty()) {
            HighlightInfo(
                searchKeyword = searchKeyword,
                titleHighlights = titleHighlights,
                subtitleHighlights = subtitleHighlights
            )
        } else null
    }
}