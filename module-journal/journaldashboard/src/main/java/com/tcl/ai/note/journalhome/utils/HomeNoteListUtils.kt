package com.tcl.ai.note.journalhome.utils

import android.text.format.DateFormat
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.journalhome.entity.HomeNoteItemEntity
import com.tcl.ai.note.journalhome.entity.HomeNoteType
import com.tcl.ai.note.utils.Logger
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Locale
import kotlin.system.measureTimeMillis


/**
 * 检测系统是否使用24小时制
 */
private fun is24HourFormat(): Boolean {
    return DateFormat.is24HourFormat(GlobalContext.appContext)
}

/**
 * 获取时间格式化器，根据系统设置动态选择24小时制或12小时制
 * 注意：这个函数用于非Compose环境，在Compose中请使用rememberTimeFormatter()
 */
private fun getTimeFormatter(): DateTimeFormatter {
    val currentLocale = Locale.getDefault()
    val pattern = if (is24HourFormat()) {
        "HH:mm"  // 24小时制
    } else {
        // 12小时制，根据语言环境选择AM/PM格式
        if (currentLocale.language == "zh") {
            "a h:mm"  // 中文：上午/下午 7:00
        } else {
            "h:mm a"  // 英文：7:00 AM/PM
        }
    }
    return DateTimeFormatter.ofPattern(pattern).withLocale(currentLocale)
}

private val dateFormatterZh by lazy {
    DateTimeFormatter.ofPattern("yyyy年MM月dd日").withLocale(Locale.getDefault())
}

private val dateFormatterEn by lazy {
    DateTimeFormatter.ofPattern("MMM dd, yyyy").withLocale(Locale.getDefault())
}

// 缓存今天的日期，避免重复计算
private var cachedToday: LocalDate? = null
private var lastDateCheck: Long = 0L

private fun getTodayDate(): LocalDate {
    val now = System.currentTimeMillis()
    // 每小时更新一次今天的日期缓存
    if (cachedToday == null || now - lastDateCheck > 3600000) {
        cachedToday = LocalDate.now()
        lastDateCheck = now
    }
    return cachedToday!!
}

/**
 * 格式化日期 - 优化版本
 */
fun formatDate(createTime: Long?, modifyTime: Long?, isCreateTimeSort: Boolean): String {
    val time = if (isCreateTimeSort) createTime else modifyTime ?: 0L

    if (time == 0L || time == null) return ""

    // 将时间戳转换为本地时区的日期时间
    val zonedDateTime = Instant.ofEpochMilli(time).atZone(ZoneId.systemDefault())
    val noteDate = zonedDateTime.toLocalDate()
    val today = getTodayDate()

    // 如果是今天，显示时间（根据系统设置选择24小时制或12小时制）
    if (noteDate.isEqual(today)) {
        return zonedDateTime.format(getTimeFormatter())
    }

    // 如果不是今天，显示完整日期
    val currentLocale = Locale.getDefault()
    val formatter = if (currentLocale.language == "zh") dateFormatterZh else dateFormatterEn
    return zonedDateTime.format(formatter)
}

/**
 * 跳转到笔记详情页
 */
/*fun gotoRichTextNoteDetail(context: Context, action: NoteListAction) {
    if (action is NoteListAction.OnAddNoteClick) {
        context.startActivity(Intent(context, MainActivity::class.java).apply {
            if (action.noteId.isBlank()) {
                putExtra("newNote", "1")
            } else {
                putExtra("noteId", action.noteId)
            }
        })
    }
}

/**
 * 是否是全部和未分类
 */
fun isAllAndUnCategorised(selectedCategoryId:String): Boolean {
    return (selectedCategoryId.isEmpty()||selectedCategoryId==TYPE_UN_CATEGORISED_ID.toString())
}*/

/**
 * 将数据库实体转换为UI数据模型
 * @param notes 数据库笔记列表
 * @param isCreateTimeSort 是否按创建时间排序（用于日期显示格式）
 */
internal fun mapToHomeNoteItems(
    notes: List<Journal>,
    isCreateTimeSort: Boolean = true
): List<HomeNoteItemEntity> {
    val result: List<HomeNoteItemEntity>
    val timeMillis = measureTimeMillis {

        result = notes.map { note ->
            val categoryIdStr = note.categoryId.toString()
            val showDate = formatDate(note.createTime, note.modifyTime, isCreateTimeSort)

            // 优化：使用预构建的映射查找分类图标
            /*val categoryIcon = if (isAllAndUnCategorised(categoryIdStr)) {
                null
            } else {
                note.categoryColorIndex?.let { getCategoryIcon(it) }
            }
*/
            // 新增：在ViewModel中处理显示信息
            //val displayInfo = getNoteDisplayInfo(note, showDate)

            HomeNoteItemEntity(
                id = note.journalId.toString(),
                title = note.title, //displayInfo.primaryTitle.ifEmpty { note.title }, // 优先使用处理后的标题
                //titleResId = displayInfo.titleResId,
                //summary = cleanSummaryText(note.summary),
                categoryId = categoryIdStr, // 复用已转换的字符串
                date = showDate,
                coverId = note.coverId,
                createTime = note.createTime,
                modifyTime = note.modifyTime,
                //categoryIcon = categoryIcon,
                isChecked = false,
                lastViewPage = note.lastViewPage
                // 缩略图相关信息（封装）
                /*thumbnailInfo = ThumbnailInfo(
                    type = displayInfo.thumbnailType,
                    showAudioIcon = displayInfo.showAudioIcon,
                    image = note.handwritingThumbnail ?: note.firstPicture,
                    handwritingThumbnail = note.handwritingThumbnail,
                    firstPicture = note.firstPicture,
                    hasAudio = note.hasAudio == true
                )*/
            )
        }
    }
    Logger.d("HomeNoteViewModel", "mapToHomeNoteItems duration: ${timeMillis}ms, data transformation: ${notes.size} -> ${result.size}")
    return result
}
