package com.tcl.ai.note.journaldashboard.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextMeasurer
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.max
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.database.entity.PageInfo
import com.tcl.ai.note.journalbase.formatTime
import com.tcl.ai.note.journaldashboard.intent.JournalContentIntent
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.journaldashboard.utils.CoverDataList
import com.tcl.ai.note.journaldashboard.vm.JournalContentViewModel
import com.tcl.ai.note.journalhome.entity.HomeNoteItemEntity
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.deviceDensity
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.setCreateCoverRect
import com.tct.theme.core.designsystem.component.TclDropDownMenuItem
import com.tct.theme.core.designsystem.component.TclDropdownMenu
import java.util.Locale

@Composable
internal fun LargeImageScreen(
    journals: List<HomeNoteItemEntity>,
    lazyGridState: LazyGridState,
    editMode: Boolean,
    searchMode: Boolean,
    onLongClick: (HomeNoteItemEntity) -> Unit,
    onClick: (HomeNoteItemEntity) -> Unit,
    viewModel: JournalContentViewModel = hiltViewModel(),
    onEditJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onShareJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onMoveJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onDeleteJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
) {
    val screenWidthDp = LocalConfiguration.current.screenWidthDp.dp
    val textMeasurer = rememberTextMeasurer()
    val localDensity = LocalDensity.current

    val item = journals.firstOrNull()
    val itemHeight = remember {
        val itemWidth = (screenWidthDp - 16.dp * 3) / 2
        max(itemWidth + 12.dp + 18.dp + (measureTextHeight(
            textMeasurer = textMeasurer,
            localDensity = localDensity,
            text = item?.title.orEmpty(),
            fontSize = 14.sp,
            lineHeight = 18.sp,
        ) / 18 * 40) + 12.dp + measureTextHeight(
            textMeasurer = textMeasurer,
            localDensity = localDensity,
            text = formatTime(
                item?.createTime ?: System.currentTimeMillis(),
                Locale.getDefault()
            ),
            fontSize = 12.sp,
            lineHeight = 16.sp,
        ), itemWidth / 156f * 246f)
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        state = lazyGridState,
        contentPadding = PaddingValues(horizontal = 16.dp, vertical = 12.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        modifier = Modifier.fillMaxSize()
    ) {
        items(journals, key = {it.id}) { journal ->
            LargeImageItem(
                modifier = searchMode.judge(Modifier, Modifier.animateItem()),
                journal = journal,
                editMode = editMode,
                isSelected = journal.isChecked,
                onClick = onClick,
                onLongClick = onLongClick,
                viewModel = viewModel,
                itemHeight = itemHeight,
                onEditJournalClick = onEditJournalClick,
                onShareJournalClick = onShareJournalClick,
                onMoveJournalClick = onMoveJournalClick,
                onDeleteJournalClick = onDeleteJournalClick,
            )
        }
    }
}

@Composable
fun LargeImageItem(
    modifier: Modifier,
    journal: HomeNoteItemEntity,
    darkTheme: Boolean = isSystemInDarkTheme(),
    editMode: Boolean = true,
    isSelected: Boolean = false,
    onClick: (HomeNoteItemEntity) -> Unit,
    onLongClick: (HomeNoteItemEntity) -> Unit,
    viewModel: JournalContentViewModel,
    itemHeight: Dp,
    onEditJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onShareJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onMoveJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onDeleteJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
) {
    var clickedJournal by remember { mutableStateOf<HomeNoteItemEntity?>(null) }
    var selectedPageInfos by remember { mutableStateOf<List<PageInfo>?>(null) }

    LaunchedEffect(viewModel.content) {
        viewModel.content.collect {
            selectedPageInfos = it.pageInfos
        }
    }

    Box(modifier = Modifier
        .fillMaxSize()
        .padding(top = 38.dp, end = 21.dp), contentAlignment = Alignment.TopEnd) {
        JournalItemDropDown(
            expanded = clickedJournal != null,
            //offset = DpOffset(12.dp, 12.dp),
            journal = clickedJournal,
            selectedPageInfos = selectedPageInfos,
            viewModel = viewModel,
            onEditJournalClick = onEditJournalClick,
            onShareJournalClick = onShareJournalClick,
            onMoveJournalClick = onMoveJournalClick,
            onDeleteJournalClick = onDeleteJournalClick,
            onDismissRequest = { clickedJournal = null }
        )
    }
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(itemHeight)
            .semantics {
                 // 设置语义标签以支持无障碍功能
                contentDescription = journal.title + ", " + formatTime(journal.createTime ?: System.currentTimeMillis(), Locale.getDefault())
                role = if (editMode) Role.Checkbox else Role.Button
            }
            .clip(
                RoundedCornerShape(
                    topStart = 12.dp,
                    topEnd = 12.dp,
                    bottomStart = 4.dp,
                    bottomEnd = 12.dp
                )
            )
            .background(TclTheme.tclColorScheme.tctStanderBgDialog)
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = {
                        onClick(journal)
                    },
                    onLongPress = {
                        onLongClick(journal)
                    }
                )
            },
    ) {
        val coverRes = if (journal.id.toLong() == 1L) {
            R.drawable.cover_sample
        } else {
            CoverDataList.coverList[journal.coverId.toInt()]?.resId
                ?: R.drawable.cover_1
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Image(
                painter = painterResource(id = coverRes),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f)
                    .alpha(darkTheme.judge(0.7f, 1f)),
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.TopCenter
            )

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 12.dp, end = 10.dp, top = 12.dp)
            ) {
                Text(
                    modifier = Modifier
                        .fillMaxWidth(),
                    text = buildHighlightedText(journal.title.replace("\n", ""), journal.highlightInfo?.searchKeyword?: ""),
                    fontSize = 14.sp,
                    color = TclTheme.tclColorScheme.tctStanderTextPrimary.copy(alpha = 0.8f),
                    maxLines = 2,
                    lineHeight = 18.sp,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.Start,
                )

                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp),
                    text = formatTime(
                        journal.createTime ?: System.currentTimeMillis(),
                        Locale.getDefault()
                    ),
                    color = darkTheme.judge(
                        Color.White,
                        Color.Black,
                    ).copy(alpha = 0.5f),
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.Start,
                )
            }
        }

        Image(
            painter = painterResource(id = R.drawable.ic_journal_cover_axis),
            contentDescription = null,
            modifier = Modifier
                .width(12.dp)
                .fillMaxHeight(),
            contentScale = ContentScale.FillHeight
        )

        if (editMode) {
            Image(
                modifier = Modifier
                    .padding(top = 12.dp, start = 12.dp)
                    .size(20.dp)
                    .align(Alignment.TopStart),
                painter = painterResource(
                    id = if (isSelected) {
                        R.drawable.ic_check_selected
                    } else {
                        R.drawable.ic_check_unselected
                    }
                ),
                contentDescription = isSelected.judge(stringResource(id = com.tcl.ai.note.base.R.string.checked_status), stringResource(id = com.tcl.ai.note.base.R.string.unchecked_status))
            )
        } else {
            Icon(
                modifier = Modifier
                    .padding(top = 12.dp, end = 12.dp)
                    .size(24.dp)
                    .align(Alignment.TopEnd)
                    .background(
                        color = Color.Black.copy(alpha = 0.15f),
                        shape = CircleShape
                    )
                    .padding(4.dp)
                    .clickable {
                        clickedJournal = journal
                        viewModel.handleEvent(JournalContentIntent.LoadContent(journal.id.toLong(), null))
                    },
                painter = painterResource(id = R.drawable.ic_journal_more_actions),
                contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.navigation_more_button),
                tint = Color.White,
            )
        }
    }
}

/**
 * 测量字体高度，返回高度Dp
 */
private fun measureTextHeight(
    textMeasurer: TextMeasurer,
    localDensity: Density,
    text: String,
    fontSize: TextUnit,
    lineHeight: TextUnit
): Dp {
    // 执行测量
    val result = textMeasurer.measure(
        text = text,
        style = TextStyle(
            fontSize = fontSize,
            lineHeight = lineHeight
        ),
    )
    // 拿到高度
    return with(localDensity) { result.size.height.toDp() }
}

@SuppressLint("DesignSystem")
@Composable
internal fun ListScreen(
    journals: List<HomeNoteItemEntity>,
    lazyListState: LazyListState,
    editMode: Boolean,
    searchMode: Boolean,
    onClick: (HomeNoteItemEntity) -> Unit,
    onLongClick: (HomeNoteItemEntity) -> Unit,
    viewModel: JournalContentViewModel = hiltViewModel(),
    onEditJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onShareJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onMoveJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onDeleteJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
) {
    LazyColumn(
        state = lazyListState,
        contentPadding = PaddingValues(12.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        modifier = Modifier
            .fillMaxSize(),
    ) {
        items(items = journals, key = { it.id }) { journal ->
            ListItem(
                modifier = searchMode.judge(Modifier, Modifier.animateItem()),
                journal = journal,
                editMode = editMode,
                isSelected = journal.isChecked,
                onClick = onClick,
                onLongClick = onLongClick,
                viewModel = viewModel,
                onEditJournalClick = onEditJournalClick,
                onShareJournalClick = onShareJournalClick,
                onMoveJournalClick = onMoveJournalClick,
                onDeleteJournalClick = onDeleteJournalClick,
            )
        }
    }
}

@Composable
internal fun ListItem(
    modifier: Modifier = Modifier,
    journal: HomeNoteItemEntity,
    darkTheme: Boolean = isSystemInDarkTheme(),
    editMode: Boolean = true,
    isSelected: Boolean = false,
    viewModel: JournalContentViewModel = hiltViewModel(),
    onClick: (HomeNoteItemEntity) -> Unit,
    onLongClick: (HomeNoteItemEntity) -> Unit,
    onEditJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onShareJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onMoveJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onDeleteJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
) {
    val context = LocalContext.current
    var clickedJournal by remember { mutableStateOf<HomeNoteItemEntity?>(null) }
    var selectedPageInfos by remember { mutableStateOf<List<PageInfo>?>(null) }

    LaunchedEffect(viewModel.content) {
        viewModel.content.collect {
            selectedPageInfos = it.pageInfos
        }
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = darkTheme.judge(
                    Color.White.copy(alpha = 0.08f),
                    Color.White
                ), RoundedCornerShape(12.dp)
            )
            .semantics {
                // 设置语义标签以支持无障碍功能
                contentDescription = journal.title + ", " + formatTime(journal.createTime ?: System.currentTimeMillis(), Locale.getDefault())
                role = if (editMode) Role.Checkbox else Role.Button
            }
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = {
                        onClick(journal)
                    },
                    onLongPress = {
                        onLongClick(journal)
                    }
                )
            }
            .padding(start = 16.dp, top = 16.dp, bottom = 16.dp, end = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        val coverRes = if (journal.id.toLong() == 1L) {
            R.drawable.cover_sample
        } else {
            CoverDataList.coverList[journal.coverId.toInt()]?.resId
                ?: R.drawable.cover_1
        }
        Image(
            painter = painterResource(id = coverRes),
            contentDescription = null,
            modifier = Modifier
                .width(56.dp)
                .aspectRatio(56f / 80f)
                .alpha(darkTheme.judge(0.7f, 1f)),
            contentScale = ContentScale.Crop
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 12.dp)
        ) {
            Text(
                modifier = Modifier
                    .fillMaxWidth(),
                text = buildHighlightedText(journal.title.replace("\n", ""), journal.highlightInfo?.searchKeyword?: ""),
                fontSize = 14.sp,
                lineHeight = 18.sp,
                color = TclTheme.tclColorScheme.tctStanderTextPrimary,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Start,
            )

            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                text = formatTime(
                    journal.createTime ?: System.currentTimeMillis(),
                    Locale.getDefault()
                ),
                color = darkTheme.judge(
                    Color.White,
                    Color.Black,
                ).copy(alpha = 0.5f),
                fontSize = 12.sp,
                lineHeight = 16.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Start,
            )
        }

        if (editMode) {
            Image(
                modifier = Modifier
                    .padding(start = 24.dp)
                    .size(20.dp),
                painter = painterResource(
                    id = if (isSelected) {
                        R.drawable.ic_check_selected
                    } else {
                        R.drawable.ic_check_unselected
                    }
                ),
                contentDescription = isSelected.judge(stringResource(id = com.tcl.ai.note.base.R.string.checked_status), stringResource(id = com.tcl.ai.note.base.R.string.unchecked_status))
            )
        } else {
            Box(modifier = Modifier.wrapContentSize()) {
                Icon(
                    modifier = Modifier
                        .padding(start = 24.dp)
                        .size(24.dp)
                        .padding(2.dp)
                        .clickable {
                            clickedJournal = journal
                            viewModel.handleEvent(
                                JournalContentIntent.LoadContent(
                                    journal.id.toLong(),
                                    null
                                )
                            )
                        },
                    painter = painterResource(id = R.drawable.ic_journal_more_actions),
                    contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.navigation_more_button),
                    tint = darkTheme.judge(
                        Color.White,
                        Color.Black,
                    ).copy(alpha = 0.7f)
                )
                JournalItemDropDown(
                    expanded = clickedJournal != null,
                    journal = clickedJournal,
                    selectedPageInfos = selectedPageInfos,
                    viewModel = viewModel,
                    onEditJournalClick = onEditJournalClick,
                    onShareJournalClick = onShareJournalClick,
                    onMoveJournalClick = onMoveJournalClick,
                    onDeleteJournalClick = onDeleteJournalClick,
                    onDismissRequest = { clickedJournal = null }
                )
            }
        }
    }
}

@Composable
fun FlatViewList(
    journals: List<HomeNoteItemEntity>,
    lazyListState: LazyListState,
    editMode: Boolean,
    searchMode: Boolean,
    //selectedNotes: Map<Long, Journal>,
    onLongClick: (HomeNoteItemEntity) -> Unit,
    onClick: (HomeNoteItemEntity, Rect?) -> Unit
) {
    val columnCount = remember { 3 }
    val rows = journals.chunked(columnCount)
    val shelfTopPadding = remember {
        if (GlobalContext.densityDpi == deviceDensity) {
            isDensity440.judge(119.dp, 110.dp)
        } else if (GlobalContext.densityDpi < deviceDensity) {
            isDensity440.judge(116.dp, 107.dp)
        } else {
            isDensity440.judge(122.dp, 112.dp)
        }
    } // Top padding for the
    LazyColumn(
        state = lazyListState,
        modifier = Modifier
            .fillMaxSize()
            .onGloballyPositioned { coordinates ->
                val boundsRect = coordinates.boundsInWindow()
                if (boundsRect.width.toInt() != GlobalContext.screenWidth) {
                    return@onGloballyPositioned
                }
                setCreateCoverRect(boundsRect, journals.getOrNull(0)?.id?.toLong())
            },
        contentPadding = PaddingValues(vertical = 12.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp) // Space between shelves
    ) {

        items(count = rows.size) { rowIndex ->
            Box(
                //contentAlignment = Alignment.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentSize()
            ) {
                Image(modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = shelfTopPadding),
                    painter = painterResource(id = com.tcl.ai.note.journaldashboard.R.drawable.ic_journal_shelf),
                    contentDescription = null,
                    contentScale = ContentScale.FillWidth)

                // Books on the shelf
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.Top,
                ) {
                    for (i in (rowIndex * columnCount) until ((rowIndex + 1) * columnCount)) {
                        if (i < journals.size) {
                            if (i != rowIndex * columnCount) {
                                Spacer(modifier = Modifier.width(16.dp))
                            }
                            Column(modifier = Modifier.weight(1f),
                                horizontalAlignment = Alignment.CenterHorizontally,) {
                                val coverRes = if (journals[i].id.toLong() == 1L) {
                                    R.drawable.cover_sample
                                } else {
                                    CoverDataList.coverList[rows[rowIndex][i % columnCount].coverId.toInt()]?.resId?: R.drawable.cover_1
                                }
                                CoverItem(
                                    modifier = Modifier.wrapContentSize(),
                                    journal = journals[i],
                                    editMode = editMode,
                                    isSelected = journals[i].isChecked,
                                    imagePainter = painterResource(id = coverRes),
                                    onClick = { journal, boundsRect ->
                                        onClick(journal, boundsRect)
                                    },
                                    onLongClick = { journal ->
                                        onLongClick(journal)
                                    },
                                    isDensity440 = isDensity440
                                )
                                InfoItem(journals[i])
                            }
                        } else {
                            // Placeholder for empty space
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                }
            }
        }
    }
}

@SuppressLint("DesignSystem")
@Composable
private fun CoverItem(
    modifier: Modifier = Modifier,
    journal: HomeNoteItemEntity,
    editMode: Boolean = true,
    isSelected: Boolean = false,
    imagePainter: Painter,
    onClick: (HomeNoteItemEntity, Rect?) -> Unit = { _, _ -> },
    onLongClick: (HomeNoteItemEntity) -> Unit = {},
    isDensity440: Boolean = false,
    darkTheme: Boolean = isSystemInDarkTheme(),
) {
    var boundsRect by remember { mutableStateOf<Rect?>(null) }
    val journalCardSize = remember {
        Pair(
            isDensity440.judge(96.dp, 88.dp), // Width
            isDensity440.judge(136.dp, 124.dp) // Height
        )
    }
    Box(modifier = modifier.semantics {
        // 设置语义标签以支持无障碍功能
        contentDescription = journal.title + ", " + formatTime(journal.createTime ?: System.currentTimeMillis(), Locale.getDefault())
        role = if (editMode) Role.Checkbox else Role.Button
    }) {
        Box(modifier = Modifier.height(journalCardSize.second)) {
            if (!darkTheme) {
                Image(
                    painter = painterResource(id = R.drawable.ic_journal_shadow),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(top = 2.dp, bottom = 4.dp)
                        .fillMaxHeight()
                        .align(Alignment.CenterStart) // 将阴影图片对齐到左侧
                        .offset(x = (-11).dp, y = 0.dp), // 偏移以放置阴影效果
                    contentScale = ContentScale.FillHeight // 使用 FillHeight 确保阴影图像与主图像等高
                )
            }
            Image(
                painter = imagePainter,
                contentDescription = null,
                modifier = Modifier
                    .width(journalCardSize.first)
                    .height(journalCardSize.second)
                    .clip(RoundedCornerShape(4.dp))
                    .alpha(darkTheme.judge(0.7f, 1f))
                    .onGloballyPositioned { coordinates ->
                        boundsRect = coordinates.boundsInWindow()
                    }
                    .pointerInput(Unit) {
                        detectTapGestures(
                            onTap = {
                                // Handle item click
                                onClick(journal, boundsRect)
                            },
                            onLongPress = {
                                // Provide feedback for long press
                                onLongClick(journal)
                            },
                        )
                    },
                contentScale = ContentScale.Crop
            )
        }
        if (editMode) {
            Image(
                modifier = Modifier
                    .padding(top = 10.dp, start = 10.dp)
                    .size(20.dp)
                    .align(Alignment.TopStart),
                painter = painterResource(id = if (isSelected) R.drawable.ic_check_selected else R.drawable.ic_check_unselected),
                contentDescription = isSelected.judge(stringResource(id = com.tcl.ai.note.base.R.string.checked_status), stringResource(id = com.tcl.ai.note.base.R.string.unchecked_status))
            )
        }
    }
}

@Composable
private fun InfoItem(
    journal: HomeNoteItemEntity,
    darkTheme: Boolean = isSystemInDarkTheme(),
) {
    val infoHorizontalPaddings = remember {
        if (GlobalContext.densityDpi == deviceDensity) {
            isDensity440.judge(8.dp, 8.dp)
        } else if (GlobalContext.densityDpi < deviceDensity) {
            isDensity440.judge(18.dp, 18.dp)
        } else {
            isDensity440.judge(0.dp, 0.dp)
        }
    }
    val highlightedTitle = buildHighlightedText(journal.title.replace("\n", ""), journal.highlightInfo?.searchKeyword?: "")
    Column(modifier = Modifier
        .fillMaxWidth()
        .padding(top = 20.dp, start = infoHorizontalPaddings, end = infoHorizontalPaddings)) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(unbounded = true),
            text = highlightedTitle,
            fontSize = isDensity440.judge(13.sp, 12.sp),
            color = TclTheme.tclColorScheme.tctStanderTextPrimary.copy(alpha = 0.8f),
            lineHeight = 16.sp,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Start,
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(unbounded = true)
                .padding(top = 4.dp),
            text = formatTime(journal.createTime?: System.currentTimeMillis(), Locale.getDefault()),
            color = darkTheme.judge(
                Color.White.copy(alpha = 0.5f),
                Color.Black.copy(alpha = 0.5f),
            ).copy(alpha = 0.8f),
            fontSize = isDensity440.judge(11.sp, 10.sp),
            lineHeight = 14.sp,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Start,
        )
    }
}

@Composable
internal fun buildHighlightedText(text: String, query: String): AnnotatedString {
    return buildAnnotatedString {
        append(text)
        if (query.isNotEmpty()) {
            var startIndex = 0
            while (startIndex < text.length) {
                val matchIndex = text.indexOf(query, startIndex, ignoreCase = true)
                if (matchIndex == -1) break

                // 添加高亮样式
                addStyle(
                    style = SpanStyle(
                        color = colorResource(com.tcl.ai.note.base.R.color.search_highlight)
                    ),
                    start = matchIndex.coerceAtLeast(0),
                    end = matchIndex + query.length
                )
                startIndex = matchIndex + query.length
            }
        }
    }
}

@Composable
internal fun JournalItemDropDown(
    modifier: Modifier = Modifier,
    expanded: Boolean,
    offset: DpOffset = DpOffset(0.dp, 0.dp),
    journal: HomeNoteItemEntity?,
    selectedPageInfos: List<PageInfo>?,
    viewModel: JournalContentViewModel,
    onEditJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onShareJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onMoveJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onDeleteJournalClick: (journal: HomeNoteItemEntity) -> Unit = { _ -> },
    onDismissRequest: () -> Unit = {},
) {
    val context = LocalContext.current
    TclDropdownMenu(modifier = modifier.width(154.dp), expanded = expanded, onDismissRequest = { onDismissRequest.invoke() }, offset = offset) {
        TclDropDownMenuItem(text = { Text(text = stringResource(id = R.string.edit_information), color = TclTheme.colorScheme.tctStanderTextPrimary) },
            onClick = {
                onEditJournalClick(journal!!)
                onDismissRequest.invoke()
            }
        )
        if (selectedPageInfos?.isNotEmpty() == true) {
            TclDropDownMenuItem(text = { Text(text = stringResource(id = R.string.share_journal), color = TclTheme.colorScheme.tctStanderTextPrimary) },
                onClick = {
                    viewModel.shareImages(context, selectedPageInfos)
                    onShareJournalClick.invoke(journal!!)
                    onDismissRequest.invoke()
                }
            )
        }
        TclDropDownMenuItem(text = { Text(text = stringResource(id = com.tcl.ai.note.base.R.string.move), color = TclTheme.colorScheme.tctStanderTextPrimary) },
            onClick = {
                onMoveJournalClick.invoke(journal!!)
                onDismissRequest.invoke()
            }
        )
        TclDropDownMenuItem(text = { Text(text = stringResource(id = com.tcl.ai.note.base.R.string.delete), color = TclTheme.colorScheme.tctStanderTextPrimary) },
            onClick = {
                onDeleteJournalClick.invoke(journal!!)
                onDismissRequest.invoke()
            }
        )
    }
}

@Preview
@Composable
fun PreviewFloatViewList() {
    val journals = listOf(
        HomeNoteItemEntity("1", "Journal 1 Journal 1 Journal 1 Journal 1 Journal 1", 0L, "2023-10-01"),
        HomeNoteItemEntity("2", "Journal 2", 1L, "2023-10-01"),
        HomeNoteItemEntity("3", "Journal 3", 2L, "2023-10-01"),
        HomeNoteItemEntity("4", "Journal 4", 3L, "2023-10-01"),
        HomeNoteItemEntity("5", "Journal 5", 4L, "2023-10-01"),
    )
    //FlatViewList(journals = journals, lazyListState = LazyListState(), editMode = true, /*emptyMap(),*//* searchQuery = "",*/ onLongClick = {}, onClick = { _, _ -> })
}