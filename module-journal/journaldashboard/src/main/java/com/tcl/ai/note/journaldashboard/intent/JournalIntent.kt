package com.tcl.ai.note.journaldashboard.intent

import com.tcl.ai.note.database.entity.Journal
import com.tcl.ai.note.database.entity.JournalContent
import com.tcl.ai.note.template.bean.PageConfigInfo

sealed class JournalIntent {
    data class CreateJournal(val journal: Journal) : JournalIntent()
    data class EditJournal(val journal: Journal) : JournalIntent()
    data class DeleteJournal(val journalId: Long) : JournalIntent()
}

sealed class JournalContentIntent {
    data class LoadContent(val journalId: Long, val configInfo: PageConfigInfo? = null) : JournalContentIntent()
    data class InsertContent(val content: JournalContent) : JournalContentIntent()
    data class UpdateContent(val content: JournalContent, val callback: () -> Unit = {}) : JournalContentIntent()
    data class UpdateJournalTitle(val journalId: Long, val journalTitle: String) : JournalContentIntent()
    data class UpdateJournalModifyTime(val journalId: Long, val modifyTime: Long) : JournalContentIntent()
}