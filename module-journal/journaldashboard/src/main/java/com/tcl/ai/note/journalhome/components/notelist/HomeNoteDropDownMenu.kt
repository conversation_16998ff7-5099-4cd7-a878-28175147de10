package com.tcl.ai.note.journalhome.components.notelist

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScalePopup
import com.tcl.ai.note.handwritingtext.ui.popup.BounceScaleRightTop
import com.tcl.ai.note.handwritingtext.utils.defShadow
import com.tcl.ai.note.journalhome.components.HomePopItem
import com.tcl.ai.note.journalhome.vm.state.HomeNoteUiState
import com.tcl.ai.note.journalhome.vm.state.HomeTitleMode
import com.tcl.ai.note.journalhome.vm.state.NoteListAction
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.TclTheme.dimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toPx

// 菜单项数据类
data class MenuItem(
    val textResId: Int,
    val action: () -> Unit,
    val shouldCloseMenu: Boolean = true,
    val showCheckIcon: Boolean = false,
    val enabled: Boolean = true // 是否启用
)

@Composable
internal fun ShowDropDownMenu(
    isDropdownMenuExpanded: Boolean,
    menuItems: List<MenuItem>,
    onDismissRequest: () -> Unit,
    showCheckIcons: Boolean = false // 是否显示选中图标
) {

    val menuWidth = when {
        showCheckIcons && isTablet -> 207.dp
        showCheckIcons -> 186.dp
        isTablet -> 128.dp
        else -> 108.dp
    }

    // 👇 给menuWidth加动画
    val animMenuWidth by animateDpAsState(
        targetValue = menuWidth,
//        animationSpec = spring(
//            dampingRatio = Spring.DampingRatioNoBouncy,
//            stiffness = Spring.StiffnessLow
//        ),
        animationSpec = tween(
            durationMillis = 100,
            easing = FastOutLinearInEasing
        ),
        label = "MenuWidth"
    )

    val offset = IntOffset(
        x = 0,
        y = dimens.navigationBarHeight.toPx.toInt() - 6.dp.toPx.toInt()
    )

    Logger.d("ShowDropDownMenu","menuWidth:$menuWidth")

    if (isDropdownMenuExpanded){
        BounceScalePopup(
            onDismissRequest = onDismissRequest,
            durationMillis = 200,
            offset = offset,
            alignment = Alignment.TopEnd,
            enterTransformOrigin = BounceScaleRightTop,
            exitTransformOrigin = BounceScaleRightTop
        ) { _ ->
            Box(
                modifier = Modifier
                    .width(animMenuWidth)
                    .wrapContentHeight()
                    .defShadow(radius = 20.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(colorResource(R.color.drop_down_menu_bg))
                        .padding(4.dp)
                )
                {
                    menuItems.forEach { menuItem ->
                        HomePopItem(
                            text = stringResource(menuItem.textResId),
                            showCheckIcons && menuItem.showCheckIcon, menuItem.action
                        )
                    }
                }
            }
        }
    }
}

/**
 * 创建笔记列表菜单项的工厂函数
 */
fun createNoteListMenuItems(
    homeNoteUiState: HomeNoteUiState,
    onAction: (NoteListAction) -> Unit,
    onMenuShow: (Boolean) -> Unit
): List<MenuItem> {
    return if (homeNoteUiState.isClickedSort) {
        // 排序菜单
        listOf(
            MenuItem(
                textResId = R.string.create_date,
                action = {
                    onAction(NoteListAction.OnChangeSortType(isCreateTimeSort = true))
                    onMenuShow(false)
                },
                showCheckIcon = homeNoteUiState.isCreateTimeSort
            ),
            MenuItem(
                textResId = R.string.modify_date,
                action = {
                    onAction(NoteListAction.OnChangeSortType(isCreateTimeSort = false))
                    onMenuShow(false)
                },
                showCheckIcon = !homeNoteUiState.isCreateTimeSort
            )
        )
    } else if (homeNoteUiState.isClickedView) {
        // 展示方式菜单
        listOf(
            MenuItem(
                textResId = com.tcl.ai.note.resources.R.string.view_large_image,
                action = {
                    onAction(NoteListAction.OnChangeViewType(viewType = DataStoreParam.VIEW_TYPE_LARGE_IMAGE))
                    onMenuShow(false)
                },
                showCheckIcon = homeNoteUiState.viewType == DataStoreParam.VIEW_TYPE_LARGE_IMAGE
            ),
            MenuItem(
                textResId = com.tcl.ai.note.resources.R.string.view_list,
                action = {
                    onAction(NoteListAction.OnChangeViewType(viewType = DataStoreParam.VIEW_TYPE_LIST))
                    onMenuShow(false)
                },
                showCheckIcon = homeNoteUiState.viewType == DataStoreParam.VIEW_TYPE_LIST
            ),
            MenuItem(
                textResId = com.tcl.ai.note.resources.R.string.view_grid,
                action = {
                    onAction(NoteListAction.OnChangeViewType(viewType = DataStoreParam.VIEW_TYPE_GRID))
                    onMenuShow(false)
                },
                showCheckIcon = homeNoteUiState.viewType == DataStoreParam.VIEW_TYPE_GRID
            )
        )
    } else {
        // 主菜单
        listOf(
            MenuItem(
                textResId = com.tcl.ai.note.resources.R.string.view,
                action = {
                    onAction(NoteListAction.OnClickView(clickView = true))
                    onMenuShow(true)
                },
                shouldCloseMenu = false
            ),
            MenuItem(
                textResId = R.string.sort,
                action = {
                    onAction(NoteListAction.OnClickSort(clickSorted = true))
                    onMenuShow(true)
                },
                shouldCloseMenu = false
            ),
            MenuItem(
                textResId = R.string.edit,
                action = {
                    onAction(NoteListAction.OnChangeTitleMode(titleMode = HomeTitleMode.Edit))
                    onMenuShow(false)
                }
            ),
            MenuItem(
                textResId = R.string.settings,
                action = {
                    onAction(NoteListAction.GoToSettings)
                    onMenuShow(false)
                }
            )
        )
    }
}

/**
 * 向后兼容的组件，使用原有的参数
 */
@Composable
internal fun ShowDropDownMenuCompat(
    isDropdownMenuExpanded: Boolean,
    homeNoteUiState: HomeNoteUiState,
    onAction: (NoteListAction) -> Unit,
    onShow: (Boolean) -> Unit
) {
    val menuItems = createNoteListMenuItems(
        homeNoteUiState = homeNoteUiState,
        onAction = onAction,
        onMenuShow = onShow
    )

    ShowDropDownMenu(
        isDropdownMenuExpanded = isDropdownMenuExpanded,
        menuItems = menuItems,
        onDismissRequest = { onShow(false) },
        showCheckIcons = homeNoteUiState.isClickedSort || homeNoteUiState.isClickedView
    )
}

/**
 * 使用示例：创建自定义菜单
 */
@Composable
fun ExampleCustomDropDownMenu(
    isExpanded: Boolean,
    onDismiss: () -> Unit
) {
    val customMenuItems = listOf(
        MenuItem(
            textResId = R.string.sort,
            action = { /* 处理排序 */ }
        ),
        MenuItem(
            textResId = R.string.edit,
            action = { /* 处理编辑 */ },
            enabled = true
        ),
        MenuItem(
            textResId = R.string.delete,
            action = { /* 处理删除 */ },
            enabled = false // 禁用状态示例
        )
    )

    ShowDropDownMenu(
        isDropdownMenuExpanded = isExpanded,
        menuItems = customMenuItems,
        onDismissRequest = onDismiss,
    )
}