<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="32dp"
    android:viewportWidth="32"
    android:viewportHeight="32">
  <path
      android:pathData="M30,16C30,17.78 29.673,19.498 29.017,21.154C28.306,22.951 27.266,24.533 25.9,25.9C24.533,27.267 22.951,28.306 21.154,29.017C19.498,29.673 17.78,30 16,30C14.22,30 12.502,29.673 10.847,29.017C9.05,28.306 7.467,27.267 6.101,25.9C4.734,24.533 3.695,22.951 2.983,21.154C2.328,19.498 2,17.78 2,16C2,14.22 2.328,12.502 2.983,10.847C3.695,9.05 4.734,7.468 6.101,6.101C7.467,4.734 9.05,3.695 10.847,2.983C12.502,2.328 14.22,2 16,2C17.78,2 19.498,2.328 21.154,2.983C22.951,3.695 24.533,4.734 25.9,6.101C27.266,7.468 28.306,9.05 29.017,10.847C29.673,12.502 30,14.22 30,16Z"
      android:strokeAlpha="0.6"
      android:fillAlpha="0.6"
      android:fillType="evenOdd">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="4.968"
          android:startY="6.536"
          android:endX="30"
          android:endY="30"
          android:type="linear">
        <item android:offset="0" android:color="#FF000000"/>
        <item android:offset="0.621" android:color="#FF000000"/>
        <item android:offset="1" android:color="#FF000000"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M16.956,20.096L20.326,16.702C20.678,16.348 20.677,15.775 20.323,15.423L16.951,12.058C16.685,11.793 16.253,11.795 15.99,12.063C15.73,12.327 15.731,12.752 15.993,13.015L18.36,15.389L11.63,15.404C11.261,15.405 10.963,15.704 10.963,16.073C10.963,16.443 11.263,16.742 11.633,16.741L18.409,16.726L15.999,19.143C15.735,19.408 15.736,19.837 16.002,20.1C16.267,20.362 16.694,20.36 16.956,20.096"
      android:fillColor="#FFFFFF"/>
</vector>
