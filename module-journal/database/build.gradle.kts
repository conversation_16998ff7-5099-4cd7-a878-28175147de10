plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.google.dagger.hilt)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.google.devtools.ksp)
    id("kotlin-parcelize")
    id("org.jetbrains.kotlin.plugin.serialization")
}

android {
    namespace = "com.tcl.ai.note.database"
    compileSdk = libs.versions.compileSdk.get().toIntOrNull() ?: 35

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toIntOrNull() ?: 34
        targetSdk = libs.versions.targetSdk.get().toIntOrNull() ?: 35
        buildConfigField("Boolean", "IS_CN", "false")
        buildConfigField("boolean", "IS_PHONE", "true")
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary = true
        }
    }

    buildFeatures {
        buildConfig = true
    }
    flavorDimensions.add("device")
    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }

    productFlavors {
        create("phone") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "true")
        }
        create("tablet") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "false")
        }
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
    buildFeatures {
        compose = true
        buildConfig = true
    }
}

dependencies {

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.activity.compose)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui)
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.androidx.material3)
    implementation(libs.androidx.adaptive)
    implementation(libs.androidx.lifecycle.service)
    implementation(libs.tcl.componentfrm)
    implementation(libs.androidx.datastore.preferences)
    implementation(libs.androidx.fragment)
    implementation(libs.androidx.appcompat)
    implementation(libs.dagger.hilt)
    implementation(libs.androidx.constraintlayout.compose.android)
    implementation(libs.androidx.constraintlayout)
    implementation(project(":module-journal:base"))
    implementation(project(":module-journal:base"))
    ksp(libs.dagger.hilt.compiler)
    ksp(libs.androidx.room.compiler)
    implementation(libs.androidx.lifecycle.viewmodel.compose)
    implementation(libs.androidx.navigation.compose)
    implementation(libs.androidx.hilt.navigation.compose)
    implementation(libs.okhttp3.okhttp)
    implementation(libs.retrofit)
    implementation(libs.airbnb.lottie.compose)
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    implementation(libs.coil.compose)
    implementation(libs.coil.network.okhttp)
    implementation(libs.compose.ui)
    implementation(libs.compose.material)
    implementation(libs.compose.ui.tooling)
    implementation(libs.compose.foundation)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(libs.androidx.work.runtime.ktx)
    implementation(libs.gson)
    implementation(libs.tclui.compose)
    implementation(libs.tclui.compose.icons)
    lintChecks(libs.tclui.compose.lint)
    implementation(libs.kotlinx.serialization.core)
    implementation(libs.kotlinx.serialization.protobuf)

    implementation(project("::module-base"))
    implementation(project(":module-journal:resources"))

    // tct Bi
    implementation(libs.bundles.tctBI)
    implementation("com.tcl.ff.component:http:1.2.3") {
        exclude(group = "xmlpull", module = "xmlpull")
    }

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
}