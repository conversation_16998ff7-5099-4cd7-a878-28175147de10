package com.tcl.ai.note.drawboard

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.RectF
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.compose.ui.geometry.Offset
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.ui.TextAndDrawBoardLayout
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.handwritingtext.ui.utils.scale.OffsetAndScaleEdgeLimitAction.CenterEdgeLimitAction
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.screenSizeMax
import com.tcl.ai.note.utils.screenSizeMin

@SuppressLint("ViewConstructor")
class JournalDrawBoardLayout(
    private val context: Context,
    private val richTextViewModel: RichTextViewModel2,
    private val suniaDrawViewModel: SuniaDrawViewModel,
    private val textAndDrawViewModel: TextAndDrawViewModel,
    private val menuBarViewModel: MenuBarViewModel
) : TextAndDrawBoardLayout(
    context = context,
    richTextViewModel = richTextViewModel,
    suniaDrawViewModel = suniaDrawViewModel,
    textAndDrawViewModel = textAndDrawViewModel,
    menuBarViewModel = menuBarViewModel
) {

    init {
        offsetAndScaleHandler.setScaleLimit(minScale = 0.7f, maxScale = 6f)
        offsetAndScaleHandler.setOffsetLimit(minOffsetX = { 0f }, maxOffsetX = { 0f }, minOffsetY = { 0f }, maxOffsetY = { 0f })
        offsetAndScaleHandler.customEdgeLimitAction = offsetAndScaleHandler.CenterEdgeLimitAction
    }

    override fun defaultMatrixInfo(): MatrixInfo {
        return MatrixInfo(scale = 1.0f)
    }

    override fun attachViewGroup(viewGroup: ViewGroup) {
        suniaDrawViewController.attachViewGroup(viewGroup)
    }

    private var hasMovedOut = false
    private var mTouchPoint: Offset = Offset.Zero
    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        if (ev == null) return super.dispatchTouchEvent(ev)

        if (textAndDrawViewModel.editMode == EditMode.DRAW && ev.pointerCount == 1) {
            if (ev.actionMasked == MotionEvent.ACTION_DOWN || ev.actionMasked == MotionEvent.ACTION_MOVE)   {
                if (ev.actionMasked == MotionEvent.ACTION_DOWN)   {
                    hasMovedOut = false
                }
                mTouchPoint = Offset(ev.x, ev.y)
                if (mTouchPoint.y < drawableRect.top || mTouchPoint.y > drawableRect.bottom) {
                    // 触摸位置在绘制区域外，拦截
                    if (ev.actionMasked == MotionEvent.ACTION_DOWN) {
                        hasMovedOut = true
                        return true
                    } else {
                        if (hasMovedOut) return true //如果
                        hasMovedOut = true
                        ev.action = MotionEvent.ACTION_UP
                        ev.setLocation(
                            ev.x,
                            (mTouchPoint.y < drawableRect.top).judge(drawableRect.top, drawableRect.bottom)
                        )
                        Logger.d(TAG, "onInterceptTouchEvent, EditMode.DRAW: ACTION UP")
                        return super.dispatchTouchEvent(ev)
                    }
                }
            }
        }
        return super.dispatchTouchEvent(ev)
    }

    override fun onScrollAndScaleEnd(matrixInfo: MatrixInfo, overRect: RectF, isGesture: Boolean) {
        super.onScrollAndScaleEnd(matrixInfo, overRect, isGesture)
        updateDrawableRect()
    }

    private var drawableRect: RectF = RectF()
    private fun updateDrawableRect() {
        val scaledWidth = (screenSizeMin * textAndDrawViewModel.scale).toInt()
        val scaledHeight = (screenSizeMax * textAndDrawViewModel.scale).toInt()
        val left = (screenSizeMin - scaledWidth) / 2f
        val top = (screenSizeMax - scaledHeight) / 2f
        val right = left + scaledWidth
        val bottom = top + scaledHeight
        drawableRect.set(left, top, right, bottom)
    }

    override fun showToastWhenScrollToBottom(overRect: RectF, isGesture: Boolean) {

    }
}