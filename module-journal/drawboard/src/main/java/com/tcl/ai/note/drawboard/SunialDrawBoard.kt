package com.tcl.ai.note.drawboard

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.vm.MenuBarViewModel
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.rememberImeHeightPxWithoutNav
import com.tcl.ai.note.widget.DisposableEffectWithLifecycle

@Composable
fun SuniaDrawBoard(
    modifier: Modifier = Modifier,
    richTextViewModel: RichTextViewModel2 = hiltViewModel(),
    suniaDrawViewModel: SuniaDrawViewModel = hiltViewModel(),
    textAndDrawViewModel: TextAndDrawViewModel = hiltViewModel(),
    menuBarViewModel: MenuBarViewModel = hiltViewModel(),
) {
    var textAndDrawBoardLayout: JournalDrawBoardLayout? by remember { mutableStateOf(null) }
    val focusManager = LocalFocusManager.current
    val isDarkTheme = isSystemInDarkTheme()
    var lastIsDarkTheme by remember { mutableStateOf(false) }

    // 获取键盘状态，需要在DisposableEffectWithLifecycle之前声明
    val menuBarState by menuBarViewModel.menuBarState.collectAsState()
    val isKeyboardActive by remember { derivedStateOf { menuBarState.isKeyboardActive } }
    val imeHeight = rememberImeHeightPxWithoutNav()

    DisposableEffectWithLifecycle(
        onPause = {
            Logger.d("JournalSuniaDrawBoard", "onPause - saving edit state")
            // 保存当前编辑状态
            val currentLayout = textAndDrawBoardLayout
            if (currentLayout != null) {
                val isKeyboardActiveFromController = currentLayout.isKeyboardActive()
                val hasFocus = currentLayout.hasRichTextFocus()
                // 使用MenuBarViewModel的状态和IME高度来更准确地判断键盘可见性
                val isKeyboardVisible = isKeyboardActive && hasFocus && imeHeight > 0
                val cursorPosition = currentLayout.getRichTextCursorPosition()
                textAndDrawViewModel.saveEditState(isKeyboardActiveFromController, hasFocus, isKeyboardVisible, cursorPosition)
                Logger.d("SuniaDrawBoard", "Saved edit state: keyboardActive=$isKeyboardActiveFromController, focus=$hasFocus, keyboardVisible=$isKeyboardVisible, imeHeight=$imeHeight, cursor=$cursorPosition")
            }
            focusManager.clearFocus()
        },
        onResume = {
            Logger.d("SuniaDrawBoard", "onResume - checking for edit state restoration")
            // 检查是否需要恢复编辑状态
            if (textAndDrawViewModel.shouldRestoreEditState()) {
                val savedState = textAndDrawViewModel.getSavedEditState()
                Logger.d("SuniaDrawBoard", "Restoring edit state: $savedState")

                // 延迟恢复，确保UI完全加载
                textAndDrawBoardLayout?.postDelayed({
                    try {
                        if (savedState != null && savedState.editMode == EditMode.TEXT) {
                            // 恢复文本编辑模式
                            textAndDrawViewModel.editMode = EditMode.TEXT

                            // 如果需要显示键盘，则切换到文本编辑模式
                            if (savedState.isKeyboardVisible) {
                                menuBarViewModel.switchToTextEditMode()
                            }

                            textAndDrawBoardLayout?.restoreEditState(savedState.cursorPosition, savedState.isKeyboardVisible)
                            Logger.d("SuniaDrawBoard", "Edit state restored successfully: keyboardVisible=${savedState.isKeyboardVisible}")
                        }
                    } catch (e: Exception) {
                        Logger.e("SuniaDrawBoard", "Failed to restore edit state: ${e.message}")
                    } finally {
                        // 清除保存的状态
                        textAndDrawViewModel.clearSavedEditState()
                    }
                }, 300) // 延迟300ms确保UI稳定
            }
        },
        onStop = {
            // 退出到后台，强制保存数据
//            NoteDataSaveController.forceSave()
        }
    )

    // 添加DisposableEffect来处理清理
    DisposableEffect(Unit) {
        Logger.d("EditContent", "EditContent composition started")
        onDispose {
            Logger.d("EditContent", "EditContent disposing, starting comprehensive cleanup")
            try {
                // 清理RichTextEventManager中的事件状态
                RichTextEventManager.clearStyleEvent()
                RichTextEventManager.restoreToolBarStyleState()

                Logger.d("EditContent", "EditContent cleanup completed successfully")

            } catch (e: Exception) {
                Logger.e("EditContent", "Error during EditContent cleanup: ${e.message}")
            }
        }
    }

    LaunchedEffect(isDarkTheme) {
        /*if (isDarkTheme != lastIsDarkTheme) {
            lastIsDarkTheme = isDarkTheme
            textAndDrawBoardLayout?.changeDarkTheme(isDarkTheme)
        }*/
    }

    Logger.d("TextAndDrawBoard", "compose init")

    val toolBarHeightPx = with(LocalDensity.current) { getGlobalDimens().richTextToolBarHeight.roundToPx() }

    AndroidView(
        factory = { context ->
            JournalDrawBoardLayout(
                context = context,
                richTextViewModel = richTextViewModel,
                suniaDrawViewModel = suniaDrawViewModel,
                textAndDrawViewModel = textAndDrawViewModel,
                menuBarViewModel = menuBarViewModel
            ).also { textAndDrawBoardLayout = it }
        },
        modifier = modifier
            .padding(
                bottom = 0.dp
            ),
        update = { view ->
            Logger.d(
                "SuniaDrawBoard",
                "AndroidView[TextAndDrawBoardLayout] update! isKeyboardActive: $isKeyboardActive"
            )
            view.setupRichTextBottomPadding(
                bottomMargin = if (isKeyboardActive) imeHeight else 0,
                bottomMenuBarHeight = if (isKeyboardActive) {
                    if (isTablet) toolBarHeightPx else 0
                } else 0,
            )
        }
    )
}