<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="120dp"
    android:height="120dp"
    android:viewportWidth="120"
    android:viewportHeight="120">
  <group>
    <clip-path
        android:pathData="M0,0h120v120h-120z"/>
    <path
        android:pathData="M0,0h120v120h-120z"
        android:fillColor="#D8D8D8"
        android:fillAlpha="0"/>
    <path
        android:pathData="M38,84C38,88.42 41.58,92 46,92L86,92C90.42,92 94,88.42 94,84L94,36C94,31.58 90.42,28 86,28L46,28C41.58,28 38,31.58 38,36L38,84Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="66"
            android:startY="28"
            android:endX="66"
            android:endY="92"
            android:type="linear">
          <item android:offset="0" android:color="#FF242424"/>
          <item android:offset="1" android:color="#FF1E1E1E"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M38,84C38,88.42 41.58,92 46,92L86,92C90.42,92 94,88.42 94,84L94,36C94,31.58 90.42,28 86,28L46,28C41.58,28 38,31.58 38,36L38,84ZM39,84Q39,86.9 41.05,88.95Q43.1,91 46,91L86,91Q88.9,91 90.95,88.95Q93,86.9 93,84L93,36Q93,33.1 90.95,31.05Q88.9,29 86,29L46,29Q43.1,29 41.05,31.05Q39,33.1 39,36L39,84Z"
        android:fillColor="#000000"
        android:fillAlpha="0.02"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M49,40L69,40A8,8 0,0 1,77 48L77,76A8,8 0,0 1,69 84L49,84A8,8 0,0 1,41 76L41,48A8,8 0,0 1,49 40z"
        android:fillColor="#CFCFCF"
        android:fillAlpha="0.35"/>
    <path
        android:pathData="M30,92C30,96.42 33.58,100 38,100L82,100C86.42,100 90,96.42 90,92L90,28C90,23.58 86.42,20 82,20L38,20C33.58,20 30,23.58 30,28L30,92Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="32.74"
            android:startY="96.93"
            android:endX="68.88"
            android:endY="8.54"
            android:type="linear">
          <item android:offset="0" android:color="#68525252"/>
          <item android:offset="1" android:color="#33808080"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M30,92C30,96.42 33.58,100 38,100L82,100C86.42,100 90,96.42 90,92L90,28C90,23.58 86.42,20 82,20L38,20C33.58,20 30,23.58 30,28L30,92ZM31,92Q31,94.9 33.05,96.95Q35.1,99 38,99L82,99Q84.9,99 86.95,96.95Q89,94.9 89,92L89,28Q89,25.1 86.95,23.05Q84.9,21 82,21L38,21Q35.1,21 33.05,23.05Q31,25.1 31,28L31,92Z"
        android:fillColor="#343434"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M75.77,47.02Q73.87,47.58 54.52,53.05Q49.16,54.81 45.2,50.08L49.6,48.29C50.36,48.55 51.07,48.52 52.02,48.23L57.02,47.02L49.52,36.15Q48,33.8 53.26,34.95Q62.8,42.08 64.51,43.39Q65.39,43.88 67.02,43.39L74.52,40.98Q79.6,39.46 80.77,42.19C82,45.04 77.67,46.45 75.77,47.02ZM44.35,49.21Q42.39,47.07 42.02,45.81Q42.42,43.19 45.77,45.81C46.74,46.57 47.78,47.1 48.44,47.53L44.35,49.21Z"
        android:fillColor="#FFFFFF"
        android:fillAlpha="0.5"/>
    <path
        android:pathData="M41,85L41,69C41,67.9 41.9,67 43,67L59,67C60.1,67 61,67.9 61,69L61,85C61,86.1 60.1,87 59,87L43,87C41.9,87 41,86.1 41,85ZM65,70.5C65,69.67 65.67,69 66.5,69L75.5,69C76.33,69 77,69.67 77,70.5C77,71.33 76.33,72 75.5,72L66.5,72C65.67,72 65,71.33 65,70.5ZM65,77.42C65,76.64 65.64,76 66.42,76L81.58,76C82.36,76 83,76.64 83,77.42C83,78.21 82.36,78.84 81.58,78.84L66.42,78.84C65.64,78.84 65,78.21 65,77.42ZM65,84.42C65,83.64 65.64,83 66.42,83L81.58,83C82.36,83 83,83.64 83,84.42C83,85.21 82.36,85.84 81.58,85.84L66.42,85.84C65.64,85.84 65,85.21 65,84.42Z"
        android:strokeAlpha="0.3"
        android:fillColor="#FFFBF4"
        android:fillAlpha="0.3"/>
  </group>
</vector>
