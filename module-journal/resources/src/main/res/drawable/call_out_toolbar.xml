<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="60dp"
    android:height="60dp"
    android:viewportWidth="60"
    android:viewportHeight="60">
  <path
      android:pathData="M0,19a19,19 0,1 0,38 0a19,19 0,1 0,-38 0z"
      android:strokeAlpha="0.2"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.2"/>
  <path
      android:pathData="M6,19a13,13 0,1 0,26 0a13,13 0,1 0,-26 0z"
      android:strokeAlpha="0.2"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.2"/>
  <group>
    <clip-path
        android:pathData="M17.693,16.136C14.598,17.495 16.192,21.182 18.913,24.481C21.633,27.78 30.639,40.88 30.263,41.074C30.1,41.159 29.437,40.303 28.594,39.215C27.503,37.806 26.11,36.006 25.104,35.349C23.322,34.185 17.881,34.476 21.539,42.141C25.198,49.807 39.086,59.065 45.741,59.899C52.397,60.733 60,56.366 60,48.837C60,41.307 58.218,32.438 50.995,23.996C47.242,20.212 43.49,22.638 42.927,23.317C40.582,22.153 37.393,21.861 34.578,24.481C33.922,23.996 29.701,23.996 28.575,25.161C28.438,25.006 28.2,24.71 27.884,24.315C25.815,21.736 20.378,14.958 17.693,16.136Z"/>
    <path
        android:pathData="M17.693,16.136C14.598,17.495 16.192,21.182 18.913,24.481C21.633,27.78 30.639,40.88 30.263,41.074C30.1,41.159 29.437,40.303 28.594,39.215C27.503,37.806 26.11,36.006 25.104,35.349C23.322,34.185 17.881,34.476 21.539,42.141C25.198,49.807 39.086,59.065 45.741,59.899C52.397,60.733 60,56.366 60,48.837C60,41.307 58.218,32.438 50.995,23.996C47.242,20.212 43.49,22.638 42.927,23.317C40.582,22.153 37.393,21.861 34.578,24.481C33.922,23.996 29.701,23.996 28.575,25.161C28.438,25.006 28.2,24.71 27.884,24.315C25.815,21.736 20.378,14.958 17.693,16.136Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="38"
            android:startY="16"
            android:endX="38"
            android:endY="60"
            android:type="linear">
          <item android:offset="0" android:color="#FFE2E2E2"/>
          <item android:offset="1" android:color="#B2C4C4C4"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <group>
    <clip-path
        android:pathData="M17.693,16.136C14.598,17.495 16.192,21.182 18.913,24.481C21.633,27.78 30.639,40.88 30.263,41.074C30.1,41.159 29.437,40.303 28.594,39.215C27.503,37.806 26.11,36.006 25.104,35.349C23.322,34.185 17.881,34.476 21.539,42.141C25.198,49.807 39.086,59.065 45.741,59.899C52.397,60.733 60,56.366 60,48.837C60,41.307 58.218,32.438 50.995,23.996C47.242,20.212 43.49,22.638 42.927,23.317C40.582,22.153 37.393,21.861 34.578,24.481C33.922,23.996 29.701,23.996 28.575,25.161C28.438,25.006 28.2,24.71 27.884,24.315C25.815,21.736 20.378,14.958 17.693,16.136Z"/>
    <path
        android:pathData="M17.693,16.136C14.598,17.495 16.192,21.182 18.913,24.481C21.633,27.78 30.639,40.88 30.263,41.074C30.1,41.159 29.437,40.303 28.594,39.215C27.503,37.806 26.11,36.006 25.104,35.349C23.322,34.185 17.881,34.476 21.539,42.141C25.198,49.807 39.086,59.065 45.741,59.899C52.397,60.733 60,56.366 60,48.837C60,41.307 58.218,32.438 50.995,23.996C47.242,20.212 43.49,22.638 42.927,23.317C40.582,22.153 37.393,21.861 34.578,24.481C33.922,23.996 29.701,23.996 28.575,25.161C28.438,25.006 28.2,24.71 27.884,24.315C25.815,21.736 20.378,14.958 17.693,16.136Z"
        android:fillAlpha="0.9">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:centerX="32.941"
            android:centerY="14.69"
            android:gradientRadius="49.957"
            android:type="radial">
          <item android:offset="0" android:color="#99FFFFFF"/>
          <item android:offset="1" android:color="#FF969696"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M36.398,33.957Q34.617,33.343 31.832,30.239Q29.095,27.188 24.87,21.506L25.482,20.894Q29.22,26.356 32.39,29.737Q35.046,32.697 36.642,33.248L36.398,33.957Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="28.184"
            android:startY="25.588"
            android:endX="35.533"
            android:endY="34.381"
            android:type="linear">
          <item android:offset="0.2" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M41.959,31.158Q40.818,30.956 39.546,29.974Q39.045,29.588 38.426,29.008Q38.057,28.662 37.307,27.919Q36.475,27.095 36.039,26.689Q35.314,26.014 34.69,25.531Q33.126,24.322 31.599,23.871L31.812,23.151Q33.474,23.642 35.149,24.937Q35.801,25.442 36.551,26.14Q36.995,26.554 37.835,27.386Q38.577,28.121 38.939,28.46Q39.532,29.015 40.005,29.38Q41.131,30.249 42.09,30.419L41.959,31.158Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="34.642"
            android:startY="24.669"
            android:endX="43.472"
            android:endY="29.086"
            android:type="linear">
          <item android:offset="0.2" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M48.447,28.059Q47.41,28.059 46.144,27.096Q45.652,26.721 45.022,26.128Q44.645,25.773 43.863,24.992Q43.005,24.135 42.553,23.709Q41.798,22.998 41.153,22.49Q39.53,21.212 38.022,20.766L38.235,20.046Q39.881,20.533 41.617,21.9Q42.289,22.429 43.068,23.163Q43.528,23.596 44.394,24.461Q45.168,25.234 45.537,25.581Q46.138,26.148 46.599,26.498Q47.663,27.308 48.447,27.308L48.447,28.059Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="42.414"
            android:startY="23.533"
            android:endX="49.099"
            android:endY="26.722"
            android:type="linear">
          <item android:offset="0.2" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#00FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M28.096,24.579C28.03,24.497 27.959,24.409 27.884,24.315C25.815,21.736 20.378,14.958 17.693,16.136C14.598,17.495 16.192,21.182 18.913,24.481C20.683,26.628 25.115,32.925 27.891,37.115C28.823,38.522 29.568,39.691 29.964,40.386C30.201,40.802 30.312,41.049 30.263,41.074C30.182,41.116 29.978,40.926 29.69,40.591C29.397,40.252 29.019,39.763 28.594,39.215C28.187,38.688 27.737,38.107 27.281,37.552C26.516,36.621 25.734,35.761 25.104,35.349C23.322,34.185 17.881,34.476 21.539,42.141C25.198,49.807 39.086,59.065 45.741,59.899C52.397,60.733 60,56.366 60,48.837C60,41.307 58.218,32.438 50.995,23.996C47.242,20.212 43.49,22.638 42.927,23.317C40.582,22.153 37.393,21.861 34.578,24.481C33.951,24.017 30.063,23.997 28.74,25.014C28.679,25.061 28.624,25.11 28.575,25.161C28.47,25.043 28.307,24.842 28.096,24.579ZM50.443,24.505Q49.275,23.334 47.994,22.91Q46.953,22.566 45.875,22.725Q45.021,22.851 44.254,23.265Q43.962,23.422 43.734,23.594Q43.567,23.721 43.505,23.796L43.126,24.253L42.593,23.989Q40.787,23.092 38.985,23.228Q36.852,23.39 35.09,25.031L34.634,25.455L34.152,25.099Q34.147,25.097 34.141,25.095Q34.088,25.074 34,25.052Q33.78,24.999 33.448,24.966Q32.698,24.892 31.802,24.951Q30.826,25.016 30.096,25.218Q29.368,25.42 29.114,25.682L28.551,26.265L28.013,25.658Q27.864,25.49 27.298,24.785Q26.062,23.243 25.227,22.273Q23.748,20.553 22.501,19.359Q20.936,17.861 19.763,17.211Q18.592,16.561 17.995,16.823Q17.155,17.192 16.889,17.836Q16.631,18.46 16.853,19.41Q17.081,20.384 17.788,21.599Q18.458,22.75 19.492,24.004Q20.485,25.208 22.496,28Q24.348,30.572 26.368,33.518Q28.416,36.505 29.663,38.46Q30.324,39.495 30.649,40.073Q30.833,40.401 30.917,40.597Q30.983,40.75 31.007,40.872Q31.037,41.021 31.017,41.163Q30.995,41.314 30.919,41.445Q30.806,41.638 30.608,41.741Q30.097,42.005 29.56,41.539Q29.38,41.383 29.114,41.073Q28.81,40.72 28.001,39.674Q26.846,38.183 26.249,37.49Q25.269,36.353 24.694,35.977Q24.449,35.818 24.084,35.714Q23.687,35.602 23.263,35.591Q22.323,35.569 21.745,36.024Q21.077,36.549 21.052,37.699Q21.018,39.306 22.217,41.818Q23.478,44.461 26.464,47.507Q29.238,50.337 32.951,52.97Q36.525,55.505 39.992,57.174Q43.496,58.861 45.835,59.154Q48.194,59.45 50.596,58.853Q53.001,58.255 54.934,56.891Q56.94,55.475 58.054,53.492Q59.25,51.361 59.25,48.837Q59.25,42.482 57.491,36.987Q55.326,30.22 50.443,24.505Z"
        android:fillType="evenOdd">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="38"
            android:startY="16"
            android:endX="38"
            android:endY="60"
            android:type="linear">
          <item android:offset="0" android:color="#FFFFFFFF"/>
          <item android:offset="1" android:color="#19FFFFFF"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
  <path
      android:pathData="M12,19a7,7 0,1 0,14 0a7,7 0,1 0,-14 0z"
      android:strokeAlpha="0.5"
      android:fillColor="#FFFFFF"
      android:fillAlpha="0.5"/>
  <path
      android:pathData="M0,0h60v60h-60z"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"/>
</vector>
