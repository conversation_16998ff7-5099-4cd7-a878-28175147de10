package com.tcl.ai.note.inspiration.core.analysis.similarity.mediapipe

import android.content.Context
import android.graphics.Bitmap
import com.google.mediapipe.framework.image.BitmapImageBuilder
import com.google.mediapipe.tasks.components.containers.Embedding
import com.google.mediapipe.tasks.core.BaseOptions
import com.google.mediapipe.tasks.core.Delegate
import com.google.mediapipe.tasks.vision.imageembedder.ImageEmbedder
import com.google.mediapipe.tasks.vision.imageembedder.ImageEmbedder.ImageEmbedderOptions
import com.tcl.ai.note.inspiration.core.analysis.similarity.ISimilarity
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

object MobileNetImpl : ISimilarity {
    private const val TAG = "MobileNetImpl"
    private const val MODEL_SMALL_PATH = "mobilenet_v3_small.tflite"

    private var imageEmbedder: ImageEmbedder? = null

    suspend fun init(context: Context) = withContext(Dispatchers.IO) {
        imageEmbedder = try {
            ImageEmbedder.createFromOptions(
                context, ImageEmbedderOptions.builder().setBaseOptions(
                    BaseOptions.builder().setModelAssetPath(MODEL_SMALL_PATH)
                        .setDelegate(Delegate.GPU).build()
                ).build()
            )
        } catch (e: IllegalStateException) {
            Logger.e(
                TAG, "Image embedder failed to load model with error: " + e.message
            )
            null
        } catch (e: RuntimeException) {
            Logger.e(
                TAG, "GPU_ERROR , Image embedder failed to load model with error: " + e.message
            )
            null
        }
    }

    override suspend fun similarityImage(
        firstBitmap: Bitmap, secondBitmap: Bitmap
    ): Double = withContext(Dispatchers.Default) {
        if (imageEmbedder == null) {
            Logger.e(TAG, "not init or init fail, return")
            return@withContext 0.0
        }
        val firstMpImage = BitmapImageBuilder(firstBitmap).build()
        val secondMpImage = BitmapImageBuilder(secondBitmap).build()
        val similarity = imageEmbedder?.let {
            val firstEmbed = it.embed(firstMpImage).embeddingResult().embeddings().first()
            val secondEmbed = it.embed(secondMpImage).embeddingResult().embeddings().first()
            ImageEmbedder.cosineSimilarity(firstEmbed, secondEmbed)
        } ?: 0.0
        similarity
    }

    suspend fun extractFeature(bitmap: Bitmap): Embedding? = withContext(Dispatchers.Default) {
        if (imageEmbedder == null) {
            Logger.e(TAG, "not init or init fail, return")
            return@withContext null
        }
        val mpImage = BitmapImageBuilder(bitmap).build()
        val embed = imageEmbedder?.embed(mpImage)
        embed?.embeddingResult()?.embeddings()?.first()
    }

    suspend fun similarityImage(firstEmbedding: Embedding, secondEmbedding: Embedding): Double =
        withContext(Dispatchers.Default) {
            if (imageEmbedder == null) {
                Logger.e(TAG, "not init or init fail, return")
                return@withContext 0.0
            }
            ImageEmbedder.cosineSimilarity(firstEmbedding, secondEmbedding)
        }

    fun release() {
        imageEmbedder?.close()
        imageEmbedder = null
    }
}