package com.tcl.ai.note.inspiration.core

import android.Manifest
import android.content.Context
import com.google.mediapipe.tasks.components.containers.Embedding
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.database.entity.Album
import com.tcl.ai.note.database.entity.Image
import com.tcl.ai.note.database.entity.ImageEmbedding
import com.tcl.ai.note.database.repository.InspirationRepository
import com.tcl.ai.note.inspiration.core.analysis.ImageMarking
import com.tcl.ai.note.inspiration.core.analysis.facedetect.mlkit.MlKitFaceDetector
import com.tcl.ai.note.inspiration.core.analysis.quality.opencv.OpenCVImpl
import com.tcl.ai.note.inspiration.core.analysis.similarity.mediapipe.MobileNetImpl
import com.tcl.ai.note.inspiration.core.analysis.sysinfo.exif.ExifImpl
import com.tcl.ai.note.inspiration.core.imagemanage.ImageGrouping
import com.tcl.ai.note.inspiration.core.imagemanage.ImageHelper
import com.tcl.ai.note.inspiration.core.imagemanage.formatTime
import com.tcl.ai.note.inspiration.core.imagemanage.getTimePeriod
import com.tcl.ai.note.inspiration.utils.checkPermission
import com.tcl.ai.note.inspiration.utils.decodeBitmapSafety
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.util.Optional
import java.util.UUID
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

class ImageAnalysisManager private constructor() {
    companion object {
        private const val TAG = "ImageAnalysisManager"

        val instance: ImageAnalysisManager by lazy {
            ImageAnalysisManager()
        }
    }

    private val threadPool: ExecutorService = Executors.newFixedThreadPool(5)
    private val analysisDispatcher: CoroutineDispatcher = threadPool.asCoroutineDispatcher()

    private val context: Context = GlobalContext.instance

    private val marker: ImageMarking

    private var inspirationRepository = InspirationRepository()

    private val imageHelper = ImageHelper(inspirationRepository)

    init {
        OpenCVImpl.init()
        marker = ImageMarking.Builder()
            .sysInfo(ExifImpl)
            .faceDetector(MlKitFaceDetector())
            .appraiser(OpenCVImpl)
            .build()
    }

    fun release() {
        threadPool.shutdownNow()
    }

    suspend fun analysisImages(onState: ((ImageAnalysisState) -> Unit)? = null) {
        if (!checkPermission(Manifest.permission.READ_MEDIA_IMAGES)) {
            return
        }
        var progress = 0
        try {
            // 获取照片
            onState?.invoke(ImageAnalysisState.Analyzing(progress))
            val unAnalyzedImages = imageHelper.getUnAnalyzedImages(context)
            Logger.i(
                TAG,
                "analysisImages --> load images done, unAnalyzedImages: ${unAnalyzedImages.size}"
            )
            progress = 1
            onState?.invoke(ImageAnalysisState.Analyzing(progress))
            if (unAnalyzedImages.isNotEmpty()) {
                // 分析信息
                val batchSize = 20

                // 分块images，每块最大20张
                val batches = unAnalyzedImages.chunked(batchSize)

                withContext(analysisDispatcher) {
                    batches.forEachIndexed { index, batch ->
                        coroutineScope {
                            val markedImages = batch.map { img ->
                                async {
                                    marker.markImage(img)
                                    img
                                }
                            }.awaitAll()

                            // 批量保存
                            inspirationRepository.saveImages(markedImages)
                        }
                        // 统计进度
                        progress = 1 + (32 * (index * 1.0f / batches.size)).toInt()
                        onState?.invoke(ImageAnalysisState.Analyzing(progress))
                    }
                }
            }
            Logger.i(TAG, "analysisImages --> mark image done")

            progress = 33
            onState?.invoke(ImageAnalysisState.Analyzing(progress))

            val markedImages = imageHelper.getMarkedImages()

            // 二次位置校验
            delay(500)

            Logger.i(TAG, "analysisImages --> start grouping")
            // 分组
            val groupImages = ImageGrouping.groupImages(markedImages, Dispatchers.Default)
            Logger.e(TAG, "analysisImages --> finish grouping, group size: ${groupImages.size}")

            Logger.i(TAG, "analysisImages --> begin parse similar")
            MobileNetImpl.init(context)
            // 提取特征
            val groupImagesFlatten = groupImages.flatten()
            val imageEmbeddings = inspirationRepository.queryImageEmbedding()
            // 构造 imageId -> Embedding 的索引
            val embeddingMap = imageEmbeddings.associateBy { it.imageId }
            // 合成 image -> embedding?
            val featureMap: MutableMap<Image, Embedding?> = groupImagesFlatten.associateWith { image ->
                val imageEmbedding = embeddingMap[image.imageId]
                imageEmbedding?.let {
                    Embedding.create(it.floatEmbedding, it.quantizedEmbedding, it.headIndex, Optional.of(it.headName))
                }
            }.toMutableMap()
            val unImageEmbeddings = groupImagesFlatten.filter { !embeddingMap.contains(it.imageId) }
            val total = unImageEmbeddings.size
            var index = 0
            unImageEmbeddings.forEach { image ->
//                images.forEach { image ->
                    val bitmap = image.decodeBitmapSafety()
                    bitmap?.let { bmp ->
                        featureMap[image] = MobileNetImpl.extractFeature(bitmap)
                        bmp.recycle()
                    } ?: run {
                        featureMap[image] = null
                    }
                    index++
                    progress = 33 + (64 * (index * 1.0f / total)).toInt()
                    onState?.invoke(ImageAnalysisState.Analyzing(progress))
//                }
            }
            val list: List<ImageEmbedding> = featureMap.map { (img, emb) ->
                ImageEmbedding(
                    imageId = img.imageId,
                    floatEmbedding = emb?.floatEmbedding() ?: FloatArray(0),
                    quantizedEmbedding = emb?.quantizedEmbedding() ?: ByteArray(0),
                    headIndex = emb?.headIndex() ?: 0,
                    headName = emb?.headName()?.orElse("") ?: ""
                )
            }
            inspirationRepository.saveImageEmbeddings(list)
            Logger.i(TAG, "analysisImages --> extractFeature done")
            progress = 97
            onState?.invoke(ImageAnalysisState.Analyzing(progress))

            val groups = groupImages
                .map { images ->
                    val sorted = images.sortedByDescending { it.score } // 按时间排序
                    val remaining = sorted.toMutableList()
                    val result = mutableListOf<Image>()
                    while (remaining.isNotEmpty()) {
                        val current = remaining.removeAt(0)
                        // 如果当前 image 特征为 null，则跳过
                        if (featureMap[current] == null) {
                            continue
                        }
                        result.add(current)

                        // 遍历剩余图片，移除与当前相似的
                        val iterator = remaining.iterator()
                        while (iterator.hasNext()) {
                            val other = iterator.next()
                            if (featureMap[other] == null) {
                                continue
                            }
                            if (MobileNetImpl.similarityImage(
                                    featureMap[current]!!,
                                    featureMap[other]!!
                                ) > 0.55
                            ) {
                                iterator.remove()
                            }
                        }
                    }
                    Logger.i(TAG, "analysisImages --> finish parse similar")
                    result.take(20)
                }
            MobileNetImpl.release()
            Logger.i(TAG, "analysisImages --> end parse similar")

            progress = 99
            onState?.invoke(ImageAnalysisState.Analyzing(progress))
            // 分组命名
            val albums = mutableListOf<Album>()
            groups.forEach { images ->
                val uuid = UUID.randomUUID().toString()
                val date = formatTime(images.first().createTime)
                val timeSlot = getTimePeriod(images.first().createTime)
                val img = images.firstOrNull { it.location != null }
                val location = img?.location
                val album = Album(
                    albumId = uuid,
                    timestamp = images.first().createTime,
                    date = date,
                    timeSlot = timeSlot,
                    location = location,
                    size = images.size,
                    albumName = if (location.isNullOrEmpty()) {
                        String.format(
                            GlobalContext.instance.getString(R.string.format_album_name),
                            date,
                            timeSlot
                        )
                    } else {
                        String.format(
                            GlobalContext.instance.getString(R.string.format_album_name_with_location),
                            date,
                            location,
                            timeSlot
                        )
                    }
                )
                if (images.isNotEmpty()) {
                    albums.add(album)
                    images.forEach {
                        it.albumId = uuid
                    }
                    inspirationRepository.updateImages(images)
                }
            }
            inspirationRepository.clearAndSaveAlbums(albums)
            progress = 100
            onState?.invoke(ImageAnalysisState.Analyzing(progress))
            Logger.e(TAG, "analysisImages --> success")
            onState?.invoke(ImageAnalysisState.Success)
        } catch (ex: Exception) {
            ex.printStackTrace()
            Logger.e(TAG, "analysisImages --> failure: ${ex.message}")
            onState?.invoke(ImageAnalysisState.Failure(progress))
        }
    }
}