package com.tcl.ai.note.inspiration.core.analysis.facedetect.mlkit

import android.graphics.Bitmap
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetector
import com.google.mlkit.vision.face.FaceDetectorOptions
import com.tcl.ai.note.inspiration.core.analysis.facedetect.IFaceDetect
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

class MlKitFaceDetector: IFaceDetect {

    private var detector: FaceDetector

    init {
        // High-accuracy landmark detection and face classification
        val highAccuracyOpts = FaceDetectorOptions.Builder()
            .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
            .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_NONE)
            .setContourMode(FaceDetectorOptions.CONTOUR_MODE_NONE)
            .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_NONE)
            .build()

        // Real-time contour detection
        // val realTimeOpts =
        //     FaceDetectorOptions.Builder().setContourMode(FaceDetectorOptions.CONTOUR_MODE_ALL)
        //         .build()

        detector = FaceDetection.getClient(highAccuracyOpts)
    }

    override suspend fun detectFace(bitmap: Bitmap): Boolean {
        return suspendCoroutine<Boolean> { c ->
            val image = InputImage.fromBitmap(bitmap, 0)
            detector.process(image).addOnSuccessListener { faces ->
                c.resume(faces.isNotEmpty())
            }.addOnFailureListener { ex ->
                c.resumeWithException(ex)
            }
        }
    }
}