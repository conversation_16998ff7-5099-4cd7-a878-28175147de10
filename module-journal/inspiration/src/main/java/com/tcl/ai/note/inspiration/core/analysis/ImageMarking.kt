package com.tcl.ai.note.inspiration.core.analysis

import com.tcl.ai.note.database.entity.Image
import com.tcl.ai.note.inspiration.core.analysis.facedetect.IFaceDetect
import com.tcl.ai.note.inspiration.core.analysis.quality.IAppraise
import com.tcl.ai.note.inspiration.core.analysis.sysinfo.ISysInfo
import com.tcl.ai.note.inspiration.utils.decodeBitmapSafety
import com.tcl.ai.note.utils.Logger

class ImageMarking private constructor() {

    companion object {
        private const val TAG = "ImageMarking"
    }

    private lateinit var sysInfo: ISysInfo
    private lateinit var appraiser: IAppraise
    private lateinit var faceDetector: IFaceDetect

    class Builder {

        private lateinit var sysInfo: ISysInfo
        private lateinit var appraiser: IAppraise
        private lateinit var faceDetector: IFaceDetect

        fun sysInfo(sysInfo: ISysInfo): Builder = apply {
            this.sysInfo = sysInfo
        }

        fun appraiser(appraiser: IAppraise): Builder = apply {
            this.appraiser = appraiser
        }

        fun faceDetector(faceDetector: IFaceDetect): Builder = apply {
            this.faceDetector = faceDetector
        }

        fun build(): ImageMarking {
            return ImageMarking().also {
                it.sysInfo = this.sysInfo
                it.appraiser = this.appraiser
                it.faceDetector = this.faceDetector
            }
        }
    }

    suspend fun markImage(image: Image) {
        Logger.i(TAG, "markImage")
        sysInfo.parseTime(image)
        sysInfo.parseLocation(image)
        val abitmap = image.decodeBitmapSafety()
        abitmap?.let { bitmap ->
            val containFace = faceDetector.detectFace(bitmap)
            val qualityScore = appraiser.appraiseImage(bitmap)
            var finalScore = qualityScore
            if (containFace) {
                finalScore += 10.0
            }
            if (image.userOperator) {
                finalScore += 10.0
            }
            with(image) {
                faceDetect = containFace
                quality = qualityScore
                score = finalScore
                marked = true
            }
            bitmap.recycle()
        }
    }
}