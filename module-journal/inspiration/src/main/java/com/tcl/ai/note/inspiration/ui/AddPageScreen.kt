package com.tcl.ai.note.inspiration.ui

import androidx.activity.compose.BackHandler
import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBackIosNew
import androidx.compose.material.icons.filled.ArrowForwardIos
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.focusProperties
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.database.entity.Album
import com.tcl.ai.note.inspiration.viewmodel.AddPageViewModel
import com.tcl.ai.note.inspiration.viewmodel.AlbumAnalysisState
import com.tcl.ai.note.inspiration.viewmodel.AlbumType
import com.tcl.ai.note.inspiration.viewmodel.type
import com.tcl.ai.note.journalbase.launchAppDetailsScreen
import com.tcl.ai.note.picturetotext.AiWritingAssistantScreen
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.template.bean.DestinationType
import com.tcl.ai.note.template.ui.TemplateMainScreen
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge
import com.tct.theme.core.designsystem.component.TclButton
import com.tct.theme.core.designsystem.component.TclLinearProgressIndicator
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

@Composable
fun AddPageScreen(
    navController: NavController,
    journalId: Long,
    journalTitle: String,
    coverId: Long,
    viewModel: AddPageViewModel = hiltViewModel(),
    onBack: () -> Unit = {
        navController.navigateUp()
    }
) {
    val uiState = viewModel.uiState.collectAsState().value
    // 显示选择图片弹窗
    var showSelectImageDialog by rememberSaveable { mutableStateOf(false) }
    // 显示AI帮写弹窗
    var showAiWritingAssistantDialog by rememberSaveable { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()
    val loginHandler = rememberLoginHandler()
    var loginCheckJob: Job? = null
    val lifecycleOwner = LocalLifecycleOwner.current

    // 选择图片/模板弹窗相关参数
    val context = LocalContext.current
    var destination by remember { mutableStateOf<DestinationType>(DestinationType.SelectImage) }
    var selectedImages by remember { mutableStateOf(emptyList<String>()) }
    var showSelectTemplateBack by remember { mutableStateOf(false) }

    DisposableEffect(lifecycleOwner) {
        // 生命周期回调
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                viewModel.initData()
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    LaunchedEffect(uiState.analysisState) {
        if (uiState.analysisState is AlbumAnalysisState.NotStart) {
            return@LaunchedEffect
        }
        val progress = if (uiState.analysisState is AlbumAnalysisState.Analyzing) {
            (uiState.analysisState as AlbumAnalysisState.Analyzing).progress
        } else {
            0
        }
        AppDataStore.putData(DataStoreParam.KEY_ANALYSIS_PROGRESS, progress)
    }

    val scrollState = rememberScrollState()
    val modifier = Modifier
        .background(color = TclTheme.colorScheme.tctGlobalBgColor)
        .fillMaxSize()
    Box(
        modifier = (showSelectImageDialog || showAiWritingAssistantDialog).judge(modifier.focusProperties { canFocus = false }.clearAndSetSemantics {  }, modifier)
    ) {
        BackHandler { onBack() }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(color = TclTheme.colorScheme.tctGlobalBgColor)
                .systemBarsPadding()
        ) {
            TitleBar(backPress = { onBack() }, navigateToAddBlankPage = {})
            if (!uiState.permissionGranted) {
                PermissionDeniedView()
                return@Column
            }
            if (!uiState.existImage) {
                SystemAlbumEmptyView()
                return@Column
            }
            if (uiState.analysisState is AlbumAnalysisState.Failed && viewModel.albumMap.isEmpty()) {
                AlbumAnalysisFailedView {
                    viewModel.analysisAlbums(isFailedRetry = true)
                }
                return@Column
            }

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState),
            ) {
                InspirationSuggestionPanel(uiState.analysisState)
                InspirationAlbumList { inspirationAlbum ->
                    loginCheckJob = coroutineScope.launch {
                        if (AccountController.getLoginState()) {
                            viewModel.selectImages(inspirationAlbum)
                            selectedImages = viewModel.selectedImages.map { it.path }.take(9)
                            showSelectImageDialog = true
                        } else {
                            loginHandler.invoke {  }
                        }
                    }
                }
            }
        }

        DisposableEffect(Unit) {
            onDispose {
                if (loginCheckJob?.isActive == true) {
                    loginCheckJob!!.cancel()
                }
            }
        }
    }

    if (showSelectImageDialog) {
        TemplateMainScreen(
            albumName = viewModel.selectedAlbumName,
            images = viewModel.selectedImages.map { it.path },
            destination = destination,
            selectedImages = selectedImages,
            showSelectTemplateBack = showSelectTemplateBack,
            onTemplateSelected = { selectedImages, template ->
                showSelectImageDialog = false
                viewModel.handleTemplateSelected(
                    template.contentFile,
                    viewModel.selectedImages.filter { selectedImages.contains(it.path) }
                )
                showAiWritingAssistantDialog = true
            },
            onImageItemClick = { clickedItem ->
                if (selectedImages.contains(clickedItem)) {
                    if (selectedImages.size == 1) {
                        ToastUtils.makeWithCancel(context.getString(R.string.msg_picture_select_one))
                    } else {
                        selectedImages = selectedImages - clickedItem
                    }
                } else {
                    if (selectedImages.size >= 9) {
                        ToastUtils.makeWithCancel(
                            String.format(
                                context.getString(R.string.msg_picture_select_max),
                                9
                            )
                        )
                    } else {
                        selectedImages = selectedImages + clickedItem
                    }
                }
            },
            onSelectTemplateBtnClicked = {
                showSelectTemplateBack = true
                destination = DestinationType.SelectTemplate
            },
            onTemplateBackClicked = {
                destination = DestinationType.SelectImage
            },
            backHandler = {
                if (destination == DestinationType.SelectTemplate) {
                    destination = DestinationType.SelectImage
                } else {
                    showSelectImageDialog = false
                }
            },
            onDismissRequest = {
                showSelectImageDialog = false
            }
        )
    }

    if (showAiWritingAssistantDialog) {
        AiWritingAssistantScreen(
            navController = navController,
            journalId = journalId,
            journalTitle = journalTitle,
            coverId = coverId,
            configInfoJson = viewModel.configInfoJson,
            imageGroups = viewModel.imageGroups,
            onDismiss = { isBack ->
                showAiWritingAssistantDialog = false
                if (isBack) {
                    showSelectImageDialog = true
                }
            }
        )
    }
}

@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TitleBar(
    backPress: () -> Unit,
    navigateToAddBlankPage: () -> Unit = {}
) {
    TopAppBar(
        title = {
            Box(modifier = Modifier.offset(x = (-8).dp)) {
                Text(stringResource(R.string.title_add_a_page), maxLines = 1, overflow = TextOverflow.Ellipsis,
                    color = TclTheme.colorScheme.tctStanderTextPrimary,
                    fontSize = isDensity440.judge(22.sp, 20.sp),
                    fontWeight = FontWeight.Medium,
                )
            }
        },
        expandedHeight = isDensity440.judge(70.dp, 56.dp),
        navigationIcon = {
            IconButton(onClick = { backPress.invoke() }) {
                Icon(
                    imageVector = when (LocalLayoutDirection.current) {
                        LayoutDirection.Ltr -> Icons.Filled.ArrowBackIosNew
                        LayoutDirection.Rtl -> Icons.Filled.ArrowForwardIos
                    },
                    contentDescription = stringResource(com.tct.theme.core.designsystem.R.string.back),
                    tint = TclTheme.tclColorScheme.tctStanderTextPrimary
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Transparent,
        ),
        actions = {
            IconButton(onClick = { navigateToAddBlankPage.invoke() }) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_add_blank_page),
                    contentDescription = stringResource(R.string.title_add_blank_page)
                )
            }
        },
    )

    /*TclBackIconButtonTopAppBar(
        title = stringResource(R.string.title_add_a_page),
        onBackIconClick = { backPress.invoke() }
    ) {
        IconButton(onClick = { navigateToAddBlankPage.invoke() }) {
            Icon(
                painter = painterResource(id = R.drawable.ic_add_blank_page),
                contentDescription = stringResource(R.string.title_add_blank_page)
            )
        }
    }*/
}

@Composable
private fun InspirationSuggestionPanel(analysisState: AlbumAnalysisState) {
    val viewModel = viewModel<AddPageViewModel>()
    Column(modifier = Modifier
        .padding(horizontal = 24.dp)
        .padding(top = 9.dp, bottom = 12.dp)) {
        Text(
            text = stringResource(R.string.inspiration_suggestion_title),
            color = TclTheme.colorScheme.tctStanderTextPrimary,
            fontSize = isDensity440.judge(17.sp, 16.sp),
            fontWeight = FontWeight.Normal,
            lineHeight = isDensity440.judge(26.sp, 24.sp)
        )

        Text(
            text = stringResource(R.string.inspiration_suggestion_desc),
            color = TclTheme.colorScheme.tctStanderTextSecondary,
            fontSize = isDensity440.judge(15.sp, 14.sp),
            fontWeight = FontWeight.Normal,
            lineHeight = isDensity440.judge(22.sp, 20.sp),
        )

        when (analysisState) {
            is AlbumAnalysisState.Failed -> {
                AlbumAnalysisFailedPanel(0f) {
                    viewModel.analysisAlbums(isFailedRetry = true)
                }
            }

            is AlbumAnalysisState.Analyzing -> {
                AlbumAnalyzingPanel((analysisState.progress / 100f).coerceAtLeast(0f).coerceAtMost(1f))
            }

            else -> {}
        }
    }
}

@Composable
private fun InspirationAlbumList(onAlbumClick: (inspirationAlbum: Album) -> Unit) {
    val viewModel = viewModel<AddPageViewModel>()
    val albumsList = viewModel.albumMap.keys.sortedByDescending { it.timestamp }.toList()
    repeat(albumsList.size) { index ->
        val inspirationAlbum = albumsList[index]
        val type = inspirationAlbum.type()
        val modifier = Modifier.clickable {
            onAlbumClick.invoke(inspirationAlbum)
        }
        when (type) {
            AlbumType.Single -> {
                SingleInspirationAlbumCard(inspirationAlbum, modifier)
            }

            AlbumType.Two -> {
                TwoInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.Three -> {
                ThreeInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.Four -> {
                FourInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.Five -> {
                FiveInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.More -> {
                MoreInspirationAlbumsCard(inspirationAlbum, modifier)
            }

            AlbumType.Empty -> {
            }
        }
    }
    /*LazyColumn(
        modifier = Modifier
            .padding(horizontal = 12.dp)
            .systemBarsPadding()
            .fillMaxWidth()
            .wrapContentHeight(),
        state = listState
    ) {
        items(items = albumsList, key = { inspirationAlbum -> inspirationAlbum.albumId }) { inspirationAlbum ->
            val type = inspirationAlbum.type()
            val modifier = Modifier.clickable {
                onAlbumClick.invoke(inspirationAlbum)
            }
            when (type) {
                AlbumType.Single -> {
                    SingleInspirationAlbumCard(inspirationAlbum, modifier)
                }

                AlbumType.Two -> {
                    TwoInspirationAlbumsCard(inspirationAlbum, modifier)
                }

                AlbumType.Three -> {
                    ThreeInspirationAlbumsCard(inspirationAlbum, modifier)
                }

                AlbumType.Four -> {
                    FourInspirationAlbumsCard(inspirationAlbum, modifier)
                }

                AlbumType.Five -> {
                    FiveInspirationAlbumsCard(inspirationAlbum, modifier)
                }

                AlbumType.More -> {
                    MoreInspirationAlbumsCard(inspirationAlbum, modifier)
                }

                AlbumType.Empty -> {
                }
            }
        }
    }*/
}

@Composable
private fun ColumnScope.AlbumAnalyzingPanel(progress: Float) {
    TclLinearProgressIndicator(
        modifier = Modifier
            .padding(top = 33.dp, bottom = 9.dp)
            .height(6.dp)
            .fillMaxWidth(),
        progress = { progress }
    )
    Text(
        modifier = Modifier.align(Alignment.CenterHorizontally),
        text = "${(progress * 100).toInt()}%",
        color = TclTheme.tclColorScheme.tctStanderAccentPrimary,
        fontSize = 20.sp,
        fontWeight = FontWeight.Normal
    )
    Text(
        modifier = Modifier
            .padding(top = 4.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        text = stringResource(R.string.inspiration_suggestion_tip),
        color = TclTheme.colorScheme.tctStanderTextSecondary,
        fontSize = 14.sp,
        fontWeight = FontWeight.Normal
    )
}

@Composable
private fun ColumnScope.AlbumAnalysisFailedPanel(progress: Float, tryToAnalysisAlbum: () -> Unit) {
    TclLinearProgressIndicator(
        modifier = Modifier
            .padding(top = 33.dp, bottom = 9.dp)
            .height(6.dp)
            .fillMaxWidth(),
        progress = { progress }
    )
    Row(
        modifier = Modifier
            .padding(top = 4.dp, bottom = 9.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(R.string.analysis_failed),
            color = TclTheme.colorScheme.tctStanderTextSecondary,
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal
        )

        Text(
            modifier = Modifier
                .padding(start = 12.dp)
                .semantics { role = Role.Button }
                .clickable { tryToAnalysisAlbum.invoke() },
            text = stringResource(R.string.try_again),
            color = TclTheme.colorScheme.tctStanderTextPrimary,
            fontSize = 14.sp,
            fontWeight = FontWeight(500)
        )
    }
}

@Composable
private fun ColumnScope.PermissionDeniedView() {
    val context = LocalContext.current
    CommonPanel(
        painterResId = R.drawable.ic_permission_denied,
        contentResId = R.string.permission_denied_desc,
        buttonContentResId = R.string.go_to_authorize,
        buttonClick = { launchAppDetailsScreen(context = context, packageName = context.packageName) }
    )
}

@Composable
private fun ColumnScope.SystemAlbumEmptyView() {
    CommonPanel(
        painterResId = R.drawable.ic_empty_album,
        contentResId = R.string.empty_album_desc
    )
}

@Composable
private fun ColumnScope.AlbumAnalysisFailedView(
    buttonClick: () -> Unit
) {
    CommonPanel(
        painterResId = R.drawable.ic_album_analysis_failed,
        contentResId = R.string.album_analysis_failed_desc,
        buttonContentResId = R.string.try_again,
        buttonClick = buttonClick
    )
}

@Composable
private fun ColumnScope.CommonPanel(
    painterResId: Int,
    contentResId: Int,
    buttonContentResId: Int? = null,
    buttonClick: (() -> Unit?)? = null
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .weight(1F),
        contentAlignment = Alignment.Center
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            Image(painter = painterResource(painterResId), contentDescription = null)
            Spacer(Modifier.height(16.dp))
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp),
                text = stringResource(contentResId),
                textAlign = TextAlign.Center,
                color = TclTheme.colorScheme.tctStanderTextSecondary,
                fontWeight = FontWeight.Normal
            )
            if (buttonContentResId != null) {
                Spacer(Modifier.height(48.dp))
                TclButton(
                    onClick = { buttonClick?.invoke() },
                    colors = ButtonColors(
                        containerColor = com.tct.theme.core.designsystem.theme.TclTheme.colorScheme.tctStanderAccentSecondary,
                        disabledContainerColor = com.tct.theme.core.designsystem.theme.TclTheme.colorScheme.tctStanderAccentSecondary,
                        contentColor = com.tct.theme.core.designsystem.theme.TclTheme.colorScheme.tctStanderAccentPrimary,
                        disabledContentColor = com.tct.theme.core.designsystem.theme.TclTheme.colorScheme.tctStanderAccentPrimary
                    )
                ) {
                    Text(text = stringResource(buttonContentResId))
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun AddPageScreenPreview() {
    NoteTclTheme {
        AddPageScreen(navController = rememberNavController(), journalId = 0, journalTitle = "", coverId = 0)
    }
}