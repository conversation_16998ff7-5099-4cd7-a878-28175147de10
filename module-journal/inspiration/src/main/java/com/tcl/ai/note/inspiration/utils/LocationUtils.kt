package com.tcl.ai.note.inspiration.utils

import android.location.Geocoder
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.database.entity.Image
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.IOException
import java.util.Locale

private const val TAG = "LocationUtils"
private val sensitiveWords = listOf("Cemetery", "Graveyard", "Public cemetery", "Funeral", "Crematorium",
    "Columbarium", "Landfill", "Waste", "Scrap", "Sewage", "Wastewater", "Landfill site", "Incineration plant",
    "Chemical industry", "Hospital", "Disease control", "Infectious disease", "Mental illness", "Clinic",
    "Rehabilitation center", "Chemical plant", "Oil refinery", "Nuclear power plant", "Danger", "Prison",
    "Detention", "Drug rehabilitation", "Toilet", "Bathroom")

suspend fun getLocationName(latitude: Double, longitude: Double, locale: Locale): String? =
    withContext(Dispatchers.Default) {
        when {
            latitude == 0.0 && longitude == 0.0 -> null
            else -> {
                try {
                    var geocoder = Geocoder(GlobalContext.appContext, Locale.US)
                    var addresses = geocoder.getFromLocation(latitude, longitude, 1)
                    if (!addresses.isNullOrEmpty()) {
                        var address = addresses[0]
                        var location = address.thoroughfare ?: address.subLocality
                        if (location == null) {
                            return@withContext null
                        }
                        var haveSensitiveWords = false
                        sensitiveWords.forEach { word ->
                            if (location.contains(word)) {
                                haveSensitiveWords = true
                                return@forEach
                            }
                        }
                        if (haveSensitiveWords) {
                            return@withContext null
                        } else {
                            geocoder = Geocoder(GlobalContext.appContext, locale)
                            addresses = geocoder.getFromLocation(latitude, longitude, 1)
                            if (!addresses.isNullOrEmpty()) {
                                address = addresses[0]
                                location = address.thoroughfare ?: address.subLocality
                                Logger.d(TAG, "location: $location")
                                return@withContext location
                            } else {
                                Logger.d(TAG, "未找到对应的地址信息")
                                return@withContext null
                            }
                        }
                    } else {
                        Logger.d(TAG, "未找到对应的地址信息")
                        null
                    }
                } catch (e: IOException) {
                    e.printStackTrace()
                    Logger.d(TAG, "获取位置失败，请检查网络或权限")
                    null
                }
            }
        }
    }

suspend fun getImageLocationName(image: Image): String? {
    val blackList = arrayOf(
        "Cemetery",
        "Graveyard",
        "Public cemetery",
        "Funeral",
        "Crematorium",
        "Columbarium",
        "Landfill",
        "Waste",
        "Scrap",
        "Sewage",
        "Wastewater",
        "Landfill site",
        "Incineration plant",
        "Chemical industry",
        "Hospital",
        "Disease control",
        "Infectious disease",
        "Mental illness",
        "Clinic",
        "Rehabilitation center",
        "Chemical plant",
        "Oil refinery",
        "Nuclear power plant",
        "Danger",
        "Prison",
        "Detention",
        "Drug rehabilitation",
        "Toilet",
        "Bathroom"
    )
    return null
}