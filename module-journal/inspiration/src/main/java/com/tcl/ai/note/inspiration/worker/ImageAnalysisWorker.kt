package com.tcl.ai.note.inspiration.worker

import android.content.Context
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.CoroutineWorker
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.inspiration.core.ImageAnalysisManager
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import java.util.Calendar
import java.util.concurrent.TimeUnit

class ImageAnalysisWorker(private val context: Context, private val workerParams: WorkerParameters): CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        Logger.i(TAG, "doWork")
        if (AppDataStore.getBoolean("InspirationSuggestion", true)) {
            ImageAnalysisManager.instance.analysisImages()
        } else {
            Logger.i(TAG, "已禁用灵感建议")
        }
        return Result.success()
    }

    companion object {
        private const val TAG = "ScheduledTask"

        fun scheduleTask() {
            Logger.i(TAG, "scheduleTask")
            // 计算到次日凌晨2点的初始延迟
            val calendar = Calendar.getInstance()
            val now = calendar.timeInMillis

            calendar.set(Calendar.HOUR_OF_DAY, 2)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)

            var initialDelay = calendar.timeInMillis - now
            if (initialDelay < 0) {
                // 如果已经过了今天的 2 点，就加一天
                initialDelay += 24 * 60 * 60 * 1000
            }

            // 构建约束（可空，根据需求添加其他约束）
            val constraints = Constraints.Builder()
                .setRequiresBatteryNotLow(true) // 不依赖系统低电量判断
                .build()

            Logger.i(TAG, "initialDelay:$initialDelay")
            // 创建每日重复的WorkRequest
            val workRequest = PeriodicWorkRequestBuilder<ImageAnalysisWorker>(
                24, // 间隔24小时
                TimeUnit.HOURS
            )
                .setInitialDelay(initialDelay, TimeUnit.MILLISECONDS)
                .setConstraints(constraints)
                .setBackoffCriteria(
                    backoffPolicy = BackoffPolicy.EXPONENTIAL,
                    backoffDelay = 30,
                    timeUnit = TimeUnit.MINUTES
                )
                .build()

            // 提交唯一任务（避免重复）
            WorkManager.getInstance(GlobalContext.instance.applicationContext).enqueueUniquePeriodicWork(
                "Analysis Image Task",
                ExistingPeriodicWorkPolicy.KEEP,
                workRequest
            )
        }
    }
}