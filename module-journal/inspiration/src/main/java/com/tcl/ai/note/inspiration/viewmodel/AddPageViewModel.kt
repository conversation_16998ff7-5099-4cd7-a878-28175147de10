package com.tcl.ai.note.inspiration.viewmodel

import android.Manifest
import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Resources
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import androidx.compose.runtime.Stable
import androidx.compose.runtime.mutableStateMapOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.database.entity.Album
import com.tcl.ai.note.database.entity.Image
import com.tcl.ai.note.database.repository.InspirationRepository
import com.tcl.ai.note.inspiration.core.ImageAnalysisManager
import com.tcl.ai.note.inspiration.core.ImageAnalysisState
import com.tcl.ai.note.inspiration.core.analysis.sysinfo.exif.ExifImpl
import com.tcl.ai.note.inspiration.core.imagemanage.ImageHelper
import com.tcl.ai.note.inspiration.utils.checkPermission
import com.tcl.ai.note.inspiration.worker.ImageAnalysisWorker
import com.tcl.ai.note.picturetotext.bean.TravelDiaryImage
import com.tcl.ai.note.picturetotext.bean.TravelDiaryImageGroup
import com.tcl.ai.note.template.bean.ElementType
import com.tcl.ai.note.template.bean.PageConfigInfo
import com.tcl.ai.note.template.bean.TemplateData
import com.tcl.ai.note.template.utils.TextCapacityCalculator
import com.tcl.ai.note.template.utils.loadJsonFromAssets
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.dp2px
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import java.io.File
import java.net.URLEncoder

@Stable
data class AddPageUiState(
    var permissionGranted: Boolean = false,
    var enabled: Boolean = true,
    var existImage: Boolean = false,
    var analysisState: AlbumAnalysisState = AlbumAnalysisState.NotStart
)

fun Album.type(): AlbumType {
    when (this.size) {
        0 -> {
            return AlbumType.Empty
        }

        1 -> {
            return AlbumType.Single
        }

        2 -> {
            return AlbumType.Two
        }

        3 -> {
            return AlbumType.Three
        }

        4 -> {
            return AlbumType.Four
        }

        5 -> {
            return AlbumType.Five
        }

        else -> {
            return AlbumType.More
        }
    }
}

sealed class AlbumAnalysisState {
    data object NotStart : AlbumAnalysisState()
    data class Analyzing(val progress: Int) : AlbumAnalysisState()
    data class Failed(val progress: Int) : AlbumAnalysisState()
    data object Success : AlbumAnalysisState()
}

sealed class AlbumType {
    data object Empty : AlbumType()
    data object Single : AlbumType()
    data object Two : AlbumType()
    data object Three : AlbumType()
    data object Four : AlbumType()
    data object Five : AlbumType()
    data object More : AlbumType()
}

class AddPageViewModel : ViewModel() {
    companion object {
        private const val TAG = "AddPageViewModel"
    }

    private val repository = InspirationRepository()
    private val imageAnalysisManager: ImageAnalysisManager by lazy { ImageAnalysisManager.instance }

    private val _uiState = MutableStateFlow(AddPageUiState())
    val uiState: StateFlow<AddPageUiState> = _uiState.asStateFlow()

    val albumMap = mutableStateMapOf<Album, List<Image>>()

    var selectedAlbumName = ""
    val selectedImages = mutableListOf<Image>()

    var configInfoJson = ""
    val imageGroups = mutableListOf<TravelDiaryImageGroup>()

    init {
        AccountController.connect()
        val grantedPermission = checkPermission(Manifest.permission.READ_MEDIA_IMAGES)
        val isExistImage = isExistImage()
        _uiState.update {
            it.copy(permissionGranted = grantedPermission, existImage = isExistImage)
        }
        viewModelScope.launch {
            launch {
                val enabled = AppDataStore.getBoolean("InspirationSuggestion", true)
                _uiState.update {
                    it.copy(enabled = enabled)
                }
                repository.queryAllAlums().collectLatest { albums ->
                    Logger.e(TAG, "AddPageViewModel collect albums: ${albums.size}")
                    albumMap.clear()
                    albums.sortedByDescending { it.timestamp }.forEach { album ->
                        val images = repository.queryImagesByAlbumId(album.albumId)
                            .filter {
                                File(it.path).exists()
                            }
                        albumMap[album.copy(size = images.size)] = images
                    }
                }
            }
        }
        ImageAnalysisWorker.scheduleTask()
    }

    fun initData() {
        Logger.d(TAG, "initData start")
        val grantedPermission = checkPermission(Manifest.permission.READ_MEDIA_IMAGES)
        val isExistImage = isExistImage()
        _uiState.update {
            it.copy(permissionGranted = grantedPermission, existImage = isExistImage)
        }
        viewModelScope.launch {
            val enabled = AppDataStore.getBoolean("InspirationSuggestion", true)
            _uiState.update {
                it.copy(enabled = enabled)
            }
        }
        if (grantedPermission && isExistImage) {
            analysisAlbums()
        }
    }

    fun analysisAlbums(isFailedRetry: Boolean = false) {
        Logger.d(TAG, "analysisAlbums start")
        viewModelScope.launch {
            var progress = AppDataStore.getData(DataStoreParam.KEY_ANALYSIS_PROGRESS, 0)
            Logger.e(TAG, "analysisAlbums progress:$progress")
            val compareImages = isFailedRetry || ImageHelper(repository).compareImages()
            if ((!compareImages && progress == 0)
                || uiState.value.analysisState is AlbumAnalysisState.Analyzing
            ) {
                return@launch
            }
            if (compareImages) {
                progress = 0
            }
            imageAnalysisManager.analysisImages { state ->
                val albumAnalysisState = when (state) {
                    is ImageAnalysisState.Analyzing -> {
                        Logger.e(TAG, "progress: ${state.progress}")
                        if (state.progress >= progress) {
                            AlbumAnalysisState.Analyzing(state.progress)
                        } else {
                            AlbumAnalysisState.Analyzing(progress)
                        }
                    }

                    is ImageAnalysisState.Failure -> {
                        AlbumAnalysisState.Failed(state.progress)
                    }

                    is ImageAnalysisState.Success -> {
                        AlbumAnalysisState.Success
                    }
                }
                _uiState.update {
                    it.copy(analysisState = albumAnalysisState)
                }
            }
        }
    }

    fun selectImages(album: Album) {
        selectedAlbumName = album.albumName.orEmpty()
        val images = albumMap[album] ?: listOf()
        selectedImages.clear()
        selectedImages.addAll(images)
    }

    private fun isExistImage(context: Context = GlobalContext.Companion.instance): Boolean {
        val projection = arrayOf(MediaStore.Images.Media._ID)
        val cursor = context.contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            projection,
            null,
            null,
            null
        )
        val existImage = (cursor?.count ?: 0) > 0
        cursor?.close()
        return existImage
    }

    @SuppressLint("ObsoleteSdkInt")
    fun handleTemplateSelected(
        jsonFileName: String,
        images: List<Image>
    ) {
        imageGroups.clear()
        val jsonStr = loadJsonFromAssets(GlobalContext.instance, jsonFileName)   // 读取assets下json文件
        val templateData = Gson().fromJson(jsonStr, TemplateData::class.java).apply {
            var imageIndex = 0
            elements.forEach { element ->
                if (element.type == ElementType.IMAGE) {
                    images.getOrNull(imageIndex)?.let { image ->
                        element.content = image.path
                        element.dateTime = image.dateTime
                        element.location = image.location
                    }
                    imageIndex++
                }
            }
        }
        val locale = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            Resources.getSystem().configuration.locales.get(0)
        } else {
            Resources.getSystem().configuration.locale
        }
        val languageCode = locale.language
        val textList = templateData.elements.filter { it.associatedId != null }
        textList.forEach { data ->
            val imageList =
                templateData.elements.filter { data.associatedId?.contains(it.imageId) == true }
                    .map {
                        TravelDiaryImage(
                            uri = Uri.fromFile(File(it.content)),
                            createdTime = it.dateTime.orEmpty(),
                            location = it.location.orEmpty()
                        )
                    }
            val maxChars = TextCapacityCalculator().calculateMaxChars(
                data.size[0].dp2px,
                data.size[1].dp2px,
                if (languageCode == "zh") 0 else 1
            )
            imageGroups.add(TravelDiaryImageGroup(imageList, maxChars / 2))
        }
        val configInfo = PageConfigInfo(
            imagePaths = images.map { it.path },
            templateJson = jsonFileName
        )
        val json = Gson().toJson(configInfo)
        val encoded = URLEncoder.encode(json, "UTF-8")
        configInfoJson = encoded
        Logger.e(
            TAG,
            "handleTemplateSelected imageGroups: $imageGroups"
        )
    }

    fun handleGalleryUris(uris: List<@JvmSuppressWildcards Uri>) {
        selectedImages.clear()
        try {
            uris.forEach { uri ->
                GlobalContext.instance.contentResolver.query(uri, null, null, null, null)
                    ?.use { cursor ->
                        if (cursor.moveToFirst()) {
                            val id = try {
                                cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID))
                            } catch (e: Exception) {
                                e.printStackTrace()
                                System.currentTimeMillis().toString()
                            }
                            val path =
                                cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA))
                            val size =
                                cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE))

                            if (File(path).exists()) {
                                val image = Image(imageId = id, path = path, size = size)
                                selectedImages.add(image)
                            }
                        }
                    }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        viewModelScope.launch {
            selectedImages.forEach { image ->
                ExifImpl.parseTime(image)
                ExifImpl.parseLocation(image)
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        viewModelScope.launch {
            AccountController.disconnectInIO()
        }
    }
}