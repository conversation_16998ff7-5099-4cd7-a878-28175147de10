package com.tcl.ai.note.inspiration.core.imagemanage

import android.content.Context
import android.provider.MediaStore
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.database.repository.InspirationRepository
import com.tcl.ai.note.database.entity.Image
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File

class ImageHelper(
    private val inspirationRepository: InspirationRepository
) {
    companion object {
        private const val TAG = "ImageHelper"
    }

    suspend fun loadImages(context: Context, count: Int): List<Image> =
        withContext(Dispatchers.IO) {
            val images = mutableListOf<Image>()
            val projection = arrayOf(
                MediaStore.Images.Media._ID,
                MediaStore.Images.Media.DATA,
                MediaStore.Images.Media.SIZE
            )
            val sortOrder = "${MediaStore.Images.Media.DATE_ADDED} DESC"
            val cursor = context.contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI, projection, null, null, sortOrder
            )
            cursor?.use {
                try {
                    while (cursor.moveToNext() && images.size < count) {
                        val id =
                            cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media._ID))
                        val path =
                            cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA))
                        val size =
                            cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.SIZE))

                        if (File(path).exists() && !isScreenshotByPath(path)) {
                            Logger.i(TAG, "load200Images --> file exists: $path")
                            val image = Image(imageId = id, path = path, size = size)
                            images.add(image)
                        }
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "load200Images exception: ${e.message}")
                }
            } ?: run {
                Logger.w(TAG, "cursor is null")
            }
            images
        }

    suspend fun getUnAnalyzedImages(context: Context): List<Image> {
        val images = loadImages(context, 200)
        val analyzedImages = inspirationRepository.queryAllImages()
        if (analyzedImages.isEmpty()) {
            return images
        }
        val analyzedImageIds = analyzedImages.map { it.imageId }
        return images.filter { !analyzedImageIds.contains(it.imageId) }
    }

    suspend fun getMarkedImages(): List<Image> {
        return inspirationRepository.queryAllImages()
    }

    suspend fun compareImages(): Boolean {
        val systemImages = loadImages(GlobalContext.appContext, 200)
        val dbImages = inspirationRepository.queryAllImages()
        // 提取imageId集合进行比较
        val sysIds = systemImages.map { it.imageId }.toSet()
        val dbIds = dbImages.map { it.imageId }.toSet()
        // 若两者一致，直接返回false
        if (sysIds == dbIds) return false

        // 处理db中存在但system中不存在的Image（需要删除的）
        val imagesToDelete = dbImages.filter { it.imageId !in sysIds }

        // 调用删除方法
        inspirationRepository.deleteImages(imagesToDelete)

        // 存在差异（新增或删除）即返回true
        return true
    }

    private fun isScreenshotByPath(imagePath: String): Boolean {
        val lowerPath = imagePath.lowercase()
        return lowerPath.contains("/pictures/screenshots")
    }
}