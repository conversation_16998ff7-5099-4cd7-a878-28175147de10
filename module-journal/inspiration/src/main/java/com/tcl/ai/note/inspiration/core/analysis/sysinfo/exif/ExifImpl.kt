package com.tcl.ai.note.inspiration.core.analysis.sysinfo.exif

import android.location.Geocoder
import androidx.exifinterface.media.ExifInterface
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.database.entity.Image
import com.tcl.ai.note.inspiration.core.analysis.sysinfo.ISysInfo
import com.tcl.ai.note.inspiration.utils.getLocationName
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Locale

object ExifImpl : ISysInfo {

    private const val TAG = "ExifImpl"

    override suspend fun parseTime(image: Image) {
        val dateTime = try {
            val exif = ExifInterface(image.path)
            exif.getAttribute(ExifInterface.TAG_DATETIME)
        } catch (e: Exception) {
            null
        }
        dateTime?.let { dt ->
            image.dateTime = dt
            try {
                val timestamp = parseExifDateTime(dt)
                image.createTime = timestamp ?: getFileLastModified(image.path)

            } catch (e: Exception) {
                image.createTime = getFileLastModified(image.path)
            }
        } ?: run {
            image.createTime = getFileLastModified(image.path)
            image.dateTime = formatDateTime(image.createTime)
        }
    }

    override suspend fun parseLocation(image: Image) {
        val exifInterface = ExifInterface(image.path)
        val latLong = exifInterface.latLong ?: doubleArrayOf(0.0, 0.0)
        image.latitude = latLong[0]
        image.longitude = latLong[1]
        image.location = getLocationName(image.latitude, image.longitude, Locale.getDefault())
    }

    private fun parseExifDateTime(dateTime: String): Long? {
        return try {
            // Exif日期格式通常为："yyyy:MM:dd HH:mm:ss"
            val sdf = SimpleDateFormat("yyyy:MM:dd HH:mm:ss", Locale.getDefault())
            sdf.parse(dateTime)?.time
        } catch (e: Exception) {
            null
        }
    }

    private fun formatDateTime(timestamp: Long): String = SimpleDateFormat("yyyy:MM:dd HH:mm:ss", Locale.getDefault()).format(timestamp)

    private fun getFileLastModified(imgPath: String): Long {
        return File(imgPath).lastModified()
    }
}