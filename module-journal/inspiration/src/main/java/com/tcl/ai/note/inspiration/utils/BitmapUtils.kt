package com.tcl.ai.note.inspiration.utils

import android.content.Context
import android.graphics.Bitmap
import androidx.core.graphics.drawable.toBitmap
import coil3.ImageLoader
import coil3.request.ImageRequest
import coil3.request.SuccessResult
import coil3.request.allowHardware
import coil3.toBitmap
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.database.entity.Image
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

private const val TAG = "BitmapUtils"

suspend fun Image.decodeBitmapSafety(): Bitmap? {
    return withContext(Dispatchers.IO) {
        try {
            val context = GlobalContext.appContext
            val loader = ImageLoader(context)
            val request = ImageRequest.Builder(context)
                .data(path) // path 可以是本地或网络图片的 String 地址
                .size(400, 300)
                .allowHardware(false)
                .build()

            val result = loader.execute(request)

            if (result is SuccessResult) {
                result.image.toBitmap()
            } else {
                null
            }
        } catch (e: Exception) {
            Logger.e(TAG, "decodeBitmapSafety --> exception: ${e.message}")
            null
        }
    }
}