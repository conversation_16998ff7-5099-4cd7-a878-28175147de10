package com.tcl.ai.note.inspiration.core.imagemanage

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.resources.R
import java.text.DateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

fun formatTime(timestamp: Long): String {
    val date = Date(timestamp)
    val dateFormat = DateFormat.getDateInstance(DateFormat.LONG, Locale.getDefault())
    return dateFormat.format(date)
}

fun getTimePeriod(timestamp: Long): String {
    val calendar = Calendar.getInstance().apply {
        timeInMillis = timestamp
    }

    val hour = calendar.get(Calendar.HOUR_OF_DAY)
    val minute = calendar.get(Calendar.MINUTE)
    val totalMinutes = hour * 60 + minute

    return GlobalContext.instance.getString(
        when (totalMinutes) {
            in 300..659 -> R.string.morning     // 05:00-10:59
            in 660..1019 -> R.string.afternoon    // 11:00-16:59
            in 1020..1199 -> R.string.dusk   // 17:00-19:59
            else -> R.string.night                 // 20:00-04:59
        }
    )
}