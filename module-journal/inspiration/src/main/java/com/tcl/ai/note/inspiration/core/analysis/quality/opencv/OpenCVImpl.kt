package com.tcl.ai.note.inspiration.core.analysis.quality.opencv

import android.graphics.Bitmap
import android.util.Log
import com.tcl.ai.note.inspiration.core.analysis.quality.IAppraise
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.opencv.android.OpenCVLoader
import org.opencv.android.Utils
import org.opencv.core.Core
import org.opencv.core.CvType
import org.opencv.core.Mat
import org.opencv.core.MatOfDouble
import org.opencv.core.MatOfFloat
import org.opencv.core.MatOfInt
import org.opencv.core.Size
import org.opencv.imgproc.Imgproc
import kotlin.math.pow

object OpenCVImpl: IAppraise {

    private const val TAG = "OpenCVImpl"

    fun init(): Boolean {
        return if (OpenCVLoader.initLocal()) {
            Logger.i(TAG, "OpenCV loaded successfully")
            true
        } else {
            Logger.e(TAG, "OpenCV initialization failed!")
            false
        }
    }

    override suspend fun appraiseImage(bitmap: Bitmap): Double {
        val sharpness = calculateSharpness(bitmap)
        val brightness = calculateBrightness(bitmap)
        val contrast = calculateContrast(bitmap)
        val sharpnessScore = calculateSharpnessScore(sharpness)
        val brightnessScore = calculateBrightnessScore(brightness)
        val contrastScore = calculateContrastScore(contrast)
        val qualityScore = calculateQualityScore(
            sharpnessScore,
            brightnessScore,
            contrastScore
        )
        return qualityScore
    }

    /**
     * 归一化尺寸
     */
    private fun resizeAndPadToSquare(src: Mat, edge: Int = 256): Mat {
        val dst = Mat()
        Imgproc.resize(src, dst, Size(edge.toDouble(), edge.toDouble()))
        return dst
    }

    /**
     * 拉普拉斯方差法
     */
    private fun calcLaplacian(grayMat: Mat): Double {
        // 计算拉普拉斯算子
        val laplacianMat = Mat()
        Imgproc.Laplacian(grayMat, laplacianMat, CvType.CV_64F)

        // 计算标准差
        val stdDev = MatOfDouble()
        Core.meanStdDev(laplacianMat, MatOfDouble(), stdDev)
        laplacianMat.release()

        // 计算方差作为清晰度指标
        return stdDev[0, 0][0].pow(2.0)
    }

    /**
     * Tenengrad 梯度法
     */
    private fun calcTenengrad(grayMat: Mat): Double {
        val gradX = Mat()
        val gradY = Mat()
        Imgproc.Sobel(grayMat, gradX, CvType.CV_64F, 1, 0)
        Imgproc.Sobel(grayMat, gradY, CvType.CV_64F, 0, 1)
        val grad = Mat()
        Core.magnitude(gradX, gradY, grad)
        return Core.mean(grad).`val`[0]
    }

    private suspend fun calculateSharpness(bitmap: Bitmap): Double = withContext(Dispatchers.Default) {
        // Bitmap 转 Mat 并转换为灰度图
        var srcMat = Mat()
        Utils.bitmapToMat(bitmap, srcMat)

        srcMat = resizeAndPadToSquare(srcMat)

        val grayMat = Mat()
        Imgproc.cvtColor(srcMat, grayMat, Imgproc.COLOR_BGR2GRAY)
        srcMat.release()

        val lapVal = calcLaplacian(grayMat)
        val tenenVal = calcTenengrad(grayMat)
        grayMat.release()
        // 简单线性加权
        lapVal * 0.5 + tenenVal * 0.5
    }

    /**
     * 返回值浮点型， 返回值域 [0, 255]
     */
    private suspend fun calculateBrightness(bitmap: Bitmap): Double = withContext(Dispatchers.Default) {
        // 将 Bitmap 转换为 OpenCV 的 Mat 对象
        val srcMat = Mat()
        Utils.bitmapToMat(bitmap, srcMat)

        // 将 RGBA 转换为 RGB（去除透明度通道）
        val rgbMat = Mat()
        Imgproc.cvtColor(srcMat, rgbMat, Imgproc.COLOR_RGBA2RGB)

        // 转换为 HSV 颜色空间
        val hsvMat = Mat()
        Imgproc.cvtColor(rgbMat, hsvMat, Imgproc.COLOR_RGB2HSV)

        // 分离 HSV 通道，获取亮度（Value）通道
        val channels = mutableListOf<Mat>()
        Core.split(hsvMat, channels)
        val brightnessChannel = channels[2]

        // 计算亮度通道的均值
        val mean = Core.mean(brightnessChannel)

        // 释放资源
        srcMat.release()
        rgbMat.release()
        hsvMat.release()
        channels.forEach { it.release() }

        mean.`val`[0] // 返回 V 通道的均值
    }

    /**
     * 返回浮点型， 值域 [0.0, 127.5]
     */
    private suspend fun calculateContrast(bitmap: Bitmap): Double = withContext(Dispatchers.Default) {
        // 将 Bitmap 转换为 OpenCV 的 Mat 对象
        val srcMat = Mat()
        Utils.bitmapToMat(bitmap, srcMat)

        // 转换为灰度图（直接处理 RGBA 到 GRAY 的转换）
        val grayMat = Mat()
        Imgproc.cvtColor(srcMat, grayMat, Imgproc.COLOR_RGBA2GRAY)

        // 计算灰度图的标准差（对比度指标）
        val mean = MatOfDouble()
        val stdDev = MatOfDouble()
        Core.meanStdDev(grayMat, mean, stdDev)

        // 提取标准差并释放资源
        val contrast = stdDev.toArray()[0]
        srcMat.release()
        grayMat.release()
        mean.release()
        stdDev.release()
        contrast
    }

    /**
     * 计算清晰度评分
     * @param sharpness 清晰度
     * 极模糊	0 - 50	    失焦拍摄、运动模糊
     * 一般模糊	50 - 200	低质量压缩、弱光噪点
     * 清晰	    200 - 1000	正常手机拍摄
     * 非常清晰	1000+       专业相机拍摄、高分辨率
     */
    private suspend fun calculateSharpnessScore(sharpness: Double): Double =
        withContext(Dispatchers.Default) {
            val ratio = when (sharpness) {
                in 0.0..50.0 -> {
                    20.0 / 50
                }

                in 50.0..150.0 -> {
                    60.0 / 150
                }

                in 150.0..300.0 -> {
                    100.0 / 300
                }

                else -> {
                    1.0
                }
            }
            (ratio * sharpness).coerceIn(0.0, 100.0)
        }

    /**
     * 计算亮度评分
     * @param brightness 亮度 值域 [0, 255]
     */
    private suspend fun calculateBrightnessScore(brightness: Double): Double =
        withContext(Dispatchers.Default) {
            (brightness / 255) * 100
        }

    /**
     * 计算对比度评分
     * @param contrast 对比度 值域 [0.0, 127.5]
     */
    private suspend fun calculateContrastScore(contrast: Double): Double =
        withContext(Dispatchers.Default) {
            (contrast / 127.5) * 100
        }

    /**
     * 对照片的清晰度、亮度、对比度三个维度进行打分，每个维度的得分范围为 0-100。
     * 质量最终评分计算规则为 （清晰度评分*1/3 + 亮度评分*1/3 + 对比对评分*1/3）
     * 返回质量得分
     */
    private suspend fun calculateQualityScore(
        sharpnessScore: Double,
        brightnessScore: Double,
        contrastScore: Double
    ): Double = withContext(Dispatchers.Default) {
        val proportion = 1 / 3.toDouble()
        val score =
            sharpnessScore * proportion + brightnessScore * proportion + contrastScore * proportion
        println(
            "tcl sharpnessScore: $sharpnessScore " +
                    "sharpnessScore: $sharpnessScore " +
                    "contrastScore: $contrastScore " +
                    "score: $score"
        )
        score
    }

    suspend fun similarBitmap(bmp1: Bitmap, bmp2: Bitmap, threshold: Double = 0.9): Double = withContext(Dispatchers.Default) {
        if (bmp1.width != bmp2.width || bmp1.height != bmp2.height) {
            return@withContext 0.0 // 尺寸不一样直接不相似
        }

        // Bitmap 转 Mat
        val mat1 = Mat()
        val mat2 = Mat()
        Utils.bitmapToMat(bmp1, mat1)
        Utils.bitmapToMat(bmp2, mat2)

        // 转换为灰度（也可用彩色直方图）
        Imgproc.cvtColor(mat1, mat1, Imgproc.COLOR_BGR2GRAY)
        Imgproc.cvtColor(mat2, mat2, Imgproc.COLOR_BGR2GRAY)

        // 计算直方图
        val histSize = MatOfInt(256)
        val histRange = MatOfFloat(0f, 256f)
        val hist1 = Mat()
        val hist2 = Mat()
        Imgproc.calcHist(listOf(mat1), MatOfInt(0), Mat(), hist1, histSize, histRange)
        Core.normalize(hist1, hist1, 0.0, 1.0, Core.NORM_MINMAX)
        Imgproc.calcHist(listOf(mat2), MatOfInt(0), Mat(), hist2, histSize, histRange)
        Core.normalize(hist2, hist2, 0.0, 1.0, Core.NORM_MINMAX)

        // 使用相关性作为相似度指标
        val similarity = Imgproc.compareHist(hist1, hist2, Imgproc.CV_COMP_CORREL)
        Log.i(TAG, "similarBitmap --> : $similarity") // 1.0 表示完全一致

        // 判断是否相似
        // similarity >= threshold
        similarity
    }
}