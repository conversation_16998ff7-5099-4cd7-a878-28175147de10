package com.tcl.ai.note.inspiration.core.imagemanage

import com.tcl.ai.note.database.entity.Image
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.pow
import kotlin.math.sin
import kotlin.math.sqrt

object ImageGrouping {
    suspend fun groupImages(
        images: List<Image>,
        dispatcher: CoroutineDispatcher
    ): List<List<Image>> = withContext(dispatcher) {
        // 辅助函数：解析时间戳
        fun parseTime(dateTime: String?): Long {
            if (dateTime.isNullOrEmpty()) {
                return 0L
            }
            return SimpleDateFormat("yyyy:MM:dd HH:mm:ss", Locale.getDefault()).parse(dateTime)?.time ?: 0L
        }

        // 1. 按日期分组
        val dateGroups = images.groupBy { it.dateTime?.substringBefore(" ") ?: "" }

        val res = dateGroups.values.flatMap { dateImages ->
            // 2. 按时间分组（30分钟间隔）
            val timeGroups = mutableListOf<MutableList<Image>>()
            dateImages.sortedBy { parseTime(it.dateTime) }.forEach { img ->
                val lastGroup = timeGroups.lastOrNull()
                if (lastGroup == null || parseTime(img.dateTime) - parseTime(lastGroup.last().dateTime) > 30 * 60 * 1000) {
                    timeGroups.add(mutableListOf(img))
                } else {
                    lastGroup.add(img)
                }
            }

            // 3. 处理每个时间组的地理分组
            val geoGroups = timeGroups.flatMap { timeGroup ->
                val (validLoc, noLoc) = timeGroup.partition { it.latitude != 0.0 || it.longitude != 0.0 }
                val clusters = clusterByDistance(validLoc, 300.0)
                clusters + listOf(noLoc).filter { it.isNotEmpty() }
            }

            // 4. 合并同名位置组
            val (validGroups, invalidGroups) = geoGroups.partition {
                it.isNotEmpty() && (it[0].latitude != 0.0 || it[0].longitude != 0.0)
            }
            val mergedGroups = validGroups
                .groupBy { it.firstOrNull()?.location }
                .values.map { it.flatten() }

            // 合并最终结果并过滤
            (mergedGroups + invalidGroups)
                .filter { it.size >= 3 }
        }
        res
    }

    // 地理聚类算法
    private fun clusterByDistance(images: List<Image>, maxDistance: Double): List<List<Image>> {
        val visited = mutableSetOf<Image>()
        return images.filterNot { it in visited }.mapNotNull { seed ->
            if (seed in visited) return@mapNotNull null

            val queue = ArrayDeque<Image>().apply { add(seed) }
            val cluster = mutableListOf<Image>()
            while (queue.isNotEmpty()) {
                val current = queue.removeFirst()
                if (current !in visited) {
                    visited.add(current)
                    cluster.add(current)
                    images.filterNot { it in visited }.forEach { neighbor ->
                        if (calculateDistance(current, neighbor) <= maxDistance) {
                            queue.add(neighbor)
                        }
                    }
                }
            }
            cluster.takeIf { it.isNotEmpty() }
        }
    }

    // Haversine距离计算
    private fun calculateDistance(a: Image, b: Image): Double {
        val earthRadius = 6371000.0 // meters
        val dLat = Math.toRadians(b.latitude - a.latitude)
        val dLon = Math.toRadians(b.longitude - a.longitude)

        val a = sin(dLat / 2).pow(2) + cos(Math.toRadians(a.latitude)) *
                cos(Math.toRadians(b.latitude)) * sin(dLon / 2).pow(2)
        return earthRadius * 2 * atan2(sqrt(a), sqrt(1 - a))
    }
}