package com.tcl.ai.note.picturetotext

import androidx.compose.runtime.Stable
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.resources.R

@Stable
data class AiWritingAssistantUiState(
    val styleOptions: List<WritingStyle> = listOf(
        WritingStyle.NoRestrictions,
        WritingStyle.Simplicity,
        WritingStyle.Art,
        WritingStyle.Story,
        WritingStyle.TravelNotes,
        WritingStyle.Humor,
        WritingStyle.Emotion
    ),
    val companionOptions: List<PeerCompanion> = listOf(
        PeerCompanion.Friend,
        PeerCompanion.Family,
        PeerCompanion.Oneself,
        PeerCompanion.Lover,
        PeerCompanion.Other
    ),
    val wordNumberOptions: List<WordNumber> = listOf(
        WordNumber.Less,
        WordNumber.Moderate,
        WordNumber.More
    ),
    val showEmptyInputTip: Boolean = false,
    val specialEvent: String = "",
    val customCompanion: String = "",
    val currentStyle: WritingStyle = WritingStyle.NoRestrictions,
    val currentCompanion: PeerCompanion = PeerCompanion.Friend,
    val currentWordNumber: WordNumber = WordNumber.Moderate,
    var screenHeight: Int = DEFAULT_HEIGHT,
)

sealed class WritingStyle(val nameResId: Int, val promptResId: Int) {
    data object NoRestrictions : WritingStyle(
        nameResId = R.string.style_no_restrictions,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_no_restrictions
    )

    data object Simplicity : WritingStyle(
        nameResId = R.string.style_simplicity,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_simplicity
    )

    data object Art : WritingStyle(
        nameResId = R.string.style_art,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_art
    )

    data object Story : WritingStyle(
        nameResId = R.string.style_story,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_story
    )

    data object TravelNotes : WritingStyle(
        nameResId = R.string.style_travel_notes,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_travel_notes
    )

    data object Humor : WritingStyle(
        nameResId = R.string.style_humor,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_humor
    )

    data object Emotion : WritingStyle(
        nameResId = R.string.style_emotion,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_emotion
    )

    fun displayName(): String {
        return GlobalContext.instance.resources.getString(nameResId)
    }

    fun prompt(): String {
        return return GlobalContext.instance.resources.getString(promptResId)
    }
}

sealed class PeerCompanion(val nameResId: Int, val promptResId: Int) {
    data object Friend : PeerCompanion(
        nameResId = R.string.companions_friend,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_friend
    )

    data object Family : PeerCompanion(
        nameResId = R.string.companions_family,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_family
    )

    data object Oneself : PeerCompanion(
        nameResId = R.string.companions_oneself,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_oneself
    )

    data object Lover : PeerCompanion(
        nameResId = R.string.companions_lover,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_lover
    )

    data object Other : PeerCompanion(
        nameResId = R.string.companions_other,
        promptResId = com.tcl.ai.note.picturetotext.R.string.prompt_other
    )

    fun displayName(): String {
        return GlobalContext.instance.resources.getString(nameResId)
    }

    fun prompt(): String {
        return return GlobalContext.instance.resources.getString(promptResId)
    }
}

sealed class WordNumber(val nameResId: Int, val ratio: Float) {
    data object Less : WordNumber(nameResId = R.string.words_number_less, ratio = 0.6F)
    data object Moderate : WordNumber(nameResId = R.string.words_number_moderate, ratio = 1.0F)
    data object More : WordNumber(nameResId = R.string.words_number_more, ratio = 1.4F)

    fun displayName(): String {
        return GlobalContext.instance.resources.getString(nameResId)
    }
}