package com.tcl.ai.note.picturetotext.bean

import com.google.gson.annotations.SerializedName

data class TravelDiaryGenerateResponse(
    @SerializedName("at") val at: String,
    @SerializedName("code") val code: Int,
    @SerializedName("data") val data: Data?,
    @SerializedName("message") val message: String,
    @SerializedName("trace_id") val traceId: String,
)

data class Data(
    @SerializedName("generated_texts") val generatedTexts: List<String>?,
    @SerializedName("image_moderation_result") val imageModerationResult: List<ImageModerationResult>?
)

data class ImageModerationResult(
    @SerializedName("group_index") val groupIndex: Int,
    @SerializedName("image_index") val imageIndex: Int,
    @SerializedName("error_message") val errorMessage: String
)