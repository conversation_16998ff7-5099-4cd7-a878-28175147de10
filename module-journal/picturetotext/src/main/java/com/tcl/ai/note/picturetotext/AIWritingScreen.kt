package com.tcl.ai.note.picturetotext

import android.view.ViewTreeObserver
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.journalbase.truncateVisibleCharacters
import com.tcl.ai.note.picturetotext.bean.TravelDiaryImageGroup
import com.tcl.ai.note.picturetotext.ui.SelectOptionsPanel
import com.tcl.ai.note.picturetotext.ui.TclSearchBar
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.getNavBarHeight
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.px2dp
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.MarqueeText
import com.tcl.ai.note.widget.verticalScrollbar
import com.tct.theme.core.designsystem.component.TclButton
import com.tct.theme.core.designsystem.theme.TclTheme
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

private const val MAX_SPECIAL_EVENT_LENGTH = 200
private const val MAX_COMPANION_OTHER_LENGTH = 20

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AIWritingScreen(
    imageGroups: List<TravelDiaryImageGroup>,
    modifier: Modifier = Modifier,
    darkTheme: Boolean = isSystemInDarkTheme(),
    createType: Int = 0, //创建方式，0: 从模板创建，1: 从空白页面创建，用于埋点
    operationType: String,
    curTemplateJson: String,
    selectTemplateJson: String,
    clearTextRectFList: () -> Unit,
    generateClick: () -> Unit
) {
    val viewModel = viewModel<AiWritingAssistantViewModel>()
    viewModel.imageGroups = imageGroups
    val uiState = viewModel.uiState.collectAsState().value
    val context = LocalContext.current
    val loginHandler = rememberLoginHandler()
    val coroutineScope = rememberCoroutineScope()
    var loginCheckJob: Job? = null
    val keyboardHeight: Dp = rememberKeyboardHeightDp()
    val isKeyboardOpen = keyboardHeight.value > 0
    val scrollState = rememberScrollState()
    val ime = WindowInsets.ime
    val imeVisible = WindowInsets.isImeVisible
    val imeHeight = ime.getBottom(LocalDensity.current)
    val bottomPadding by animateDpAsState(imeVisible.judge(imeHeight.px2dp.dp, 0.dp), label = "")

    // 键盘弹出时，按钮移动至表单下面。 滚动区域内可滚动内容
    LaunchedEffect(imeVisible, imeHeight) {
        if (imeVisible) {
            scrollState.animateScrollTo(scrollState.maxValue)
        }
    }

    LaunchedEffect(Unit) {
        viewModel.updateOperationType(operationType)
        viewModel.updateCreateType(createType)
    }

    DisposableEffect(Unit) {
        onDispose {
            viewModel.resetData()
        }
    }
    DisposableEffect(Unit) {
        onDispose {
            if (loginCheckJob?.isActive == true) {
                loginCheckJob?.cancel()
            }
            ToastUtil.release()
        }
    }

    Column(
        modifier = modifier
            .padding(bottom = bottomPadding)
            .fillMaxSize()
            .verticalScroll(scrollState)
            .verticalScrollbar(state = scrollState, offsetX = 2.dp.toPx)
    ) {
        Spacer(modifier = Modifier.height(8.dp))

        WritingStyleOptionPanel()

        PeerCompanionOptionPanel()

        WordsNumberOptionPanel()

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .height(32.dp),
            text = stringResource(R.string.label_special_event),
            fontSize = 14.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Normal,
            color = TclTheme.colorScheme.tctStanderTextPrimary
        )

        SpecialEventTextField(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .heightIn(min = 44.dp, max = 114.dp),
            placeholder = stringResource(R.string.placeholder_enter_special_event),
            maxInputLength = MAX_SPECIAL_EVENT_LENGTH,
            onInputExceed = {
                ToastUtils.makeWithCancel(
                    R.string.toast_limit_enter_special_event_length,
                    length = Toast.LENGTH_SHORT
                )
            },
            onQueryChange = { viewModel.onSpecialEventInput(it) },
        )

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
        )

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        )

        TclButton(
            modifier = Modifier
                .padding(bottom = if (isKeyboardOpen) 16.dp else 20.dp + getNavBarHeight())
                .padding(horizontal = 24.dp)
                .fillMaxWidth()
                .height(44.dp),
            colors = ButtonColors(
                containerColor = darkTheme.judge(
                    Color(0xFF4882FF),
                    Color(0xFF155BF0),
                ),
                contentColor = Color.White,
                disabledContainerColor = TclTheme.colorScheme.tctStanderAccentPrimary,
                disabledContentColor = TclTheme.colorScheme.tctStanderTextButton
            ),
            contentPadding = PaddingValues(horizontal = 16.dp),
            onClick = {
                if (!NetworkUtils.isNetworkAvailable(context)) {
                    val message = context.resources.getString(R.string.network_exception)
                    ToastUtil.show(message = message)
                    return@TclButton
                }
                loginCheckJob = coroutineScope.launch {
                    if (AccountController.getLoginState()) {
                        val companion = uiState.currentCompanion
                        if ((companion is PeerCompanion.Other) && (uiState.customCompanion.isEmpty())) {
                            viewModel.showEmptyInputTip()
                            return@launch
                        }
                        if (curTemplateJson != selectTemplateJson) {
                            clearTextRectFList()
                        }
                        viewModel.generate()
                        generateClick()
                    } else {
                        loginHandler {

                        }
                    }
                }
            }
        ) {
            Text(
                text = stringResource(R.string.btn_text_generate),
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium
            )
        }
    }
}

@Composable
private fun WritingStyleOptionPanel() {
    val viewModel = viewModel<AiWritingAssistantViewModel>()
    val uiState = viewModel.uiState.collectAsState().value
    SelectOptionsPanel(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        typeName = stringResource(R.string.style_title),
        options = uiState.styleOptions.map { it.nameResId },
        currentIndex = uiState.styleOptions.indexOf(uiState.currentStyle),
        onSelected = { viewModel.onStyleSelected(uiState.styleOptions[it]) }
    )
}

@Composable
private fun PeerCompanionOptionPanel(
    darkTheme: Boolean = isSystemInDarkTheme()
) {
    val viewModel = viewModel<AiWritingAssistantViewModel>()
    val uiState = viewModel.uiState.collectAsState().value
    var isKeyboardVisible by remember { mutableStateOf(false) }
    val paddingTop = 8
    val searchPanelHeight = 44
    val emptyInputTipHeight = 18

    SelectOptionsPanel(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(top = 16.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        typeName = stringResource(R.string.companions_title),
        options = uiState.companionOptions.map { it.nameResId },
        currentIndex = uiState.companionOptions.indexOf(uiState.currentCompanion),
        onSelected = { viewModel.onCompanionSelected(uiState.companionOptions[it]) }
    )

    if (uiState.currentCompanion is PeerCompanion.Other) {
        SearchPanel(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(top = paddingTop.dp)
                .fillMaxWidth()
                .height(searchPanelHeight.dp),
            autoRequestFocus = true,
            isError = uiState.showEmptyInputTip,
            placeholder = stringResource(R.string.placeholder_enter_your_peer_companions),
            maxInputLength = MAX_COMPANION_OTHER_LENGTH,
            onInputExceed = {},
            onQueryChange = { viewModel.onCompanionOtherInput(it) },
            onFocusChange = { viewModel.onCompanionOtherInputFocusChanged(it) },
            onKeyboardVisible = { isKeyboardVisible = it }
        )
        viewModel.onSizeChanged((paddingTop + searchPanelHeight + DEFAULT_HEIGHT))
        if (uiState.showEmptyInputTip) {
            Text(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(start = 1.dp, top = paddingTop.dp)
                    .wrapContentHeight()
                    .fillMaxWidth(),
                text = stringResource(R.string.enter_prompt),
                fontSize = 14.sp,
                lineHeight = 16.sp,
                fontWeight = FontWeight.Normal,
                color = darkTheme.judge(
                    Color(0xFFD93D38),
                    Color(0xFFCE3226),
                )
            )
            viewModel.onSizeChanged((paddingTop * 2 + searchPanelHeight + emptyInputTipHeight + DEFAULT_HEIGHT))
        } else {
            viewModel.onSizeChanged((paddingTop + searchPanelHeight + DEFAULT_HEIGHT))
        }
    } else {
        viewModel.onSizeChanged(DEFAULT_HEIGHT)
    }
}

@Composable
private fun WordsNumberOptionPanel() {
    val viewModel = viewModel<AiWritingAssistantViewModel>()
    val uiState = viewModel.uiState.collectAsState().value
    SelectOptionsPanel(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(top = 16.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        minItemWidth = 106.dp,
        typeName = stringResource(R.string.words_number_title),
        options = uiState.wordNumberOptions.map { it.nameResId },
        currentIndex = uiState.wordNumberOptions.indexOf(uiState.currentWordNumber),
        onSelected = { viewModel.onWordNumberSelected(uiState.wordNumberOptions[it]) }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SearchPanel(
    modifier: Modifier,
    autoRequestFocus: Boolean = false,
    isError: Boolean = false,
    maxInputLength: Int,
    placeholder: String,
    darkTheme: Boolean = isSystemInDarkTheme(),
    onQueryChange: (String) -> Unit,
    onInputExceed: () -> Unit,
    onFocusChange: (isFocus: Boolean) -> Unit = {},
    onKeyboardVisible: (isVisible: Boolean) -> Unit
) {
    var text by rememberSaveable { mutableStateOf("") }
    val context = LocalContext.current
    val view = LocalView.current
    var rootViewHeight by remember { mutableIntStateOf(0) }
    var firstFocusable by remember { mutableStateOf(false) }
    var isSearchViewFocused by remember { mutableStateOf(false) }

    val errorColor = remember(darkTheme) {
        darkTheme.judge(
            Color(0xFFD93D38),
            Color(0xFFC42310),
        )
    }
    val focusedColor = remember(darkTheme) {
        darkTheme.judge(
            Color(0xFF4882FF),
            Color(0xFF155BF0),
        )
    }
    var boxWidthPx by remember { mutableIntStateOf(0) }

    DisposableEffect(view) {
        val rootView = (context as ComponentActivity).window.decorView
        val listener = ViewTreeObserver.OnGlobalLayoutListener {
            val rect = android.graphics.Rect()
            rootView.getWindowVisibleDisplayFrame(rect)
            val screenHeight = rootView.height
            val keypadHeight = screenHeight - rect.bottom
            val isKeyboardVisible = keypadHeight > screenHeight * 0.15
            onKeyboardVisible.invoke(isKeyboardVisible)
            rootViewHeight = rect.height()
        }
        rootView.viewTreeObserver.addOnGlobalLayoutListener(listener)
        onDispose {
            rootView.viewTreeObserver.removeOnGlobalLayoutListener(listener)
        }
    }

    Box(
        modifier = modifier
            .border(
                color = isError.judge(
                    errorColor,
                    isSearchViewFocused.judge(
                        focusedColor,
                        Color.Transparent
                    )
                ),
                width = darkTheme.judge(1.dp, 1.5.dp),
                shape = RoundedCornerShape(percent = 50)
            )
            .onSizeChanged {
                boxWidthPx = it.width
            }
    ) {
        TclSearchBar(
            maxInputLength = maxInputLength,
            leadingIcon = { Spacer(Modifier.size(12.dp)) },
            barPadding = PaddingValues(horizontal = 0.dp, vertical = 0.dp),
            query = text,
            onQueryChange = {
                val codePoint = truncateVisibleCharacters(it, maxInputLength)
                text = codePoint
                if (codePoint.length >= maxInputLength) {
                    onInputExceed.invoke()
                } else {
                    onQueryChange.invoke(text.trimStart().trimEnd())
                }
            },
            placeholder = {
                Box(
                    modifier = Modifier
                        .size(boxWidthPx.px2dp.dp - 32.dp)
                        .fillMaxHeight(),
                    contentAlignment = Alignment.CenterStart
                ) {
                    MarqueeText(
                        text = placeholder,
                        fontSize = 12.sp,
                        lineHeight = 16.sp,
                        fontWeight = FontWeight.Normal,
                        color = darkTheme.judge(
                            Color.White.copy(alpha = 0.5f),
                            Color.Black.copy(alpha = 0.3f),
                        )
                    )
                }
            },
            onSearch = {},
            expanded = false,
            onExpandedChange = {
                isSearchViewFocused = it
                if (it && !firstFocusable) {
                    firstFocusable = true
                }
                if (firstFocusable) {
                    onFocusChange.invoke(it)
                }
            },
            clearQueryContentDescription = null,
            excludeWindowInsets = false,
            autoRequestFocus = autoRequestFocus
        )
    }
}

@Composable
fun SpecialEventTextField(
    modifier: Modifier,
    maxInputLength: Int,
    placeholder: String,
    darkTheme: Boolean = isSystemInDarkTheme(),
    onQueryChange: (String) -> Unit,
    onInputExceed: () -> Unit,
) {
    var text by remember { mutableStateOf("") }
    var lineCount by remember { mutableIntStateOf(1) }
    val interactionSource = remember { MutableInteractionSource() }
    val isFocused by interactionSource.collectIsFocusedAsState()
    val scrollState = rememberScrollState()
    var layoutResult by remember { mutableStateOf<TextLayoutResult?>(null) }
    val coroutineScope = rememberCoroutineScope()

    // 动态圆角
    val radius = if (lineCount <= 1) 22.dp else 12.dp

    val errorColor = remember(darkTheme) {
        darkTheme.judge(
            Color(0xFFD93D38),
            Color(0xFFC42310),
        )
    }
    val focusedColor = remember(darkTheme) {
        darkTheme.judge(
            Color(0xFF4882FF),
            Color(0xFF155BF0),
        )
    }

    // 👇 每次文本变化or布局完成 -> 滚到底部
    LaunchedEffect(text, layoutResult) {
        // 需要布局后(TextLayoutResult不为空)
        layoutResult?.let { _ ->
            // 🔥 滚动到底部
            coroutineScope.launch {
                scrollState.animateScrollTo(scrollState.maxValue)
            }
        }
    }

    Column(
        modifier = modifier
            .background(
                color = darkTheme.judge(
                    Color.White.copy(alpha = 0.08f),
                    Color.White,
                ),
                shape = RoundedCornerShape(radius)
            )
            .border(
                width = darkTheme.judge(1.dp, 1.5.dp),
                color = (text.length == maxInputLength).judge(
                    errorColor,
                    isFocused.judge(
                        focusedColor,
                        Color.Transparent
                    )
                ),
                shape = RoundedCornerShape(radius)
            )
            .padding(horizontal = 16.dp, vertical = 12.dp)
    ) {
        Box(
            modifier = Modifier
                .weight(1f, fill = false)
                .verticalScroll(scrollState)
        ) {
            BasicTextField(
                value = text,
                onValueChange = {
                    val codePoint = truncateVisibleCharacters(it, maxInputLength)
                    text = codePoint
                    if (codePoint.length >= maxInputLength) {
                        onInputExceed.invoke()
                    } else {
                        onQueryChange.invoke(text.trimStart().trimEnd())
                    }
                },
                modifier = Modifier
                    .fillMaxWidth(),
                textStyle = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Normal,
                    lineHeight = 20.sp,
                    color = darkTheme.judge(
                        Color.White.copy(0.9f),
                        Color.Black.copy(0.8f),
                    )
                ),
                interactionSource = interactionSource,
                cursorBrush = SolidColor(focusedColor),
                onTextLayout = { layout: TextLayoutResult ->
                    lineCount = layout.lineCount
                    layoutResult = layout
                },
                maxLines = 4,
                decorationBox = { innerTextField ->
                    Box(
                        Modifier.fillMaxSize()
                    ) {
                        innerTextField()

                        if (text.isEmpty()) {
                            Text(
                                text = placeholder,
                                color = darkTheme.judge(
                                    Color.White.copy(alpha = 0.5f),
                                    Color.Black.copy(alpha = 0.3f),
                                ),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Normal,
                                lineHeight = 16.sp,
                            )
                        }
                    }
                }
            )

            if (lineCount == 1 && isFocused && text.isEmpty()) {
                CustomCounter(
                    curLength = text.length,
                    maxLength = maxInputLength,
                    errorColor = errorColor,
                    modifier = Modifier.align(Alignment.CenterEnd)
                )
            }
        }

        if (text.isNotEmpty()) {
            Spacer(modifier = Modifier.height(8.dp))

            CustomCounter(
                curLength = text.length,
                maxLength = maxInputLength,
                errorColor = errorColor,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

/**
 * 特殊事件右下角计数
 */
@Composable
fun CustomCounter(
    curLength: Int = 0,
    maxLength: Int = 0,
    errorColor: Color,
    modifier: Modifier = Modifier,
    darkTheme: Boolean = isSystemInDarkTheme(),
) {
    val normalTextColor = remember(darkTheme) {
        darkTheme.judge(
            Color.White.copy(alpha = 0.5f),
            Color.Black.copy(alpha = 0.5f)
        )
    }
    Text(
        text = buildAnnotatedString {
            withStyle(
                SpanStyle(
                    color = if (curLength == maxLength) {
                        errorColor
                    } else {
                        normalTextColor
                    },
                ),
            ) {
                append("$curLength")
            }
            append("/${maxLength}")
        },
        fontSize = 12.sp,
        modifier = modifier,
        textAlign = TextAlign.End,
        lineHeight = 18.sp,
        color = normalTextColor,
    )
}

@Composable
private fun rememberKeyboardHeight(): Int {
    val density = LocalDensity.current
    val insets = WindowInsets.ime.getBottom(density)
    return remember(insets) { insets }
}

@Composable
private fun rememberKeyboardHeightDp(): Dp {
    val heightPx = rememberKeyboardHeight()
    return with(LocalDensity.current) { heightPx.toDp() }
}