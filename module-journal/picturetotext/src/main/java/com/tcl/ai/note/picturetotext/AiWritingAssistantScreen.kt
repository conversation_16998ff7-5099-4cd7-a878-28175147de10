package com.tcl.ai.note.picturetotext

import android.view.ViewTreeObserver
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.journalbase.truncateVisibleCharacters
import com.tcl.ai.note.picturetotext.bean.TravelDiaryImageGroup
import com.tcl.ai.note.picturetotext.ui.BottomSheet
import com.tcl.ai.note.picturetotext.ui.SelectOptionsPanel
import com.tcl.ai.note.picturetotext.ui.TclSearchBar
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.getNavBarHeight
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.verticalScrollbar
import com.tct.theme.core.designsystem.component.TclButton
import com.tct.theme.core.designsystem.theme.TclTheme
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

private const val MAX_SPECIAL_EVENT_LENGTH = 200
private const val MAX_COMPANION_OTHER_LENGTH = 20
val DEFAULT_HEIGHT: Int = isDensity440.judge(517, 483)

@Composable
fun AiWritingAssistantScreen(
    navController: NavController,
    journalId: Long,
    journalTitle: String,
    coverId: Long,
    configInfoJson: String,
    imageGroups: List<TravelDiaryImageGroup>,
    needNavigate: Boolean = true,
    onDismiss: (isBack: Boolean) -> Unit
) {
    val viewModel = viewModel<AiWritingAssistantViewModel>()
    viewModel.imageGroups = imageGroups
    val uiState = viewModel.uiState.collectAsState().value
    val context = LocalContext.current
    val loginHandler = rememberLoginHandler()
    val coroutineScope = rememberCoroutineScope()
    var loginCheckJob: Job? = null
    val keyboardHeight: Dp = rememberKeyboardHeightDp()
    val isKeyboardOpen = keyboardHeight.value > 0
    val scrollState = rememberScrollState()

    DisposableEffect(Unit) {
        onDispose {
            viewModel.resetData()
        }
    }

    BottomSheet(
        originalHeight = uiState.screenHeight.dp,
        visible = true,
        onDismissRequest = { onDismiss.invoke(false) },
        onDismissCallback = { }
    ) {
        BackHandler {
            onDismiss.invoke(true)
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Text(
                modifier = Modifier
                    .padding(bottom = 16.dp)
                    .padding(horizontal = 16.dp),
                text = stringResource(R.string.title_writing_assistant),
                fontSize = 20.sp,
                fontWeight = FontWeight.Medium,
                color = TclTheme.colorScheme.tctStanderTextPrimary
            )

            Column(
                modifier = Modifier
                    .verticalScroll(scrollState)
                    .verticalScrollbar(state = scrollState, offsetX = 2.dp.toPx)
                    .fillMaxWidth()
                    .weight(1F)
            ) {
                WritingStyleOptionPanel()

                PeerCompanionOptionPanel()

                WordsNumberOptionPanel()
            }

            Text(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .height(32.dp),
                text = stringResource(R.string.label_special_event),
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal,
                color = TclTheme.colorScheme.tctStanderTextPrimary
            )

            Row(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(bottom = if (isKeyboardOpen) 16.dp else 20.dp + getNavBarHeight())
            ) {
                SearchPanel(
                    modifier = Modifier
                        .height(44.dp)
                        .weight(1F),
                    placeholder = stringResource(R.string.tip_enter_special_event),
                    maxInputLength = MAX_SPECIAL_EVENT_LENGTH,
                    onInputExceed = {
                        ToastUtils.makeWithCancel(
                            R.string.toast_limit_enter_special_event_length,
                            length = Toast.LENGTH_SHORT
                        )
                    },
                    onQueryChange = { viewModel.onSpecialEventInput(it) },
                    onKeyboardVisible = { }
                )

                TclButton(
                    modifier = Modifier
                        .padding(start = 8.dp)
                        .height(44.dp)
                        .width(120.dp),
                    colors = ButtonColors(
                        containerColor = TclTheme.colorScheme.tctStanderAccentPrimary,
                        contentColor = TclTheme.colorScheme.tctStanderTextButton,
                        disabledContainerColor = TclTheme.colorScheme.tctStanderAccentPrimary,
                        disabledContentColor = TclTheme.colorScheme.tctStanderTextButton
                    ),
                    contentPadding = PaddingValues(horizontal = 16.dp),
                    onClick = {
                        if (!NetworkUtils.isNetworkAvailable(context)) {
                            val message = context.resources.getString(R.string.network_exception)
                            ToastUtil.show(message = message)
                            return@TclButton
                        }
                        loginCheckJob = coroutineScope.launch {
                            if (AccountController.getLoginState()) {
                                val companion = uiState.currentCompanion
                                if ((companion is PeerCompanion.Other) && (uiState.customCompanion.isEmpty())) {
                                    viewModel.showEmptyInputTip()
                                    return@launch
                                }
                                viewModel.generate()
                                val previousRoute = navController.previousBackStackEntry?.destination?.route
                                if (previousRoute?.contains("journal_content_screen") == true) {
                                    navController.previousBackStackEntry?.savedStateHandle?.set("configInfoJson", configInfoJson)
                                    navController.navigateUp()
                                } else if (needNavigate) {
                                    navController.navigate("journal_content_screen?journalId=$journalId&journalTitle=$journalTitle&coverId=$coverId&configInfoJson=${configInfoJson}") {
                                        navController.currentDestination?.route?.let {
                                            popUpTo(it) { inclusive = true }
                                        }
                                    }
                                }
                                onDismiss.invoke(false)
                            } else {
                                loginHandler {

                                }
                            }
                        }
                    }
                ) {
                    Text(text = stringResource(R.string.btn_text_generate))
                }
            }
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            if (loginCheckJob?.isActive == true) {
                loginCheckJob?.cancel()
            }
            ToastUtil.release()
        }
    }
}

@Composable
private fun ColumnScope.WritingStyleOptionPanel() {
    val viewModel = viewModel<AiWritingAssistantViewModel>()
    val uiState = viewModel.uiState.collectAsState().value
    SelectOptionsPanel(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        typeName = stringResource(R.string.style_title),
        options = uiState.styleOptions.map { it.nameResId },
        currentIndex = uiState.styleOptions.indexOf(uiState.currentStyle),
        onSelected = { viewModel.onStyleSelected(uiState.styleOptions[it]) }
    )
}

@Composable
private fun ColumnScope.PeerCompanionOptionPanel() {
    val viewModel = viewModel<AiWritingAssistantViewModel>()
    val uiState = viewModel.uiState.collectAsState().value
    var isKeyboardVisible by remember { mutableStateOf(false) }
    val paddingTop = 8
    val searchPanelHeight = 44
    val emptyInputTipHeight = 18

    SelectOptionsPanel(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(top = 16.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        typeName = stringResource(R.string.companions_title),
        options = uiState.companionOptions.map { it.nameResId },
        currentIndex = uiState.companionOptions.indexOf(uiState.currentCompanion),
        onSelected = { viewModel.onCompanionSelected(uiState.companionOptions[it]) }
    )

    if (uiState.currentCompanion is PeerCompanion.Other) {
        SearchPanel(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .padding(top = paddingTop.dp)
                .fillMaxWidth()
                .height(searchPanelHeight.dp),
            autoRequestFocus = true,
            placeholder = stringResource(R.string.tip_enter_peer_companion),
            maxInputLength = MAX_COMPANION_OTHER_LENGTH,
            onInputExceed = {},
            onQueryChange = { viewModel.onCompanionOtherInput(it) },
            onFocusChange = { viewModel.onCompanionOtherInputFocusChanged(it) },
            onKeyboardVisible = { isKeyboardVisible = it }
        )
        viewModel.onSizeChanged((paddingTop + searchPanelHeight + DEFAULT_HEIGHT))
        if (uiState.showEmptyInputTip) {
            Text(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .padding(start = 2.dp)
                    .wrapContentHeight()
                    .fillMaxWidth(),
                text = stringResource(R.string.tip_empty_peer_companion),
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal,
                color = Color(0xFFF3432F)
            )
            viewModel.onSizeChanged((paddingTop * 2 + searchPanelHeight + emptyInputTipHeight + DEFAULT_HEIGHT))
        } else {
            viewModel.onSizeChanged((paddingTop + searchPanelHeight + DEFAULT_HEIGHT))
        }
    } else {
        viewModel.onSizeChanged(DEFAULT_HEIGHT)
    }
}

@Composable
private fun ColumnScope.WordsNumberOptionPanel() {
    val viewModel = viewModel<AiWritingAssistantViewModel>()
    val uiState = viewModel.uiState.collectAsState().value
    SelectOptionsPanel(
        modifier = Modifier
            .padding(horizontal = 16.dp)
            .padding(top = 16.dp)
            .fillMaxWidth()
            .wrapContentHeight(),
        minItemWidth = 106.dp,
        typeName = stringResource(R.string.words_number_title),
        options = uiState.wordNumberOptions.map { it.nameResId },
        currentIndex = uiState.wordNumberOptions.indexOf(uiState.currentWordNumber),
        onSelected = { viewModel.onWordNumberSelected(uiState.wordNumberOptions[it]) }
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun SearchPanel(
    modifier: Modifier,
    autoRequestFocus: Boolean = false,
    maxInputLength: Int,
    placeholder: String,
    onQueryChange: (String) -> Unit,
    onInputExceed: () -> Unit,
    onFocusChange: (isFocus: Boolean) -> Unit = {},
    onKeyboardVisible: (isVisible: Boolean) -> Unit
) {
    var text by rememberSaveable { mutableStateOf("") }
    val context = LocalContext.current
    val view = LocalView.current
    var rootViewHeight by remember { mutableIntStateOf(0) }
    var firstFocusable by remember { mutableStateOf(false) }
    var isSearchViewFocused by remember { mutableStateOf(false) }

    DisposableEffect(view) {
        val rootView = (context as ComponentActivity).window.decorView
        val listener = ViewTreeObserver.OnGlobalLayoutListener {
            val rect = android.graphics.Rect()
            rootView.getWindowVisibleDisplayFrame(rect)
            val screenHeight = rootView.height
            val keypadHeight = screenHeight - rect.bottom
            val isKeyboardVisible = keypadHeight > screenHeight * 0.15
            onKeyboardVisible.invoke(isKeyboardVisible)
            rootViewHeight = rect.height()
        }
        rootView.viewTreeObserver.addOnGlobalLayoutListener(listener)
        onDispose {
            rootView.viewTreeObserver.removeOnGlobalLayoutListener(listener)
        }
    }

    Box(modifier = modifier.border(
        color = isSearchViewFocused.judge(TclTheme.colorScheme.tctStanderAccentPrimary,
            Color.Transparent), width = 1.dp,
            shape = RoundedCornerShape(percent = 50))
    ) {
        TclSearchBar(
            maxInputLength = maxInputLength,
            leadingIcon = { Spacer(Modifier.size(24.dp)) },
            barPadding = PaddingValues(horizontal = 0.dp, vertical = 0.dp),
            query = text,
            onQueryChange = {
                val codePoint = truncateVisibleCharacters(it, maxInputLength)
                text = codePoint
                if (codePoint.length >= maxInputLength) {
                    onInputExceed.invoke()
                } else {
                    onQueryChange.invoke(text.trimStart().trimEnd())
                }
            },
            placeholder = {
                Text(
                    text = placeholder,
                    color = TclTheme.colorScheme.tctStanderTextSecondary
                )
            },
            onSearch = {},
            expanded = false,
            onExpandedChange = {
                isSearchViewFocused = it
                if (it && !firstFocusable) {
                    firstFocusable = true
                }
                if (firstFocusable) {
                    onFocusChange.invoke(it)
                }
            },
            clearQueryContentDescription = null,
            excludeWindowInsets = false,
            autoRequestFocus = autoRequestFocus
        )
    }
}

@Composable
private fun rememberKeyboardHeight(): Int {
    val density = LocalDensity.current
    val insets = WindowInsets.ime.getBottom(density)
    return remember(insets) { insets }
}

@Composable
private fun rememberKeyboardHeightDp(): Dp {
    val heightPx = rememberKeyboardHeight()
    return with(LocalDensity.current) { heightPx.toDp() }
}
