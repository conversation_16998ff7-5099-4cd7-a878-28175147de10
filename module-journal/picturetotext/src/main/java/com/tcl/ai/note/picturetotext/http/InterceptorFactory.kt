package com.tcl.ai.note.picturetotext.http

import android.util.Log
import com.tcl.ai.note.utils.Logger
import okhttp3.Interceptor
import okhttp3.Request
import okhttp3.logging.HttpLoggingInterceptor
import okio.Buffer
import java.net.URL
import java.nio.charset.StandardCharsets
import java.security.InvalidKeyException
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.util.Base64
import java.util.UUID
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

class InterceptorFactory {

    companion object {
        private const val TAG = "InterceptorFactory"
        private const val TIMEOUT_CONNECT = 60 * 60 * 24 //1天
        private const val ALGORITHM: String = "tcl1-hmac-sha256"
        private const val APP_ID: String = "aiphone-api"
        private const val SECRET_PROD: String = "^6*!\$EgFCtdX5lDV0Nk6jfpNs6$\$GfoQ"
        private const val SECRET_TEST: String = "2wjr3ZYbj~Fs7f7bvqCiI2QlFe@p&J@h"
    }

    fun getLoggingInterceptor() = HttpLoggingInterceptor {
        //Logger.d(TAG, it)
    }.apply { level = HttpLoggingInterceptor.Level.BODY }

    /**
     * head interceptor
     */
    fun getHeadInterceptor() = Interceptor { chain ->
        val originalRequest: Request = chain.request()
        val request = originalRequest
            .newBuilder()
            .header("Content-Type", "application/json;charset:utf-8")
            .build()
        chain.proceed(request)
    }

    fun getTclApiInterceptor() = Interceptor { chain ->
        val originalRequest = chain.request()
        val app = originalRequest.header("xtcl-app")!!
        val appId = APP_ID
        val nonce = UUID.randomUUID().toString().replace("-", "")
        val timestamp = (System.currentTimeMillis() / 1000).toString()

        val map = mapOf(
            Pair("xtcl-app", app),
            Pair("xtcl-appid", appId),
            Pair("xtcl-nonce", nonce),
            Pair("xtcl-timestamp", timestamp)
        )

        val authorization = getAuthorization(originalRequest, map)

        val request = originalRequest.newBuilder()
            .addHeader("xtcl-appid", appId)
            .addHeader("xtcl-nonce", nonce)
            .addHeader("xtcl-timestamp", timestamp)
            .addHeader("xtcl-authorization", authorization)
            .build()
        chain.proceed(request)
    }

    private fun getAuthorization(originalRequest: Request, map: Map<String, String>): String {
        val canonicalHeaders = map.keys.sorted().joinToString(separator = "") { "${it.lowercase().trim()};" }
        val timestamp = map["xtcl-timestamp"] ?: ""
        val signUrl = "${ALGORITHM}\n${timestamp}\n${sha256(getCanonicalRequest(originalRequest, map))}"
        val signature = hmacSha256(SECRET_TEST, signUrl)
        val authorization = "${ALGORITHM} Credential=${map["xtcl-appid"]},SignedHeaders=${canonicalHeaders},Signature=$signature"
        return authorization
    }

    private fun getCanonicalRequest(request: Request, map: Map<String, String>): String {
        val httpRequestMethod = request.method.uppercase()
        val canonicalURI = URL(request.url.toString()).path
        var canonicalQueryString = request.url.query ?: ""

        if (httpRequestMethod == "GET" && canonicalQueryString.isNotEmpty()) {
            canonicalQueryString = canonicalQueryString.split("&")
                .sorted()
                .joinToString(separator = "\n") {
                    val (key, value) = it.split("=")
                    "${key.lowercase().trim()}:${value.lowercase().trim()};"
                }
        }

        val canonicalHeaders = map.keys.sorted().joinToString(separator = "") {
            val value = map[it]?.lowercase()?.trim()
            "${it.lowercase().trim()}:${value};"
        }

        var reqBody: String = ""
        request.body?.let {
            val buffer = Buffer()
            it.writeTo(buffer)
            reqBody = buffer.readUtf8()
        }
        val hashedRequestPayload = sha256(reqBody)
        Log.i(
            TAG, "$httpRequestMethod\n$canonicalURI\n$canonicalQueryString\n$canonicalHeaders\n$hashedRequestPayload"
        )
        return "$httpRequestMethod\n$canonicalURI\n$canonicalQueryString\n$canonicalHeaders\n$hashedRequestPayload"
    }

    /**
     * hmacSha256
     *
     * @param str 要加密的字符串
     * @return 加密后的字符串
     */
    @Throws(NoSuchAlgorithmException::class, InvalidKeyException::class)
    fun hmacSha256(secret: String, str: String): String {
        val sha256_HMAC = Mac.getInstance("HmacSHA256")
        val secretKey = SecretKeySpec(secret.toByteArray(StandardCharsets.UTF_8), "HmacSHA256")
        sha256_HMAC.init(secretKey)
        val hash = sha256_HMAC.doFinal(str.toByteArray(StandardCharsets.UTF_8))
        return Base64.getEncoder().encodeToString(hash)
    }

    /**
     * sha256加密
     *
     * @param str 要加密的字符串
     * @return 加密后的字符串
     */
    @Throws(NoSuchAlgorithmException::class)
    fun sha256(str: String): String {
        val messageDigest = MessageDigest.getInstance("SHA-256")
        messageDigest.update(str.toByteArray(StandardCharsets.UTF_8))
        return Base64.getEncoder().encodeToString(messageDigest.digest())
    }
}