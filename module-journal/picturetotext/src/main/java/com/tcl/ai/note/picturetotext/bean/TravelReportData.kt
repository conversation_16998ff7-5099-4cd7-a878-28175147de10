package com.tcl.ai.note.picturetotext.bean

import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Parcelize
data class TravelReportData(
    val createType: Int, // 0 for add, 1 for regenerate
    val style: String,
    val companion: String,
    val wordCount: String,
    val specialEventFilled: String,
    val isAdopted: Boolean,
    val operationType: Int,
    val generateStartTime: Long = System.currentTimeMillis(), // Time when the generation started
    val generateEndTime: Long = System.currentTimeMillis(), // Time when the generation ended
    val newPageStartTime: Long = System.currentTimeMillis(), // Time when the new page started
) : Parcelable