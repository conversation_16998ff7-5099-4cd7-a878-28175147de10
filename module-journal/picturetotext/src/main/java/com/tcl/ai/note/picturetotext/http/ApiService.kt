package com.tcl.ai.note.picturetotext.http

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.picturetotext.bean.TravelDiaryGenerateRequest
import com.tcl.ai.note.picturetotext.bean.TravelDiaryGenerateResponse
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

interface ApiService {

/*
    @POST("v/v4/assistants/travelDiary/Generation")
*/
    @POST("v1/assistants/travelDiary/Generation")
    suspend fun generateTravelDiary(
        @Header("Authorization") authorization: String,
        @Header("xtcl-app") packageName: String = GlobalContext.instance.packageName,
        @Body requestBody: TravelDiaryGenerateRequest
    ): TravelDiaryGenerateResponse

    companion object {
        fun get(): ApiService {
            return RetrofitClient.instance.create(ApiService::class.java)
        }
    }
}