package com.tcl.ai.note.picturetotext

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Build
import android.util.Base64
import android.util.Log
import com.google.gson.Gson
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.picturetotext.bean.Image
import com.tcl.ai.note.picturetotext.bean.ImageGroup
import com.tcl.ai.note.picturetotext.bean.ServerCode
import com.tcl.ai.note.picturetotext.bean.Status
import com.tcl.ai.note.picturetotext.bean.TextConstraint
import com.tcl.ai.note.picturetotext.bean.TravelDiaryGenerateRequest
import com.tcl.ai.note.picturetotext.bean.TravelDiaryGenerateResponse
import com.tcl.ai.note.picturetotext.bean.TravelDiaryGenerateResult
import com.tcl.ai.note.picturetotext.bean.TravelDiaryImageGroup
import com.tcl.ai.note.picturetotext.bean.TravelReportData
import com.tcl.ai.note.picturetotext.http.ApiService
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getAIApiLangCode
import com.tcl.ai.note.utils.getSystemProperty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import retrofit2.HttpException
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.security.MessageDigest
import java.util.Locale

class TravelDiaryRepository {
    companion object {
        const val TAG = "TravelDiaryRepository"
        val instance: TravelDiaryRepository = TravelDiaryRepository()
    }

    private val workScope: CoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var generateJob: Job? = null

    val generateResult = MutableSharedFlow<TravelDiaryGenerateResult?>(replay = 1)
    var imageGroups = mutableListOf<TravelDiaryImageGroup>()

    fun generateTravelDiary(
        travelDiaryImageGroups: List<TravelDiaryImageGroup>?,
        writingStyle: String,
        peerCompanion: String,
        wordNumberRatio: Float,
        customPeerCompanion: String = "",
        specialEvent: String = "",
    ) {
        if ((travelDiaryImageGroups == null) || (travelDiaryImageGroups.isEmpty())) {
            Logger.i(TAG, "generateTravelDiary:ImageGroups is null.")
            generateResult.tryEmit(TravelDiaryGenerateResult(status = Status.Failure(errorCode = ServerCode.EXCEPTION, errorMessage = "ImageGroups is null")))
            return
        }
        imageGroups = travelDiaryImageGroups.toMutableList()
        Logger.w(TAG, "imageGroups :$imageGroups")
        generateJob = workScope.launch(Dispatchers.IO) {
            generateResult.tryEmit(TravelDiaryGenerateResult(status = Status.Start))
            var response: TravelDiaryGenerateResponse? = null
            try {
                val authorization = AccountController.token
                val outputLanguageCode = getAIApiLangCode()
                val serialNo = serialNumber()
                val projectName = productName()
                val countryCode = AccountController.countryCode
                val devCountryCode = Locale.getDefault().country
                val imageGroups: MutableList<ImageGroup> = mutableListOf()

                for (i in 0 until travelDiaryImageGroups.size) {
                    val images: MutableList<Image> = mutableListOf()
                    val travelDiaryImageGroup = travelDiaryImageGroups[i]
                    for (i in 0 until travelDiaryImageGroup.images.size) {
                        val travelDiaryImage = travelDiaryImageGroup.images[i]
                        compressImageToBase64(imageUri = travelDiaryImage.uri)?.let {
                            val image = Image(content = it, createdTime = travelDiaryImage.createdTime, location = travelDiaryImage.location)
                            images.add(image)
                        }
                    }
                    val imageGroup =
                        ImageGroup(images = images, textConstraint = TextConstraint(length = (travelDiaryImageGroup.textLength * wordNumberRatio).toInt()))
                    imageGroups.add(imageGroup)
                }

                val request = TravelDiaryGenerateRequest(
                    imageGroups = imageGroups,
                    sn = serialNo,
                    devCountryCode = devCountryCode,
                    countryCode = countryCode,
                    projectName = projectName,
                    specialEvent = specialEvent,
                    customPeerCompanion = customPeerCompanion,
                    peerCompanion = peerCompanion,
                    writingStyle = writingStyle,
                    outputLanguageCode = outputLanguageCode,
                )
                Logger.i(TAG, "generateTravelDiary:request=${request}")
                response = ApiService.get().generateTravelDiary(authorization = authorization, requestBody = request)
                Logger.i(TAG, "generateTravelDiary:response=${response}")
                if (generateJob?.isActive == true) {
                    if (response.code == ServerCode.SUCCESS) {
                        generateResult.emit(TravelDiaryGenerateResult(status = Status.Success, content = response.data?.generatedTexts))
                    } else {
                        generateResult.emit(TravelDiaryGenerateResult(status = Status.Failure(errorCode = response.code, errorMessage = response.message)))
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, "generateTravelDiary:Exception=${e.message}")
                if (generateJob?.isActive == true) {
                    when (e) {
                        is HttpException -> {
                            // 尝试解析错误body
                            val errorBody = e.response()?.errorBody()?.string()
                            Logger.e(TAG, "generateTravelDiary:HttpException errorBody=${errorBody}")
                            Gson().fromJson(errorBody, TravelDiaryGenerateResponse::class.java)?.let {
                                generateResult.emit(
                                    TravelDiaryGenerateResult(
                                        status = Status.Failure(
                                            errorCode = it.code,
                                            errorMessage = errorBody?: ""
                                        )
                                    )
                                )
                            } ?: generateResult.emit(
                                TravelDiaryGenerateResult(
                                    status = Status.Failure(
                                        errorCode = ServerCode.EXCEPTION,
                                        errorMessage = e.message ?: ""
                                    )
                                )
                            )
                        }

                        else -> {
                            generateResult.emit(
                                TravelDiaryGenerateResult(
                                    status = Status.Failure(
                                        errorCode = ServerCode.EXCEPTION,
                                        errorMessage = e.message ?: ""
                                    )
                                )
                            )
                        }
                    }
                }
            }
        }
    }

    fun stopTravelDiaryGeneration() {
        generateJob?.cancel()
        generateResult.tryEmit(TravelDiaryGenerateResult(status = Status.Stop))
    }

    fun compressImageToBase64(context: Context = GlobalContext.instance, imageUri: Uri): String? {
        return try {
            val inputStream: InputStream? = context.contentResolver.openInputStream(imageUri)
            inputStream?.use { stream ->
                val options = BitmapFactory.Options().apply {
                    inJustDecodeBounds = true
                }
                BitmapFactory.decodeStream(stream, null, options)
                stream.close()
                val sampleSize = calculateSampleSize(options, 1024, 1024)
                val decodeOptions = BitmapFactory.Options().apply {
                    inSampleSize = sampleSize
                }
                val newStream = context.contentResolver.openInputStream(imageUri)
                val bitmap = BitmapFactory.decodeStream(newStream, null, decodeOptions)
                newStream?.close()
                bitmap?.let { compressToTargetSize(it) }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    private fun calculateSampleSize(options: BitmapFactory.Options, reqWidth: Int, reqHeight: Int): Int {
        val (height: Int, width: Int) = options.run { outHeight to outWidth }
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val halfHeight: Int = height / 2
            val halfWidth: Int = width / 2
            while (halfHeight / inSampleSize >= reqHeight && halfWidth / inSampleSize >= reqWidth) {
                inSampleSize *= 2
            }
        }
        return inSampleSize
    }

    private fun compressToTargetSize(bitmap: Bitmap): String {
        val outputStream = ByteArrayOutputStream()
        var quality = 85
        do {
            outputStream.reset()
            bitmap.compress(Bitmap.CompressFormat.JPEG, quality, outputStream)
            quality -= 5
        } while (outputStream.size() > 60 * 1024 && quality > 10)

        val byteArray = outputStream.toByteArray()
        return "data:image/jpeg;base64," + Base64.encodeToString(byteArray, Base64.NO_WRAP)
    }

    fun productName(): String {
        val productName = getSystemProperty("ro.product.device", "")
        return productName.ifEmpty { "Goldfinch_NP_Pro" }
    }

    fun serialNumber(): String {
        var serialNumber = ""
        try {
            serialNumber = Build.getSerial()
        } catch (e: Exception) {
            Log.w(TAG, "serialNumber exception: ${e.message}")
        }
        serialNumber = serialNumber.ifEmpty { "JFOVFQHIFE9DUKDI" }
        val md = MessageDigest.getInstance("MD5")
        val messageDigest = md.digest(serialNumber.toByteArray())
        val sb = StringBuilder()
        for (b in messageDigest) {
            sb.append(String.format("%02x", b))
        }
        return sb.toString()
    }

    var travelReportData = MutableSharedFlow<TravelReportData?>(replay = 1)
    fun reportAIWritingSettings(reportData: TravelReportData) {
        travelReportData.tryEmit(reportData)
    }
}