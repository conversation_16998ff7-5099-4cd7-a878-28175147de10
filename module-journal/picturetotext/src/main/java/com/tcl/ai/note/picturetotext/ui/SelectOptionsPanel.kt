package com.tcl.ai.note.picturetotext.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.utils.judge
import com.tct.theme.core.designsystem.theme.TclTheme

@Composable
fun SelectOptionsPanel(
    modifier: Modifier,
    minItemWidth: Dp = 64.dp,
    typeName: String,
    options: List<Int>,
    currentIndex: Int,
    darkTheme: Boolean = isSystemInDarkTheme(),
    onSelected: (index: Int) -> Unit
) {
    val lazyListState = rememberLazyListState()
    val startIndex = if ((currentIndex >= 2) && (currentIndex < options.size)) (currentIndex - 1) else 0

    LaunchedEffect(startIndex) {
        lazyListState.animateScrollToItem(startIndex)
    }

    Column(modifier = modifier) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .height(32.dp),
            text = typeName,
            fontSize = 14.sp,
            lineHeight = 16.sp,
            fontWeight = FontWeight.Normal,
            color = TclTheme.colorScheme.tctStanderTextPrimary
        )

        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .height(48.dp)
                .clip(shape = RoundedCornerShape(percent = 50))
                .background(darkTheme.judge(
                    Color.White.copy(alpha = 0.08f),
                    Color.White,
                )),
            state = lazyListState,
            userScrollEnabled = true,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            itemsIndexed(options) { index, option ->
                Box(
                    modifier = Modifier
                        .padding(vertical = 4.dp)
                        .padding(start = 4.dp, end = if (index == options.size - 1) 4.dp else 0.dp)
                        .widthIn(min = minItemWidth)
                        .fillMaxHeight()
                        .clip(shape = RoundedCornerShape(percent = 50))
                        .background(color = if (index == currentIndex) {
                            darkTheme.judge(
                                Color(0xFF3B3C3E),
                                Color(0x14155BF0),
                            )
                        } else {
                            Color.Transparent
                        })
                        .clickable { onSelected.invoke(index) },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        text = stringResource(option),
                        fontSize = 14.sp,
                        fontWeight = if (index == currentIndex) FontWeight.Medium else FontWeight.Normal,
                        color = if (index == currentIndex) {
                            TclTheme.colorScheme.tctStanderTextPrimary
                        } else {
                            darkTheme.judge(
                                Color.White.copy(alpha = 0.5f),
                                Color.Black.copy(alpha = 0.7f),
                            )
                        }
                    )
                }
            }
        }
    }
}