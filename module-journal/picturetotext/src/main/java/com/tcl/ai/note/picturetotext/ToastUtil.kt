package com.tcl.ai.note.picturetotext

import android.content.Context
import android.os.Looper
import android.widget.Toast
import com.tcl.ai.note.GlobalContext

object ToastUtil {
    private var toast: Toast? = null
    private var toastShow: Boolean = false

    private val toastCallback = object : Toast.Callback() {
        override fun onToastShown() {
            super.onToastShown()
            toastShow = true
        }

        override fun onToastHidden() {
            super.onToastHidden()
            toastShow = false
        }
    }

    fun show(context: Context = GlobalContext.instance, message: String, duration: Int = Toast.LENGTH_LONG) {
        if (toast == null) {
            toast = Toast.makeText(context, message, duration)
            toast?.addCallback(toastCallback)
        }
        if (!toastShow) {
            toast?.show()
        }
    }

    fun release() {
        toast?.cancel()
        toast?.removeCallback(toastCallback)
        toastShow = false
    }
}