package com.tcl.ai.note.picturetotext.ui

import android.annotation.SuppressLint
import androidx.activity.BackEventCompat
import androidx.activity.compose.PredictiveBackHandler
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.FiniteAnimationSpec
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Indication
import androidx.compose.foundation.MutatorMutex
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsFocusedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.MutableWindowInsets
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.calculateEndPadding
import androidx.compose.foundation.layout.calculateStartPadding
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.onConsumedWindowInsetsChanged
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.text.input.TextFieldState
import androidx.compose.foundation.text.input.clearText
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.IconButtonDefaults
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.LocalUseFallbackRippleImplementation
import androidx.compose.material3.SearchBarColors
import androidx.compose.material3.SearchBarDefaults
import androidx.compose.material3.SearchBarDefaults.inputFieldColors
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextFieldColors
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.contentColorFor
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableFloatState
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.structuralEqualityPolicy
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusEventModifierNode
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.FocusState
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.RoundRect
import androidx.compose.ui.geometry.toRect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.takeOrElse
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.node.ModifierNodeElement
import androidx.compose.ui.node.ObserverModifierNode
import androidx.compose.ui.node.observeReads
import androidx.compose.ui.platform.InspectorInfo
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.onClick
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.constrainHeight
import androidx.compose.ui.unit.constrainWidth
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.fastFirst
import androidx.compose.ui.util.fastFirstOrNull
import androidx.compose.ui.util.lerp
import androidx.compose.ui.zIndex
import com.tct.theme.core.designsystem.R
import com.tct.theme.core.designsystem.icons.TclIcons
import com.tct.theme.core.designsystem.icons.automirrored.Search
import com.tct.theme.core.designsystem.theme.TclTheme
import kotlinx.coroutines.delay
import kotlin.coroutines.cancellation.CancellationException
import kotlin.math.abs
import kotlin.math.max
import kotlin.math.min
import kotlin.math.roundToInt
import kotlin.math.sign

private const val LayoutIdInputField = "InputField"
private const val LayoutIdSurface = "Surface"
private const val LayoutIdSearchContent = "Content"
private const val LayoutIdAction = "Action"
internal val SearchBarVerticalPadding: Dp = 6.dp
private const val SearchBarPredictiveBackMinScale: Float = 9f / 10f
private val SearchBarPredictiveBackMinMargin: Dp = 8.dp

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun SearchBarLayout(
    animationProgress: Animatable<Float, AnimationVector1D>,
    finalBackProgress: MutableFloatState,
    firstBackEvent: MutableState<BackEventCompat?>,
    currentBackEvent: MutableState<BackEventCompat?>,
    modifier: Modifier,
    windowInsets: WindowInsets,
    excludeWindowInsets: Boolean,
    barPadding: PaddingValues,
    inputField: @Composable () -> Unit,
    surface: @Composable () -> Unit,
    actions: @Composable (() -> Unit)? = null,
    content: (@Composable () -> Unit)?,
) {
    val layoutDirection = LocalLayoutDirection.current

    // `Modifier.windowInsetsPadding` does not support animation,
    // so the insets are converted to paddings in the Layout's MeasureScope
    // and the animation calculations are done manually.
    val unconsumedInsets = remember { MutableWindowInsets() }
    Layout(
        modifier = if (excludeWindowInsets) {
            modifier
                .zIndex(1f)
                .onConsumedWindowInsetsChanged { consumedInsets ->
                    unconsumedInsets.insets = windowInsets.exclude(consumedInsets)
                }
                .consumeWindowInsets(windowInsets)
        } else modifier,
        content = {
            Box(Modifier.layoutId(LayoutIdSurface), propagateMinConstraints = true) { surface() }
            Box(Modifier.layoutId(LayoutIdInputField), propagateMinConstraints = true) {
                inputField()
            }
            actions?.let { action ->
                Box(Modifier.layoutId(LayoutIdAction), propagateMinConstraints = true) {
                    action()
                }
            }
            content?.let { content ->
                Box(Modifier.layoutId(LayoutIdSearchContent), propagateMinConstraints = true) {
                    content()
                }
            }
        },
    ) { measurables, constraints ->
        @Suppress("NAME_SHADOWING") val animationProgress = animationProgress.value

        val inputFieldMeasurable = measurables.fastFirst { it.layoutId == LayoutIdInputField }
        val surfaceMeasurable = measurables.fastFirst { it.layoutId == LayoutIdSurface }
        val actionsMeasurable = measurables.fastFirstOrNull { it.layoutId == LayoutIdAction }
        val contentMeasurable = measurables.fastFirstOrNull { it.layoutId == LayoutIdSearchContent }

        val topPadding = unconsumedInsets.getTop(this) + SearchBarVerticalPadding.roundToPx()
        val bottomPadding = SearchBarVerticalPadding.roundToPx()

        val actionsMinWidth = actionsMeasurable?.minIntrinsicWidth(constraints.maxHeight) ?: 0

        val defaultStartWidth =
            constraints.constrainWidth(
                inputFieldMeasurable.maxIntrinsicWidth(constraints.maxHeight),
            )
        val defaultStartHeight =
            constraints.constrainHeight(
                inputFieldMeasurable.minIntrinsicHeight(constraints.maxWidth),
            )

        val predictiveBackStartWidth =
            (constraints.maxWidth * SearchBarPredictiveBackMinScale).roundToInt()
        val predictiveBackStartHeight =
            (constraints.maxHeight * SearchBarPredictiveBackMinScale).roundToInt()
        val predictiveBackMultiplier =
            calculatePredictiveBackMultiplier(
                currentBackEvent.value,
                animationProgress,
                finalBackProgress.floatValue,
            )

        val startWidth = lerp(defaultStartWidth, predictiveBackStartWidth, predictiveBackMultiplier)
        val startHeight =
            lerp(
                topPadding + defaultStartHeight,
                predictiveBackStartHeight,
                predictiveBackMultiplier,
            )

        val maxWidth = constraints.maxWidth
        val maxHeight = constraints.maxHeight

        val minWidth = lerp(startWidth, maxWidth, animationProgress)
        val height = lerp(startHeight, maxHeight, animationProgress)

        // Note: animatedTopPadding decreases w.r.t. animationProgress
        val animatedTopPadding = lerp(topPadding, 0, animationProgress)
        val animatedBottomPadding = lerp(0, bottomPadding, animationProgress)
        val startPadding = barPadding.calculateStartPadding(layoutDirection).roundToPx()
        val endPadding = barPadding.calculateEndPadding(layoutDirection).roundToPx()
        val animatedStartPadding = lerp(startPadding, 0, animationProgress)
        val animatedEndPadding = lerp(endPadding, 0, animationProgress)

        val inputFieldPlaceable =
            inputFieldMeasurable.measure(
                Constraints(
                    minWidth = (minWidth - actionsMinWidth).coerceAtLeast(0),
                    maxWidth = (maxWidth - actionsMinWidth).coerceAtLeast(0),
                    minHeight = defaultStartHeight,
                    maxHeight = defaultStartHeight,
                ),
            )
        val width = inputFieldPlaceable.width

        // As the animation proceeds, the surface loses its padding
        // and expands to cover the entire container.
        val expandedDifWidth = actionsMinWidth * (animationProgress * 8).coerceAtMost(1f)
        val surfacePlaceable = surfaceMeasurable.measure(
            Constraints.fixed(
                maxWidth - actionsMinWidth + expandedDifWidth.roundToInt() - animatedStartPadding - animatedEndPadding,
                height - animatedTopPadding,
            ),
        )
        val actionsPlaceable = actionsMeasurable?.measure(
            Constraints(
                minWidth = actionsMeasurable.minIntrinsicWidth(constraints.maxHeight),
                maxWidth = maxWidth,
                minHeight = defaultStartHeight,
                maxHeight = defaultStartHeight,
            ),
        )
        val contentPlaceable =
            contentMeasurable?.measure(
                Constraints(
                    minWidth = width,
                    maxWidth = width,
                    minHeight = 0,
                    maxHeight =
                        if (constraints.hasBoundedHeight) {
                            (constraints.maxHeight -
                                    (topPadding + defaultStartHeight + bottomPadding))
                                .coerceAtLeast(0)
                        } else {
                            constraints.maxHeight
                        },
                ),
            )

        layout(width, height + ((1 - animationProgress) * bottomPadding).roundToInt()) {
            val minOffsetMargin = SearchBarPredictiveBackMinMargin.roundToPx()
            val predictiveBackOffsetX =
                calculatePredictiveBackOffsetX(
                    constraints = constraints,
                    minMargin = minOffsetMargin,
                    currentBackEvent = currentBackEvent.value,
                    layoutDirection = layoutDirection,
                    progress = animationProgress,
                    predictiveBackMultiplier = predictiveBackMultiplier,
                )
            val predictiveBackOffsetY =
                calculatePredictiveBackOffsetY(
                    constraints = constraints,
                    minMargin = minOffsetMargin,
                    currentBackEvent = currentBackEvent.value,
                    firstBackEvent = firstBackEvent.value,
                    height = height,
                    maxOffsetY = SearchBarPredictiveBackMaxOffsetY.roundToPx(),
                    predictiveBackMultiplier = predictiveBackMultiplier,
                )

            surfacePlaceable.placeRelative(
                predictiveBackOffsetX + animatedStartPadding,
                predictiveBackOffsetY + animatedTopPadding,
            )
            inputFieldPlaceable.placeRelative(
                predictiveBackOffsetX,
                predictiveBackOffsetY + topPadding,
            )
            actionsPlaceable?.placeRelative(
                maxWidth - endPadding - actionsMinWidth,
                predictiveBackOffsetY + topPadding,
            )
            contentPlaceable?.placeRelative(
                predictiveBackOffsetX,
                predictiveBackOffsetY +
                        topPadding +
                        inputFieldPlaceable.height +
                        animatedBottomPadding,
            )
        }
    }
}


private fun calculatePredictiveBackMultiplier(
    currentBackEvent: BackEventCompat?,
    progress: Float,
    finalBackProgress: Float,
) =
    when {
        currentBackEvent == null -> 0f // Not in predictive back at all.
        finalBackProgress.isNaN() -> 1f // User is currently swiping predictive back.
        finalBackProgress <= 0 -> 0f // Safety check for divide by zero.
        else -> progress / finalBackProgress // User has released predictive back swipe.
    }

private fun calculatePredictiveBackOffsetX(
    constraints: Constraints,
    minMargin: Int,
    currentBackEvent: BackEventCompat?,
    layoutDirection: LayoutDirection,
    progress: Float,
    predictiveBackMultiplier: Float,
): Int {
    if (currentBackEvent == null || predictiveBackMultiplier == 0f) {
        return 0
    }
    val directionMultiplier = if (currentBackEvent.swipeEdge == BackEventCompat.EDGE_LEFT) 1 else -1
    val rtlMultiplier = if (layoutDirection == LayoutDirection.Ltr) 1 else -1
    val maxOffsetX = (constraints.maxWidth * SearchBarPredictiveBackMaxOffsetXRatio) - minMargin
    val interpolatedOffsetX = maxOffsetX * (1 - progress)
    return (interpolatedOffsetX * predictiveBackMultiplier * directionMultiplier * rtlMultiplier)
        .roundToInt()
}

private fun calculatePredictiveBackOffsetY(
    constraints: Constraints,
    minMargin: Int,
    currentBackEvent: BackEventCompat?,
    firstBackEvent: BackEventCompat?,
    height: Int,
    maxOffsetY: Int,
    predictiveBackMultiplier: Float,
): Int {
    if (firstBackEvent == null || currentBackEvent == null || predictiveBackMultiplier == 0f) {
        return 0
    }
    val availableVerticalSpace = max(0, (constraints.maxHeight - height) / 2 - minMargin)
    val adjustedMaxOffsetY = min(availableVerticalSpace, maxOffsetY)
    val yDelta = currentBackEvent.touchY - firstBackEvent.touchY
    val yProgress = abs(yDelta) / constraints.maxHeight
    val directionMultiplier = sign(yDelta)
    val interpolatedOffsetY = lerp(0, adjustedMaxOffsetY, yProgress)
    return (interpolatedOffsetY * predictiveBackMultiplier * directionMultiplier).roundToInt()
}

private const val SearchBarPredictiveBackMaxOffsetXRatio: Float = 1f / 20f
private val SearchBarPredictiveBackMaxOffsetY: Dp = 24.dp

/**
 * A text field to input a query in a search bar
 *
 * @param query the query text to be shown in the input field.
 * @param onQueryChange the callback to be invoked when the input service updates the query. An
 *   updated text comes as a parameter of the callback.
 * @param onSearch the callback to be invoked when the input service triggers the
 *   [ImeAction.Search] action. The current [query] comes as a parameter of the callback.
 * @param expanded whether the search bar is expanded and showing search results.
 * @param onExpandedChange the callback to be invoked when the search bar's expanded state is
 *   changed.
 * @param modifier the [Modifier] to be applied to this input field.
 * @param enabled the enabled state of this input field. When `false`, this component will not
 *   respond to user input, and it will appear visually disabled and disabled to accessibility
 *   services.
 * @param placeholder the placeholder to be displayed when the [query] is empty.
 * @param leadingIcon the leading icon to be displayed at the start of the input field.
 * @param trailingIcon the trailing icon to be displayed at the end of the input field.
 * @param colors [TextFieldColors] that will be used to resolve the colors used for this input
 *   field in different states. See [SearchBarDefaults.inputFieldColors].
 * @param interactionSource an optional hoisted [MutableInteractionSource] for observing and
 *   emitting [Interaction]s for this input field. You can use this to change the search bar's
 *   appearance or preview the search bar in different states. Note that if `null` is provided,
 *   interactions will still happen internally.
 */
@SuppressLint("DesignSystem")
@ExperimentalMaterial3Api
@Composable
private fun InputField(
    query: String,
    maxInputLength: Int = Int.MAX_VALUE,
    onQueryChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    placeholder: @Composable (() -> Unit)? = null,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    colors: TextFieldColors = inputFieldColors(),
    interactionSource: MutableInteractionSource? = null,
    clearQueryContentDescription: String? = null,
    autoRequestFocus: Boolean = false
) {
    @Suppress("NAME_SHADOWING")
    val interactionSource = interactionSource ?: remember { MutableInteractionSource() }

    val focused = interactionSource.collectIsFocusedAsState().value
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current

//    val searchSemantics = getString(Strings.SearchBarSearch)
//    val suggestionsAvailableSemantics = getString(Strings.SuggestionsAvailable)

    val textColor =
        LocalTextStyle.current.color.takeOrElse {
            TclTheme.colorScheme.tctStanderTextPrimary
//            colors.textColor(enabled, isError = false, focused = focused)
        }

    Box(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 4.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            leadingIcon?.let {
                it()
            }

            BasicTextField(
                value = query,
                onValueChange = {
                    onQueryChange(it)
                },
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .focusRequester(focusRequester)
                    .onFocusChanged {
                        onExpandedChange(it.isFocused)
                    }
                    .semantics {
                        onClick {
                            focusRequester.requestFocus()
                            true
                        }
                    },
                enabled = enabled,
                singleLine = true,
                textStyle = LocalTextStyle.current.merge(TextStyle(color = textColor, fontSize = 14.sp)),
                cursorBrush = SolidColor(TclTheme.colorScheme.tctStanderAccentPrimary),
                keyboardOptions = KeyboardOptions(imeAction = ImeAction.Send),
                keyboardActions = KeyboardActions {
                    onSearch(query)
                },
                decorationBox = { innerTextField ->
                    TextFieldDefaults.DecorationBox(
                        value = query,
                        innerTextField = innerTextField,
                        enabled = enabled,
                        singleLine = true,
                        visualTransformation = VisualTransformation.None,
                        interactionSource = interactionSource,
                        placeholder = placeholder,
                        shape = SearchBarDefaults.inputFieldShape,
                        colors = colors,
                        contentPadding = PaddingValues(),
                        container = {},
                    )
                }
            )

            Box(Modifier.heightIn(max = 44.dp)) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    if (query.isNotBlank()) {
                        Icon(
                            Icons.Filled.Close,
                            contentDescription = clearQueryContentDescription,
                            modifier = Modifier
                                .size(40.dp)
                                .clickable(
                                    indication = rippleOrFallbackImplementation(
                                        bounded = false,
                                        radius = 18.dp
                                    ),
                                    interactionSource = null,
                                ) {
                                    onQueryChange.invoke("")
                                }
                                .padding(10.dp)
                                .background(
                                    color = TclTheme.colorScheme.tctStanderTextSecondary,
                                    shape = CircleShape,
                                )
                                .padding(2.dp),
                            tint = TclTheme.colorScheme.tctStanderBgBasic,
                        )
                    }

                    trailingIcon?.invoke()
                }
            }
        }
    }

    LaunchedEffect(Unit) {
        if (autoRequestFocus) {
            delay(AnimationDelayMillis.toLong())
            focusRequester.requestFocus()
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            focusManager.clearFocus()
        }
    }
}

private const val AnimationDelayMillis: Int = 100

private const val AnimationExitDurationMillis: Int = 350
private val AnimationExitEasing = CubicBezierEasing(0.0f, 1.0f, 0.0f, 1.0f)
private val AnimationPredictiveBackExitFloatSpec: FiniteAnimationSpec<Float> =
    tween(
        durationMillis = AnimationExitDurationMillis,
        easing = AnimationExitEasing,
    )

/**
 * @param excludeWindowInsets 是否要算上系统栏的高度，
 *                            如果不是通用的紧挨着状态栏，而是具体页面业务定义的显示，应设置false
 *                            同时点击搜索框停留在当前页面而非展开抽屉
 * @param clearQueryContentDescription 清除搜索按钮的talkback内容
 */
@ExperimentalMaterial3Api
@Composable
fun TclSearchBar(
    query: String,
    maxInputLength: Int = Int.MAX_VALUE,
    autoRequestFocus: Boolean = false,
    onQueryChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    excludeWindowInsets: Boolean = true,
    barPadding: PaddingValues = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    clearQueryContentDescription: String?,
    leadingIcon: @Composable (() -> Unit)? = {
        IconButton(
            onClick = {},
            enabled = false,
            colors = IconButtonDefaults.iconButtonColors(disabledContentColor = LocalContentColor.current),
        ) {
            Icon(
                TclIcons.AutoMirrored.Filled.Search,
                contentDescription = stringResource(R.string.search),
            )
        }
    },
    placeholder: @Composable (() -> Unit)? = {
        Text(
            stringResource(R.string.search),
            color = TclTheme.colorScheme.tctStanderTextSecondary,
        )
    },
    trailingIcon: @Composable (() -> Unit)? = null,
    actions: @Composable (() -> Unit)? = null,
    colors: SearchBarColors = SearchBarDefaults.colors(
        dividerColor = Color.Transparent,
        containerColor = TclTheme.colorScheme.tctStanderBgLayout,
    ),
    content: @Composable ColumnScope.() -> Unit = {},
) {
    val density = LocalDensity.current
    val shape = SearchBarDefaults.inputFieldShape

    val animationProgress = remember { Animatable(initialValue = if (expanded) 1f else 0f) }

    val finalBackProgress = remember { mutableFloatStateOf(Float.NaN) }
    val firstBackEvent = remember { mutableStateOf<BackEventCompat?>(null) }
    val currentBackEvent = remember { mutableStateOf<BackEventCompat?>(null) }

    val defaultInputFieldShape = SearchBarDefaults.inputFieldShape
    val defaultFullScreenShape = SearchBarDefaults.fullScreenShape
    val useFullScreenShape by remember {
        derivedStateOf(structuralEqualityPolicy()) { animationProgress.value == 1f }
    }
    val animatedShape =
        remember(useFullScreenShape, shape) {
            when {
                shape == defaultInputFieldShape ->
                    // The shape can only be animated if it's the default spec value
                    GenericShape { size, _ ->
                        val radius =
                            with(density) {
                                (28.dp * (1 - animationProgress.value)).toPx()
                            }
                        addRoundRect(RoundRect(size.toRect(), CornerRadius(radius)))
                    }

                useFullScreenShape -> defaultFullScreenShape
                else -> shape
            }
        }

    LaunchedEffect(expanded) {
        val animationInProgress = animationProgress.value > 0 && animationProgress.value < 1
        val animationSpec =
            if (animationInProgress) tween<Float>(
                durationMillis = 350,
                easing = AnimationExitEasing,
            )
            else if (expanded) tween(
                durationMillis = 600,
                delayMillis = 100,
                easing = CubicBezierEasing(0.05f, 0.7f, 0.1f, 1.0f),
            ) else tween(
                durationMillis = 350,
                delayMillis = 100,
                easing = CubicBezierEasing(0.05f, 0.7f, 0.1f, 1.0f),
            )
        val targetValue = if (expanded) 1f else 0f
        if (animationProgress.value != targetValue) {
            animationProgress.animateTo(targetValue, animationSpec)
        }
        if (!expanded) {
            finalBackProgress.floatValue = Float.NaN
            firstBackEvent.value = null
            currentBackEvent.value = null
        }
    }

    val mutatorMutex = remember { MutatorMutex() }
    PredictiveBackHandler(enabled = expanded) { progress ->
        mutatorMutex.mutate {
            try {
                finalBackProgress.floatValue = Float.NaN
                progress.collect { backEvent ->
                    if (firstBackEvent.value == null) {
                        firstBackEvent.value = backEvent
                    }
                    currentBackEvent.value = backEvent
                    val interpolatedProgress =
                        CubicBezierEasing(0.1f, 0.1f, 0f, 1f).transform(backEvent.progress)
                    animationProgress.snapTo(targetValue = 1 - interpolatedProgress)
                }
                finalBackProgress.floatValue = animationProgress.value
                onExpandedChange(false)
            } catch (e: CancellationException) {
                animationProgress.animateTo(
                    targetValue = 1f,
                    animationSpec = AnimationPredictiveBackExitFloatSpec,
                )
                finalBackProgress.floatValue = Float.NaN
                firstBackEvent.value = null
                currentBackEvent.value = null
            }
        }
    }

    val windowInsets: WindowInsets = SearchBarDefaults.windowInsets

    val ld = LocalLayoutDirection.current
    val inputFieldPadding = PaddingValues(
        start = barPadding.calculateStartPadding(ld),
        end = barPadding.calculateEndPadding(ld),
    )
    val inputField = @Composable {
        InputField(
            modifier = Modifier
                .padding(inputFieldPadding)
                .fillMaxWidth()
                .heightIn(min = 44.dp)
                .wrapContentHeight(),
            query = query,
            maxInputLength = maxInputLength,
            onQueryChange = onQueryChange,
            onSearch = onSearch,
            expanded = expanded,
            onExpandedChange = onExpandedChange,
            placeholder = placeholder,
            leadingIcon = leadingIcon,
            trailingIcon = trailingIcon,
            clearQueryContentDescription = clearQueryContentDescription,
            autoRequestFocus = autoRequestFocus,
        )
    }

    val showContent by remember {
        derivedStateOf(structuralEqualityPolicy()) { animationProgress.value > 0 }
    }
    val wrappedContent: (@Composable () -> Unit)? =
        if (showContent) {
            {
                Column(Modifier.graphicsLayer { alpha = animationProgress.value }) {
                    HorizontalDivider(color = colors.dividerColor)
                    content()
                }
            }
        } else null

    SearchBarLayout(
        animationProgress = animationProgress,
        finalBackProgress = finalBackProgress,
        firstBackEvent = firstBackEvent,
        currentBackEvent = currentBackEvent,
        modifier = Modifier,
        windowInsets = windowInsets,
        excludeWindowInsets = excludeWindowInsets,
        inputField = inputField,
        barPadding = barPadding,
        surface = {
            Surface(
                shape = animatedShape,
                color = colors.containerColor,
                contentColor = contentColorFor(colors.containerColor),
                content = {},
            )
        },
        actions = actions,
        content = wrappedContent,
    )
}

@ExperimentalMaterial3Api
@Composable
fun TclSearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    clearQueryContentDescription: String?,
    barPadding: PaddingValues = PaddingValues(horizontal = 12.dp, vertical = 6.dp),
    leadingIcon: @Composable (() -> Unit)? = {
        IconButton(
            onClick = {},
            enabled = false,
            colors = IconButtonDefaults.iconButtonColors(disabledContentColor = LocalContentColor.current),
        ) {
            Icon(
                TclIcons.AutoMirrored.Filled.Search,
                contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.search_icon),
            )
        }
    },
    placeholder: @Composable (() -> Unit)? = {
        Text(
            stringResource(id = com.tcl.ai.note.base.R.string.search_icon),
            color = TclTheme.colorScheme.tctStanderTextSecondary,
        )
    },
    trailingIcon: @Composable (() -> Unit)? = null,
    actions: @Composable (() -> Unit)? = null,
    colors: SearchBarColors = SearchBarDefaults.colors(
        dividerColor = Color.Transparent,
        containerColor = TclTheme.colorScheme.tctStanderBgLayout,
    ),
    content: @Composable ColumnScope.() -> Unit = {},
) {
    TclSearchBar(
        query = query,
        onQueryChange = onQueryChange,
        onSearch = onSearch,
        clearQueryContentDescription = clearQueryContentDescription,
        excludeWindowInsets = false,
        barPadding = barPadding,
        expanded = false,
        onExpandedChange = {},
        leadingIcon = leadingIcon,
        placeholder = placeholder,
        trailingIcon = trailingIcon,
        actions = actions,
        colors = colors,
        content = content,
    )
}

internal class StateSyncingModifier(
    private val state: TextFieldState,
    private val value: TextFieldValue,
    private val onValueChanged: (TextFieldValue) -> Unit,
    private val writeSelectionFromTextFieldValue: Boolean,
) : ModifierNodeElement<StateSyncingModifierNode>() {

    override fun create(): StateSyncingModifierNode =
        StateSyncingModifierNode(state, onValueChanged, writeSelectionFromTextFieldValue)

    override fun update(node: StateSyncingModifierNode) {
        node.update(value, onValueChanged)
    }

    override fun equals(other: Any?): Boolean {
        // Always call update, without comparing the text. Update can compare more efficiently.
        return false
    }

    override fun hashCode(): Int {
        // Avoid calculating hash from values that can change on every recomposition.
        return state.hashCode()
    }

    override fun InspectorInfo.inspectableProperties() {
        // no inspector properties
    }
}

@OptIn(ExperimentalFoundationApi::class)
internal class StateSyncingModifierNode(
    private val state: TextFieldState,
    private var onValueChanged: (TextFieldValue) -> Unit,
    private val writeSelectionFromTextFieldValue: Boolean,
) : Modifier.Node(), ObserverModifierNode, FocusEventModifierNode {

    private var isFocused = false
    private var lastValueWhileFocused: TextFieldValue? = null

    override val shouldAutoInvalidate: Boolean
        get() = false

    /**
     * Synchronizes the latest [value] to the [TextFieldState] and updates our [onValueChanged]
     * callback. Should be called from [ModifierNodeElement.update].
     */
    fun update(value: TextFieldValue, onValueChanged: (TextFieldValue) -> Unit) {
        this.onValueChanged = onValueChanged

        // Don't modify the text programmatically while an edit session is in progress.
        // WARNING: While editing, the code that holds the external state is temporarily not the
        // actual source of truth. This "stealing" of control is generally an anti-pattern. We do it
        // intentionally here because text field state is very sensitive to timing, and if a state
        // update is delivered a frame late, it breaks text input. It is very easy to accidentally
        // introduce small bits of asynchrony in real-world scenarios, e.g. with Flow-based reactive
        // architectures. The benefit of avoiding that easy pitfall outweighs the weirdness in this
        // case.
        if (!isFocused) {
            updateState(value)
        } else {
            this.lastValueWhileFocused = value
        }
    }

    override fun onAttach() {
        // Don't fire the callback on first frame.
        observeTextState(fireOnValueChanged = false)
    }

    override fun onFocusEvent(focusState: FocusState) {
        if (this.isFocused && !focusState.isFocused) {
            // Lost focus, perform deferred synchronization.
            lastValueWhileFocused?.let(::updateState)
            lastValueWhileFocused = null
        }
        this.isFocused = focusState.isFocused
    }

    /** Called by the modifier system when the [TextFieldState] has changed. */
    override fun onObservedReadsChanged() {
        observeTextState()
    }

    private fun updateState(value: TextFieldValue) {
        state.edit {
            // Ideally avoid registering a state change if the text isn't actually different.
            // Take a look at `setTextIfChanged` implementation in TextFieldBuffer
            replace(0, length, value.text)

            // The BasicTextField2(String) variant can't push a selection value, so ignore it.
            if (writeSelectionFromTextFieldValue) {
                selection = value.selection
            }
        }
    }

    private fun observeTextState(fireOnValueChanged: Boolean = true) {
        lateinit var value: TextFieldValue
        observeReads {
            value = TextFieldValue(
                state.text.toString(),
                state.selection,
                state.composition
            )
        }

        // This code is outside of the observeReads lambda so we don't observe any state reads the
        // callback happens to do.
        if (fireOnValueChanged) {
            onValueChanged(value)
        }
    }
}

@Suppress("DEPRECATION_ERROR")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun rippleOrFallbackImplementation(
    bounded: Boolean = true,
    radius: Dp = Dp.Unspecified,
    color: Color = Color.Unspecified
): Indication {
    return if (LocalUseFallbackRippleImplementation.current) {
        rememberRipple(bounded, radius, color)
    } else {
        ripple(bounded, radius, color)
    }
}