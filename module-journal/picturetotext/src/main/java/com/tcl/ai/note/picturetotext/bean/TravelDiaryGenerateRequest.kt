package com.tcl.ai.note.picturetotext.bean

import com.google.gson.annotations.SerializedName

data class TravelDiaryGenerateRequest(
    @SerializedName("image_groups")
    val imageGroups: List<ImageGroup>,
    @SerializedName("output_language_code")
    val outputLanguageCode: String,
    /*
    * concise\literary\narrative\travel_style\humorous\emotional\unspecified
    * */
    @SerializedName("writing_style")
    val writingStyle: String,
    /*
    * oneself\friend\family\lover\other
    * */
    @SerializedName("peer_companion")
    val peerCompanion: String,
    @SerializedName("custom_peer_companion")
    val customPeerCompanion: String,
    @SerializedName("special_event")
    val specialEvent: String = "",
    @SerializedName("projectName")
    val projectName: String,
    @SerializedName("sn")
    val sn: String,
    @SerializedName("countryCode")
    val countryCode: String,
    @SerializedName("devCountryCode")
    val devCountryCode: String,
    @SerializedName("timestamp")
    val timestamp: String = System.currentTimeMillis().toString()
) {
    override fun toString(): String {
        return "TravelDiaryGenerateRequest(outputLanguageCode=$outputLanguageCode, imageGroups=$imageGroups, writingStyle='$writingStyle', peerCompanion='$peerCompanion', customPeerCompanion='$customPeerCompanion', specialEvent='$specialEvent', countryCode='$countryCode', devCountryCode='$devCountryCode')"
    }
}

data class ImageGroup(
    @SerializedName("images")
    val images: List<Image>,
    @SerializedName("text_constraint")
    val textConstraint: TextConstraint
) {
    override fun toString(): String {
        return "ImageGroup(images=$images, textConstraint=$textConstraint)"
    }
}

data class Image(
    @SerializedName("content")
    val content: String, //base64，size less than 500kb
    @SerializedName("created_time") //2024-09-06 10:58:56
    val createdTime: String,
    @SerializedName("location")
    val location: String
) {
    override fun toString(): String {
        return "Image(createdTime='$createdTime', location='$location')"
    }
}

data class TextConstraint(
    @SerializedName("length")
    val length: Int

) {
    override fun toString(): String {
        return "TextConstraint(length=$length)"
    }
}