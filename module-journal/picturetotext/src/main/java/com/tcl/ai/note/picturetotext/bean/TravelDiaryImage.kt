package com.tcl.ai.note.picturetotext.bean

import android.net.Uri
import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NavType
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize

@Parcelize
data class TravelDiaryImageGroup(
    val images: List<TravelDiaryImage>,
    val textLength: Int
) : Parcelable

@Parcelize
data class TravelDiaryImage(
    val uri: Uri,
    val createdTime: String,
    val location: String
) : Parcelable

class TravelDiaryImageGroupListNavType : NavType<List<TravelDiaryImageGroup>>(isNullableAllowed = false) {
    private val gson = Gson()

    override fun get(bundle: Bundle, key: String): List<TravelDiaryImageGroup>? {
        return bundle.getString(key)?.let { parseString(it) }
    }

    override fun parseValue(value: String): List<TravelDiaryImageGroup> {
        return parseString(value)
    }

    private fun parseString(value: String): List<TravelDiaryImageGroup> {
        val type = object : TypeToken<List<TravelDiaryImageGroup>>() {}.type
        return gson.fromJson(value, type)
    }

    override fun put(bundle: Bundle, key: String, value: List<TravelDiaryImageGroup>) {
        bundle.putString(key, gson.toJson(value))
    }
}