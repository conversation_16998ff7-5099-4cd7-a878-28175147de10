package com.tcl.ai.note.picturetotext

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.picturetotext.bean.TravelDiaryImageGroup
import com.tcl.ai.note.picturetotext.bean.TravelReportData
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch

var newPageStartTime: Long = System.currentTimeMillis()

class AiWritingAssistantViewModel : ViewModel() {
    companion object {
        private const val TAG = "AiWritingAssistantViewModel"
    }

    private val _uiState = MutableStateFlow(AiWritingAssistantUiState())
    val uiState: StateFlow<AiWritingAssistantUiState> = _uiState.asStateFlow()

    private val _operationType = MutableStateFlow(0)

    private val _createType = MutableStateFlow(0)

    var imageGroups: List<TravelDiaryImageGroup>? = null

    init {
        AccountController.connect()
        viewModelScope.launch {
            loadConfig()
        }
    }

    fun updateOperationType(type: String) {
        Logger.d(TAG, "updateOperationType: $type")
        when (type) {
            DataStoreParam.OPERATION_TYPE_ADD -> {
                _operationType.update { 0 }
            }
            else  -> {
                _operationType.update { 1 }
            }
        }
    }

    fun updateCreateType( type: Int) {
        Logger.d(TAG, "updateCreateType: $type")
        _createType.update { type }
    }

    fun onSizeChanged(height: Int) {
        _uiState.update {
            it.copy(screenHeight = height)
        }
    }

    fun onStyleSelected(style: WritingStyle) {
        Logger.i(TAG, "onStyleSelected: ${style.displayName()}")
        _uiState.update {
            it.copy(currentStyle = style)
        }
        viewModelScope.launch {
            AppDataStore.putData(DataStoreParam.KEY_WRITING_STYLE, style.prompt())
        }
    }

    fun onCompanionSelected(companion: PeerCompanion) {
        Logger.i(TAG, "onCompanionSelected: ${companion.displayName()}")
        val customCompanion = if (companion is PeerCompanion.Other) _uiState.value.customCompanion else ""
        _uiState.update {
            it.copy(currentCompanion = companion, showEmptyInputTip = false, customCompanion = customCompanion)
        }
        viewModelScope.launch {
            AppDataStore.putData(DataStoreParam.KEY_PEER_COMPANION, companion.prompt())
        }
    }

    fun onCompanionOtherInput(value: String) {
        Logger.i(TAG, "onCompanionOtherInput: $value")
        if (_uiState.value.currentCompanion is PeerCompanion.Other) {
            _uiState.update {
                it.copy(customCompanion = value)
            }
        }
    }

    fun onCompanionOtherInputFocusChanged(focus: Boolean) {
        Logger.i(TAG, "onCompanionOtherInputFocusChanged: $focus")
        when (focus) {
            true -> {
                _uiState.update {
                    it.copy(showEmptyInputTip = false)
                }
            }

            false -> {
                _uiState.update {
                    it.copy(showEmptyInputTip = it.customCompanion.isEmpty())
                }
            }
        }
    }

    fun onWordNumberSelected(wordNumber: WordNumber) {
        Logger.i(TAG, "onWordNumberSelected: ${wordNumber.displayName()}")
        _uiState.update {
            it.copy(currentWordNumber = wordNumber)
        }
        viewModelScope.launch {
            AppDataStore.putData(DataStoreParam.KEY_WORD_NUMBER, wordNumber.ratio)
        }
    }

    fun onSpecialEventInput(event: String) {
        _uiState.update {
            it.copy(specialEvent = event)
        }
    }

    fun showEmptyInputTip() {
        _uiState.update {
            it.copy(showEmptyInputTip = true)
        }
    }

    fun resetData() {
        _uiState.update {
            it.copy(showEmptyInputTip = false, specialEvent = "", customCompanion = "")
        }
    }

    fun generate() {
        viewModelScope.launchIO {
            TravelDiaryRepository.instance.reportAIWritingSettings(
                TravelReportData(
                    createType = _createType.value,
                    style = _uiState.value.currentStyle.displayName(),
                    companion = _uiState.value.customCompanion,
                    wordCount = _uiState.value.currentWordNumber.displayName(),
                    specialEventFilled = _uiState.value.specialEvent,
                    isAdopted = true,
                    operationType = _operationType.value,
                    newPageStartTime = newPageStartTime
                )
            )
        }
        viewModelScope.launchIO {
            TravelDiaryRepository.instance.generateTravelDiary(
                travelDiaryImageGroups = imageGroups,
                writingStyle = _uiState.value.currentStyle.prompt(),
                peerCompanion = _uiState.value.currentCompanion.prompt(),
                customPeerCompanion = _uiState.value.customCompanion,
                wordNumberRatio = _uiState.value.currentWordNumber.ratio,
                specialEvent = _uiState.value.specialEvent
            )
        }
    }

    override fun onCleared() {
        super.onCleared()
        viewModelScope.launch {
            AccountController.disconnectInIO()
        }
    }

    private suspend fun loadConfig() {
        val writingStyle = AppDataStore.getData(DataStoreParam.KEY_WRITING_STYLE, WritingStyle.NoRestrictions.prompt())
        _uiState.value.styleOptions.find { it.prompt() == writingStyle }?.let { style ->
            _uiState.update { it ->
                it.copy(currentStyle = style)
            }
        }
        val peerCompanion = AppDataStore.getData(DataStoreParam.KEY_PEER_COMPANION, PeerCompanion.Friend.prompt())
        _uiState.value.companionOptions.find { it.prompt() == peerCompanion }?.let { companion ->
            _uiState.update { it ->
                it.copy(currentCompanion = companion)
            }
        }
        val wordNumberRatio = AppDataStore.getData(DataStoreParam.KEY_WORD_NUMBER, WordNumber.Moderate.ratio)
        _uiState.value.wordNumberOptions.find { it.ratio == wordNumberRatio }?.let { wordNumber ->
            _uiState.update { it ->
                it.copy(currentWordNumber = wordNumber)
            }
        }
    }
}