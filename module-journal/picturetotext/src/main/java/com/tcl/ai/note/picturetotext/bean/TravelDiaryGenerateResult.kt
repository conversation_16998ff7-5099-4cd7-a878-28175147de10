package com.tcl.ai.note.picturetotext.bean

data class TravelDiaryGenerateResult(
    val status: Status,
    val content: List<String>? = null
)

sealed class Status() {
    data object Start : Status()
    data object Stop : Status()
    data object Success : Status()
    data class Failure(var errorCode: Int = -1, var errorMessage: String) : Status()
}

object ServerCode {
    const val SUCCESS = 0
    const val EXCEPTION = -1
    const val SENSITIVE = 1273
}
object ErrorMessage {
    const val SENSITIVETEXT = "Sensitive words are detected in user input question by TCL Content Filter"
}
