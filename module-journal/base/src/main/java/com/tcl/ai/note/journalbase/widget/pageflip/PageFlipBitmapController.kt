package com.tcl.ai.note.journalbase.widget.pageflip

import android.graphics.Bitmap
import android.graphics.Canvas
import androidx.annotation.IntRange
import com.tcl.ai.note.utils.Logger

private val BITMAP_COLOR_CONFIG = Bitmap.Config.ARGB_8888

/**
 * 处于当前页时，需要已经绘制好当前页、前一页和后一页的Bitmap，如果在第一页，则不画前一页，在最后同理
 *
 * 使用SideEffect监听重组，如果重组发生，则重画当前三页
 */
internal class PageFlipBitmapController(@IntRange(from = 0L) var totalPage: Int) {

    //记录前一页，当前页，后一页，如果current==0，则bitmapBuffer[0]为null。current==total同理
    private val bitmapBuffer = arrayOfNulls<Bitmap?>(3)
    private val retryCountMap = mutableMapOf<Int, Int>()

    //需要获取的页面，以及对应的BufferIndex
    private var needBitmapPages = mutableListOf<Pair<Int, Int>>()

    private val canvas = Canvas()

    //当前页面
    var currentPage = 0
        private set

    var exeRecompositionBlock: (() -> Unit)? = null
        set(value) {
            if (field != null) return
            field = value
        }

    private fun calculateNeedBitmapPages(page: Int) {
        if (page !in 0 until totalPage) return

        currentPage = page

        //需要缓存的页数范围
        val needs = mutableListOf<Pair<Int, Int>>()
        if (page > 0) {
            needs.add(Pair(page - 1, 0))
        }
        needs.add(Pair(page, 1))
        if (page < totalPage - 1) {
            needs.add(Pair(page + 1, 2))
        }

        needBitmapPages = needs
    }

    fun needBitmapAt(page: Int) {
        //这个机制在高速刷新状态时（例如LazyColumn）有可能会出错，needBitmapPages也许还没清空，导致needBitmap需求被拒绝，不能正确刷新状态
        //但是为了一般情况下的性能，似乎只能这么做了
        if (needBitmapPages.isNotEmpty()) return
        calculateNeedBitmapPages(page)
        exeRecompositionBlock?.let { it() }
    }

    fun refresh() {
        needBitmapAt(currentPage)
    }

    fun getNeedPage(): Int {
        if (needBitmapPages.isEmpty()) calculateNeedBitmapPages(currentPage)
        return needBitmapPages.first().first
    }

    /**
     * 只分配一次内存，除非新的size有变化
     * @since v1.1.0
     */
    fun renderThenSave(width: Int, height: Int, render: (drawable: Canvas) -> Unit) {
        //如果不再需要bitmap，则不再绘制了
        if (needBitmapPages.isEmpty() || width <= 0 || height <= 0) {
            return
        }

        val first = needBitmapPages.first()
        val idx = first.second

        var needNew = false
        if (bitmapBuffer[idx] == null) {
            needNew = true
        } else {
            //新的大小发生变化（因为config不变，所以bitmap的大小可以认为只受width, height影响，而不再去计算allocationByteCount）
            bitmapBuffer[idx]?.let {
                if (width != it.width || height != it.height) {
                    it.recycle()
                    needNew = true
                }
            }
        }

        if (needNew) {
            bitmapBuffer[idx] = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)
        }

        canvas.let {
            it.setBitmap(bitmapBuffer[idx]!!)
            render(it)
            it.setBitmap(null)
        }

        val bmp = bitmapBuffer[idx]!!
        if (isBitmapAllBlack(bmp)) {
            Logger.d("renderThenSave bitmapBuffer[$idx] 渲染全黑, 自动重试~")
            bmp.recycle()
            bitmapBuffer[idx] = null

            // 记录重试次数
            val oldCount = retryCountMap[idx] ?: 0
            retryCountMap[idx] = oldCount + 1

            // 移除当前队首，为防止死循环，最多允许n次
            needBitmapPages.removeAt(0)

            if (retryCountMap[idx]!! < 3) {
                // 重新入队，等待下次dispatchDraw
                needBitmapPages.add(Pair(first.first, idx))
                exeRecompositionBlock?.let { it() }
                Logger.d("renderThenSave 重新入队 idx=$idx, 当前次数=${retryCountMap[idx]}")
            } else {
                // 超过最大重试，报错或提示
                Logger.d("renderThenSave【致命错误】idx=$idx 多次渲染失败，已放弃！")
            }

            return
        } else {
            // 渲染成功，重试计数器清空
            retryCountMap.remove(idx)
        }

        needBitmapPages.removeAt(0)
        if (needBitmapPages.isEmpty()) return
        exeRecompositionBlock?.let { it() }
    }

    /**
     * 检测bitmap是否全黑（true为全黑，false为有内容）
     */
    private fun isBitmapAllBlack(bmp: Bitmap): Boolean {
        // 🚩角点+中心采样更高效
        val w = bmp.width
        val h = bmp.height
        if (w == 0 || h == 0) return true // 非法bitmap
        // 抽查左上、右下、中心，保证主要区域不是全黑
        val coordinates = listOf(
            0 to 0,
            w - 1 to 0,
            0 to h - 1,
            w - 1 to h - 1,
            w / 2 to h / 2
        )
        return coordinates.all { (x, y) -> bmp.getPixel(x, y) == 0xFF000000.toInt() }
    }

    fun destroy() {
        bitmapBuffer.forEach {
            it?.recycle()
        }
    }

    /**
     * 获取当前Bitmap
     * @param which 0=前一张 1=当前 2=下一张
     */
    fun getBitmapCurrent(which: Int): Bitmap {
        if (bitmapBuffer[which] == null) {
            bitmapBuffer[which] = Bitmap.createBitmap(1, 1, BITMAP_COLOR_CONFIG)
        }
        return bitmapBuffer[which]!!
    }

    fun isRenderOk() = needBitmapPages.isEmpty()
}