package com.tcl.ai.note.journalbase

import android.annotation.SuppressLint
import android.content.Context.VIBRATOR_SERVICE
import android.os.Vibrator
import androidx.activity.compose.BackHandler
import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getNavBarHeight
import com.tcl.ai.note.utils.getStatusBarHeight
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.isLandScape
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.widget.clickableNoRipple
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 底部弹窗默认,初始高度
 */
fun getOriginalHeight(navBarHeight: Dp): Dp {
    //初始高度设置为UI图总结和润色的初始高度
    var defaultHeight = 410.dp
    if (isTablet&& isLandScape) {//平板横屏时的原始高度，测试建议不要那么高，和最大高度区别度大一点
        defaultHeight=360.dp
    }
    defaultHeight+=navBarHeight
    Logger.d("BottomSheetDialog","getOriginalHeight:${defaultHeight}")
    return defaultHeight
}

/**
 * 底部弹窗最大高度
 */
fun getMaxHeight(screenHeightDp: Dp, statusBarHeight: Dp, navBarHeight: Dp): Dp {
    //手机竖屏的最大高度
    var defaultMaxHeight: Dp
    val topHeight = isDensity440.judge(71.dp, 80.dp)//手机的顶部高度
    defaultMaxHeight = screenHeightDp - topHeight - statusBarHeight
    defaultMaxHeight += navBarHeight
    //Logger.d("BottomSheetDialog","defaultMaxHeight:${defaultMaxHeight}")
    return defaultMaxHeight
}

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun BottomSheetDialog(
    modifier: Modifier = Modifier,
    isDarkMode: Boolean = false,
    originalHeight: Dp? = null,
    visible: Boolean,
    cancelable: Boolean = true,//指定 BottomSheetDialog 是否可以被取消。如果设置为 true，用户可以通过按下返回键或者其他取消操作来关闭对话框；如果设置为 false，则对话框不能被取消。
    canceledOnTouchOutside: Boolean = true,//决定当用户点击对话框外部区域时，对话框是否会被取消。如果设置为 true，用户点击对话框外部区域时，对话框会关闭；如果设置为 false，点击外部区域不会产生任何效果
    onDismissRequest: () -> Unit,
    showIndicatorWhenFullScreen: Boolean = false,
    bgColor: Color = isDarkMode.judge(Color(0XFF212121), Color(0XFFF5F5F5)),//底部弹窗背景色
    horizontalDividerColor: Color = colorResource(id = R.color.bottom_sheet_dialog_drag_bar_color),
    showFullScreenCallBack: (Boolean) -> Unit = {},
    backHandler: (suspend (suspend () -> Unit) -> Unit)? = null,
    extraTopPadding: Boolean = false,
    showAlphaAnimation: Boolean = true,
    @DrawableRes topBgResId: Int? = null,
    isNavigationBarsPaddingNeeded: Boolean = false,//底部是否需要导航栏padding，录音转文本需求，不然是透明的
    onDismissCallback: (suspend () -> Unit) -> Unit,
    content: @Composable () -> Unit,
) {
    var visibleState by remember { mutableStateOf(!visible) }

    // 启动后自动触发动画
    LaunchedEffect(Unit) {
        visibleState = visible
    }
    val backgroundAlpha = remember {
        Animatable(if (showAlphaAnimation) 0f else 1f)
    }
    BackHandler(visibleState, cancelable, backgroundAlpha, backHandler, onDismissRequest)
    BoxWithConstraints(
        modifier = modifier
            .fillMaxSize()
//            .then(addNavigationBarPadding(isNavigationBarsPaddingNeeded))//加上底部导航栏的padding，才不会出现弹窗底部有蒙板的效果
//            .navigationBarsPadding()
//            .imePadding()
    ) {
        DialogBackgroundVisibility(
            visibleState,
            backgroundAlpha,
            isNavigationBarsPaddingNeeded,
            canceledOnTouchOutside,
            onDismissRequest,

        )
        val navBarHeight = getNavBarHeight()
        val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp

        InnerDialog(
            visible = visibleState,
            onDismissRequest = onDismissRequest,
            content = content,
            originalHeight = originalHeight ?: getMaxHeight(screenHeightDp, getStatusBarHeight(), navBarHeight),
            showFullScreenCallBack = showFullScreenCallBack,
            bgColor = bgColor,
            horizontalDividerColor = horizontalDividerColor,
            extraTopPadding = extraTopPadding,
            topBgResId = topBgResId,
            animationBeforeDropDown = {
                backgroundAlpha.animateTo(
                    0f, animationSpec = tween(durationMillis = 200, easing = LinearEasing)
                )
            },
            onDismissCallback = onDismissCallback
        )
    }
}

@Composable
private fun BackHandler(
    visible: Boolean,
    cancelable: Boolean,
    backgroundAlpha: Animatable<Float, AnimationVector1D>,
    backHandler: (suspend (suspend () -> Unit) -> Unit)?,
    onDismissRequest: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    BackHandler(enabled = visible) {
        if (cancelable) {
            coroutineScope.launch {
                var beforeDismiss: suspend () -> Unit = {
                    backgroundAlpha.animateTo(
                        0f,
                        animationSpec = tween(durationMillis = 200, easing = LinearEasing)
                    )
                }
                if (backHandler != null) {
                    backHandler(beforeDismiss)
                } else {
                    beforeDismiss.invoke()
                    onDismissRequest()
                }
            }
        }
    }
}

/**
 * 背景层
 */
@Composable
private fun DialogBackgroundVisibility(
    visible: Boolean,
    backgroundAlpha: Animatable<Float, AnimationVector1D>,
    isNavigationBarsPaddingNeeded: Boolean,
    canceledOnTouchOutside: Boolean,
    onDismissRequest: () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    LaunchedEffect(key1 = null) {
        coroutineScope.launch {
            delay(300)
            if (backgroundAlpha.value == 1f) {
                return@launch
            }
            backgroundAlpha.animateTo(
                1f, animationSpec = tween(durationMillis = 100, easing = LinearEasing)
            )
        }
    }
    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(animationSpec = tween(durationMillis = 100, easing = LinearEasing)),
        exit = fadeOut(animationSpec = tween(durationMillis = 100, easing = LinearEasing))
    ) {
        Box(modifier = Modifier
            .fillMaxSize()
            .alpha(backgroundAlpha.value)
            .background(color = Color(color = 0x4D000000))
            .clearAndSetSemantics { }
            .then(addNavigationBarPadding(isNavigationBarsPaddingNeeded))
            .clickableNoRipple {
                if (canceledOnTouchOutside) {
                    onDismissRequest()
                }
            })
    }
}

// 底部是否需要导航栏padding，录音转文本需求，不然是透明的
@Composable
private fun addNavigationBarPadding(isNavigationBarsPaddingNeeded: Boolean) =
    if (isNavigationBarsPaddingNeeded) {
        Modifier.navigationBarsPadding()
    } else {
        Modifier
    }


@Composable
private fun BoxScope.InnerDialog(
    visible: Boolean,
    onDismissRequest: () -> Unit,
    content: @Composable () -> Unit,
    originalHeight: Dp,
    showFullScreenCallBack: (Boolean) -> Unit,
    bgColor: Color,
    horizontalDividerColor: Color,
    extraTopPadding: Boolean = false,
    @DrawableRes topBgResId: Int?,
    animationBeforeDropDown: suspend () -> Unit = {},
    onDismissCallback: (suspend () -> Unit) -> Unit
) {
    val statusBarHeight = getStatusBarHeight()
    val navBarHeight = getNavBarHeight()
    val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp
    val topPadding = 20.dp

    val maxHeight = getMaxHeight(screenHeightDp, statusBarHeight,navBarHeight) // 设置最大高度 为UI图标注的高度
    // 加上顶部padding高度 全屏才能刚好到Title  maxHeight += topPadding
    var innerDialogHeight = remember {
        Animatable(originalHeight.value)
    }
    var currentHeight = remember {
        mutableFloatStateOf(originalHeight.value)
    }
    val originalHeightPaddingTop = originalHeight.value + 20
    val originalHeightPaddingBottom = originalHeight.value - 20

    val fullHeightPaddingTop = maxHeight - topPadding

    val vibratorController = LocalContext.current.getSystemService(VIBRATOR_SERVICE) as Vibrator
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    // 修改点1：新增状态变量，控制小横线显示
//    var shouldShowIndicator by remember(innerDialogHeight.value) {
//        mutableStateOf(innerDialogHeight.value != fullHeightPaddingTop.value)
//    }
    //指示器 handler 始终显示
    val shouldShowIndicator = true
    val corner = 18.dp//横屏24dp 太大 UX说 统一改成18dp
    val rounderModifier = Modifier.clip(
        shape = RoundedCornerShape(
            topStart = corner, topEnd = corner
        )
    )
    val isFullScreen = remember {
        mutableStateOf(false)
    }

    // 封装的方法
    suspend fun dismissDialog() {
        Logger.d("BoxScope.InnerDialog", "dismissDialog-----")
        innerDialogHeight.animateTo(0f)
        animationBeforeDropDown()
        onDismissRequest()
    }

    onDismissCallback(::dismissDialog)
    AnimatedVisibility(
        visible = visible,
        modifier = Modifier
            //.imePadding()
            .align(alignment = Alignment.BottomCenter)
            .clickableNoRipple {}
            // 修改点2：限制高度不超过最大高度
            .height(innerDialogHeight.value.dp.coerceAtMost(maxHeight)),
        enter = slideInVertically(animationSpec = tween(
            durationMillis = 400,
            easing = LinearOutSlowInEasing
        ),
            initialOffsetY = {
                it
            }),
        exit = slideOutVertically(animationSpec = tween(
            durationMillis = 400,
            easing = LinearOutSlowInEasing
        ),
            targetOffsetY = {
                it
            })
    ) {

        Box(
            modifier = (if (shouldShowIndicator) rounderModifier else Modifier)
                .background(color = bgColor)
        ) {
//            if (useAiBG) {
//                Image(
//                    painter = painterResource(id = R.drawable.bottom_dialog_bg),
//                    contentDescription = null,
//                    modifier = Modifier.fillMaxSize(),
//                    contentScale = ContentScale.Crop,
//                    alignment = Alignment.TopCenter // 确保顶部对齐，优先显示顶部内容
//                )
//            }
            if (topBgResId != null) {
                Image(
                    painter = painterResource(id = topBgResId),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop,
                    alignment = Alignment.TopCenter // 确保顶部对齐，优先显示顶部内容
                )
            }
//            Image(painter = painterResource(id = R.drawable.bottom_dialog_bg), contentDescription = null, modifier = Modifier.fillMaxWidth(), contentScale = ContentScale.FillWidth)
            Column {
                val draggableState = rememberDraggableState(onDelta = {
                    val scale = context.resources.displayMetrics.density
                    var newHeight = innerDialogHeight.value - it * 1.2f / scale
                    // 修改点3：限制高度范围在0到最大高度之间
                    newHeight = newHeight.coerceIn(0f, maxHeight.value)
                    var shouldVibrate = false
                    //向上滑动：从下往上滑动有2次震动--小于1/2到1/2，1/2到大于1/2

                    if (it < 0) {
                        if (innerDialogHeight.value < originalHeightPaddingBottom && newHeight > originalHeightPaddingBottom) {

                            shouldVibrate = true
                        }
                        if (innerDialogHeight.value > originalHeightPaddingBottom && innerDialogHeight.value < originalHeightPaddingTop && newHeight > originalHeightPaddingTop) {

                            shouldVibrate = true
                        }
                    }
                    //向下滑动：从上往下滑动1次震动--大于1/2到1/2
                    if (it > 0 && innerDialogHeight.value > originalHeightPaddingTop && newHeight < originalHeightPaddingTop) {

                        shouldVibrate = true
                    }
                    coroutineScope.launch {
                        innerDialogHeight.snapTo(newHeight)
                    }
                    if (shouldVibrate) {
                        //vibratorController.vibrate(75)
                    }

                    // 修改点4：根据新高度动态更新小横线显示状态
//                        shouldShowIndicator = newHeight > 0
                })/*
                * 这里如果是全屏模式，则补充相应高度，note里设定最高上限的情况下不需要
                * *//*                    AnimatedVisibility(visible = extraTopPadding && isFullScreen.value) {
                                        Spacer(modifier = Modifier.height(14.dp))
                                    }*/
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .draggable(state = draggableState,
                            orientation = Orientation.Vertical,
                            enabled = true,
                            onDragStarted = {},
                            onDragStopped = {
                                coroutineScope.launch {
                                    // 新增条件：定义屏幕的三个区域
                                    val screenTopThird = screenHeightDp.value * 0.33f
                                    var screenMiddleThird = screenHeightDp.value * 0.66f
                                    // 定义半屏高度
                                    val halfScreenHeight = screenHeightDp.value * 0.5f
                                    // 根据设备类型调整全屏阈值
                                    if (isTablet) {
                                        screenMiddleThird = maxHeight.value * 0.66f
                                    }
                                    val targetHeight = when {
                                        // 全屏状态下的逻辑
                                        currentHeight.floatValue == maxHeight.value -> {
                                            Logger.d(
                                                "BoxScope.InnerDialog",
                                                "全屏状态下的逻辑 " + innerDialogHeight.value + "-- maxHeight:" + maxHeight.value
                                                        + "-- screenTopThird:" + screenTopThird + "-- screenMiddleThird:" + screenMiddleThird
                                            )
                                            when {
                                                // 下拉至屏幕顶部1/3位置，保持全屏
                                                innerDialogHeight.value > screenMiddleThird -> maxHeight.value
                                                // 下拉至屏幕中间1/3位置，变为半屏
                                                //innerDialogHeight.value < screenMiddleThird -> originalHeight.value
                                                // 下拉至屏幕底部1/3位置，关闭弹窗
                                                else -> 0f
                                            }
                                        }
                                        // 初始状态下的逻辑
                                        else -> when {
                                            // 拖拽超过半屏则全屏
                                            innerDialogHeight.value >= halfScreenHeight -> maxHeight.value
                                            // 低于半屏则关闭
                                            else -> 0f
                                        }
                                    }
                                    Logger.d(
                                        "BottomSheetDialog",
                                        "targetHeight -----  ***  $targetHeight   originalHeight.value   ${originalHeight.value}  innerDialogHeight.value : ${innerDialogHeight.value}"
                                    )
                                    currentHeight.floatValue = targetHeight
                                    innerDialogHeight.animateTo(targetHeight)

                                    if (targetHeight > originalHeight.value) {
                                        showFullScreenCallBack(true)
                                        isFullScreen.value = true
                                    } else if (targetHeight == originalHeight.value) {
                                        showFullScreenCallBack(false)
                                        isFullScreen.value = false
                                    } else if (targetHeight == 0f) {
                                        animationBeforeDropDown()
                                        onDismissRequest()
                                    }
                                    // 动画完成后更新小横线显示状态
//                                    shouldShowIndicator = targetHeight > 0
                                }
                            })
                        .padding(top = 14.dp, bottom = 6.dp),
                    horizontalArrangement = Arrangement.Center
                ) {
                    // 根据当前高度判断是否显示小横线
                    if (shouldShowIndicator) {
                        HorizontalDivider(
                            thickness = 4.dp,
                            color = horizontalDividerColor,
                            modifier = Modifier
                                .clip(RoundedCornerShape(10.dp))
                                .width(40.dp)
                        )
                    } else {
                        Spacer(modifier = Modifier.height(4.dp))
                    }
                }
                content()
            }
        }
    }
}
