package com.tcl.ai.note.journalbase.widget.pageflip

import android.graphics.Canvas
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.AbstractComposeView
import androidx.compose.ui.viewinterop.AndroidView

/**
 * @param modifier 修饰符[支持以padding方式设置组件位置和大小，或以height和width方式设置大小，不支持offset和size修饰符]
 * @param config 组件配置，请使用[rememberPageFlipViewConfig]
 * @param state 组件状态，请使用[rememberPageFlipViewState]
 * @param pageFlipViewScope 翻页器提供的各类回调 [PageFlipViewScope]
 */
@Composable
fun PageFlipView(
    modifier: Modifier = Modifier,
    config: PageFlipViewConfig = PageFlipViewConfig(),
    state: PageFlipViewState,
    pageFlipViewScope: PageFlipViewScope.() -> Unit
) {
    Box(modifier.fillMaxSize()) {
        val controller by remember(state.pageCount) {
            mutableStateOf(PageFlipBitmapController(state.pageCount))
        }

        DisposableEffect(Unit) {
            onDispose {
                controller.destroy()
            }
        }

        remember(state) {
            controller.totalPage = state.pageCount
            state.currentPage?.let {
                controller.needBitmapAt(it)
            }
            mutableStateOf(state)
        }

        val callbacks = remember(state) {
            mutableStateOf(PageFlipViewScopeImpl().apply(pageFlipViewScope))
        }

        AndroidView(
            modifier = Modifier.fillMaxSize(),
            factory = { context ->
                object : AbstractComposeView(context) {

                    @Composable
                    override fun Content() {
                        //重组触发器
                        var recomposeTrigger by remember { mutableLongStateOf(0L) }

                        controller.exeRecompositionBlock = {
                            recomposeTrigger = System.currentTimeMillis()
                        }

                        Box(
                            Modifier
                                .fillMaxSize()
                                .align(Alignment.Center)
                        ) {
                            callbacks.value.contents(
                                this,
                                controller.getNeedPage()
                            ) { controller.refresh() }
                            recomposeTrigger
                            invalidate()
                        }
                    }

                    override fun dispatchDraw(canvas: Canvas) {
                        /**
                         * 复用canvas、bitmap，且把流程控制在BitmapController内部，dispatchDraw只负责dispatchDraw
                         * @since v1.1.0
                         */
                        controller.renderThenSave(width, height) {
                            super.dispatchDraw(it)
                        }
                    }
                }
            }
        )

        //貌似必须包裹在CompositionLocalProvider，否则就会不断重组，没想通为什么
        CompositionLocalProvider(
            LocalPageFlipViewConfig provides config
        ) {
            var position by remember { mutableStateOf(Rect.Zero) }

            Box(
                Modifier
                    .fillMaxSize()
                    .align(Alignment.Center)
                    .clipToBounds()
                    .onGloballyPositioned {
                        position = it.boundsInRoot()
                    }
            ) {
                PageFlipViewInner(
                    bounds = position,
                    controller = controller,
                    callbacks = callbacks.value,
                    onNext = { eventType ->
                        if (state.currentPage == null && controller.currentPage < controller.totalPage - 1) {
                            controller.needBitmapAt(controller.currentPage + 1)
                            callbacks.value.onTurnPageRequest(eventType, controller.currentPage, true, true)
                        } else {
                            callbacks.value.onTurnPageRequest(
                                eventType,
                                controller.currentPage,
                                true,
                                controller.currentPage < controller.totalPage - 1
                            )
                        }
                    }, onPrevious = { eventType ->
                        if (state.currentPage == null && controller.currentPage > 0) {
                            controller.needBitmapAt(controller.currentPage - 1)
                            callbacks.value.onTurnPageRequest(eventType, controller.currentPage, false, true)
                        } else {
                            callbacks.value.onTurnPageRequest(
                                eventType,
                                controller.currentPage,
                                false,
                                controller.currentPage > 0
                            )
                        }
                    }
                )
            }
        }
    }
}