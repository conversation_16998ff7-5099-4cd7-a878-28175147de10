package com.tcl.ai.note.journalbase.widget.indicator

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.withTransform
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.times
import com.tcl.ai.note.utils.toPx
import kotlin.math.min

@Composable
fun StickIndicator(
    modifier: Modifier = Modifier,
    totalCount: Int,
    curIndex: Int,
    showCount: Int = 5,
    dotPadding: Dp = 4.dp,
    bigDotWidth: Dp = 4.dp,
    smallDotWidth: Dp = 4.dp,
    selectColor: Color = com.tcl.ai.note.theme.TclTheme.colorScheme.indicatorSelectedColor,
    defaultColor: Color = com.tcl.ai.note.theme.TclTheme.colorScheme.indicatorUnSelectedColor,
    animTime: Int = 100,
    isStickEnable: Boolean = false,
) {
    val dotPadPx = dotPadding.toPx
    val bigDotPx = bigDotWidth.toPx
    val smallDotPx = smallDotWidth.toPx

    var scrollIndex by remember { mutableStateOf(0) }
    // 平移动画 scrollIndex
    val animatedScrollIndex = remember { Animatable(scrollIndex.toFloat()) }

    val drawCount = min(showCount, totalCount)
    val widthDp = ((drawCount - 1) * smallDotWidth + (drawCount - 1) * dotPadding + bigDotWidth)
    val heightDp = bigDotWidth

    val animatedIndex = remember { Animatable(curIndex.toFloat()) }
    val stickAnimX = remember { Animatable(0f) }
    var isSwitchFinish by remember { mutableStateOf(true) }

    // 动画处理, 算法核心：窗口能不平移就不平移，只有选中点“滚动到左/右边界外”时才动
    LaunchedEffect(curIndex, totalCount, showCount) {
        // 目标窗口左下标(一格一格滑)
        var targetScrollIndex = scrollIndex
        if (curIndex < scrollIndex) {
            targetScrollIndex = curIndex
        } else if (curIndex > scrollIndex + showCount - 1) {
            targetScrollIndex = curIndex - showCount + 1
        }
        // overflow处理
        if (totalCount <= showCount) targetScrollIndex = 0
        if (targetScrollIndex > totalCount - showCount) targetScrollIndex = totalCount - showCount
        if (targetScrollIndex < 0) targetScrollIndex = 0

        // step 2. 平移动画（挂起函数，动画完成再往下执行）
        if (targetScrollIndex != scrollIndex) {
            animatedScrollIndex.animateTo(
                targetScrollIndex.toFloat(),
                animationSpec = tween(animTime, easing = LinearOutSlowInEasing)
            )
            scrollIndex = targetScrollIndex
        }

        if (!isStickEnable) {
            animatedIndex.snapTo(curIndex.toFloat())
            isSwitchFinish = true
        } else {
            isSwitchFinish = false
            stickAnimX.snapTo(animatedIndex.value - scrollIndex)
            animatedIndex.animateTo(
                targetValue = curIndex.toFloat(),
                animationSpec = tween(durationMillis = animTime, easing = LinearOutSlowInEasing)
            )
            stickAnimX.animateTo(
                targetValue = curIndex - scrollIndex.toFloat(),
                animationSpec = tween(durationMillis = animTime, easing = LinearOutSlowInEasing)
            )
            isSwitchFinish = true
        }
    }

    Box(
        modifier = modifier
            .width(widthDp)
            .height(heightDp)
            .clipToBounds()
    ) {
        Canvas(modifier = Modifier.matchParentSize()) {
            val centerY = size.height / 2
            // 平移全窗口渲染
            val dx = -(animatedScrollIndex.value - scrollIndex) * (smallDotPx + dotPadPx)
            withTransform({
                translate(left = dx)
            }) {
                var startX = 0f
                var selectCircleX = 0f
                // 只画窗口内drawCount个点
                for (i in 0 until drawCount) {
                    val realIndex = scrollIndex + i
                    val isSelected = realIndex == animatedIndex.value.toInt()
                    if (isSelected) {
                        drawCircle(
                            color = selectColor,
                            radius = bigDotPx / 2,
                            center = Offset(startX + bigDotPx / 2, centerY)
                        )
                        selectCircleX = startX + bigDotPx / 2
                        startX += bigDotPx + dotPadPx
                    } else if (i == 0 || i == drawCount - 1) {
                        startX += smallDotPx / 2
                        drawCircle(
                            color = defaultColor,
                            radius = smallDotPx / 2,
                            center = Offset(startX, centerY)
                        )
                        startX += smallDotPx / 2 + dotPadPx
                    } else {
                        startX += bigDotPx / 2
                        drawCircle(
                            color = defaultColor,
                            radius = bigDotPx / 2,
                            center = Offset(startX, centerY)
                        )
                        startX += bigDotPx / 2 + dotPadPx
                    }
                }
                // 粘性动画
                if (isStickEnable && !isSwitchFinish) {
                    val stickPos = stickAnimX.value * (smallDotPx + dotPadPx)
                    val stickPath = Path().apply {
                        val quadStartX = selectCircleX
                        val quadStartY = centerY - bigDotPx / 2
                        moveTo(quadStartX, quadStartY)
                        quadraticTo(
                            (quadStartX + stickPos) / 2, centerY,
                            stickPos, quadStartY
                        )
                        lineTo(stickPos, quadStartY + bigDotPx)
                        quadraticTo(
                            (quadStartX + stickPos) / 2, centerY,
                            quadStartX, quadStartY + bigDotPx
                        )
                        close()
                    }
                    drawCircle(
                        color = selectColor,
                        radius = bigDotPx / 2,
                        center = Offset(stickPos, centerY)
                    )
                    drawPath(
                        path = stickPath,
                        color = selectColor
                    )
                }
            }
        }
    }
}