package com.tcl.ai.note.journalbase

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.provider.Settings
import android.text.format.DateFormat
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.judge
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.BreakIterator
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale

fun formatTime(timeMillis: Long, locale: Locale): String {
    val zoneId = ZoneId.systemDefault()
    val input = LocalDateTime.ofInstant(Instant.ofEpochMilli(timeMillis), zoneId)
    val now = LocalDateTime.now(zoneId)
    val is24HourFormat = DateFormat.is24HourFormat(GlobalContext.instance)
    return when {
        input.toLocalDate() == now.toLocalDate() ->
            input.format(DateTimeFormatter.ofPattern(is24HourFormat.judge("HH:mm", "h:mm a"), locale))
        input.year == now.year ->
            if (locale.language == "zh") {
                input.format(DateTimeFormatter.ofPattern("MM月dd日", locale))
            } else {
                input.format(DateTimeFormatter.ofPattern("dd MMM", locale))
            }
        else ->
            if (locale.language == "zh") {
                input.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日", locale))
            } else {
                input.format(DateTimeFormatter.ofPattern("dd MMM yyyy", locale))
            }
    }
}

fun launchAppDetailsScreen(context: Context, packageName: String) {
    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
        data = Uri.fromParts("package", packageName, null)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    try {
        context.startActivity(intent)
    } catch (ex: Exception) {
        Logger.e("Utils", "startActivitySafely#Exception: ${ex.message}")
    }
}

/**
 * assets整个目录递归拷贝到 external 目录
 */
fun copyAssetDirToExternal(assetDir: String, outDir: File) {
    try {
        val context = GlobalContext.instance
        val assets = context.assets
        val files = assets.list(assetDir)
        if (files.isNullOrEmpty()) {
            // 拷贝文件
            copyAssetFileToExternal(assetDir, outDir)
        } else {
            // 是文件夹
            val newOutDir = File(outDir, assetDir.substringAfterLast('/'))
            if (!newOutDir.exists()) newOutDir.mkdirs()
            for (fileName in files) {
                val childAssetPath = if (assetDir.isEmpty()) fileName else "$assetDir/$fileName"
                copyAssetDirToExternal(childAssetPath, newOutDir)
            }
        }
    } catch (e: IOException) {
        e.printStackTrace()
    }
}

/**
 * assets整个目录递归拷贝到 external 目录
 */
fun copySampleAssetsToExternal(assetDir: String, outDir: File) {
    try {
        val context = GlobalContext.instance
        val assets = context.assets
        val files = assets.list(assetDir)
        if (files.isNullOrEmpty()) {
            // 拷贝文件
            copyAssetFileToExternal(assetDir, outDir)
        } else {
            // 是文件夹
            if (!outDir.exists()) outDir.mkdirs()
            for (fileName in files) {
                val childAssetPath = if (assetDir.isEmpty()) fileName else "$assetDir/$fileName"
                copyAssetDirToExternal(childAssetPath, outDir)
            }
        }
    } catch (e: IOException) {
        e.printStackTrace()
    }
}

/**
 * assets文件拷贝到 external 目录
 */
private fun copyAssetFileToExternal(assetPath: String, outDir: File) {
    val context = GlobalContext.instance
    val outFile = File(outDir, assetPath.substringAfterLast('/'))
    if (!outFile.exists()) {
        context.assets.open(assetPath).use { input ->
            FileOutputStream(outFile).use { output ->
                val buffer = ByteArray(1024)
                var length: Int
                while (input.read(buffer).also { length = it } > 0) {
                    output.write(buffer, 0, length)
                }
            }
        }
    }
}

/*
* 从输入字符串中截取可见字符的子字符串
* */
fun truncateVisibleCharacters(input: String, maxVisibleCharacters: Int): String {
    val iterator = BreakIterator.getCharacterInstance()
    iterator.setText(input)

    var count = 0
    var endIndex = 0

    while (count < maxVisibleCharacters && iterator.next() != BreakIterator.DONE) {
        endIndex = iterator.current()
        count++
    }

    return input.substring(0, endIndex)
}

fun isOtgPhysicalKeyboardMode(context: Context): Boolean {
    val config = context.resources.configuration
    return config.keyboard == Configuration.KEYBOARD_QWERTY
}