package com.tcl.imageanalysisdemo.entity

data class ImageData(
    var imgPath: String,
    var arcFace: Boolean = false,
    var containsFace: Boolean = false,
    var facesNumber: Int = 0, //人脸数量
    var facesSpacing: String = "", //人脸间距
    var sharpness: Double = 0.0,
    var brightness: Double = 0.0,
    var contrast: Double = 0.0,
    var sharpnessScore: Double = 0.0,
    var brightnessScore: Double = 0.0,
    var contrastScore: Double = 0.0,
    var qualityScore: Double = 0.0,
    var time: String = "",
    var latLong: String = ""
)