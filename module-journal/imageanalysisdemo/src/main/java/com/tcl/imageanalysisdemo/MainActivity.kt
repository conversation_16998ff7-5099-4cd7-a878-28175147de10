package com.tcl.imageanalysisdemo

import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Button
import androidx.compose.material3.Divider
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.lifecycle.viewmodel.compose.viewModel
import coil3.compose.rememberAsyncImagePainter
import com.google.mlkit.vision.face.FaceDetectorOptions
import com.tcl.imageanalysisdemo.ui.theme.TCL_NoteTheme
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            TCL_NoteTheme {
                Scaffold(modifier = Modifier.fillMaxSize()) { innerPadding ->
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(innerPadding)
                    ) {

                        var page by remember {
                            mutableStateOf("home")
                        }

                        when (page) {
                            "home" -> {
                                Column {
                                    Button(
                                        onClick = {
                                            page = "parse"
                                        }
                                    ) {
                                        Text("parse image")
                                    }

                                    Button(
                                        onClick = {
                                            page = "similar"
                                        }
                                    ) {
                                        Text("similar image")
                                    }
                                }
                            }

                            "parse" -> {
                                ParseImage {
                                    page = "home"
                                }
                            }

                            "similar" -> {
                                SimilarJudge {
                                    page = "home"
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun SimilarJudge(onBack: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {
        BackHandler {
            onBack.invoke()
        }

        var imageUri1 by remember {
            mutableStateOf<Uri?>(null)
        }

        var imageUri2 by remember {
            mutableStateOf<Uri?>(null)
        }

        val galleryLauncher1 = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.GetContent(),
            onResult = { uri ->
                uri?.let {
                    imageUri1 = uri
                }
            }
        )

        val galleryLauncher2 = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.GetContent(),
            onResult = { uri ->
                uri?.let {
                    imageUri2 = uri
                }
            }
        )

        Column {
            Row {
                Column(modifier = Modifier.weight(1f)) {
                    imageUri1?.let {
                        Image(
                            painter = rememberAsyncImagePainter(model = imageUri1),
                            contentDescription = null,
                            modifier = Modifier.size(260.dp)
                        )
                    }
                    TextButton(onClick = {
                        galleryLauncher1.launch("image/*")
                    }) {
                        Text("Pick image")
                    }
                }
                Column(modifier = Modifier.weight(1f)) {
                    imageUri1?.let {
                        Image(
                            painter = rememberAsyncImagePainter(model = imageUri2),
                            contentDescription = null,
                            modifier = Modifier.size(260.dp)
                        )
                    }
                    TextButton(onClick = {
                        galleryLauncher2.launch("image/*")
                    }) {
                        Text("Pick image")
                    }
                }
            }
        }

        Row {
            val viewmodel = viewModel<MainViewModel>()
            Button(
                onClick = {
                    viewmodel.compareImage(imageUri1, imageUri2)
                }
            ) {
                Text("compare")
            }

            Text(
                "similar: ${viewmodel.similar.value}"
            )
        }
    }
}

@Composable
fun ParseImage(onBack: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
    ) {

        BackHandler {
            onBack.invoke()
        }

        Text("照片分析测试")
        Spacer(modifier = Modifier.height(5.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            AllImageGrid()
        }
        Spacer(modifier = Modifier.height(5.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        ) {
            OperatorArea()
        }
    }
}


@Composable
fun AllImageGrid() {
    val viewModel = viewModel<MainViewModel>()
    LazyVerticalGrid(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF5F5F5)),
        columns = GridCells.Fixed(4),
        contentPadding = PaddingValues(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        itemsIndexed(
            items = viewModel.allImages,
            key = { _, it -> it.imgPath }) { _, item ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.White)
            ) {
                val bitmap = remember(item.imgPath) {
                    viewModel.getBitmapFormFile(item.imgPath)
                }
                Text(
                    text = item.imgPath.substringAfterLast('/'),
                    lineHeight = 10.sp,
                    fontSize = 10.sp
                )
                if (bitmap == null) {
                    Box(
                        Modifier
                            .fillMaxWidth()
                            .height(120.dp)
                    )
                } else {
                    Image(
                        bitmap = bitmap.asImageBitmap(),
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(120.dp),
                        contentScale = ContentScale.Crop
                    )
                }
                ScoreItem(
                    name = "arc人脸",
                    value = item.arcFace
                )
                ScoreItem(
                    name = "人脸",
                    value = item.containsFace
                )
                ScoreItem(
                    name = "人脸数量",
                    value = item.facesNumber
                )
                ScoreItem(
                    name = "人脸间距",
                    value = item.facesSpacing
                )
//                ScoreItem(
//                    name = "清晰度",
//                    value = item.sharpness.formatNum()
//                )
//                ScoreItem(
//                    name = "评分",
//                    value = item.sharpnessScore.formatNum()
//                )
//                ScoreItem(
//                    name = "亮度",
//                    value = item.brightness.formatNum()
//                )
//                ScoreItem(
//                    name = "评分",
//                    value = item.brightnessScore.formatNum()
//                )
//                ScoreItem(
//                    name = "对比度",
//                    value = item.contrast.formatNum()
//                )
//                ScoreItem(
//                    name = "评分",
//                    value = item.contrastScore.formatNum()
//                )
                ScoreItem(
                    name = "质量",
                    value = item.qualityScore.formatNum()
                )
                ScoreItem(
                    name = "时间",
                    value = if (item.time != "") { formatTime(item.time) } else ""
                )
                ScoreItem(
                    name = "latLong",
                    value = item.latLong
                )
            }
        }
    }
}

private fun Double.formatNum() = "%.2f".format(this)

private fun formatTime(time: String): String {
    val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
    return sdf.format(Date(time.toLong()))
}

@Composable
fun ScoreItem(
    modifier: Modifier = Modifier,
    name: String,
    value: Any
) {
    Text(
        text = "$name: $value",
        modifier = modifier,
        maxLines = 3,
        lineHeight = 10.sp,
        fontSize = 10.sp
    )
}

@Composable
fun OperatorArea() {
    Column {
        val viewModel = viewModel<MainViewModel>()
        Row(
            modifier = Modifier.padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                "人脸宽度比率(0-1)：",
                fontSize = 12.sp,
                modifier = Modifier.padding(bottom = 10.dp)
            )
            BasicTextField(
                modifier = Modifier
                    .width(80.dp)
                    .height(40.dp),
                textStyle = LocalTextStyle.current.copy(fontSize = 12.sp, lineHeight = 30.sp),
                value = viewModel.minFaceSize,
                onValueChange = { newValue ->
                    try {
                        // 匹配 0 或 0.xxx 或 1 或 1.0... 的正则
                        val regex = Regex("""^(0(\.\d*)?|1(\.0*)?)?$""")
                        // 允许为空（方便删除），否则校验
                        if (newValue.isEmpty() || regex.matches(newValue)) {
                            viewModel.minFaceSize = newValue
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                },
                decorationBox = { innerTextField ->
                    Column {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(bottom = 1.dp) // 为下划线留出空间
                        ) {
                            innerTextField()
                        }
                        // 绘制下划线
                        Divider(
                            color = Color.Black,
                            thickness = 1.dp
                        )
                    }
                },
            )
        }
        Row(
            modifier = Modifier.padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text("速度&准确性：", fontSize = 12.sp)
            Row(
                modifier = Modifier
                    .width(180.dp)
                    .height(40.dp)
                    .background(Color.White, RoundedCornerShape(20.dp))
                    .padding(3.dp), verticalAlignment = Alignment.CenterVertically
            ) {
                SwitchText(
                    modifier = Modifier
                        .weight(1f),
                    text = "速度优先",
                    selected = viewModel.performanceMode == FaceDetectorOptions.PERFORMANCE_MODE_FAST,
                    click = {
                        viewModel.performanceMode = FaceDetectorOptions.PERFORMANCE_MODE_FAST
                    }
                )
                SwitchText(
                    modifier = Modifier
                        .weight(1f),
                    text = "准确性优先",
                    selected = viewModel.performanceMode == FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE,
                    click = {
                        viewModel.performanceMode = FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE
                    }
                )
            }
        }

        Row(verticalAlignment = Alignment.CenterVertically) {
            var showPopup by remember { mutableStateOf(false) }
            var anchorSize by remember { mutableStateOf(IntSize.Zero) }

            Box(
                modifier = Modifier.weight(1f), contentAlignment = Alignment.Center
            ) {
                Text(text = viewModel.parseState.value)
            }

            Box(
                modifier = Modifier
                    .weight(1f)
                    .onGloballyPositioned { layoutCoordinates ->
                        anchorSize = layoutCoordinates.size
                    }, contentAlignment = Alignment.Center
            ) {
                Button(enabled = viewModel.buttState.value, onClick = {
                    showPopup = true
                    viewModel.getAllImageFoldersNew()
                }) {
                    Text("加载图片")
                }
            }

            Box(
                modifier = Modifier.weight(1f), contentAlignment = Alignment.Center
            ) {
                Button(
                    enabled = viewModel.buttState.value && viewModel.allImages.isNotEmpty(),
                    onClick = {
                        viewModel.startParse()
                    }) {
                    Text("开始分析")
                }
            }

            if (showPopup) {
                ImageFoldersPopup(
                    offset = IntOffset(
                        0,
                        -anchorSize.height
                    ),
                    list = viewModel.allImageFolders,
                    itemClick = {
                        viewModel.getImages(it)
                        showPopup = false
                    },
                    onDismiss = {
                        showPopup = false
                    }
                )
            }
        }
    }
}

@Composable
fun SwitchText(
    text: String,
    selected: Boolean = false,
    modifier: Modifier = Modifier,
    bgColor: Color = Color(0x14155BF0),
    selectedColor: Color = Color(0xe6000000),
    normalColor: Color = Color(0x80000000),
    click: () -> Unit = {}
) {
    Box(
        modifier = modifier
            .fillMaxHeight()
            .background(
                if (selected) {
                    bgColor
                } else {
                    Color.Transparent
                }, RoundedCornerShape(20.dp)
            )
            .clickable {
                click()
            },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text,
            lineHeight = 22.sp,
            fontSize = 12.sp,
            color = if (selected) {
                selectedColor
            } else {
                normalColor
            }
        )
    }
}

@Composable
fun ImageFoldersPopup(
    offset: IntOffset = IntOffset(0, 0),
    list: List<String>,
    itemClick: (String) -> Unit,
    onDismiss: () -> Unit
) {
    Popup(
        alignment = Alignment.BottomCenter,
        onDismissRequest = onDismiss,
        offset = offset,
    ) {
        Surface(
            shadowElevation = 8.dp,
            shape = RoundedCornerShape(8.dp),
            modifier = Modifier.width(240.dp)
        ) {
            LazyColumn(
                modifier = Modifier
                    .heightIn(max = 292.dp)
                    .padding(horizontal = 16.dp)
            ) {
                itemsIndexed(list) { index, item ->
                    Box(
                        modifier = Modifier
                            .heightIn(min = 30.dp)
                            .clickable {
                                itemClick(item)
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        if (index != 0) {
                            HorizontalDivider(
                                color = Color.Gray,
                                thickness = 1.dp,
                                modifier = Modifier.align(Alignment.TopStart)
                            )
                        }
                        Text(
                            text = item,
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }
    }
}