package com.tcl.imageanalysisdemo

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import com.tcl.imageanalysisdemo.core.opencv.OpenCVHelper

class TestApp: Application() {
    companion object {
        @SuppressLint("StaticFieldLeak")
        lateinit var context: Context
    }

    override fun onCreate() {
        super.onCreate()
        context = this@TestApp
        OpenCVHelper.init()
    }
}