package com.tcl.imageanalysisdemo.core.facedetect

import android.graphics.Bitmap
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.Face
import com.google.mlkit.vision.face.FaceDetectorOptions
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine

class FaceDetectorHelper {
    private lateinit var detector: com.google.mlkit.vision.face.FaceDetector

    fun initDetector(
        performanceMode: Int = FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE,
        minFaceSize: Float = 0.05f
    ) {
        // High-accuracy landmark detection and face classification
        // High-accuracy landmark detection and face classification
        val highAccuracyOpts = FaceDetectorOptions.Builder()
            .setPerformanceMode(performanceMode)
            .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_NONE)
            .setContourMode(FaceDetectorOptions.CONTOUR_MODE_NONE)
            .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_NONE)
            .setMinFaceSize(minFaceSize)
            .build()

        // Real-time contour detection
        // val realTimeOpts =
        //     FaceDetectorOptions.Builder().setContourMode(FaceDetectorOptions.CONTOUR_MODE_ALL)
        //         .build()

        detector = com.google.mlkit.vision.face.FaceDetection.getClient(highAccuracyOpts)
    }

    suspend fun detectImageContainsFace(bitmap: Bitmap): List<Face>? {
        return suspendCoroutine { c ->
            val image = InputImage.fromBitmap(bitmap, 0)
            detector.process(image).addOnSuccessListener { faces ->
                c.resume(faces)
            }.addOnFailureListener { ex ->
                c.resumeWithException(ex)
            }
        }
    }
}