package com.tcl.imageanalysisdemo

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Rect
import android.net.Uri
import android.provider.MediaStore
import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.exifinterface.media.ExifInterface
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.arcsoft.facedetection.FaceDetectionHelper
import com.google.mlkit.vision.face.FaceDetectorOptions
import com.tcl.imageanalysisdemo.core.facedetect.FaceDetectorHelper
import com.tcl.imageanalysisdemo.core.opencv.OpenCVHelper
import com.tcl.imageanalysisdemo.entity.ImageData
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Locale

class MainViewModel : ViewModel() {

    private val faceDetector = FaceDetectorHelper()

    private val faceDetectionHelper: FaceDetectionHelper = FaceDetectionHelper()

    val allImageFolders = mutableStateListOf<String>()
    val allImages = mutableStateListOf<ImageData>()

    private var _parseState = mutableStateOf("未分析")
    val parseState: State<String> get() = _parseState

    private var _buttonEnable = mutableStateOf(true)
    val buttState: State<Boolean> get() = _buttonEnable

    private var _similar = mutableStateOf<Double?>(null)
    val similar: State<Double?> get() = _similar

    var performanceMode by mutableIntStateOf(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
    var minFaceSize by mutableStateOf("0.05")

    init {
        getAllImageFoldersNew()
    }

    fun getAllImageFoldersNew() {
        if (allImageFolders.isNotEmpty()) return
        viewModelScope.launch {
            val folders = mutableSetOf<String>()
            val uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
            val projection = arrayOf(MediaStore.Images.Media.RELATIVE_PATH)
            TestApp.context.contentResolver.query(uri, projection, null, null, null)
                ?.use { cursor ->
                    val pathIndex =
                        cursor.getColumnIndexOrThrow(MediaStore.Images.Media.RELATIVE_PATH)
                    while (cursor.moveToNext()) {
                        folders.add(cursor.getString(pathIndex))
                    }
                }
            allImageFolders.addAll(folders)
            allImageFolders.add("全部")
        }
    }

    fun getImages(dirName: String) {
        allImages.clear()
        _parseState.value = "未分析"
        if (dirName == "全部") {
            queryAllImages()
        } else {
            getImagesFromDirByMediaStore(dirName)
        }
    }

    private fun queryAllImages() {
        val projection = arrayOf(MediaStore.Images.Media._ID, MediaStore.Images.Media.DATA)
        val sortOrder = MediaStore.Images.Media.SIZE + " DESC"
        val cursor = TestApp.context.contentResolver.query(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI, projection, null, null, sortOrder
        )
        cursor?.use {
            while (cursor.moveToNext()) {
                val path =
                    cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA))
                allImages.add(ImageData(imgPath = path))
            }
        }
    }

    private fun getImagesFromDirByMediaStore(dirName: String) {
        val uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
        val projection =
            arrayOf(MediaStore.Images.Media.DATA, MediaStore.Images.Media.RELATIVE_PATH)
        val sortOrder = MediaStore.Images.Media.SIZE + " DESC"
        // 只筛选特定目录名
        val selection = "${MediaStore.Images.Media.RELATIVE_PATH} LIKE ?"
        val selectionArgs = arrayOf(dirName)

        val cursor = TestApp.context.contentResolver.query(
            uri,
            projection,
            selection,
            selectionArgs,
            sortOrder
        )
        cursor?.use {
            while (cursor.moveToNext()) {
                val path =
                    cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA))
                allImages.add(ImageData(imgPath = path))
            }
        }
    }

    fun startParse() {
        viewModelScope.launch {
            faceDetector.initDetector(performanceMode, minFaceSize.toFloatOrNull() ?: 0.05f)
            faceDetectionHelper.init()
            _parseState.value = "分析中..."
            _buttonEnable.value = false
            for (i in 0 until allImages.size) {
                val img = allImages[i]
                Log.d("MainViewModel", "startParse path:${img.imgPath}")
                var bmp = getBitmapFormFile(img.imgPath, 512) ?: continue
                Log.d("MainViewModel", "startParse config:${bmp.config}")

                // face detect
                val faces = try {
                    faceDetector.detectImageContainsFace(bmp)
                } catch (ex: Exception) {
                    null
                }
                val boundingBoxList = faces?.map { it.boundingBox }?.sortedBy { it.left }
                val facesSpacingList = mutableListOf<Int>()
                boundingBoxList?.let {
                    for (j in 0 until it.size - 1) {
                        facesSpacingList.add(rectDistance(it[j + 1], it[j]))
                    }
                }
                val hasFace = try {
                    faceDetectionHelper.detectFace(bmp)
                } catch (e: Exception) {
                    false
                }

                val img1 = img.copy(
                    arcFace = hasFace,
                    containsFace = !faces.isNullOrEmpty(),
                    facesNumber = faces?.size ?: 0,
                    facesSpacing = facesSpacingList.joinToString()
                )
                allImages[i] = img1

                if (bmp.config != Bitmap.Config.ARGB_8888
                    && bmp.config != Bitmap.Config.RGB_565
                ) {
                    bmp = bmp.copy(Bitmap.Config.ARGB_8888, true)
                }
                val sharpness = OpenCVHelper.calculateSharpness(bmp)
                val brightness = OpenCVHelper.calculateBrightness(bmp)
                val contrast = OpenCVHelper.calculateContrast(bmp)
                val sharpnessScore = OpenCVHelper.calculateSharpnessScore(sharpness)
                val brightnessScore = OpenCVHelper.calculateBrightnessScore(brightness)
                val contrastScore = OpenCVHelper.calculateContrastScore(contrast)
                val qualityScore = OpenCVHelper.calculateQualityScore(
                    sharpnessScore,
                    brightnessScore,
                    contrastScore
                )
                val time = getTime(img.imgPath)
                val latLong = getLatLong(img.imgPath)

                allImages[i] = img1.copy(
                    sharpness = sharpness,
                    brightness = brightness,
                    contrast = contrast,
                    sharpnessScore = sharpnessScore,
                    brightnessScore = brightnessScore,
                    contrastScore = contrastScore,
                    qualityScore = qualityScore,
                    time = "$time",
                    latLong = "${latLong[0]}-${latLong[1]}"
                )
            }
            _parseState.value = "分析完成"
            _buttonEnable.value = true
        }
    }

    // 计算两个 Rect 间距离（重叠返回 0）
    private fun rectDistance(rect1: Rect, rect2: Rect): Int {
        val dx =
            0.coerceAtLeast(rect1.left.coerceAtLeast(rect2.left) - rect1.right.coerceAtMost(rect2.right))
        return dx
    }

    fun compareImage(imgUri1: Uri?, imgUri2: Uri?) {
        viewModelScope.launch {
            val bmp1 = TestApp.context.contentResolver.openInputStream(imgUri1!!)?.use {
                BitmapFactory.decodeStream(it)
            }
            val bmp2 = TestApp.context.contentResolver.openInputStream(imgUri2!!)?.use {
                BitmapFactory.decodeStream(it)
            }
            val similarBitmap = OpenCVHelper.similarBitmap(bmp1!!, bmp2!!)
            _similar.value = similarBitmap
        }
    }

    fun getBitmapFormFile(pathName: String, size: Int = 256): Bitmap? {
        // 1. 先只解码图片边界获取原尺寸
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(pathName, options)

        // 2. 计算采样率（例如目标尺寸 100x100）📏
        options.inSampleSize = calculateInSampleSize(options, size, size)

        // 3. 设置为 false，加载正式图片
        options.inJustDecodeBounds = false
        Log.d(
            "MainViewModel",
            "getBitmapFormFile pathName:$pathName inSampleSize:${options.inSampleSize}"
        )
        return BitmapFactory.decodeFile(pathName, options)
    }

    // 辅助采样率计算方法
    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        val height = options.outHeight
        val width = options.outWidth
        var inSampleSize = 1

        if (height > reqHeight || width > reqWidth) {
            val halfHeight = height / 2
            val halfWidth = width / 2

            while ((halfHeight / inSampleSize) >= reqHeight
                && (halfWidth / inSampleSize) >= reqWidth
            ) {
                inSampleSize *= 2
            }
        }
        return inSampleSize
    }

    private fun getTime(imgPath: String): Long {
        val exifInterface: ExifInterface = ExifInterface(imgPath)
        val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        val dateStr = exifInterface.getAttribute(ExifInterface.TAG_DATETIME)
        return if (dateStr != null) {
            try {
                val date = format.parse(dateStr)
                date?.time ?: File(imgPath).lastModified()
            } catch (ex: Exception) {
                Log.w(TAG, "getTime --> parse ExifTime failure: ${ex.message}")
                File(imgPath).lastModified()
            }
        } else {
            File(imgPath).lastModified()
        }
    }

    private fun getLatLong(imgPath: String): DoubleArray {
        val exifInterface: ExifInterface = ExifInterface(imgPath)
        return exifInterface.latLong ?: doubleArrayOf(0.0, 0.0)
    }

    companion object {
        private const val TAG = "MainViewModel"
    }
}