package com.arcsoft.facedetection

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import java.io.File

/**
 * 人脸检测辅助类
 * 提供更简单的API来使用人脸检测功能
 */
class FaceDetectionHelper {
    companion object {
        private const val TAG = "FaceDetectionHelper"
    }

    private var engine: FaceDetectionEngine = FaceDetectionEngine()
    private var isInitialized: Boolean = false

    /**
     * 初始化人脸检测引擎
     * @return 是否初始化成功
     */
    fun init(): Boolean {
        if (isInitialized) {
            Log.w(TAG, "Already initialized")
            return true
        }
        isInitialized = engine.init()
        if (isInitialized) {
            Log.i(TAG, "Face detection helper initialized successfully")
            Log.i(TAG, "SDK Version: ${engine.getVersion()}")
        } else {
            Log.e(TAG, "Failed to initialize face detection helper")
        }
        return isInitialized
    }

    /**
     * 检测图像文件中的人脸
     * @param imagePath 图像文件路径
     * @return 人脸检测结果
     */
    fun detectFaceFromFile(imagePath: String?): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "Helper not initialized")
            return false
        }
        if (imagePath.isNullOrEmpty()) {
            Log.e(TAG, "Image path is null or empty")
            return false
        }
        val imageFile = File(imagePath)
        if (!imageFile.exists()) {
            Log.e(TAG, "Image file does not exist: $imagePath")
            return false
        }
        return try {
            val bitmap = BitmapFactory.decodeFile(imagePath)
            if (bitmap == null) {
                Log.e(TAG, "Failed to decode image file: $imagePath")
                false
            } else {
                detectFace(bitmap)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception while detecting face from file: ${e.message}")
            false
        }
    }

    /**
     * 检测Bitmap中的人脸
     * @param bitmap 输入图像
     * @return 人脸检测结果
     */
    fun detectFace(bitmap: Bitmap?): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "Helper not initialized")
            return false
        }
        if (bitmap == null) {
            Log.e(TAG, "Input bitmap is null")
            return false
        }
        return try {
            engine.detectFace(
                bitmap,
                FaceDetectionEngine.DeviceOrientation.ORIENTATION_0,
                FaceDetectionEngine.CameraType.CAMERA_REAR
            )
        } catch (e: Exception) {
            Log.e(TAG, "Exception during face detection: ${e.message}")
            false
        }
    }

    /**
     * 检测NV21格式的图像数据中的人脸
     * @param nv21Data NV21格式的图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @return 人脸检测结果
     */
    fun detectFace(nv21Data: ByteArray?, width: Int, height: Int): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "Helper not initialized")
            return false
        }
        if (nv21Data == null || nv21Data.isEmpty()) {
            Log.e(TAG, "Input NV21 data is null or empty")
            return false
        }
        return try {
            engine.detectFace(
                nv21Data, width, height,
                FaceDetectionEngine.DeviceOrientation.ORIENTATION_0,
                FaceDetectionEngine.CameraType.CAMERA_REAR
            )
        } catch (e: Exception) {
            Log.e(TAG, "Exception during face detection: ${e.message}")
            false
        }
    }

    /**
     * 获取SDK版本信息
     * @return 版本信息字符串
     */
    val version: String
        get() = engine.getVersion()

    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized && engine.isInitialized()

    /**
     * 释放资源
     */
    fun release() {
        engine.release()
        isInitialized = false
        Log.i(TAG, "Face detection helper released")
    }
}