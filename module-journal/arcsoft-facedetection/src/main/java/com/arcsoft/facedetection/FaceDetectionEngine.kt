package com.arcsoft.facedetection

import android.graphics.Bitmap
import android.util.Log

/**
 * ArcSoft人脸检测引擎
 * 封装了ArcSoft人脸检测SDK的Kotlin接口
 */
class FaceDetectionEngine {
    companion object {
        private const val TAG = "FaceDetectionEngine"

        init {
            try {
                System.loadLibrary("face_detection_jni")
                System.loadLibrary("face_detection.arcsoft")
                System.loadLibrary("mpbase")
            } catch (e: UnsatisfiedLinkError) {
                Log.e(TAG, "Failed to load native libraries: ${e.message}")
            }
        }
    }

    // 设备方向枚举
    enum class DeviceOrientation(val value: Int) {
        ORIENTATION_0(0),
        ORIENTATION_90(90),
        ORIENTATION_180(180),
        ORIENTATION_270(270)
    }

    // 相机类型枚举
    enum class CameraType(val value: Int) {
        CAMERA_REAR(0),
        CAMERA_FRONT(1)
    }

    private var nativeHandle: Long = 0
    private var isInitialized: Boolean = false

    /**
     * 初始化人脸检测引擎
     * @return 是否初始化成功
     */
    fun init(): Boolean {
        if (isInitialized) {
            Log.w(TAG, "Engine already initialized")
            return true
        }
        try {
            nativeHandle = nativeInit()
            isInitialized = nativeHandle != 0L
            if (isInitialized) {
                Log.i(TAG, "Face detection engine initialized successfully")
            } else {
                Log.e(TAG, "Failed to initialize face detection engine")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Exception during initialization: ${e.message}")
            isInitialized = false
        }
        return isInitialized
    }

    /**
     * 检测图像中的人脸
     * @param bitmap 输入图像
     * @param deviceOrientation 设备方向
     * @param cameraType 相机类型
     * @return 是否检测到人脸
     */
    fun detectFace(
        bitmap: Bitmap?,
        deviceOrientation: DeviceOrientation,
        cameraType: CameraType
    ): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "Engine not initialized")
            return false
        }
        if (bitmap == null) {
            Log.e(TAG, "Input bitmap is null")
            return false
        }
        return try {
            nativeDetectFace(nativeHandle, bitmap, deviceOrientation.value, cameraType.value)
        } catch (e: Exception) {
            Log.e(TAG, "Exception during face detection: ${e.message}")
            false
        }
    }

    /**
     * 检测NV21格式的图像数据中的人脸
     * @param nv21Data NV21格式的图像数据
     * @param width 图像宽度
     * @param height 图像高度
     * @param deviceOrientation 设备方向
     * @param cameraType 相机类型
     * @return 是否检测到人脸
     */
    fun detectFace(
        nv21Data: ByteArray?,
        width: Int,
        height: Int,
        deviceOrientation: DeviceOrientation,
        cameraType: CameraType
    ): Boolean {
        if (!isInitialized) {
            Log.e(TAG, "Engine not initialized")
            return false
        }
        if (nv21Data == null || nv21Data.isEmpty()) {
            Log.e(TAG, "Input NV21 data is null or empty")
            return false
        }
        return try {
            nativeDetectFaceNV21(
                nativeHandle, nv21Data, width, height,
                deviceOrientation.value, cameraType.value
            )
        } catch (e: Exception) {
            Log.e(TAG, "Exception during face detection: ${e.message}")
            false
        }
    }

    /**
     * 获取SDK版本信息
     * @return 版本信息字符串
     */
    fun getVersion(): String {
        return try {
            nativeGetVersion()
        } catch (e: Exception) {
            Log.e(TAG, "Exception getting version: ${e.message}")
            "Unknown"
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        if (isInitialized && nativeHandle != 0L) {
            try {
                nativeUninit(nativeHandle)
                Log.i(TAG, "Face detection engine released")
            } catch (e: Exception) {
                Log.e(TAG, "Exception during release: ${e.message}")
            } finally {
                nativeHandle = 0L
                isInitialized = false
            }
        }
    }

    /**
     * 检查引擎是否已初始化
     * @return 是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized

    // Native方法声明
    private external fun nativeInit(): Long
    private external fun nativeDetectFace(handle: Long, bitmap: Bitmap, deviceOrientation: Int, cameraType: Int): Boolean
    private external fun nativeDetectFaceNV21(handle: Long, nv21Data: ByteArray, width: Int, height: Int, deviceOrientation: Int, cameraType: Int): Boolean
    private external fun nativeUninit(handle: Long)
    private external fun nativeGetVersion(): String
}