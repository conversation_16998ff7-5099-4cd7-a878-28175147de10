#include <jni.h>
#include <android/bitmap.h>
#include <android/log.h>
#include <string>
#include <vector>

// ArcSoft头文件
#include "arcsoft_facedetection.h"
#include "arcsoft_facedetection_def.h"
#include "amcomdef.h"
#include "ammem.h"
#include "merror.h"
#include "asvloffscreen.h"

#define LOG_TAG "FaceDetectionJNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// 全局变量
static JavaVM* g_jvm = nullptr;

// JNI_OnLoad函数
JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    g_jvm = vm;
    JNIEnv* env;
    if (vm->GetEnv(reinterpret_cast<void**>(&env), JNI_VERSION_1_6) != JNI_OK) {
        return JNI_ERR;
    }
    LOGI("JNI_OnLoad called");
    return JNI_VERSION_1_6;
}

// JNI_OnUnload函数
JNIEXPORT void JNICALL JNI_OnUnload(JavaVM* vm, void* reserved) {
    g_jvm = nullptr;
    LOGI("JNI_OnUnload called");
}

// 获取JNI环境
JNIEnv* getJNIEnv() {
    JNIEnv* env;
    if (g_jvm->GetEnv(reinterpret_cast<void**>(&env), JNI_VERSION_1_6) != JNI_OK) {
        return nullptr;
    }
    return env;
}

// 设置设备方向优先级
void setOrientationPriority(MInt32 deviceOrientation, MInt32 cameraType, MInt32& orientPriority) {
    // MInt32 adjustedOrientation = deviceOrientation;
    //
    // if (deviceOrientation == 0) {
    //     if (cameraType == ARC_FD_CAMERA_FRONT) {
    //         adjustedOrientation = 4; // 270度
    //     } else {
    //         adjustedOrientation = 2; // 90度
    //     }
    // } else if (deviceOrientation == 90) {
    //     adjustedOrientation = 3; // 180度
    // } else if (deviceOrientation == 180) {
    //     if (cameraType == ARC_FD_CAMERA_FRONT) {
    //         adjustedOrientation = 2; // 90度
    //     } else {
    //         adjustedOrientation = 4; // 270度
    //     }
    // } else if (deviceOrientation == 270) {
    //     adjustedOrientation = 1; // 0度
    // }
    //
    // switch (adjustedOrientation) {
    //     case 1:
    //         orientPriority = eARC_FD_OPF_0_ONLY;
    //         break;
    //     case 2:
    //         orientPriority = eARC_FD_OPF_90_ONLY;
    //         break;
    //     case 3:
    //         orientPriority = eARC_FD_OPF_180_ONLY;
    //         break;
    //     case 4:
    //         orientPriority = eARC_FD_OPF_270_ONLY;
    //         break;
    //     default:
    //         orientPriority = eARC_FD_OPF_0_ONLY;
    //         break;
    // }

    // eARC_FD_OPF_ALL_OUT 表示需要检测任何方向的人脸
    orientPriority = eARC_FD_OPF_ALL_OUT;
}

// 将Bitmap转换为ASVLOFFSCREEN
bool bitmapToASVLOFFSCREEN(JNIEnv* env, jobject bitmap, ASVLOFFSCREEN& offscreen) {
    AndroidBitmapInfo info;
    void* pixels;
    if (AndroidBitmap_getInfo(env, bitmap, &info) < 0) return false;
    if (info.format != ANDROID_BITMAP_FORMAT_RGBA_8888) return false; // 只支持 ARGB_8888
    if (AndroidBitmap_lockPixels(env, bitmap, &pixels) < 0) return false;
    offscreen.i32Width = info.width;
    offscreen.i32Height = info.height;
    offscreen.u32PixelArrayFormat = ASVL_PAF_RGB32_R8G8B8A8;
    offscreen.pi32Pitch[0] = info.stride;
    offscreen.ppu8Plane[0] = static_cast<MUInt8*>(pixels);
    AndroidBitmap_unlockPixels(env, bitmap);
    return true;
}

// Native方法实现

extern "C" JNIEXPORT jlong JNICALL
Java_com_arcsoft_facedetection_FaceDetectionEngine_nativeInit(JNIEnv* env, jobject thiz) {
    MHandle handle = nullptr;
    MRESULT result = ARC_FD_Init(&handle);
    
    if (result == MOK && handle != nullptr) {
        LOGI("Face detection engine initialized successfully");
        return reinterpret_cast<jlong>(handle);
    } else {
        LOGE("Failed to initialize face detection engine, result: %d", result);
        return 0;
    }
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_arcsoft_facedetection_FaceDetectionEngine_nativeDetectFace(
    JNIEnv* env, jobject thiz, jlong handle, jobject bitmap, 
    jint deviceOrientation, jint cameraType) {
    
    if (handle == 0) {
        LOGE("Invalid handle");
        return JNI_FALSE;
    }
    
    ASVLOFFSCREEN offscreen;
    if (!bitmapToASVLOFFSCREEN(env, bitmap, offscreen)) {
        LOGE("Failed to convert bitmap to ASVLOFFSCREEN");
        return JNI_FALSE;
    }
    
    MInt32 orientPriority;
    setOrientationPriority(deviceOrientation, cameraType, orientPriority);
    
    ARC_FD_FACEINFO faceInfo = {0};
    MRESULT result = ARC_FD_Process(reinterpret_cast<MHandle>(handle), 
                                   &offscreen, orientPriority, &faceInfo);
    
    if (result != MOK) {
        LOGE("Face detection failed, result: %d", result);
        return JNI_FALSE;
    }
    
    return faceInfo.isFaceDetected ? JNI_TRUE : JNI_FALSE;
}

extern "C" JNIEXPORT jboolean JNICALL
Java_com_arcsoft_facedetection_FaceDetectionEngine_nativeDetectFaceNV21(
    JNIEnv* env, jobject thiz, jlong handle, jbyteArray nv21Data, 
    jint width, jint height, jint deviceOrientation, jint cameraType) {
    
    if (handle == 0) {
        LOGE("Invalid handle");
        return JNI_FALSE;
    }
    
    jbyte* data = env->GetByteArrayElements(nv21Data, nullptr);
    if (!data) {
        LOGE("Failed to get NV21 data");
        return JNI_FALSE;
    }
    
    ASVLOFFSCREEN offscreen;
    offscreen.i32Width = width;
    offscreen.i32Height = height;
    offscreen.u32PixelArrayFormat = ASVL_PAF_NV21;
    offscreen.pi32Pitch[0] = (width + 3) & 0xfffffffc;
    offscreen.pi32Pitch[1] = (width + 3) & 0xfffffffc;
    offscreen.ppu8Plane[0] = reinterpret_cast<MUInt8*>(data);
    offscreen.ppu8Plane[1] = offscreen.ppu8Plane[0] + height * offscreen.pi32Pitch[0];
    
    MInt32 orientPriority;
    setOrientationPriority(deviceOrientation, cameraType, orientPriority);
    
    ARC_FD_FACEINFO faceInfo = {0};
    MRESULT result = ARC_FD_Process(reinterpret_cast<MHandle>(handle), 
                                   &offscreen, orientPriority, &faceInfo);
    
    env->ReleaseByteArrayElements(nv21Data, data, JNI_ABORT);
    
    if (result != MOK) {
        LOGE("Face detection failed, result: %d", result);
        return JNI_FALSE;
    }
    
    return faceInfo.isFaceDetected ? JNI_TRUE : JNI_FALSE;
}

extern "C" JNIEXPORT void JNICALL
Java_com_arcsoft_facedetection_FaceDetectionEngine_nativeUninit(
    JNIEnv* env, jobject thiz, jlong handle) {
    
    if (handle != 0) {
        MHandle h = reinterpret_cast<MHandle>(handle);
        MRESULT result = ARC_FD_Uninit(&h);
        if (result == MOK) {
            LOGI("Face detection engine uninitialized successfully");
        } else {
            LOGE("Failed to uninitialize face detection engine, result: %d", result);
        }
    }
}

extern "C" JNIEXPORT jstring JNICALL
Java_com_arcsoft_facedetection_FaceDetectionEngine_nativeGetVersion(
    JNIEnv* env, jobject thiz) {
    
    const MPBASE_Version* version = ARC_FD_GetVersion();
    if (version && version->Version) {
        return env->NewStringUTF(version->Version);
    } else {
        return env->NewStringUTF("Unknown");
    }
} 