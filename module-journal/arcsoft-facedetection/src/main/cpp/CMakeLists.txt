cmake_minimum_required(VERSION 3.4.1)

project(face_detection_jni)

add_library(face_detection_jni SHARED face_detection_jni.cpp)

# 头文件目录
include_directories(include)

# 引入第三方 so（从标准 jniLibs 目录）
add_library(face_detection.arcsoft SHARED IMPORTED)
set_target_properties(face_detection.arcsoft PROPERTIES IMPORTED_LOCATION
    ${CMAKE_CURRENT_LIST_DIR}/../jniLibs/${ANDROID_ABI}/libface_detection.arcsoft.so)

add_library(mpbase SHARED IMPORTED)
set_target_properties(mpbase PROPERTIES IMPORTED_LOCATION
    ${CMAKE_CURRENT_LIST_DIR}/../jniLibs/${ANDROID_ABI}/libmpbase.so)

find_library(log-lib log)
find_library(android-lib android)

target_link_libraries(
    face_detection_jni
    face_detection.arcsoft
    mpbase
    jnigraphics
    ${log-lib}
    ${android-lib}
)