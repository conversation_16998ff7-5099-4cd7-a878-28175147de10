/*******************************************************************************
Copyright(c) ArcSoft, All right reserved.

This file is ArcSoft's property. It contains ArcSoft's trade secret, proprietary
and confidential information.

The information and code contained in this file is only for authorized ArcSoft
employees to design, create, modify, or review.

DO NOT DISTRIBUTE, DO NOT DUPLICATE OR TRANSMIT IN ANY FORM WITHOUT PROPER
AUTHORIZATION.

If you are not an intended recipient of this file, you must not copy,
distribute, modify, or take any action in reliance on it.

If you have received this file in error, please immediately notify ArcSoft and
permanently delete the original and any copy of any file and any printout
thereof.
*******************************************************************************/

#ifndef _ARCSOFT_FACEDETECTION_DEF_H_
#define _ARCSOFT_FACEDETECTION_DEF_H_

#include "amcomdef.h"
#include "ammem.h"
#include "merror.h"
#include "asvloffscreen.h"

#ifdef __cplusplus
extern "C" {
#endif

#define ARC_FD_MAX_FACE_NUM      100

/* Defines camera type */
#define ARC_FD_CAMERA_REAR      0x00000000
#define ARC_FD_CAMERA_FRONT     0x00000001

enum ARC_FD_OrientPriority{
    eARC_FD_OPF_0_ONLY = 0,          // 0 only:0,0...
    eARC_FD_OPF_90_ONLY = 90,         // 90 only:90,90...
    eARC_FD_OPF_270_ONLY = 270,        // 270 only:270,270...
    eARC_FD_OPF_180_ONLY = 180,        // 180 only:180,180...
    eARC_FD_OPF_ALL_OUT = 4          // output faces with different orientation
};

/* Defines face detection result */
typedef struct _tag_ARC_FD_FACEINFO
{
    MInt32    isFaceDetected;
}ARC_FD_FACEINFO, * LPARC_FD_FACEINFO;


#ifdef __cplusplus
}
#endif
#endif //_ARCSOFT_FACEDETECTION_DEF_H_
