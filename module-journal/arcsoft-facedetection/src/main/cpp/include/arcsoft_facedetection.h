/*******************************************************************************
Copyright(c) ArcSoft, All right reserved.

This file is ArcSoft's property. It contains ArcSoft's trade secret, proprietary
and confidential information.

The information and code contained in this file is only for authorized ArcSoft
employees to design, create, modify, or review.

DO NOT DISTRIBUTE, DO NOT DUPLICATE OR TRANSMIT IN ANY FORM WITHOUT PROPER
AUTHORIZATION.

If you are not an intended recipient of this file, you must not copy,
distribute, modify, or take any action in reliance on it.

If you have received this file in error, please immediately notify ArcSoft and
permanently delete the original and any copy of any file and any printout
thereof.
*******************************************************************************/

#ifndef _ARCSOFT_FACEDETECTION_H_
#define _ARCSOFT_FACEDETECTION_H_

#ifdef ARC_FACEDETECTIONDLL_EXPORTS
#ifdef PLATFORM_LINUX
#define ARC_FACEDETECTION_API __attribute__((visibility ("default")))
#else
#define ARC_FACEDETECTION_API __declspec(dllexport)
#endif
#else
#define ARC_FACEDETECTION_API
#endif

#include "amcomdef.h"
#include "ammem.h"
#include "merror.h"
#include "asvloffscreen.h"
#include "arcsoft_facedetection_def.h"


#ifdef __cplusplus
extern "C" {
#endif

ARC_FACEDETECTION_API MRESULT ARC_FD_Init(        // return MOK if success, otherwise fail
        MHandle     *phHandle                    // [in/out] The algorithm engine will be initialized by this API
);


ARC_FACEDETECTION_API MRESULT ARC_FD_Process(        // return MOK if success, otherwise fail
		MHandle              hHandle,               // [in] The algorithm engine will be initialized by this API
		LPASVLOFFSCREEN      pInputImg,             // [in] Input image.
		MInt32               i32OrientPriority,     // [in] ARC_FD_OrientPriority
		LPARC_FD_FACEINFO    pFaceInfo              // [out] The face results
);


ARC_FACEDETECTION_API MRESULT ARC_FD_Uninit(            // return MOK if success, otherwise fail
        MHandle    *phHandle                  // [in/out] The algorithm engine will be un-initialized by this API
);

ARC_FACEDETECTION_API const MPBASE_Version* ARC_FD_GetVersion();
//Use it for ARC_FD_GetVersionStatus
#define ARC_FD_VERSION_MASK_RC   0x3FL
#define ARC_FD_VERSION_MASK_TIMESTAMP  0x4L
#define ARC_FD_VERSION_MASK_WATERMARK  0x8L
/** @brief Get version information of the algorithm.
* @return            The version status
* @sample
*          if(ARC_FD_VERSION_MASK_RC & ARC_FD_GetVersionStatus() == 0)
*             printf("it is RC version");
*/
ARC_FACEDETECTION_API MUInt64 ARC_FD_GetVersionStatus();


#ifdef __cplusplus
}
#endif
#endif //_ARCSOFT_FACEDETECTION_H_
