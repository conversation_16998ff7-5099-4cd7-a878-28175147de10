{"id": "simple_template_2_2", "style": "simple", "layoutType": 1, "elements": [{"imageId": 1, "type": "image", "size": [360, 480], "position": [0, 0], "angle": 0, "content": "image_1.png"}, {"type": "sticker", "size": [33, 25.5], "position": [17, 707], "angle": 0, "content": "sticker_01.webp"}, {"imageId": 2, "type": "image", "size": [130, 230], "position": [214, 504], "angle": 0, "content": "image_1.png"}, {"type": "sticker", "size": [24, 24], "position": [174, 708], "angle": 0, "content": "sticker_06.webp"}, {"type": "text", "size": [180, 63], "position": [16, 492], "textColor": "#b3000000", "angle": 0, "content": "Paris, France — Romantic hub where Eiffel Tower embodies timeless elegance", "associatedId": [1]}, {"type": "text", "size": [180, 63], "position": [16, 627], "textColor": "#b3000000", "angle": 0, "content": "Paris, France — Romantic hub where Eiffel Tower embodies timeless elegance", "associatedId": [2]}]}