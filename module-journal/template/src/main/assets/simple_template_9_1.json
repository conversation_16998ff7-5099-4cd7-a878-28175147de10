{"id": "simple_template_9_2", "style": "simple", "layoutType": 0, "elements": [{"type": "photo_frame", "size": [359, 152], "position": [0, 17], "angle": 3, "content": "photo_frame_359_152.webp"}, {"imageId": 1, "type": "image", "size": [320, 112], "position": [19.2, 34.9], "angle": 3, "content": "image_1.png"}, {"type": "photo_frame", "size": [127, 127], "position": [0, 172], "angle": 0, "content": "photo_frame_127_127.webp"}, {"imageId": 2, "type": "image", "size": [88, 88], "position": [20, 189], "angle": 0, "content": "image_1.png"}, {"type": "photo_frame", "size": [127, 127], "position": [116, 162], "angle": 0, "content": "photo_frame_127_127.webp"}, {"imageId": 3, "type": "image", "size": [88, 88], "position": [136, 179], "angle": 0, "content": "image_1.png"}, {"type": "photo_frame", "size": [127, 127], "position": [232, 172], "angle": 0, "content": "photo_frame_127_127.webp"}, {"imageId": 4, "type": "image", "size": [88, 88], "position": [252, 189], "angle": 0, "content": "image_1.png"}, {"type": "photo_frame", "size": [127, 127], "position": [0, 288], "angle": 0, "content": "photo_frame_127_127.webp"}, {"imageId": 5, "type": "image", "size": [88, 88], "position": [20, 305], "angle": 0, "content": "image_1.png"}, {"type": "photo_frame", "size": [127, 127], "position": [110, 302], "angle": 0, "content": "photo_frame_127_127.webp"}, {"imageId": 6, "type": "image", "size": [88, 88], "position": [130, 319], "angle": 0, "content": "image_1.png"}, {"type": "photo_frame", "size": [127, 127], "position": [232, 288], "angle": 0, "content": "photo_frame_127_127.webp"}, {"imageId": 7, "type": "image", "size": [88, 88], "position": [252, 305], "angle": 0, "content": "image_1.png"}, {"type": "photo_frame", "size": [185, 152], "position": [0, 413], "angle": 3, "content": "photo_frame_185_152.webp"}, {"imageId": 8, "type": "image", "size": [144, 112], "position": [21, 432.1], "angle": 3, "content": "image_1.png"}, {"type": "photo_frame", "size": [185, 152], "position": [174, 413], "angle": -3, "content": "photo_frame_185_152.webp"}, {"imageId": 9, "type": "image", "size": [144, 112], "position": [195, 432.1], "angle": -3, "content": "image_1.png"}, {"type": "text", "size": [336, 64], "position": [12, 577], "associatedId": [1, 2, 3, 4, 5, 6, 7, 8, 9], "textColor": "#80000000", "angle": 0, "content": "Paris, France — Romantic hub where Eiffel Tower embodies timeless elegance"}]}