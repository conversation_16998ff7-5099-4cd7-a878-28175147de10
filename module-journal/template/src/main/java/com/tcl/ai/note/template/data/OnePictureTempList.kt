package com.tcl.ai.note.template.data

import com.tcl.ai.note.template.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType

object OnePictureTempList {
    val onePictureTempList = listOf(
        Template(id = "1_1", thumbnailRes = R.drawable.thumbnail_template_1_1, type = TemplateType.OTHER, pictureNum = 1, contentFile = "simple_template_1_1.json", content = "一张图片的模板"),
        Template(id = "1_2", thumbnailRes = R.drawable.thumbnail_template_1_2, type = TemplateType.OTHER, pictureNum = 1, contentFile = "simple_template_1_2.json", content = "一张图片的模板"),
        Template(id = "1_3", thumbnailRes = R.drawable.thumbnail_template_1_3, type = TemplateType.OTHER, pictureNum = 1, contentFile = "fashion_template_1_3.json", content = "一张图片的模板"),
        Template(id = "1_4", thumbnailRes = R.drawable.thumbnail_template_1_4, type = TemplateType.OTHER, pictureNum = 1, contentFile = "retro_template_1_4.json", content = "一张图片的模板"),
    )
}