package com.tcl.ai.note.template.ui

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.navigation.NavController
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBackIosNew
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.repo.TemplateRepositoryImpl
import com.tcl.ai.note.utils.deviceDensity
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge
import com.tct.theme.core.designsystem.theme.TclTheme

@SuppressLint("DesignSystem")
@Composable
fun SelectTemplateScreen(
    //navController: NavController,
    selectedImages: List<String>,
    showSelectTemplateBack: Boolean = true,
    onDismissRequest: () -> Unit,
    onTemplateSelected: (template: Template) -> Unit = {},
    onBackClicked: () -> Unit = {},
) {
    val templates by remember {
        mutableStateOf(TemplateRepositoryImpl.getTemplatesByPictureNum(selectedImages.size))
    }

    Column(
        modifier = Modifier
            .fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = isDensity440.judge(9.dp, 20.dp),
                    start = isDensity440.judge(9.dp, 10.dp)
                ),
        ) {
            if (showSelectTemplateBack) {
                IconButton(onClick = { /*navController.navigateUp()*/onBackClicked.invoke() }) {
                    Icon(imageVector = Icons.Filled.ArrowBackIosNew, contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.edit_top_menu_back_icon))
                }
            }
            Text(
                text = stringResource(id = com.tcl.ai.note.resources.R.string.title_select_template),
                fontSize = isDensity440.judge(22.sp, 20.sp),
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(start = if (showSelectTemplateBack) 0.dp else isDensity440.judge(26.dp, 24.dp))
            )
        }

        // Placeholder for templates selection part
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            modifier = Modifier
                .clip(RoundedCornerShape(16.dp)),
            horizontalArrangement = Arrangement.spacedBy(isDensity440.judge(26.dp, 24.dp)), // 横向卡片间距
            verticalArrangement = Arrangement.spacedBy(24.dp), // 纵向卡片间距
            contentPadding = PaddingValues(horizontal = 24.dp, vertical = 32.dp) // 可选：四周间距

        ) {
            items(templates.size) { index ->
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(
                            isDensity440.judge(
                                (GlobalContext.densityDpi <= deviceDensity).judge(
                                    265.dp,
                                    240.dp
                                ), 240.dp
                            )
                        )
                        .clip(RoundedCornerShape(16.dp))
                        .clickable {
                            onTemplateSelected(templates[index])
                        },
                    contentAlignment = Alignment.Center,

                ) {
                    Image(painter = painterResource(id = templates[index].thumbnailRes),
                        contentDescription = stringResource(id = R.string.template_thumbnail_description),
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier
                            .fillMaxSize()
                            .clip(RoundedCornerShape(16.dp))
                            .border(
                                width = 1.dp,
                                color = Color(0x334983FF),
                                shape = RoundedCornerShape(16.dp)
                            ))
                }
            }
        }
    }
}

@Preview
@Composable
fun SelectTemplateScreenPreview() {
    val selectedImages = listOf("image1", "image2", "image3")
    SelectTemplateScreen(
        selectedImages = selectedImages,
        onDismissRequest = {},
        onTemplateSelected = {}
    )
}