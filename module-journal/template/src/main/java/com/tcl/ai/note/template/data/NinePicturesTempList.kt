package com.tcl.ai.note.template.data

import com.tcl.ai.note.template.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType

object NinePicturesTempList {
    val ninePicturesTempList = listOf(
        Template(id = "9_1", thumbnailRes = R.drawable.thumbnail_template_9_1, type = TemplateType.OTHER, pictureNum = 9, contentFile = "simple_template_9_1.json", content = "一张图片的模板"),
        Template(id = "9_2", thumbnailRes = R.drawable.thumbnail_template_9_2, type = TemplateType.OTHER, pictureNum = 9, contentFile = "simple_template_9_2.json", content = "一张图片的模板"),
        Template(id = "9_3", thumbnailRes = R.drawable.thumbnail_template_9_3, type = TemplateType.OTHER, pictureNum = 9, contentFile = "fashion_template_9_3.json", content = "一张图片的模板"),
        Template(id = "9_4", thumbnailRes = R.drawable.thumbnail_template_9_4, type = TemplateType.OTHER, pictureNum = 9, contentFile = "retro_template_9_4.json", content = "一张图片的模板"),
    )
}