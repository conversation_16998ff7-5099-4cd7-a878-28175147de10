package com.tcl.ai.note.template.data

import com.tcl.ai.note.template.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType

object ThreePicturesTempList {
    val threePicturesTempList = listOf(
        Template(id = "3_1", thumbnailRes = R.drawable.thumbnail_template_3_1, type = TemplateType.OTHER, pictureNum = 3, contentFile = "simple_template_3_1.json", content = "一张图片的模板"),
        Template(id = "3_2", thumbnailRes = R.drawable.thumbnail_template_3_2, type = TemplateType.OTHER, pictureNum = 3, contentFile = "simple_template_3_2.json", content = "一张图片的模板"),
        Template(id = "3_3", thumbnailRes = R.drawable.thumbnail_template_3_3, type = TemplateType.OTHER, pictureNum = 3, contentFile = "fashion_template_3_3.json", content = "一张图片的模板"),
        Template(id = "3_4", thumbnailRes = R.drawable.thumbnail_template_3_4, type = TemplateType.OTHER, pictureNum = 3, contentFile = "retro_template_3_4.json", content = "一张图片的模板"),
    )
}