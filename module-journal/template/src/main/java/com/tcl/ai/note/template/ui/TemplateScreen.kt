package com.tcl.ai.note.template.ui

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.repo.TemplateRepositoryImpl
import com.tcl.ai.note.utils.deviceDensity
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge

@SuppressLint("DesignSystem")
@Composable
fun TemplateScreen(
    selectedImages: List<String>,
    onTemplateSelected: (template: Template) -> Unit = {},
) {
    val templates by remember {
        mutableStateOf(TemplateRepositoryImpl.getTemplatesByPictureNum(selectedImages.size))
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(16.dp)),
        horizontalArrangement = Arrangement.spacedBy(isDensity440.judge(26.dp, 24.dp)), // 横向卡片间距
        verticalArrangement = Arrangement.spacedBy(24.dp), // 纵向卡片间距
        contentPadding = PaddingValues(horizontal = 24.dp) // 可选：四周间距
    ) {
        items(2) {
            Box(modifier = Modifier.height(0.dp))
        }
        items(templates.size) { index ->
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(
                        isDensity440.judge(
                            (GlobalContext.densityDpi <= deviceDensity).judge(
                                265.dp,
                                240.dp
                            ), 240.dp
                        )
                    )
                    .clip(RoundedCornerShape(16.dp))
                    .clickable {
                        onTemplateSelected(templates[index])
                    },
                contentAlignment = Alignment.Center,
            ) {
                Image(
                    painter = painterResource(id = templates[index].thumbnailRes),
                    contentDescription = stringResource(id = com.tcl.ai.note.resources.R.string.template_thumbnail_description),
                    contentScale = ContentScale.FillBounds,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(16.dp))
                        .border(
                            width = 1.dp,
                            color = Color(0x334983FF),
                            shape = RoundedCornerShape(16.dp)
                        )
                )
            }
        }
        items(2) {
            Box(modifier = Modifier.height(0.dp))
        }
    }
}