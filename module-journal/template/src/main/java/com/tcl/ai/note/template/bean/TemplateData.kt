package com.tcl.ai.note.template.bean

/**
 * 元素类型
 * */
object ElementType {
    const val IMAGE = "image" //图片
    const val TEXT = "text" //文本
}

data class JournalContentInfo(
    val journalId: Long,
    val pageIndex: Int,
    val statusBarHeight: Float,
    val topBarHeight: Float,
    val templateData: TemplateData? = null,
    val entFile: String? = null,
)

data class TemplateData(
    val id: String,
    val style: String,
    val layoutType: Int, // 0 从标题栏下方开始 1 全屏 2 从状态栏下方开始
    val elements: List<Element>
)

data class Element(
    val imageId: Int?, //图片id
    val type: String,
    val size: List<Float>,
    val position: List<Float>,
    val angle: Float,
    var content: String,
    val associatedId: List<Int>?, //文本关联图片id
    val cornerRadius: List<Float>?, // 圆角 [左上，右上，左下，右下]
    var dateTime: String?,
    var location: String?,
    var textColor: String?,
)