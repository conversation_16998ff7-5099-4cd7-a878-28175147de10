package com.tcl.ai.note.template.data

import com.tcl.ai.note.template.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType

object SevenPicturesTempList {
    val sevenPicturesTempList = listOf(
        Template(id = "7_1", thumbnailRes = R.drawable.thumbnail_template_7_1, type = TemplateType.OTHER, pictureNum = 7, contentFile = "simple_template_7_1.json", content = "一张图片的模板"),
        Template(id = "7_2", thumbnailRes = R.drawable.thumbnail_template_7_2, type = TemplateType.OTHER, pictureNum = 7, contentFile = "simple_template_7_2.json", content = "一张图片的模板"),
        Template(id = "7_3", thumbnailRes = R.drawable.thumbnail_template_7_3, type = TemplateType.OTHER, pictureNum = 7, contentFile = "fashion_template_7_3.json", content = "一张图片的模板"),
        Template(id = "7_4", thumbnailRes = R.drawable.thumbnail_template_7_4, type = TemplateType.OTHER, pictureNum = 7, contentFile = "retro_template_7_4.json", content = "一张图片的模板"),
    )
}