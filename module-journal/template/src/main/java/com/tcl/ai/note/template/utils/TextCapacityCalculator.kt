package com.tcl.ai.note.template.utils

import android.graphics.Paint
import android.graphics.Rect
import android.text.TextPaint
import com.tcl.ai.note.utils.sp2px
import kotlin.math.max

class TextCapacityCalculator {

    fun calculateMaxChars(
        width: Int,
        height: Int,
        languageType: Int,
        textSize: Float = 12F,
        lineSpacingMultiplier: Float = 1.2F,
        paddingHorizontal: Int = 0,
        paddingVertical: Int = 0
    ): Int {
        val effectiveWidth = width - 2 * paddingHorizontal
        val effectiveHeight = height - 2 * paddingVertical
        val paint = TextPaint()
        paint.textSize = textSize.sp2px

        val fontMetrics: Paint.FontMetrics = paint.fontMetrics
        val lineHeight: Float = (fontMetrics.descent - fontMetrics.ascent) * lineSpacingMultiplier
        val charWidth: Int = when (languageType) {
            0 -> measureCJKCharWidth(paint)
            1 -> measureLatinCharWidth(paint)
            else -> throw IllegalArgumentException("Invalid language type")
        }
        val charsPerLine = max(1.0, (effectiveWidth / charWidth).toDouble()).toInt()
        val maxLines = max(1.0, (effectiveHeight / lineHeight).toInt().toDouble()).toInt()
        return charsPerLine * maxLines
    }

    /**
     * 测量中文字符平均宽度
     */
    private fun measureCJKCharWidth(paint: Paint): Int {
        val bounds = Rect()
        paint.getTextBounds("字", 0, 1, bounds)
        return bounds.width()
    }

    /**
     * 测量英文字符平均宽度
     */
    private fun measureLatinCharWidth(paint: Paint): Int {
        // 使用包含多种宽度字符的文本测量平均宽度
        val sampleText = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
        val totalWidth: Float = paint.measureText(sampleText)
        return (totalWidth / sampleText.length).toInt()
    }
}