package com.tcl.ai.note.template.data

import com.tcl.ai.note.template.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType

object SixPicturesTempList {
    val sixPicturesTempList = listOf(
        Template(id = "6_1", thumbnailRes = R.drawable.thumbnail_template_6_1, type = TemplateType.OTHER, pictureNum = 6, contentFile = "simple_template_6_1.json", content = "一张图片的模板"),
        Template(id = "6_2", thumbnailRes = R.drawable.thumbnail_template_6_2, type = TemplateType.OTHER, pictureNum = 6, contentFile = "simple_template_6_2.json", content = "一张图片的模板"),
        Template(id = "6_3", thumbnailRes = R.drawable.thumbnail_template_6_3, type = TemplateType.OTHER, pictureNum = 6, contentFile = "fashion_template_6_3.json", content = "一张图片的模板"),
        Template(id = "6_4", thumbnailRes = R.drawable.thumbnail_template_6_4, type = TemplateType.OTHER, pictureNum = 6, contentFile = "retro_template_6_4.json", content = "一张图片的模板"),
    )
}