package com.tcl.ai.note.template.repo

import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType
import com.tcl.ai.note.template.data.EightPicturesTempList.eightPicturesTempList
import com.tcl.ai.note.template.data.FivePicturesTempList.fivePicturesTempList
import com.tcl.ai.note.template.data.FourPicturesTempList.fourPicturesTempList
import com.tcl.ai.note.template.data.NinePicturesTempList.ninePicturesTempList
import com.tcl.ai.note.template.data.OnePictureTempList.onePictureTempList
import com.tcl.ai.note.template.data.SevenPicturesTempList.sevenPicturesTempList
import com.tcl.ai.note.template.data.SixPicturesTempList.sixPicturesTempList
import com.tcl.ai.note.template.data.ThreePicturesTempList.threePicturesTempList
import com.tcl.ai.note.template.data.TwoPicturesTempList.twoPicturesTempList

object TemplateRepositoryImpl {
    //All template data
    private val templates = onePictureTempList + twoPicturesTempList + threePicturesTempList +
            fourPicturesTempList + fivePicturesTempList + sixPicturesTempList +
            sevenPicturesTempList + eightPicturesTempList + ninePicturesTempList

    // 实现接口方法
    fun getTemplatesByPictureNum(pictureNum: Int): List<Template> {
        return templates.filter { it.pictureNum == pictureNum }
    }

    fun getTemplateById(id: String): Template? {
        return templates.find { it.id == id }
    }

    fun getTemplatesByNumAndType(pictureNum: Int, type: TemplateType): List<Template> {
        return templates.filter { it.type == type && it.pictureNum == pictureNum }
    }
}