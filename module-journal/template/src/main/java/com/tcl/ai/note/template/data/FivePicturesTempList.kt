package com.tcl.ai.note.template.data

import com.tcl.ai.note.template.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType

object FivePicturesTempList {
    val fivePicturesTempList = listOf(
        Template(id = "5_1", thumbnailRes = R.drawable.thumbnail_template_5_1, type = TemplateType.OTHER, pictureNum = 5, contentFile = "simple_template_5_1.json", content = "一张图片的模板"),
        Template(id = "5_2", thumbnailRes = R.drawable.thumbnail_template_5_2, type = TemplateType.OTHER, pictureNum = 5, contentFile = "simple_template_5_2.json", content = "一张图片的模板"),
        Template(id = "5_3", thumbnailRes = R.drawable.thumbnail_template_5_3, type = TemplateType.OTHER, pictureNum = 5, contentFile = "fashion_template_5_3.json", content = "一张图片的模板"),
        Template(id = "5_4", thumbnailRes = R.drawable.thumbnail_template_5_4, type = TemplateType.OTHER, pictureNum = 5, contentFile = "retro_template_5_4.json", content = "一张图片的模板"),
    )
}