package com.tcl.ai.note.template.data

import com.tcl.ai.note.template.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType

object EightPicturesTempList {
    val eightPicturesTempList = listOf(
        Template(id = "8_1", thumbnailRes = R.drawable.thumbnail_template_8_1, type = TemplateType.OTHER, pictureNum = 8, contentFile = "simple_template_8_1.json", content = "一张图片的模板"),
        Template(id = "8_2", thumbnailRes = R.drawable.thumbnail_template_8_2, type = TemplateType.OTHER, pictureNum = 8, contentFile = "simple_template_8_2.json", content = "一张图片的模板"),
        Template(id = "8_3", thumbnailRes = R.drawable.thumbnail_template_8_3, type = TemplateType.OTHER, pictureNum = 8, contentFile = "fashion_template_8_3.json", content = "一张图片的模板"),
        Template(id = "8_4", thumbnailRes = R.drawable.thumbnail_template_8_4, type = TemplateType.OTHER, pictureNum = 8, contentFile = "retro_template_8_4.json", content = "一张图片的模板"),
    )
}