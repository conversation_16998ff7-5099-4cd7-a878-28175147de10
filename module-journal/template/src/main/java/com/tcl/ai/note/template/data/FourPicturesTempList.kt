package com.tcl.ai.note.template.data

import com.tcl.ai.note.template.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType

object FourPicturesTempList {
    val fourPicturesTempList = listOf(
        Template(id = "4_1", thumbnailRes = R.drawable.thumbnail_template_4_1, type = TemplateType.OTHER, pictureNum = 4, contentFile = "simple_template_4_1.json", content = "一张图片的模板"),
        Template(id = "4_2", thumbnailRes = R.drawable.thumbnail_template_4_2, type = TemplateType.OTHER, pictureNum = 4, contentFile = "simple_template_4_2.json", content = "一张图片的模板"),
        Template(id = "4_3", thumbnailRes = R.drawable.thumbnail_template_4_3, type = TemplateType.OTHER, pictureNum = 4, contentFile = "fashion_template_4_3.json", content = "一张图片的模板"),
        Template(id = "4_4", thumbnailRes = R.drawable.thumbnail_template_4_4, type = TemplateType.OTHER, pictureNum = 4, contentFile = "retro_template_4_4.json", content = "一张图片的模板"),
    )
}