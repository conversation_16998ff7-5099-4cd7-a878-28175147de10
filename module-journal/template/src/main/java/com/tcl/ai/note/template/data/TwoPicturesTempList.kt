package com.tcl.ai.note.template.data

import com.tcl.ai.note.template.R
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.template.bean.TemplateType

object TwoPicturesTempList {
    val twoPicturesTempList = listOf(
        Template(id = "2_1", thumbnailRes = R.drawable.thumbnail_template_2_1, type = TemplateType.OTHER, pictureNum = 2, contentFile = "simple_template_2_1.json", content = "一张图片的模板"),
        Template(id = "2_2", thumbnailRes = R.drawable.thumbnail_template_2_2, type = TemplateType.OTHER, pictureNum = 2, contentFile = "simple_template_2_2.json", content = "一张图片的模板"),
        Template(id = "2_3", thumbnailRes = R.drawable.thumbnail_template_2_3, type = TemplateType.OTHER, pictureNum = 2, contentFile = "fashion_template_2_3.json", content = "一张图片的模板"),
        Template(id = "2_4", thumbnailRes = R.drawable.thumbnail_template_2_4, type = TemplateType.OTHER, pictureNum = 2, contentFile = "retro_template_2_4.json", content = "一张图片的模板"),
    )
}