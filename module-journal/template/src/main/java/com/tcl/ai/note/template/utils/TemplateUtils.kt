package com.tcl.ai.note.template.utils

import com.tcl.ai.note.template.R

object TemplateUtils {
    fun getTemplateBg(contentFile: String): Int? {
        return when (contentFile) {
            "retro_template_1_4.json" -> R.drawable.bg_template_1_4
            "retro_template_2_4.json" -> R.drawable.bg_template_2_4
            "retro_template_3_4.json" -> R.drawable.bg_template_3_4
            "retro_template_4_4.json" -> R.drawable.bg_template_4_4
            "retro_template_5_4.json" -> R.drawable.bg_template_5_4
            "retro_template_6_4.json" -> R.drawable.bg_template_6_4
            "retro_template_7_4.json" -> R.drawable.bg_template_7_4
            "retro_template_8_4.json" -> R.drawable.bg_template_8_4
            "retro_template_9_4.json" -> R.drawable.bg_template_9_4
            "simple_template_1_1.json", "simple_template_2_1.json",
            "simple_template_3_1.json", "simple_template_4_1.json",
            "simple_template_5_1.json", "simple_template_6_1.json",
            "simple_template_7_1.json", "simple_template_8_1.json",
            "simple_template_9_1.json" -> R.drawable.bg_template_1_1
            else -> null
        }
    }
}