package com.tcl.ai.note.template.ui

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Surface
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.Text
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.ImageLoader
import coil3.compose.rememberAsyncImagePainter
import coil3.request.crossfade
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.journalbase.isOtgPhysicalKeyboardMode
import com.tcl.ai.note.journalbase.widget.indicator.StickIndicator
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.utils.deviceDensity
import com.tct.theme.core.designsystem.theme.TclTheme
import com.tcl.ai.note.utils.getNavBarHeight
import com.tcl.ai.note.utils.isDensity440
import com.tcl.ai.note.utils.judge
import com.tct.theme.core.designsystem.component.TclButton
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch

@SuppressLint("MutableCollectionMutableState")
@Composable
fun SelectImageScreen(
    //navController: NavController,
    albumName: String,
    images: List<String>,
    selectedImages: List<String> = emptyList(),
    onDismissRequest: () -> Unit,
    onItemClick: (String) -> Unit = {},
    onSelectTemplateBtnClicked: () -> Unit = {},
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val pagerState = rememberPagerState(initialPage = 0, pageCount = { images.size })
    val imageLoader = remember { // 用 remember 保证 imageLoader 只初始化一次
        ImageLoader.Builder(context)
            .crossfade(true)
            .build()
    }
    val imageWidth = isDensity440.judge(306.dp, 280.dp) // 根据屏幕密度调整图片宽度
    val navigationBarHeight = getNavBarHeight()
    val pagerTopPadding = remember {
        if (GlobalContext.densityDpi == deviceDensity) {
            isDensity440.judge(76.dp, 66.dp)
        } else if (GlobalContext.densityDpi < deviceDensity) {
            isDensity440.judge(86.dp, 56.dp)
        } else {
            isDensity440.judge(18.dp, 12.dp)
        }
    }
    val selectedTextTopPadding = remember {
        if (GlobalContext.densityDpi <= deviceDensity) {
            isDensity440.judge(52.dp, 48.dp)
        }  else {
            isDensity440.judge(32.dp, 24.dp)
        }
    }
    val scrollState = rememberScrollState()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = isDensity440.judge(18.dp, 20.dp)),
            contentAlignment = Alignment.CenterStart
        ) {
            Text(
                text = albumName,
                fontSize = isDensity440.judge(22.sp, 20.sp),
                fontWeight = FontWeight.Medium,
                modifier = Modifier.focusable()
                    .padding(horizontal = isDensity440.judge(26.dp, 24.dp)),
            )
        }

        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(pagerTopPadding)
        )

        HorizontalPager(
            state = pagerState,
            key = { images[it] }, // 用imageUrl作唯一key，最稳妥！
            pageSpacing = isDensity440.judge(26.dp, 24.dp),
            contentPadding = PaddingValues(start = isDensity440.judge(26.dp, 24.dp), end = 56.dp),
            modifier = Modifier
                .fillMaxWidth()
                .focusable()
        ) { page ->
            val imageUrl = images[page]
            val isSelected = selectedImages.contains(imageUrl)
            ImageCard(
                imageUrl = imageUrl,
                imageLoader = imageLoader,
                isSelected = isSelected,
                onClick = { onItemClick(imageUrl) },
                modifier = Modifier
                    .size(imageWidth)
                    .aspectRatio(1f)
            )
        }

        Spacer(
            modifier = Modifier
                .height(isDensity440.judge(25.dp, 23.dp))
                .fillMaxWidth()
        )

        // Indicator
        //PagerIndicator(pagerState = pagerState, pageCount = images.size)
        StickIndicator(totalCount = pagerState.pageCount,
            curIndex = pagerState.currentPage)

        Spacer(
            modifier = Modifier
                .height(selectedTextTopPadding)
                .fillMaxWidth()
        )

        Text(
            modifier = Modifier.focusable(),
            text = String.format(
                context.getString(R.string.msg_picture_selected),
                selectedImages.size
            ),
            fontSize = isDensity440.judge(17.sp, 16.sp),
            color = TclTheme.colorScheme.tctStanderTextPrimary,
        )

        Spacer(
            modifier = Modifier
                .weight(1f, fill = true)
                .fillMaxWidth()
        )
        // Placeholder for selected images count and blue button
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 24.dp, end = 24.dp, bottom = navigationBarHeight + 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            //val string = selectedImages.joinToString("SPLIT_@@@_SPLIT_###")
            Text(
                text = stringResource(id = R.string.text_create_journal_without_template),
                color = colorResource(id = com.tcl.ai.note.base.R.color.text_summary),
                fontSize = isDensity440.judge(15.sp, 14.sp),
                modifier = Modifier.focusable().padding(bottom = isDensity440.judge(13.dp, 12.dp))
            )

            TclButton(
                onClick = { /*navController.navigate("selectTemplate?selectedImages=${string}")*/onSelectTemplateBtnClicked.invoke() },
                enabled = selectedImages.isNotEmpty(),
                colors = ButtonColors(
                    containerColor = TclTheme.colorScheme.tctStanderAccentPrimary,
                    contentColor = TclTheme.colorScheme.tctStanderTextButton,
                    disabledContainerColor = TclTheme.colorScheme.tctStanderAccentPrimary,
                    disabledContentColor = TclTheme.colorScheme.tctStanderTextButton
                ),
                modifier = Modifier
                    .focusable()
                    .fillMaxWidth()
                    .heightIn(min = isDensity440.judge(48.dp, 44.dp)),
            ) {
                Text(text = stringResource(id = R.string.title_select_template))
            }
        }
    }
}

@Composable
fun ImageCard(
    imageUrl: String,
    imageLoader: ImageLoader,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    val selected by rememberUpdatedState(isSelected) // always the latest
    Box(
        modifier = modifier
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(20.dp)
            )
            .background(Color.LightGray, RoundedCornerShape(20.dp))
            .clickable(onClick = onClick),
        contentAlignment = Alignment.TopEnd
    ) {
        Image(
            painter = rememberAsyncImagePainter(model = imageUrl, imageLoader = imageLoader),
            contentDescription = null,
            modifier = modifier
                .fillMaxSize(),
            //.clip(RoundedCornerShape(20.dp)),
            contentScale = ContentScale.Crop
        )
        Image(
            modifier = Modifier
                .padding(top = 12.dp, end = 12.dp)
                .size(20.dp)
                .align(Alignment.TopEnd),
            painter = painterResource(id = selected
                .judge(R.drawable.ic_check_selected, R.drawable.ic_check_unselected)),
            contentDescription = isSelected.judge(stringResource(id = com.tcl.ai.note.base.R.string.checked_status), stringResource(id = com.tcl.ai.note.base.R.string.unchecked_status))
        )
    }
}

@Composable
fun PagerIndicator(pagerState: PagerState, pageCount: Int) {
    Row(
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        repeat(pageCount) { pageIndex ->
            Box(
                modifier = Modifier
                    .padding(horizontal = 4.dp)
                    .size(4.dp)
                    .background(
                        if (pagerState.currentPage == pageIndex) {
                            Color(0x334983FF)
                        } else {
                            Color(0xFFD8D8D8)
                        },
                        shape = CircleShape
                    )
            )
        }
    }
}