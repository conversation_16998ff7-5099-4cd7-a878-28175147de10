package com.tcl.ai.note.template.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.tcl.ai.note.journalbase.BottomSheetDialog
import com.tcl.ai.note.resources.R
import com.tcl.ai.note.template.bean.DestinationType
import com.tcl.ai.note.template.bean.Template
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.utils.ToastUtils

@Composable
fun TemplateMainScreen(
    albumName: String,
    images: List<String>,
    destination: DestinationType,
    selectedImages: List<String>,
    showSelectTemplateBack: Boolean,
    onTemplateSelected: (selectedImages: List<String>, template: Template) -> Unit = { _, _ -> },
    onImageItemClick: (image: String) -> Unit,
    onSelectTemplateBtnClicked: () -> Unit,
    onTemplateBackClicked: () -> Unit,
    backHandler: () -> Unit,
    onDismissRequest: () -> Unit = {},
) {
    BottomSheetDialog(
        modifier = Modifier,
        visible = true,
        onDismissRequest = {
            onDismissRequest()
        },
        canceledOnTouchOutside = false,
        showFullScreenCallBack = {

        },
        backHandler = {
            backHandler()
        },
        extraTopPadding = true,
        isNavigationBarsPaddingNeeded = true,
        onDismissCallback = {

        },
    ) {
        NoteTclTheme {
            if (destination == DestinationType.SelectImage) {
                SelectImageScreen(
                    albumName = albumName,
                    images = images,
                    selectedImages = selectedImages,
                    onDismissRequest = onDismissRequest,
                    onItemClick = onImageItemClick,
                    onSelectTemplateBtnClicked = onSelectTemplateBtnClicked
                )
            } else if (destination == DestinationType.SelectTemplate) {
                SelectTemplateScreen(
                    selectedImages = selectedImages,
                    showSelectTemplateBack = showSelectTemplateBack,
                    onDismissRequest = onDismissRequest,
                    onTemplateSelected = { template ->
                        onTemplateSelected(selectedImages, template)
                    },
                    onBackClicked = onTemplateBackClicked,
                )
            }
        }
    }
}