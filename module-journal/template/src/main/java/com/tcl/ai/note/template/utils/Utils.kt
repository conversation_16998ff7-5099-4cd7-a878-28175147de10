package com.tcl.ai.note.template.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode
import android.graphics.RectF
import android.media.ExifInterface
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.Logger
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Date

/**
 * 解析 assets 目录下的 JSON 文件数据
 */
fun loadJsonFromAssets(context: Context, fileName: String): String {
    return context.assets.open(fileName)
        .bufferedReader()
        .use { it.readText() }
}

/**
 * 图片 path 输入，输出等比缩放且居中裁剪的新 bitmap，内存友好
 * @param srcPath 原图片路径
 * @param rectW 目标框宽
 * @param rectH 目标框高
 * @return 新 bitmap（建议用完即回收），失败返回 null
 */
fun createCenterCropAndScaleBitmapFromPath(
    srcPath: String,
    dstPath: String,
    rectW: Int,
    rectH: Int,
    cornerRadius: List<Float>?
): Boolean {
    //Logger.d("zzz", "createCenterCropAndScaleBitmapFromPath start")
    if (!File(srcPath).exists()) return false
    // 1. 获取图片方向
    val rotation = getImageRotation(srcPath)
    // 2. 获取原始图片尺寸
    val options = BitmapFactory.Options().apply {
        inJustDecodeBounds = true
    }
    BitmapFactory.decodeFile(srcPath, options)
    val srcW = options.outWidth
    val srcH = options.outHeight
    //Logger.d("zzz", "createCenterCropAndScaleBitmapFromPath srcW: $srcW, srcH: $srcH， rotation: $rotation")

    // inSampleSize 只能2的倍数缩放，要使decode出来的边>=目标边
    options.inJustDecodeBounds = false
    options.inSampleSize = calculateInSampleSize(srcW, srcH, GlobalContext.screenWidth  / 4, GlobalContext.screenHeight / 4)
    options.inPreferredConfig = Bitmap.Config.RGB_565

    val decoded = BitmapFactory.decodeFile(srcPath, options)?: return false

    val rotated = rotateBitmapIfNeeded(decoded, rotation) // 需要旋转图片，否则处理后的图片插入画布后显示的方向不对
    val rotatedW = rotated.width
    val rotatedH = rotated.height
    if (decoded != rotated && !decoded.isRecycled) decoded.recycle()
    //Logger.d("zzz", "createCenterCropAndScaleBitmapFromPath rotated w: $rotatedW, rotated h: $rotatedH， rotation: $rotation")

    // 3. 精确缩放
    val scale = calculateTargetImageScaleSize(
        rotatedW, rotatedH,
        rectW, rectH
    )

    val scaledW = (rectW / scale).toInt()
    val scaledH = (rectH / scale).toInt()

    // 4. 居中裁剪
    val offsetX = (rotatedW - scaledW) / 2
    val offsetY = (rotatedH - scaledH) / 2
    //Logger.d("zzz", "createCenterCropAndScaleBitmapFromPath scale: $scale, rectW: $rectW, rectH: $rectH, offsetX: $offsetX, offsetY: $offsetY")
    val result = Bitmap.createBitmap(rotated, offsetX, offsetY, scaledW, scaledH)
    if (rotated != result && !rotated.isRecycled) rotated.recycle()

    // 5. 圆角处理
    cornerRadius?.let {
        val rounded = roundCornersBitmap(result, cornerRadius, scale)
        //Logger.d("zzz", "createCenterCropAndScaleBitmapFromPath roundCornersBitmap")
        val saveResult = saveBitmap(rounded, dstPath)
        //Logger.d("zzz", "createCenterCropAndScaleBitmapFromPath end 1")
        return saveResult
    }?: run {
        val saveResult = saveBitmap(result, dstPath)
        //Logger.d("zzz", "createCenterCropAndScaleBitmapFromPath end 2")
        return saveResult
    }
}

fun rotateBitmapIfNeeded(bitmap: Bitmap, rotation: Int): Bitmap {
    if (rotation == 0) return bitmap
    val matrix = Matrix()
    matrix.postRotate(rotation.toFloat())
    val rotated = Bitmap.createBitmap(bitmap, 0, 0, bitmap.width, bitmap.height, matrix, true)
    if (rotated != bitmap && !bitmap.isRecycled) bitmap.recycle()
    return rotated
}

fun getImageRotation(path: String): Int {
    return try {
        val exif = ExifInterface(path)
        when (exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL)) {
            ExifInterface.ORIENTATION_ROTATE_90 -> 90
            ExifInterface.ORIENTATION_ROTATE_180 -> 180
            ExifInterface.ORIENTATION_ROTATE_270 -> 270
            else -> 0 // 不需要旋转
        }
    } catch (e: Exception) {
        0 // 失败时默认不旋转
    }
}

/**
 * 计算最佳 inSampleSize，保证解码后宽高≥期望缩放目标，用于大图友好
 */
fun calculateInSampleSize(
    srcW: Int, srcH: Int,
    reqW: Int, reqH: Int
): Int {
    var inSampleSize = 1
    if (srcH > reqH && srcW > reqW) {
        val halfHeight = srcH / 2
        val halfWidth = srcW / 2
        while ((halfHeight / inSampleSize > reqH) &&
            (halfWidth / inSampleSize > reqW)) {
            inSampleSize *= 2
        }
    }
    return inSampleSize
}

fun calculateTargetImageScaleSize(
    srcW: Int, srcH: Int,
    rectW: Int, rectH: Int
): Float {
    when {
        srcW > rectW && srcH > rectH -> {
            var scale = 1f
            while (rectW * scale < srcW && rectH * scale < srcH) {
                scale += 0.5f
            }
            // 如果原图大于目标框，则需要缩小原图
            return if (scale == 1f) 1f else 1f / (scale - 0.5f)
        }
        else -> {
            var scale = 1f
            while (srcW * scale < rectW || srcH * scale < rectH) {
                scale += 0.5f
            }
            // 如果原图小于目标框，则需要放大原图
            return scale
        }
    }
}

fun roundCornersBitmap(
    bitmap: Bitmap,
    radius: List<Float>,
    scale: Float
): Bitmap {
    val output = Bitmap.createBitmap(bitmap.width, bitmap.height, Bitmap.Config.ARGB_8888)
    val canvas = Canvas(output)
    val paint = Paint(Paint.ANTI_ALIAS_FLAG)
    paint.isFilterBitmap = true

    // 8个半径分别代表四个角（顺时针，每个角[x轴半径, y轴半径]，都一样就是圆形）
    val radii = floatArrayOf(
        radius[0] / scale, radius[0] / scale,      // 左上
        radius[1] / scale, radius[1] / scale,      // 右上
        radius[3] / scale, radius[3] / scale,      // 右下
        radius[2] / scale, radius[2] / scale       // 左下
    )

    val path = Path()
    path.addRoundRect(
        RectF(0f, 0f, bitmap.width.toFloat(), bitmap.height.toFloat()),
        radii,
        Path.Direction.CW
    )
    canvas.drawPath(path, paint)
    paint.xfermode = PorterDuffXfermode(PorterDuff.Mode.SRC_IN)
    canvas.drawBitmap(bitmap, 0f, 0f, paint)
    return output
}

fun saveBitmap(
    bitmap: Bitmap,
    savePath: String,
    format: Bitmap.CompressFormat = Bitmap.CompressFormat.WEBP_LOSSY, //要处理为PNG格式，否则某些圆角会变黑色
    quality: Int = 90
): Boolean {
    return try {
        val out = FileOutputStream(savePath)
        bitmap.compress(format, quality, out)
        out.flush()
        out.close()
        true
    } catch (e: Exception) {
        e.printStackTrace()
        false
    }
}

fun getNewImagePath(
    srcPath: String,
    journalId: Long
): String {
    if (!File(srcPath).exists()) {
        return ""
    }
    val dir = File(GlobalContext.instance.cacheDir, "journalImages/$journalId")
    if (!dir.exists()) {
        dir.mkdirs()
    }
    val fileName = System.currentTimeMillis().toString() + "." + File(srcPath).extension
    Logger.d("zzz", "getNewImagePath srcPath: $srcPath, fileName: $fileName")
    return File(dir, fileName).absolutePath
}