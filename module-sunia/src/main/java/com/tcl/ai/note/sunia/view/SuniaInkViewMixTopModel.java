package com.tcl.ai.note.sunia.view;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.PointF;
import android.graphics.Rect;
import android.graphics.RectF;
import android.os.Build;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;

import androidx.annotation.NonNull;

import com.sunia.penengine.sdk.operate.canvas.IScreen;
import com.sunia.penengine.sdk.operate.canvas.ScaleInfo;
import com.sunia.penengine.sdk.operate.canvas.ScreenInfo;
import com.sunia.penengine.sdk.operate.edit.LayerMode;
import com.sunia.singlepage.sdk.InkFunc;
import com.sunia.singlepage.sdk.InkSDK;
import com.sunia.singlepage.sdk.listener.ICanvasChangedListener;
import com.sunia.viewlib.view.InkBackgroundView;
import com.sunia.viewlib.view.model.IInkViewModel;
import com.tcl.ai.note.GlobalContext;
import com.tcl.ai.note.utils.Logger;

import java.lang.ref.WeakReference;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 原InkViewMixTopModel
 */
public class SuniaInkViewMixTopModel implements IInkViewModel, SurfaceHolder.Callback{
    private static final String TAG = "InkViewMixTopModel";
    private AbsSuniaDrawInkView inkView;
    private Context context;
    private InkFunc inkFunc;
    private LayerMode layerMode;
    private MyHandler mainHandler;
    private boolean initFinished;
    private CanvasDrawView canvasDrawView;
    private SurfaceView surfaceView;
    private IInkViewListener inkViewListener;
    private int oldWidth;
    private int oldHeight;
    private int visibleWidth, visibleHeight;
    private int lastVisibleWidth, lastVisibleHeight;
    private int surfaceWidth, surfaceHeight;
    private int screenState = ICanvasChangedListener.STATE_OTHER;
    private IScreen iScreen;
    private final ReentrantLock lock = new ReentrantLock();
    private boolean needRefresh = false;
    private AtomicBoolean isFirstAddView = new AtomicBoolean(true);

    public SuniaInkViewMixTopModel(AbsSuniaDrawInkView inkView, LayerMode layerMode) {
        this.inkView = inkView;
        this.context = inkView.getContext();
        this.inkFunc = inkView.getInkFunc(this);
        this.layerMode = layerMode;
        // 设置日志级别，注意路径必须带个.否则会报错，sunia问题
        String path = GlobalContext.getAppContext().getFilesDir().getPath() + "/sunia.log";
        // 0 Close log，关闭日志
        // 1 Error - Display error message， 异常信息
        // 2 Info - Info and error information are displayed， 一般信息和错误信息
        // 3、Debug - The debugging information, info information,
        // and error information are displayed 显示调试信息、一般信息和错误信息
        int logLevel = 0;
        if (Logger.INSTANCE.isLogEnabled()) {
            logLevel = 3;
            Logger.i(TAG, "sunia log enabled");
        } else {
            Logger.i(TAG, "sunia log disable");
        }
        InkSDK.setLog(logLevel, path);
        mainHandler = new MyHandler(this);
    }

    @Override
    public void surfaceCreated(@NonNull SurfaceHolder holder) {

    }

    @Override
    public void surfaceChanged(@NonNull SurfaceHolder holder, int format, int width, int height) {
        surfaceWidth = width;
        surfaceHeight = height;
        if (isFirstAddView.get()) {
            surfaceView.setTranslationX(-(int) Math.hypot(surfaceWidth, surfaceHeight));
            isFirstAddView.set(false);
        }
        onSurfaceSizeChanged();
        Log.d(TAG, "surfaceChanged");
    }

    @Override
    public void surfaceDestroyed(@NonNull SurfaceHolder holder) {
        lock.lock();
        if (iScreen != null) {
            iScreen.destroyScreen();
            iScreen = null;
        }
        lock.unlock();
        Log.d(TAG, "surfaceDestroyed");
    }

    private InkBackgroundView inkBackgroundView;
    @SuppressLint("SetJavaScriptEnabled")
    @Override
    public void addDrawView() {
//        inkBackgroundView = new InkBackgroundView(context);
//        inkView.addView(inkBackgroundView);

        canvasDrawView = new CanvasDrawView(context, inkFunc);
        canvasDrawView.setLayerMode(layerMode);
        canvasDrawView.setBackgroundColor(Color.TRANSPARENT);
        inkView.addView(canvasDrawView);

        surfaceView = new SurfaceView(context);
        surfaceView.getHolder().addCallback(this);
        surfaceView.getHolder().setFormat(PixelFormat.TRANSLUCENT);
        // surfaceView.setZOrderMediaOverlay(false);
        surfaceView.setZOrderOnTop(true);
        inkView.addView(surfaceView);
        isFirstAddView.set(true);

        surfaceVisibleState.set(SURFACE_STATE_INVISIBLE);
    }

    @Override
    public void onInitFinish() {
        initFinished = true;
        onViewSizeChanged(visibleWidth, visibleHeight);
    }

    @Override
    public void onSizeChanged(int width, int height) {
        sizeChanged(width, height);
    }

    private final int SURFACE_STATE_VISIBLE = 0;
    private final int SURFACE_STATE_INVISIBLE = 2;
    private final int SURFACE_STATE_CUT = 3;
    private final AtomicInteger surfaceVisibleState = new AtomicInteger();
    @Override
    public void rendToScreen(RectF rectF, int state) {
        screenState = state;
        Log.d(TAG, "rendToScreen: " + rectF + ", state: " + screenState + " screenState: "
                + screenState + ", " + surfaceVisibleState
                + ", visible:" + surfaceView.getVisibility() + " t " + surfaceView.getTranslationX());
        if (screenState == ICanvasChangedListener.STATE_DRAW_COMPLETED) {
            if (surfaceVisibleState.get() == SURFACE_STATE_VISIBLE) {
                CountDownLatch countDownLatch = new CountDownLatch(1);
                mainHandler.post(() -> {
                    rendToCanvas();
                    countDownLatch.countDown();
                });
                try {
                    countDownLatch.await();
                } catch (InterruptedException e) {
                }
            }
            postRendToCanvas();
        } else {
            int visibleState = surfaceVisibleState.get();
            if (visibleState == SURFACE_STATE_INVISIBLE) {
                if (mainHandler != null) {
                    CountDownLatch countDownLatch = new CountDownLatch(1);
                    mainHandler.post(() -> {
                        surfaceCut();
                        countDownLatch.countDown();
                    });
                    try {
                        countDownLatch.await();
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            if (visibleState == SURFACE_STATE_CUT) {
                if (mainHandler != null) {
                    if (!mainHandler.hasCallbacks(showSurfaceRunnable)) {
                        mainHandler.post(showSurfaceRunnable);
                    }
                }
            }
            if (InkSDK.surfaceFullScreenRefresh && layerMode == LayerMode.layerModel6) {
                // 模式6需要刷全屏
                rectF.set(0, 0, surfaceWidth, surfaceHeight);
            }
            if (needRefresh) {
                // 第一次显示Surface需要刷全屏
                rectF.set(0, 0, surfaceWidth, surfaceHeight);
                if (visibleState == SURFACE_STATE_VISIBLE) {
                    needRefresh = false;
                }
            }
            lock.lock();
            if (iScreen != null) {
                ScreenInfo screenInfo = new ScreenInfo();
                RectF rendRectF = new RectF(rectF);
                screenInfo.screen = iScreen;
                Rect srcRectF = new Rect((int) Math.floor(rectF.left),
                        (int) Math.floor(rectF.top),
                        (int) Math.ceil(rectF.right),
                        (int) Math.ceil(rectF.bottom));
                screenInfo.srcRect = srcRectF;
                screenInfo.dstRect = new Rect(srcRectF);
                long t = System.nanoTime();
                inkFunc.rendToScreen(rendRectF, screenInfo);
                Log.d(TAG, "rendToScreen iScreen: " + iScreen + ", " + srcRectF + ", " + rendRectF + " time: " + (System.nanoTime() - t) / 1000000f);
            }
            lock.unlock();
        }
    }

    Runnable showSurfaceRunnable = new Runnable() {
        @Override
        public void run() {
            surfaceViewVisible();
        }
    };

    private void rendToCanvas() {
        canvasDrawView.invalidate();
        if (inkBackgroundView != null) {
            inkBackgroundView.redraw();
        }
        if (canvasDrawView.getVisibility() != View.VISIBLE) {
            canvasDrawView.setVisibility(View.VISIBLE);
            surfaceView.setTranslationX(-(int) Math.hypot(surfaceWidth, surfaceHeight));
            surfaceVisibleState.set(SURFACE_STATE_INVISIBLE);
        }
    }

    private void postRendToCanvas(){
        canvasDrawView.postInvalidate();
        if (inkBackgroundView != null) {
            inkBackgroundView.postRedraw();
        }
    }

    private void surfaceViewVisible() {
        if (surfaceVisibleState.get() == SURFACE_STATE_CUT) {
            // 标记为需要全局刷新
            needRefresh = true;
            // 普通view也同时上屏
            canvasDrawView.postInvalidate();
            if (inkBackgroundView != null) {
                inkBackgroundView.redraw();
            }
            // 设置等待状态, 注意这两个方法调用时序，不可调换顺序
            lock.lock();
            if (iScreen != null) {
                surfaceVisibleState.set(SURFACE_STATE_VISIBLE);
                surfaceView.setTranslationX(0);
                canvasDrawView.setVisibility(View.INVISIBLE);
            }
            lock.unlock();
        }
    }

    // 落笔显示SurfaceView的一部分
    private void surfaceCut() {
        if (surfaceVisibleState.get() == SURFACE_STATE_INVISIBLE) {
            // 标记为需要全局刷新
            needRefresh = true;
            // 普通view也同时上屏
            canvasDrawView.postInvalidate();
            if (inkBackgroundView != null) {
                inkBackgroundView.redraw();
            }
            // 设置等待状态, 注意这两个方法调用时序，不可调换顺序
            lock.lock();
            if (iScreen != null) {
                surfaceVisibleState.set(SURFACE_STATE_CUT);
                float currentX = surfaceView.getTranslationX();
                surfaceView.setTranslationX(currentX+1f);
                canvasDrawView.setVisibility(View.VISIBLE);
            }
            lock.unlock();
        }
    }

    private void clearSurface() {
        lock.lock();
        if (iScreen != null) {
            iScreen.clearScreen(Color.TRANSPARENT);
        }
        lock.unlock();
    }

    @Override
    public void setInkViewListener(IInkViewListener inkViewListener) {
        this.inkViewListener = inkViewListener;
    }

    @Override
    public void onScaleChanged(float scale, float offsetX, float offsetY, PointF center) {
        if (inkBackgroundView != null) {
            inkBackgroundView.doScale(scale, offsetX, offsetY);
        }
    }

    private int lastWriteState = ICanvasChangedListener.STATE_DRAW_COMPLETED;
    @Override
    public void onCanvasStateChanged(int state) {
        if (state == lastWriteState) {
            return;
        }
        // 状态变化
        if (state == ICanvasChangedListener.STATE_DRAWING_PROGRESS) {
            surfaceCut();
        }  else if (state == ICanvasChangedListener.STATE_DRAW_COMPLETED){
            rendToCanvas();
        }
        lastWriteState = state;

    }

    @Override
    public void release() {
        Log.d(TAG, "release");
        canvasDrawView.release();
        lock.lock();
        if (iScreen != null) {
            iScreen.destroyScreen();
            iScreen = null;
        }
        lock.unlock();
    }

    private void onSurfaceSizeChanged() {
        if (surfaceWidth == 0 || surfaceHeight == 0) {
            return;
        }
        if (!initFinished) {
            return;
        }
        lock.lock();
        if (iScreen != null) {
            iScreen.destroyScreen();
        }
        iScreen = inkFunc.createScreen(surfaceView.getHolder().getSurface(), true);
        lock.unlock();
    }

    private void sizeChanged(int w, int h) {
        lastVisibleWidth = w;
        lastVisibleHeight = h;
        if (visibleWidth == w && visibleHeight == h) {
            return;
        }
        // 废代码始终ture，不return
        if (!inkViewListener.canVisibleSizeChanged(w)) {
            return;
        }
        inkFunc.getSelectEditFunc().finishSelect();
        mainHandler.removeMessages(VISIBLE_SIZE_CHANGED);
        int delayTime = 150;
        if(Build.BRAND.equalsIgnoreCase("alps")){
            delayTime = 600;
        }
        mainHandler.sendEmptyMessageDelayed(VISIBLE_SIZE_CHANGED, delayTime);
    }

    private void onViewSizeChanged(int w, int h) {
        if (w == 0 || h == 0) {
            return;
        }
        if (!initFinished) {
            return;
        }
        inkFunc.setVisibleSize(w, h);
        inkFunc.setBackgroundColor(Color.TRANSPARENT);
        if (oldWidth > 0f && oldWidth != visibleWidth) {
            ScaleInfo scaleInfo = new ScaleInfo();
            float ratio = visibleWidth * 1f / oldWidth;
            scaleInfo.scale = inkFunc.getScaleInfo().scale;
            scaleInfo.offsetX = inkFunc.getScaleInfo().offsetX * ratio;
            scaleInfo.offsetY = inkFunc.getScaleInfo().offsetY * ratio;
            inkFunc.updateScaleInfo(scaleInfo);
        }
        oldWidth = visibleWidth;
        oldHeight = visibleHeight;
        if (!initFinishFlag) {
            if (inkViewListener != null) {
                inkViewListener.onInitAndSetVisibleSizeFinish();
            }
            initFinishFlag = true;
        }
        onSurfaceSizeChanged();
    }

    private boolean initFinishFlag;

    private void doSizeChanged(int w, int h) {
        visibleWidth = w;
        visibleHeight = h;
        if (inkViewListener != null) {
            inkViewListener.onVisibleSizeChanged(visibleWidth, visibleHeight);
        }
        onViewSizeChanged(visibleWidth, visibleHeight);
    }
    private void doSizeChanged() {
        doSizeChanged(lastVisibleWidth, lastVisibleHeight);
    }

    private static final int VISIBLE_SIZE_CHANGED = 1;
    private static final int MSG_DRAW_CANVAS = 2;
    private static final int MSG_SURFACE_VISIBLE = 3;
    private static final int MSG_SURFACE_CLEAR = 4;
    private static class MyHandler extends Handler {
        private WeakReference<SuniaInkViewMixTopModel> weakReference;
        private MyHandler(SuniaInkViewMixTopModel inkViewMixTopModel) {
            weakReference = new WeakReference<>(inkViewMixTopModel);
        }

        @Override
        public void handleMessage(@NonNull Message msg) {
            SuniaInkViewMixTopModel inkViewMixTopModel = weakReference.get();
            if (inkViewMixTopModel == null) {
                return;
            }
            if (msg.what == VISIBLE_SIZE_CHANGED) {
                inkViewMixTopModel.doSizeChanged();
            } else if (msg.what == MSG_DRAW_CANVAS) {
                inkViewMixTopModel.rendToCanvas();
            } else if (msg.what == MSG_SURFACE_VISIBLE) {
                inkViewMixTopModel.surfaceViewVisible();
            } else if (msg.what == MSG_SURFACE_CLEAR) {
                inkViewMixTopModel.clearSurface();
            }
        }
    }
}
