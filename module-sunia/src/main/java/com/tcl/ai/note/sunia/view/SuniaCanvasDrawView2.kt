package com.tcl.ai.note.sunia.view

import android.content.Context
import android.graphics.*
import android.os.Build
import android.util.AttributeSet
import android.util.Log
import android.view.View
import com.sunia.penengine.sdk.operate.edit.LayerMode
import com.sunia.singlepage.sdk.InkFunc

/**
 * 对应安格斯DEMO的CanvasDrawView2，仅仅修改名字
 *
 * 纯手绘
 *
 */
internal class SuniaCanvasDrawView2 : View {
    private val TAG: String = "CanvasDrawView"
    var hardwareBitmapHigh: Bitmap? = null
    var hardwareBitmap: Bitmap? = null
    private var paint: Paint? = null
    private var layerMode: LayerMode = LayerMode.layerModel1
    private var inkFunc: InkFunc? = null
    private val rect = RectF(0f, 0f, 200f, 200f)
    private val debug = false
    private var renderNode: RenderNode? = null

    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    constructor(context: Context?, inkFunc: InkFunc?) : super(context) {
        this.inkFunc = inkFunc
        renderNode = RenderNode("CanvasDrawView")
        renderNode?.setUseCompositingLayer(true, null)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        var layer = canvas?.saveLayer(null, null, Canvas.ALL_SAVE_FLAG)
        renderNode?.apply {
            discardDisplayList()
            beginRecording().let { canvasRender ->
                hardwareBitmap?.let { canvasRender.drawBitmap(it, 0f, 0f, getPaint()) }
            }
            canvas?.drawRenderNode(this)
            endRecording()
        }
        if (debug) {
            canvas?.save()
            canvas?.clipRect(rect)
            canvas?.drawColor(Color.RED)
            canvas?.restore()
        }
        layer?.let { canvas?.restoreToCount(it) }
    }

    fun getPaint(): Paint {
        if (paint == null) {
            paint = Paint()
            paint!!.style = Paint.Style.FILL_AND_STROKE
            paint!!.color = Color.RED
            paint!!.setXfermode(PorterDuffXfermode(PorterDuff.Mode.SRC))
        }
        return paint!!
    }


    override fun invalidate() {
        super.invalidate()
        //为调试hardwareBitmap内容一般情况不要开启
//        viewHardwareBitmap()
    }

    fun updateHardwareBitmap(width: Int, height: Int) {
        val millis = System.currentTimeMillis()
        if (hardwareBitmap == null) {
            val mutableBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            hardwareBitmap = mutableBitmap.copy(Bitmap.Config.HARDWARE, false)
            Log.d(TAG, "hardwareBitmap 1:" + (System.currentTimeMillis() - millis))
            mutableBitmap.recycle()
        } else if (hardwareBitmap?.width != width || hardwareBitmap?.height != height) {
            hardwareBitmap?.recycle()
            hardwareBitmap = null
            val mutableBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            hardwareBitmap = mutableBitmap.copy(Bitmap.Config.HARDWARE, false)
            Log.d(TAG, "hardwareBitmap 2:" + (System.currentTimeMillis() - millis))
            mutableBitmap.recycle()
        }
        renderNode?.let {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                renderNode?.setPosition(0, 0, width, height)
            }
        }
    }

    fun release() {
        if (hardwareBitmap != null && hardwareBitmap?.isRecycled == false) {
            hardwareBitmap?.recycle()
            hardwareBitmap = null
        }
        if (hardwareBitmapHigh != null && hardwareBitmapHigh?.isRecycled == false) {
            hardwareBitmapHigh?.recycle()
            hardwareBitmapHigh = null
        }
    }

    fun updateHardwareBitmapHight(width: Int, height: Int) {
        val millis = System.currentTimeMillis()
        if (hardwareBitmapHigh == null) {
            val mutableBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            hardwareBitmapHigh = mutableBitmap.copy(Bitmap.Config.HARDWARE, false)
            Log.d(TAG, "hardwareBitmapHight 1:" + (System.currentTimeMillis() - millis))
            mutableBitmap.recycle()
        } else if (hardwareBitmapHigh?.width != width || hardwareBitmapHigh?.height != height) {
            hardwareBitmapHigh?.recycle()
            hardwareBitmapHigh = null
            val mutableBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            hardwareBitmapHigh = mutableBitmap.copy(Bitmap.Config.HARDWARE, false)
            Log.d(TAG, "hardwareBitmapHight 2:" + (System.currentTimeMillis() - millis))
            mutableBitmap.recycle()
        }
    }

    fun setLayerMode(layerMode: LayerMode) {
        this.layerMode = layerMode
    }

    interface DrawListener {
        fun onDrawFinished();
    }
}