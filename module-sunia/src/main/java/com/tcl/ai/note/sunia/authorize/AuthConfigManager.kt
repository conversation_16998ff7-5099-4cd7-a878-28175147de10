package com.tcl.ai.note.sunia.authorize
import com.tcl.ai.note.base.BuildConfig
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isLiteVersion
import com.tcl.ai.note.utils.isTablet

/**
 *  author : junze.liu
 *  date : 2025-09-04 16:05
 *  description : 鉴权参数管理类
 */

class AuthConfigManager private constructor(
    val appId: String,
    val publicKey: String,
    val offlineCert: String
) {
    companion object {
        private const val TAG = "AuthConfigManager"

        // Debug版本鉴权参数
        private const val DEBUG_APP_ID = "20250380110525152"
        private const val DEBUG_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAkhvrYGuFDPJMCXIzX9hy8RpuZXTYHhOrExAm2EZZawoOkgw2wytXqkgE8tp6iXEZMoAlxEoRzpraX84kHQaiLRty66Xs4Nu3uG6dmuKCCU0DXK26OwcTMe4uFiu4GW6HNZTrsXS8Xw+W2TY7R4bTNSwsakhJYG7eg2x0HfhWuY2nGs7HrK1dGyywtqqPhnPaLrwN3aG9Z42CPhGz+2+1c1wAQbmx3LhNiLzD4vGgBEABU5/pZlnNizG3sylZzMuqfHen7eySq5NpPAOSFNtHDmNmomk/1B3hhRWWpIHLWqvwysuO8OOh+9ln6T6vRfADvtn/QowRwnNqGUgLJK5wLwIDAQAB"
        private const val DEBUG_OFFLINE_CERT = ""

        // 平板鉴权参数
        private const val TABLET_APP_ID = "20250380110525152"
        private const val TABLET_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmk8l/bJjaByugOGgUMb7AhtYGyZ3rvZPQ2ueFeDzK+8HiKdysmAa+VUBfAOPeT3eOHthGLd4ZzTvNQQy6lvfAnB0HPeduOMhlNaHHzER4ZpoXgDGyQPaWScQWFts8oDW4pY4MAzx2qn3FnDW8/N/n0hwaG8EdHK3CU5Nzz7X53UHnQtaTJ9lVuZISj7A0rhORps8/Ay9EWZDtA5QlSBC/2dvp6IjgvOXOuR62AXkN925jUg4dsd2CFBAb3oSn8vJH5X1Zv/6TMjhZ6+P0ZionLGYk/EIim3UDB5ANLXg4OSDDmQ8II+R8262ewkdu5EMEwoXWyn6t9NIQb67E2fm0wIDAQAB"
        private const val TABLET_OFFLINE_CERT = "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"


        // 手机鉴权参数
        private const val DEFAULT_APP_ID = "202508238174171331"
        private const val DEFAULT_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAq83bBehqtSY4cwSJmGjOifNOfvInzSCZ4kiIu6/PqaXmhxLyE0sRTba7S01S4EJnign1yWrU3Ipjzu8KUGzI89ROol7JDibcTKvGlelJY8STUYDKZXMVcfBJHwB5+j7QjNPgXV6KVvZYsbuzniB7Ba8xS1hyMc4SGFJYq4xj3h1BAKDU/aVplTXhQSqsV83Ojv5o/XEtium4PY0YuzsxV8ru5IwYPlopKM7RF3BWfAwxBTk5wF5/1vz7T5J5eE20NV4vtIYT0wL3z817IcXum+rCkP2jcHldmVLv0FPBKlJDYczAH9hqTKgx340AP3CHXBG8BqRcAvXcHKveSyYCVwIDAQAB"
        private const val DEFAULT_OFFLINE_CERT = "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"

        // 阉割版项目鉴权参数
        private const val LITE_APP_ID = "202508238180064155"
        private const val LITE_PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlspNQpG9gDzbbMaM4YGFoLU4X+2koglTneyToFzXEIXKgNTxLeq7ZhHrj+NrDuoc1/SpuHxmOURTDHppYzVnPN4PO4b3ZFuvRcJ14YDyUqVHb5zw/oLf0ir99frtYE/d8HwrNF59oApmyZ2BRnPinf0mBnaVgijK107LfiSCm60AyRO5CFZiC4qe4SR6xoxb7A8/iOB2s7U9URs0Gdw+e+KcFIDyS5IrcpDKeSa0+jtbaeWpfE6YNvr8G9yCg1IvJCxFd/5MWuEDDrOVqAGuNoxndKcOgmJxsiXIPQO1fmSxMcgHOlO3submiaxvdCHoFu3mUm0kuPtELwEx5ak+RwIDAQAB"
        private const val LITE_OFFLINE_CERT = "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"


        fun create(): AuthConfigManager {
            return if (BuildConfig.DEBUG) {
                Logger.d(TAG, "create, Using DEBUG auth parameters")
                AuthConfigManager(DEBUG_APP_ID, DEBUG_PUBLIC_KEY, DEBUG_OFFLINE_CERT)
            } else {
                // 平板
                if (isTablet) {
                    Logger.i(TAG, "create, Detected tablet device, using tablet auth parameters")
                    return AuthConfigManager(TABLET_APP_ID, TABLET_PUBLIC_KEY, TABLET_OFFLINE_CERT)
                }
                val config = matchAuthParams()
                Logger.i(TAG, "create, matchAuthParameters config: $config")
                AuthConfigManager(config.appId, config.publicKey, config.offlineCert)
            }
        }

        //匹配项目类型
        private fun matchAuthParams(): AuthConfig {
            return when{
                isLiteVersion -> {
                    AuthConfig(LITE_APP_ID,LITE_PUBLIC_KEY,LITE_OFFLINE_CERT)
                }
                else -> {
                    AuthConfig(DEFAULT_APP_ID,DEFAULT_PUBLIC_KEY,DEFAULT_OFFLINE_CERT)
                }
            }
        }
    }

    private data class AuthConfig(
        val appId: String,
        val publicKey: String,
        val offlineCert: String
    )
}

