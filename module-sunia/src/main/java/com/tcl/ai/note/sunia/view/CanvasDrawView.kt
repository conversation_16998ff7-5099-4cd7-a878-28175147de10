package com.tcl.ai.note.sunia.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.util.Log
import android.view.View
import com.sunia.penengine.sdk.operate.edit.LayerMode
import com.sunia.singlepage.sdk.InkFunc

/**
 * 原sunia demo的CanvasDrawView
 */
class CanvasDrawView: View {
    private val TAG: String = "CanvasDrawView"
    var hardwareBitmap: Bitmap? = null
    var hardwareBitmapHigh: Bitmap? = null
    private var paint: Paint? = null
    private var layerMode: LayerMode = LayerMode.layerModel1
    private var inkFunc: InkFunc? = null

    constructor(context: Context?) : super(context)
    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context?, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    constructor(context: Context?, inkFunc: InkFunc?) : super(context) {
        this.inkFunc = inkFunc
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if(inkFunc!=null){
            val t = System.currentTimeMillis()
            inkFunc?.onDraw(canvas)
        }
    }

    fun getPaint(): Paint {
        if (paint == null) {
            paint = Paint()
            paint!!.style = Paint.Style.FILL_AND_STROKE
            paint!!.color = Color.RED
        }
        return paint!!
    }

    private fun viewHardwareBitmap(){
        hardwareBitmap?.let {
            if(!it.isRecycled){
                var bitmap = it.copy(Bitmap.Config.ARGB_8888,false)
                Log.e(TAG,"viewHandBitmap ")
            }
        }
    }


    override fun invalidate() {
        super.invalidate()
        //为调试hardwareBitmap内容一般情况不要开启
//        viewHardwareBitmap()
    }

    fun updateHardwareBitmap(width: Int, height: Int) {
        val millis = System.currentTimeMillis()
        if (hardwareBitmap == null) {
            val mutableBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            hardwareBitmap = mutableBitmap.copy(Bitmap.Config.HARDWARE, false)
            Log.d(TAG,"hardwareBitmap 1:" + (System.currentTimeMillis() - millis))
            mutableBitmap.recycle()
        } else if (hardwareBitmap?.width != width || hardwareBitmap?.height != height) {
            hardwareBitmap?.recycle()
            hardwareBitmap = null
            val mutableBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            hardwareBitmap = mutableBitmap.copy(Bitmap.Config.HARDWARE, false)
            Log.d(TAG,"hardwareBitmap 2:" + (System.currentTimeMillis() - millis))
            mutableBitmap.recycle()
        }
    }

    fun release(){
        if (hardwareBitmap!=null&& hardwareBitmap?.isRecycled == false){
            hardwareBitmap?.recycle()
            hardwareBitmap = null
        }
        if (hardwareBitmapHigh!=null&& hardwareBitmapHigh?.isRecycled == false){
            hardwareBitmapHigh?.recycle()
            hardwareBitmapHigh = null
        }
    }

    fun updateHardwareBitmapHight(width: Int, height: Int) {
        val millis = System.currentTimeMillis()
        if (hardwareBitmapHigh == null) {
            val mutableBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            hardwareBitmapHigh = mutableBitmap.copy(Bitmap.Config.HARDWARE, false)
            Log.d(TAG,"hardwareBitmapHight 1:" + (System.currentTimeMillis() - millis))
            mutableBitmap.recycle()
        } else if (hardwareBitmapHigh?.width != width || hardwareBitmapHigh?.height != height) {
            hardwareBitmapHigh?.recycle()
            hardwareBitmapHigh = null
            val mutableBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
            hardwareBitmapHigh = mutableBitmap.copy(Bitmap.Config.HARDWARE, false)
            Log.d(TAG,"hardwareBitmapHight 2:" + (System.currentTimeMillis() - millis))
            mutableBitmap.recycle()
        }
    }

    fun setLayerMode(layerMode: LayerMode) {
        this.layerMode = layerMode
    }

    interface DrawListener {
        fun onDrawFinished();
    }
}