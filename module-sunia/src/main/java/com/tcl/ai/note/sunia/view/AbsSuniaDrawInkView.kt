package com.tcl.ai.note.sunia.view

//import com.sunia.viewlib.R
import android.content.Context
import android.graphics.Color
import android.graphics.PointF
import android.graphics.Path
import android.graphics.RectF
import android.os.Handler
import android.os.Looper
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.annotation.CallSuper
import com.sunia.penengine.sdk.data.IAttachment
import com.sunia.penengine.sdk.operate.canvas.ScaleInfo
import com.sunia.penengine.sdk.operate.edit.LayerMode
import com.sunia.penengine.sdk.operate.edit.PasteInfo
import com.sunia.penengine.sdk.operate.edit.RecoDataType
import com.sunia.penengine.sdk.operate.edit.RecoGroupData
import com.sunia.penengine.sdk.operate.edit.SelectRectF
import com.sunia.penengine.sdk.operate.edit.ShapeFlag
import com.sunia.singlepage.sdk.InitInfo
import com.sunia.singlepage.sdk.InkFactory
import com.sunia.singlepage.sdk.InkFunc
import com.sunia.singlepage.sdk.listener.ICanvasChangedListener
import com.sunia.singlepage.sdk.listener.IEngineVerifyListener
import com.sunia.singlepage.sdk.listener.IInkEditListener
import com.sunia.singlepage.sdk.listener.IInkSelectListener
import com.sunia.singlepage.sdk.param.BackgroundGridBean
import com.sunia.singlepage.sdk.param.LayoutMode
import com.sunia.singlepage.sdk.spanned.view.KspSpannedLayout
import com.sunia.viewlib.IInitListener
import com.sunia.viewlib.ViewLib
import com.sunia.viewlib.helper.AttachmentHelper
import com.sunia.viewlib.utils.PasteHelper
import com.sunia.viewlib.utils.XLog
import com.sunia.viewlib.view.EditDrawView
import com.sunia.viewlib.view.UpperScreenThreadHandler
import com.sunia.viewlib.view.model.IInkViewModel
import com.sunia.viewlib.view.model.IInkViewModel.IInkViewListener
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import kotlinx.coroutines.DelicateCoroutinesApi
import java.lang.ref.WeakReference
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 对应安格斯DEMO的InkView，简化版，只保留纯手绘上屏逻辑
 *
 * 纯手绘, 这里只处理上屏逻辑，不处理笔粗、颜色等逻辑
 */
abstract class AbsSuniaDrawInkView : ViewGroup, ICanvasChangedListener, IInkViewListener, IInkEditListener,
    IInkSelectListener {
    private var TAG = "AbsSuniaDrawInkView"
    private var upperScreenThreadHandler: UpperScreenThreadHandler? = null
    private var mainHandler: Handler = Handler(Looper.getMainLooper())
    private var inkFunc: InkFunc = InkFactory.newInkFunc()
    fun IInkViewModel.getInkFunc() = inkFunc

    private var loadingView: View? = null
    private var viewPortFlag: Boolean = false
    var layoutModeValue = LayoutMode.INFINITE_DEFAULT
    private var screenMode: Int = 0
    var visibleWidth: Int = 0
    var visibleHeight: Int = 0
    private var initListener: IInitListener? = null
    private var topDragHeight: Int = 0
    private var headerTag = Object()
    private var DP100: Int = 0
    private var layerMode: LayerMode = LayerMode.layerModel2
    var editDrawView: EditDrawView? = null
    var iInkViewModel: IInkViewModel? = null
    private var pasteHelper: PasteHelper? = null
    protected var isCanvasDrawing = false


    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr)
    constructor(context: Context, layoutMode: LayoutMode = LayoutMode.INFINITE_DEFAULT, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : super(context, attrs, defStyleAttr) {
        Logger.d(TAG, "init start")
        this.layoutModeValue = layoutMode
        init(initListener = object : IInitListener {
            override fun onFinish() {
                Logger.d(TAG, "init finish, isDestroy: ${isDestroy.get()}")
                if (isDestroy.get()) {
                    return
                }
                onInitEngineFinish(inkFunc)
            }

            override fun onWriteUP() {
                Logger.d(TAG, "init onWriteUP")
            }

        })
    }

    // 初始化结束
    abstract fun onInitEngineFinish(inkFunc: InkFunc)

    private fun init(
        layoutMode: LayoutMode = layoutModeValue,
        layerMode: LayerMode = LayerMode.layerModel2,
        initListener: IInitListener
    ) {
        this.layerMode = layerMode
        DP100 = 100 * resources.displayMetrics.densityDpi
        screenMode = ViewLib.screenMode
        upperScreenThreadHandler = UpperScreenThreadHandler()
        upperScreenThreadHandler?.init()
        layoutModeValue = layoutMode

        // 上屏模式
//        iInkViewModel = SuniaDrawViewMixTopBitmapModel(this, layerMode)
        iInkViewModel = SuniaInkViewMixTopModel(this, layerMode)
        iInkViewModel?.setInkViewListener(this)
        iInkViewModel?.addDrawView()
//        loadingView = LayoutInflater.from(context).inflate(R.layout.layout_multi_loading, this, false).apply {
//            layoutParams = FrameLayout.LayoutParams(
//                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
//            )
//        }
//        loadingView?.setBackgroundColor(Color.WHITE)
//        addView(loadingView)

        editDrawView = EditDrawView(context, isTablet)
        editDrawView?.setInkFunc(inkFunc)
        addView(editDrawView)

        // SDK提供的文本控件
        val kspSpannedLayout = KspSpannedLayout(context)
        addView(kspSpannedLayout)
        kspSpannedLayout.spannedEditModel.setTextColor(Color.parseColor("#80000000"))
        // 绑定文本业务，不设置则无法添加和编辑文本
        inkFunc.setISpannedEditModel(kspSpannedLayout.spannedEditModel)

        inkFunc.setCanvasChangedListener(this)
        inkFunc.setInkEditListener(this)
        inkFunc.setInkSelectListener(this)
        pasteHelper = PasteHelper(context)
        //设置默认颜色
        setBackgroundColor(Color.TRANSPARENT)

        initEngine(layoutMode, layerMode, initListener)
        editDrawView?.setInkFunc(inkFunc)
        editDrawView?.setLayoutMode(layoutMode)
    }


    private fun handleLoading(show: Boolean) {
        if (show) showLoading()
    }

    override fun onInitAndSetVisibleSizeFinish() {
        initListener?.onFinish()
    }

    override fun onVisibleSizeChanged(width: Int, height: Int) {
        visibleWidth = width
        visibleHeight = height
    }

    override fun canVisibleSizeChanged(width: Int): Boolean {
        return true
    }

    override fun onViewRefresh(width: Int, height: Int) {
        upperScreenThreadHandler?.postRendToScreenMsg(
            getRendToRunnable(
                RectF(
                    0f,
                    0f,
                    width.toFloat(),
                    height.toFloat()
                ), 0
            )
        )
    }

    private fun getRendToRunnable(rectF: RectF?, state: Int): RendToRunnable {
        return RendToRunnable(this, rectF, state)
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun initEngine(layoutMode: LayoutMode, layerMode: LayerMode, listener: IInitListener) {
        initListener = listener
        Thread {
            val initInfo = InitInfo(layoutMode, layerMode, true, inkFunc.hashCode())
            // initInfo.enableCanvasBlend = ViewLib.enable_canvas_blend
            initInfo.enableEngineDrawRuler = true
            initInfo.enableMergeMultiable = false
            inkFunc.init(initInfo, object : IEngineVerifyListener {
                override fun onVerify(code: Int, msg: String?) {
                    Logger.d(TAG, "init onVerify,  code: $code, msg: $msg")
                }
            })
            inkFunc.setAttachmentCallback(AttachmentHelper(context.assets))
            Logger.d(TAG, "init2 ")

            if (isDestroy.get()) {
                inkFunc.destroy()
                return@Thread
            }
            Logger.d(TAG, "init3 ")

            mainHandler?.post {
                if (isDestroy.get()) {
                    inkFunc.destroy()
                    return@post
                }
                Logger.d(TAG, "init4 ")
                initEngineFinish(layoutMode)
                iInkViewModel?.onInitFinish()
            }
        }.start()
    }

    private fun initEngineFinish(layoutMode: LayoutMode) {
        Logger.d(TAG, "initEngineFinish,  layoutMode: $layoutMode")
        inkFunc.enableScaleMode(true)
        inkFunc.enableShapeEdit(ViewLib.ENABLE_GRAPHIC_EDITING)
    }

    open fun rendToScreen(rectF: RectF, state: Int) {
        iInkViewModel?.rendToScreen(
            rectF,
            if (writeState.get()) ICanvasChangedListener.STATE_DRAWING_PROGRESS else ICanvasChangedListener.STATE_DRAW_COMPLETED
        )
    }

    fun showLoading() {
        Log.d(TAG, "showLoading")
//        if (loadingView!!.visibility != VISIBLE) {
//            loadingView!!.visibility = VISIBLE
//        }
    }

    fun hideLoading() {
        Log.d(TAG, "hideLoading")
        if (layoutModeValue == LayoutMode.INFINITE_REGIONAL && !viewPortFlag) {
            return
        }
//        loadingView!!.visibility = INVISIBLE
    }

    fun setViewPortFlag() {
        viewPortFlag = true
        hideLoading()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        measureChildren(widthMeasureSpec, heightMeasureSpec)
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        if (!changed) {
            return
        }
        val count = childCount
        for (i in 0 until count) {
            val view = getChildAt(i)
            if (view.tag == headerTag) {
                view.layout(0, 0, r - l, DP100)
            } else {
                view.layout(0, topDragHeight, r - l, b - t + topDragHeight)
            }
        }
    }

    fun layoutChild() {
        val count = childCount
        for (i in 0 until count) {
            val view = getChildAt(i)
            if (view.tag == headerTag) {
                view.layout(0, 0, width, topDragHeight)
            } else {
                view.layout(0, topDragHeight, width, height + topDragHeight)
            }
        }
    }

    fun isSpannedEditing(): Boolean {
        return spannedEditing
    }

    private var spannedEditing = false

    fun setSpannedEditing(editing: Boolean) {
        spannedEditing = editing;
    }

    //小米设备会连续执行2次onSizeChanged
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        if (w == 0 || h == 0) {
            return
        }
        Logger.i(TAG, "onSizeChanged,  w: $w, h: $h")
        iInkViewModel?.onSizeChanged(w, h)
    }


    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        XLog.d(TAG, "InkView dispatchTouchEvent wwei")
        return super.dispatchTouchEvent(ev)
    }

    var isDownOnHeader = false
    var isDownOnLoading = false
    var downTopDragHeight = 0
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        XLog.d(TAG, "InkView onTouchEvent wwei")
        if (event?.actionMasked == MotionEvent.ACTION_DOWN) {
//            isDownOnLoading = loadingView!!.visibility == VISIBLE
            isDownOnHeader = false
            if (topDragHeight > 0 && event.y < topDragHeight) {
                isDownOnHeader = true
            }
        }

        if (isDownOnHeader) {
            return false
        }
        if (event?.actionMasked == MotionEvent.ACTION_DOWN) {
            if (topDragHeight > 0) {
                downTopDragHeight = topDragHeight
            } else {
                downTopDragHeight = 0
            }
        }
        if (downTopDragHeight > 0) {
            event?.offsetLocation(0f, -downTopDragHeight.toFloat())
        }

        if (event?.actionMasked == MotionEvent.ACTION_UP || event?.actionMasked == MotionEvent.ACTION_CANCEL) {
//            editDrawView?.clearHighLight()
        }
        return inkFunc.onTouchEvent(event)
    }


    fun setBgColor(color: Int) {
        if (screenMode == 4) {
            //Canvas+SurfaceView置顶方案,设置背景色无效
            return
        }
        setBackgroundColor(color)
        Log.d(TAG, "setBackgroundColor color: $color")
        if (screenMode < 3) { // 非 Canvas+SurfaceView置顶方案
            if (ViewLib.enable_canvas_blend) {
                inkFunc.setBackgroundColor(Color.TRANSPARENT)
            } else {
                inkFunc.setBackgroundColor(color)
            }
        }
        if (color == Color.TRANSPARENT) {
//            loadingView?.setBackgroundColor(Color.WHITE)
        } else {
//            loadingView?.setBackgroundColor(color)
        }
    }

    fun setBackgroundGrid(bean: BackgroundGridBean) {
        if (screenMode == 4) {
            //Canvas+SurfaceView置顶方案,设置背景色无效
            return
        }
        inkFunc.setBackgroundGridStyle(bean)
    }

    private var isDestroy: AtomicBoolean = AtomicBoolean(false)

    @CallSuper
    open fun release() {
        isDestroy.set(true)
        upperScreenThreadHandler?.quit()
        try {
            iInkViewModel?.release()
        } catch (e: Exception) {
            Log.d(TAG, "release: ${e.message}")
        }
        editDrawView?.setListener(null)
        inkFunc.setCanvasChangedListener(null)
        inkFunc.setInkWriteListener(null)
        inkFunc.setInkEditListener(null)
        inkFunc.setInkClickListener(null)
        inkFunc.setCanvasStateListener(null)
        inkFunc.setInkSelectListener(null)
        inkFunc.setShapeRecognizeListener(null)
        inkFunc.destroy()
    }

    override fun onCanvasDataChanged(rectF: RectF?) {
        Log.d(TAG, "printScale rect: $rectF")
        if (rectF?.isEmpty != true) {
            upperScreenThreadHandler?.postRendToScreenMsg(getRendToRunnable(rectF, 0))
        }
    }

    private var writeState: AtomicBoolean = AtomicBoolean(false)
    override fun onCanvasStateChanged(state: Int) {
        XLog.d(TAG, "onCanvasStateChanged state: $state")
        mainHandler?.post(Runnable {
            if (state == ICanvasChangedListener.STATE_DRAWING_PROGRESS) {
                writeState.set(true)
            } else if (state == ICanvasChangedListener.STATE_DRAW_COMPLETED) {
                writeState.set(false)
            }
            iInkViewModel?.onCanvasStateChanged(state)
        })
    }

    override fun onSetVisibleSizeFinish() {
        XLog.d(TAG, "onSetVisibleSizeFinish visibleWidth: $visibleWidth, $visibleHeight")
    }

    private val hideLoading = Runnable {
        hideLoading()
    }

    override fun onCanvasHandleFinish() {
        Log.d(TAG, "onCanvasHandleFinish ")
        mainHandler?.removeCallbacks(hideLoading)
        mainHandler?.postDelayed(hideLoading, 50)
    }

    class RendToRunnable(inkView: AbsSuniaDrawInkView, rectF: RectF?, state: Int) :
        UpperScreenThreadHandler.IRendToRunnable {
        private val rendToRect: RectF = RectF()
        private val weakReference: WeakReference<AbsSuniaDrawInkView> = WeakReference<AbsSuniaDrawInkView>(inkView)
        private val rendState: Int
        override fun run() {
            weakReference.get()?.rendToScreen(rendToRect, rendState)
        }

        override fun getRendToRect(): RectF {
            return rendToRect
        }

        init {
            if (rectF != null) {
                rendToRect.set(rectF)
            }
            rendState = state
        }
    }


    var isHandleResultOutside: Boolean = false
    fun handleResultOutside(data: MutableList<RecoGroupData>, offset: Int) {
        isHandleResultOutside = true
        val scaleInfo = inkFunc.scaleInfo
        // x轴偏移
        scaleInfo.offsetX -= offset
        // 右侧留白距离
        scaleInfo.offsetX -= 20
        // 更新画布位置
        inkFunc.updateScaleInfo(scaleInfo)
        // 添加结果数据
        inkFunc.iRecoOptFunc.addRecognizeData(data)
    }

    fun doScaleChanged(scaleInfo: ScaleInfo?) {
        if (scaleInfo == null) {
            return
        }
        iInkViewModel?.onScaleChanged(scaleInfo.scale, scaleInfo.offsetX, scaleInfo.offsetY, PointF(scaleInfo.scaleCenterX, scaleInfo.scaleCenterY))
    }

    @CallSuper
    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        release()
    }

    override fun onEraserDraw(p0: FloatArray?, p1: Boolean) {
//        if (!isCanvasDrawing) return
//        editDrawView?.onEraserDraw(p0, p1)
    }

    override fun onInsertShapeDraw(p0: Path?) {
        if (!isCanvasDrawing) return
        editDrawView?.onInsertShapeDraw(p0)
    }

    override fun onSelectPathDraw(p0: Path?) {
        if (!isCanvasDrawing) return
        editDrawView?.onSelectPathDraw(p0)
    }

    override fun isDownOnScaleRect(p0: Float, p1: Float): Boolean {
        if (!isCanvasDrawing) return false
        return editDrawView?.isDownOnScaleRect(p0, p1) ?: false
    }

    override fun isDownOnRotateRect(p0: Float, p1: Float): Boolean {
        if (!isCanvasDrawing) return false
        return editDrawView?.isDownOnRotateRect(p0, p1) ?: false
    }

    override fun isDownOnMixedRect(p0: Float, p1: Float): Boolean {
        if (!isCanvasDrawing) return false
        return editDrawView?.isDownOnMixedRect(p0, p1) ?: false
    }

    override fun isDownOnSpannedLeft(p0: Float, p1: Float): Boolean {
        if (!isCanvasDrawing) return false
        return editDrawView?.isDownOnSpannedLeft(p0, p1) ?: false
    }

    override fun isDownOnSpannedRight(p0: Float, p1: Float): Boolean {
        if (!isCanvasDrawing) return false
        return editDrawView?.isDownOnSpannedRight(p0, p1) ?: false
    }

    override fun onSelectedDraw(p0: Int, p1: SelectRectF?, p2: RectF?, p3: Boolean) {
        if (!isCanvasDrawing) return
        if (p1 != null && !p1.isEmpty) {
            editDrawView?.onSelectedDraw(
                p0,
                p1,
                p2,
                p3
            )
        }
    }

    override fun onSelectEditDraw(p0: Int, p1: Path?, p2: SelectRectF?, p3: RectF?, p4: PointF?) {
        if (!isCanvasDrawing) return
        editDrawView?.onSelectEditDraw(
            p0,
            p1,
            p2,
            p3,
            p4
        )
    }

    override fun showTableEditWindow(p0: RectF?, p1: RectF?, p2: Int, p3: Int) {
        if (!isCanvasDrawing) return
        editDrawView?.showTableEditWindow(p0, p1, p2, p3)
    }

    override fun onVertexShapePoint(p0: MutableList<PointF>?, p1: SelectRectF?) {
        if (!isCanvasDrawing) return
        editDrawView?.onVertexShapePoint(p0, p1)
    }

    override fun onOutLineDraw(p0: Path?) {
        if (!isCanvasDrawing) return
        val rectF = RectF()
        p0?.let {
            it.computeBounds(rectF, false)
        }
        Log.d(TAG, "onOutLineDraw rectF:$rectF")
        editDrawView?.onOutLineDraw(p0)

    }

    override fun showPasteWindow(p0: Int, p1: Float, p2: Float) {
        //editDrawView?.showPasteWindow(p0, p1, p2)
    }

    override fun onSelectLimited(p0: Int) {
        if (!isCanvasDrawing) return
        editDrawView?.onSelectLimited(p0)
    }

    override fun onSelectingHint() {
        if (!isCanvasDrawing) return
        editDrawView?.onSelectingHint()
    }

    override fun onDataCopied(p0: PasteInfo?) {
        //pasteHelper?.setPasteInfo(p0)
    }

    override fun reset() {
//        if (!isCanvasDrawing) return
        editDrawView?.reset()
    }

    override fun checkSpannedEdit(p0: SelectRectF?) {
        //setSpannedEditing(false)
    }

    override fun finishSpannedEdit() {
    }

    override fun getShapeFlagByShapeId(p0: Int): ShapeFlag? {
        //return ShapeFlag.SHAPE_FLAG_STROKE
        return null
    }

    override fun onCanvasHandleBusy() {
        editDrawView?.onCanvasHandleBusy()
    }

    override fun isDownOnCustomClick(p0: Float, p1: Float): Boolean {
        if (!isCanvasDrawing) return false
        return editDrawView?.isDownOnCustomClick(p0, p1) ?: false
    }

    override fun onCustomClick(p0: Float, p1: Float) {
        if (!isCanvasDrawing) return
        val type = editDrawView!!.customClickType
        if (type == EditDrawView.CUSTOM_CLICK_DELETE) {
            inkFunc.selectEditFunc.onDelete()
        }
    }

    override fun onAutoSelectByEngine(p0: Int) {
    }

    override fun onCurveClicked(p0: Long) {
    }

    override fun onCancelAutoPlay() {
    }

    override fun onClickBlankArea(p0: Float, p1: Float) {
        /*post {
            if (pasteHelper == null) {
                return@post
            }
            pasteHelper?.checkClipboardText()
            if (!pasteHelper!!.hasPasteData()) {
                return@post
            }
            if (ViewLib.PASTE_MODE == PasteHelper.PASTE_MODE_OLD) {
                var type = 0
                if (pasteHelper!!.hasEngineData()) {
                    type += 1
                }
                if (pasteHelper!!.hasTextData()) {
                    type += 2
                }
                if (type > 0) {
                    editDrawView?.showPasteWindow(type, p0, p1)
                }
                return@post
            }
            var type = PasteType.PASTE_TYPE_DEFAULT
            if (pasteHelper!!.hasTextData()) {
                type += PasteType.PASTE_TYPE_MIXTURE
            }
            editDrawView?.showPasteWindow(type, p0, p1)
        }*/
    }

    override fun onSelectFinished() {

    }

    override fun onSelectChanged(p0: Boolean) {

    }
}