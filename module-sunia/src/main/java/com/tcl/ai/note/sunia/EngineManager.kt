package com.tcl.ai.note.sunia

import android.content.Context
import com.sunia.HTREngine.sdk.Engine
import com.sunia.HTREngine.sdk.EngineAuthenticateCallback
import com.sunia.HTREngine.utils.LogUtil
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.sunia.authorize.SuniaVerifyHandler
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import java.io.File

/**
 *  author : junze.liu
 *  date : 2025-08-15 19:15
 *  description : Engine管理类
 *  一个Engine可以对应多个editor，每个功能维护好自己的editor，editor要在Engine销毁之前释放
 *  鉴权完之后，就可以正常初始化Engine
 */
object EngineManager {

    private val TAG = "EngineManager"

    private var level = 3

    private var fileDir: String? = null

    private var engine: Engine? = null

    private val mEngineState = MutableStateFlow<Engine?>(null)
    val engineState  = mEngineState.asStateFlow()

    private var callback: EngineInitCallback? = null

    fun init(context: Context) {
        Logger.d(TAG, "-----init-----")
        GlobalContext.applicationScope.launch {
            SuniaVerifyHandler.suniaVerifyStateFlow.filterNotNull().collect {
                Logger.d(TAG, "-----init-----collect, it:$it")
                initEngine(context,it.authEncryptionPath)
            }
        }

    }


    private fun initEngine(context: Context,authEncryptionPath: String) {
        Logger.d(TAG, "initEngine")
        if (engine != null) {
            Logger.w(TAG, "initEngine, engine is not null, return")
            return
        }
        var path = authEncryptionPath
        val startMillis = System.currentTimeMillis()
        GlobalContext.applicationScope.launchIO {
            // 初始化失败，无限重试
            while (engine == null) {
                //避免过早的去取鉴权数据拿不到，所以这里每次重新初始化引擎时如果为空时，重新取一下
                if (path.isEmpty()){
                    path = SuniaVerifyHandler.suniaVerifyStateFlow.value?.authEncryptionPath.toString()
                }
                Logger.d(TAG, "initEngine, path:$path")
                runCatching {
                    createEngine(context, path, startMillis)
                }.onFailure {
                    Logger.w(TAG, "initEngine, error: ${it.message}")
                }
                // 延时3秒，避免频繁初始化
                delay(3000)
            }
            Logger.d(TAG, "init, success , exit loop")
        }

    }


    // 在初始化引擎成功后，需要同时更新 mEngineState
    private fun updateEngine() {
        mEngineState.value = engine  // 更新状态流的值，触发收集器
    }


    private fun createEngine(context: Context, authEncryptionPath: String?, startMillis: Long) {
        if (authEncryptionPath.isNullOrEmpty()) {
            Logger.w(TAG, "createEngine, authEncryptionPath is null, return")
            return
        }
        val start = System.currentTimeMillis()
        Logger.d(TAG, "createEngine start, authEncryptionPath:$authEncryptionPath")
        if (engine == null) {
            engine = Engine.createInstance(context, authEncryptionPath)
            Logger.d(TAG, "createEngine end, use time:" + (System.currentTimeMillis() - start))
            initLog(context)

            engine?.setInitializeCallback(object : EngineAuthenticateCallback {

                override fun onSuccess(p0: Engine?, p1: String?, p2: String?) {
                    Logger.d(TAG, "engine create success")
                    updateEngine()
                    Logger.i(
                        TAG,
                        "initEngine, success, use time: ${System.currentTimeMillis() - startMillis}"
                    )
//                    createEditor()
                    callback?.onSuccess(p0, p1, p2)
                }

                override fun onError(e: Exception, s: String?) {
                    Logger.e(TAG, "engine create error:${e.message}, s:$s")
                    engine = null
                    callback?.onError(e, s)
                }
            })
            engine?.start()
        }
    }


    fun getEngine(): Engine? {
        return engine
    }


    private fun initLog(context: Context) {
        val file = File(context.externalCacheDir, "hwr_engine").apply {
            if (!exists()) mkdirs()
        }

        val logFile = File(file, "engineLog").apply {
            if (!exists()) mkdirs()
        }

        fileDir = logFile.absolutePath
        configureEngineLog()
        Logger.d(TAG, "initLog, log path: $fileDir")
    }

    private fun configureEngineLog() {
        engine?.apply {
            setLogDir(fileDir)
            setDebugLevel(level)
        }
        LogUtil.setDebugLevel(level)
    }

    fun setEngineInitCallback(callback: EngineInitCallback) {
        this.callback = callback
    }

}

interface EngineInitCallback {
    fun onSuccess(var1: Engine?, var2: String?, var3: String?)

    fun onError(var1: java.lang.Exception?, var2: String?)
}