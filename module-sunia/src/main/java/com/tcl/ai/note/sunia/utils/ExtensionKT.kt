package com.tcl.ai.note.sunia.utils

import com.sunia.penengine.sdk.operate.canvas.ScaleInfo

fun ScaleInfo.isNotEqual(scaleInfo: ScaleInfo): Boolean = !this.isEqual(scaleInfo)
fun ScaleInfo.isEqual(scaleInfo: ScaleInfo): Boolean {
    return this.offsetX == scaleInfo.offsetX &&
            this.offsetY == scaleInfo.offsetY &&
            this.scaleCenterX == scaleInfo.scaleCenterX &&
            this.scaleCenterY == scaleInfo.scaleCenterY &&
            this.scale == scaleInfo.scale
}


