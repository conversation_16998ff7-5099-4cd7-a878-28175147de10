package com.tcl.ai.note.sunia

import android.annotation.SuppressLint
import android.content.Context
import androidx.startup.Initializer
import com.sunia.singlepage.sdk.InkSDK
import com.sunia.viewlib.ViewLib
import com.tcl.ai.note.GlobalContext.Companion.appScope
import com.tcl.ai.note.sunia.authorize.SuniaVerifyHandler
import com.tcl.ai.note.utils.launchIO
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first

@SuppressLint("StaticFieldLeak")
object SuniaModule : Initializer<Unit> {
    override fun create(context: Context) {
        context.appScope.launchIO {
            SuniaVerifyHandler.suniaVerifyStateFlow.filter { it != null }.first()
            /**
             * log print level. Value：
             *    0 Close log，关闭日志
             *    1 Error - Display error message， 异常信息
             *    2 Info - Info and error information are displayed， 一般信息和错误信息
             *    3、Debug - The debugging information, info information, and error information are displayed 显示调试信息、一般信息和错误信息
             * th log file path 日志文件路径
             * */
            val logLevel = 3
            val logPath = context.cacheDir.absolutePath + "/suniaLog.log"
            InkSDK.setLog(logLevel, logPath)
            InkSDK.logLevel = logLevel
            InkSDK.init(
                context.applicationContext,
                SuniaVerifyHandler.suniaVerifyStateFlow.filterNotNull().first()
            )
        }
    }

    override fun dependencies(): List<Class<out Initializer<*>?>?> {
        return emptyList()
    }
}