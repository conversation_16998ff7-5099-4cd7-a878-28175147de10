package com.tcl.ai.note.sunia.authorize

import com.sunia.authlib.bean.DeviceIdType
import com.sunia.authlib.bean.URLType
import com.sunia.authlib.bean.VerifyData
import com.sunia.authlib.managers.AuthManager
import com.sunia.penengine.sdk.engine.VerifyInfo
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.sunia.EngineManager
import com.tcl.ai.note.sunia.BuildConfig
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isLiteVersion
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.suspendCoroutineWithTimeoutOrNull
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlin.coroutines.resume

object SuniaVerifyHandler {
    private val _suniaVerifyStateFlow = MutableStateFlow<VerifyInfo?>(null)
    val suniaVerifyStateFlow = _suniaVerifyStateFlow.asStateFlow()
    val mAuthConfigManager = AuthConfigManager.create()

    init {
        //鉴权成功之后，初始化全局混合识别引擎
        EngineManager.init(GlobalContext.Companion.instance)
        GlobalContext.Companion.applicationScope.launch(Dispatchers.IO) {
            while (suniaVerifyStateFlow.value == null) {
                _suniaVerifyStateFlow.value = getVerifyInfo()
                delay(3000)
            }
        }
    }

    private suspend fun getVerifyInfo() =
        suspendCoroutineWithTimeoutOrNull(3000) { cont ->
            val context = GlobalContext.Companion.instance
            val verifyDir = context.getExternalFilesDir(VERIFY_INFO_DIR)
            if (verifyDir == null) {
                cont.resume(null)
                return@suspendCoroutineWithTimeoutOrNull
            }
            if (!verifyDir.exists()) {
                if (!verifyDir.mkdirs()) {
                    cont.resume(null)
                    return@suspendCoroutineWithTimeoutOrNull
                }
            }
            val encryptPath = verifyDir.absolutePath + "/" + LICENSE_FILE
            val verifyData = VerifyData(mAuthConfigManager.appId, mAuthConfigManager.publicKey, encryptPath, mAuthConfigManager.offlineCert).apply {
                deviceIdType = DeviceIdType.ANDROID_ID
                urlType = URLType.PROD.value
            }
            Logger.v(TAG, "getVerifyInfo, isTablet: $isTablet, isLiteVersion: $isLiteVersion, isDebug: ${BuildConfig.DEBUG}")
            Logger.v(TAG, "getVerifyInfo, mAuthConfigManager.appId: ${mAuthConfigManager.appId}")
            // 打开鉴权日志
            //AuthManager.getInstance().setEnableLog(
            //    true,
            //    verifyDir.absolutePath,
            //    "authLogUtil.txt"
            //)

            // 开始鉴权
            AuthManager.getInstance().authorize(context, verifyData) { _, p1, _ ->
                Logger.v(TAG, "OnAuthorResultCallback: $p1, APP_ID: ${mAuthConfigManager.appId}")
                cont.resume(
                    VerifyInfo(encryptPath).apply {
                        appId = mAuthConfigManager.appId
                        deviceIdType = DeviceIdType.ANDROID_ID.value
                    }
                )
            }
        }

    private const val TAG = "SuniaVerifyHandler"
    private const val VERIFY_INFO_DIR = "verifyInfos"
    private const val LICENSE_FILE = "license_auth_cert.text"
}