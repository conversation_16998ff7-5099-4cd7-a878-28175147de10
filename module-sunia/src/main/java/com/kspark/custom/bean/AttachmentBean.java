package com.kspark.custom.bean;

public class AttachmentBean {
    // 对象用途 0 联想poc 默认用途 1 插入自定义附件文件（pdf word）
    private int dataType =0;
    private int id;
    private String defaultFilePath;
    private String clickedFilePath;
    private String fileName;
    private int width;
    private int height;
    //版本控制，用于兼容字段扩展
    private final int version = 1;
    // 0 pdf
    private int fileType = 0;
    // 图片角度
    private int angle = 0;

    @Override
    public String toString() {
        return "AttachmentBean{" +
                "dataType=" + dataType +
                ", id=" + id +
                ", defaultFilePath='" + defaultFilePath + '\'' +
                ", clickedFilePath='" + clickedFilePath + '\'' +
                ", fileName='" + fileName + '\'' +
                ", width=" + width +
                ", height=" + height +
                ", version=" + version +
                ", fileType=" + fileType +
                ", angle=" + angle +
                '}';
    }

    public int getHeight() {
        return height;
    }

    public int getWidth() {
        return width;
    }

    public String getFileName() {
        return fileName;
    }

    public String getClickedFilePath() {
        return clickedFilePath;
    }

    public String getDefaultFilePath() {
        return defaultFilePath;
    }

    public int getFileType() {
        return fileType;
    }

    public void setFileType(int fileType) {
        this.fileType = fileType;
    }

    public int getDataType() {
        return dataType;
    }

    public void setDataType(int dataType) {
        this.dataType = dataType;
    }

    public int getAngle() {
        return angle;
    }

    public void setAngle(int angle) {
        this.angle = angle;
    }

    public AttachmentBean(String fileName, String defaultFilePath, String clickedFilePath, int width, int height) {
        this.fileName = fileName;
        this.defaultFilePath = defaultFilePath;
        this.clickedFilePath = clickedFilePath;
        this.width = width;
        this.height = height;
    }

    public AttachmentBean(int fileType, String defaultFilePath, int dataType) {
        this.fileType = fileType;
        this.defaultFilePath = defaultFilePath;
        this.dataType = dataType;
    }
}
