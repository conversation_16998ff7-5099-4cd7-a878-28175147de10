package com.kspark.custom.utils

import android.annotation.SuppressLint
import android.content.ContentValues
import android.content.Context
import android.content.res.Resources
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.util.Log
import com.sunia.authlib.utils.LogUtils
import java.io.*
import java.text.SimpleDateFormat
import java.util.*
import kotlin.concurrent.thread


/**
 *@name    HTREngineDemo
 *@package        com.sunia.HTREngine.recognizeDemo.utils
 *@className       BitmapUtils
 *@description
 *<AUTHOR>
 *@createDate     2022/4/12 14:21
 *@version      2.0.0
 */
object BitmapUtilKT {
    private val TAG = BitmapUtilKT::class.java.simpleName
    fun toByteArray(bitmap: Bitmap): ByteArray {
        val os = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, os)
        return os.toByteArray()
    }

    fun mirror(rawBitmap: Bitmap): Bitmap {
        val matrix = Matrix()
        matrix.postScale(-1f, 1f)
        return Bitmap.createBitmap(rawBitmap, 0, 0, rawBitmap.width, rawBitmap.height, matrix, true)
    }

    fun rotate(rawBitmap: Bitmap, degree: Float): Bitmap {
        val matrix = Matrix()
        matrix.postRotate(degree)
        return Bitmap.createBitmap(rawBitmap, 0, 0, rawBitmap.width, rawBitmap.height, matrix, true)
    }

    fun decodeBitmap(bitmap: Bitmap, reqWidth: Int, reqHeight: Int): Bitmap {
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true

        val bos = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 100, bos)
        BitmapFactory.decodeByteArray(bos.toByteArray(), 0, bos.size(), options)

        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)
        options.inJustDecodeBounds = false
        return BitmapFactory.decodeByteArray(bos.toByteArray(), 0, bos.size(), options)
    }

    fun decodeBitmapFromFile(path: String, reqWidth: Int, reqHeight: Int): Bitmap {
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeFile(path, options)

        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)
        options.inJustDecodeBounds = false
        return BitmapFactory.decodeFile(path, options)
    }
    fun createScaledBitmap(srcBitmap:Bitmap, dstWidth: Int, dstHeight: Int): Bitmap {
        val createScaledBitmap = Bitmap.createScaledBitmap(srcBitmap, dstWidth, dstHeight, true)
//        srcBitmap.recycle()
        return createScaledBitmap
    }

    fun decodeBitmapFromResource(
        res: Resources,
        resId: Int,
        reqWidth: Int,
        reqHeight: Int
    ): Bitmap {
        val options = BitmapFactory.Options()
        options.inJustDecodeBounds = true
        BitmapFactory.decodeResource(res, resId, options)

        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight)
        options.inJustDecodeBounds = false
        return BitmapFactory.decodeResource(res, resId, options)
    }

    private fun calculateInSampleSize(
        options: BitmapFactory.Options,
        reqWidth: Int,
        reqHeight: Int
    ): Int {
        val rawWidth = options.outWidth
        val rawHeight = options.outHeight
        var inSampleSize = 1

        if (rawWidth > reqWidth || rawHeight > reqHeight) {
            val halfWidth = rawWidth / 2
            val halfHeight = rawHeight / 2

            // The reason why the in Sample Size is set to a power of 2 is that the decoder will eventually
            // process numbers that are not powers of 2 downwards, and obtain the number closest to the power of 2
            while ((halfWidth / inSampleSize) > reqWidth && (halfHeight / inSampleSize) > reqHeight) {
                inSampleSize *= 2
            }
        }
        return inSampleSize
    }

//    fun savePic(
//        data: ByteArray?,
//        folderName: String = "Camera",
//        isMirror: Boolean = false,
//        onSuccess: (savedPath: String, time: String) -> Unit,
//        onFailed: (msg: String) -> Unit,
//        degree: Float
//    ) {
//        thread {
//            try {
//                val temp = System.currentTimeMillis()
//                val picFile = FileUtils.createCameraFile(folderName)
//                if (picFile != null && data != null) {
//                    val rawBitmap = BitmapFactory.decodeByteArray(data, 0, data.size)
//                    // Compress the bitmap and write it to the output stream (parameters are image
//                    // format, image quality, and output stream)
//                    var resultBitmap = if (isMirror) mirror(rawBitmap) else rawBitmap
//                    resultBitmap = rotate(resultBitmap, degree)
//                    val outputStream = FileOutputStream(picFile)
//                    outputStream.buffered().write(toByteArray(resultBitmap))
//                    outputStream.close()
//                    onSuccess(picFile.absolutePath, "${System.currentTimeMillis() - temp}")
//                }
//            } catch (e: Exception) {
//                e.printStackTrace()
//                onFailed("${e.message}")
//            }
//        }
//    }

    @SuppressLint("SimpleDateFormat")
    fun savePicToPublicStorage(
        context: Context,
        data: ByteArray?,
        isMirror: Boolean = false,
        onSuccess: (savedPath: String, time: String) -> Unit,
        onFailed: (msg: String) -> Unit
    ) {
        thread {
            try {
                val temp = System.currentTimeMillis()

                val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss").format(Date())
                val fileName = "IMG_$timeStamp.jpg"

                val value = ContentValues().apply {
                    put(MediaStore.Images.Media.DISPLAY_NAME, fileName)
                    put(MediaStore.Images.Media.MIME_TYPE, "image/jpg")
                    put(MediaStore.Images.Media.DATE_ADDED, temp)
                }

                val imageUri = context.contentResolver.insert(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    value
                )

                if (imageUri != null && data != null) {
                    val outputStream = context.contentResolver.openOutputStream(imageUri)
                    val rawBitmap = BitmapFactory.decodeByteArray(data, 0, data.size)
                    val resultBitmap = if (isMirror) mirror(rawBitmap) else rawBitmap
                    if (outputStream != null) {
                        resultBitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream)
                    }

                    onSuccess(imageUri.path ?: "", "${System.currentTimeMillis() - temp}")
                }
            } catch (e: Exception) {
                e.printStackTrace()
                onFailed("${e.message}")
            }
        }
    }

    /**
     * Write the Bitmap to a file in the SD card and return the Uri written to the file
     *
     * @param bm
     * @return
     */
    fun saveBitmap(bm: Bitmap, fileName: String, appDir: String): Uri? {
        // Create a new folder for storing cropped pictures
        val tmpDir = File(appDir)
        // Create a new file to store the cropped image
        val img = File(tmpDir.absolutePath + File.separator + fileName)
        return try {
            // open file output stream
            val fos = FileOutputStream(img)
            // Compress the bitmap and write it to the output stream (parameters are image format, image quality, and output stream)
            bm.compress(Bitmap.CompressFormat.JPEG, 50, fos)
            // Flush the output stream
            fos.flush()
            // close output stream
            fos.close()
            // Returns Uri of type File
            Uri.fromFile(img)
        } catch (e: FileNotFoundException) {
            e.printStackTrace()
            null
        } catch (e: IOException) {
            e.printStackTrace()
            null
        }
    }

    fun getPhotoRotation(inputStream: InputStream): Float {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                val exifInterface = ExifInterface(inputStream)
                val orientation = exifInterface.getAttributeInt(
                    ExifInterface.TAG_ORIENTATION,
                    ExifInterface.ORIENTATION_NORMAL
                )
                return when (orientation) {
                    ExifInterface.ORIENTATION_ROTATE_90 -> 90f
                    ExifInterface.ORIENTATION_ROTATE_180 -> 180f
                    ExifInterface.ORIENTATION_ROTATE_270 -> 270f
                    else -> 0f
                }
            }
        } catch (e: java.lang.Exception) {
            LogUtils.e(">>>>>>>>>>>>", "获取图片旋转的角度报错:" + e.message)
        }
        return 0f
    }

    /**
     * 根据图片路径获取需要矫正的角度
     * @param filepath 图片路径
     * @return 需矫正的角度
     */
    fun getExifOrientation(filepath: String?): Int {
        var degrees = 0
        var exif: ExifInterface? = null
        try {
            exif = ExifInterface(filepath!!)
        } catch (e: IOException) {
            Log.d(TAG, "cannot read exif$e")
        }
        if (exif != null) {
            val orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, -1)
            if (orientation != -1) {
                when (orientation) {
                    ExifInterface.ORIENTATION_ROTATE_90 -> degrees = 90
                    ExifInterface.ORIENTATION_ROTATE_180 -> degrees = 180
                    ExifInterface.ORIENTATION_ROTATE_270 -> degrees = 270
                }
            }
        }
        return degrees
    }
}