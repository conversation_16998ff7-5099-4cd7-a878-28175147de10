package com.kspark.custom.utils;

import android.app.Activity;
import android.content.ContentValues;
import android.content.Context;
import android.content.res.Resources;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Picture;
import android.graphics.Rect;
import android.graphics.pdf.PdfDocument;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.Build;
import android.print.PrintAttributes;
import android.provider.MediaStore;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;

import androidx.annotation.RequiresApi;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

public class BitmapUtil {
    private static final String TAG = "BitmapUtil";

    public static String bitmapToPNG(Bitmap bitmap, String path, String filename) {
        if (bitmap == null || bitmap.isRecycled()) {
            return null;
        }
        File dir = new File(path);
        if (dir.mkdirs()) {
            Log.d("", path + " dir is already exist");
        }
        File file = new File(dir, filename);
        try {
            FileOutputStream outputStream = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 70, outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (!bitmap.isRecycled()) {
//            bitmap.recycle();
        }
        return file.getAbsolutePath();
    }

    public static String bitmapToJPEG(Bitmap bitmap, String path, String filename) {
        if (bitmap == null) {
            return null;
        }
        File dir = new File(path);
        if (dir.mkdirs()) {
            Log.d("", path + " dir is already exist");
        }
        File file = new File(dir, filename);
        try {
            FileOutputStream outputStream = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.JPEG, 70, outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (!bitmap.isRecycled()) {
            bitmap.recycle();
        }
        return file.getAbsolutePath();
    }

    public static String bitmap2PDF(Bitmap bitmap, String path, String filename, boolean isEmptyContent) {
        PdfDocument doc = new PdfDocument();
        int pageWidth = PrintAttributes.MediaSize.ISO_A4.getWidthMils() * 72 / 1000;

        float scale = (float) pageWidth / (float) bitmap.getWidth();
        int pageHeight = (int) (bitmap.getHeight() * scale);

        Matrix matrix = new Matrix();
        matrix.postScale(scale, scale);
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        PdfDocument.PageInfo newPage = new PdfDocument.PageInfo.Builder(pageWidth, pageHeight, 0).create();
        PdfDocument.Page page = doc.startPage(newPage);
        if (!isEmptyContent) {
            Canvas canvas = page.getCanvas();
            canvas.drawBitmap(bitmap, matrix, paint);
        }
        doc.finishPage(page);
        File file = new File(path, filename);
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file);
            doc.writeTo(outputStream);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            doc.close();
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return file.exists() ? file.getAbsolutePath() : null;
    }


    /**
     * 按屏幕尺寸压缩图片
     *
     * @param context
     * @param filePath
     * @return
     */
    public static Bitmap imageSizeCompress(Context context, String filePath) {
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true; //第一次解析图片原始宽高信息，不加载图片
        BitmapFactory.decodeFile(filePath, options);
        options.inSampleSize = calculateSampleSize(context, options); //已屏幕的高度宽度压缩，计算采样率
        //第二次解析图片
        options.inJustDecodeBounds = false;
        return BitmapFactory.decodeFile(filePath, options);
    }

    public static Bitmap getBitmapByFilePath(String filePath) {
        try {
            return BitmapFactory.decodeStream(new FileInputStream(filePath));
        } catch (FileNotFoundException e) {
            return null;
        }
    }

    public static boolean saveBitmap(Bitmap bitmap, File file) {
        FileOutputStream outputStream = null;
        try {
            outputStream = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, outputStream);
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        if (!bitmap.isRecycled()) {
            bitmap.recycle();
        }
        return true;
    }

    public static Bitmap imageSizeCompress(Context context, Uri uri) {
        InputStream Stream = null;
        InputStream inputStream = null;
        try {
            //根据uri获取图片的流
            inputStream = context.getContentResolver().openInputStream(uri);
            BitmapFactory.Options options = new BitmapFactory.Options();
            //options的in系列的设置了，injustdecodebouond只解析图片的大小，而不加载到内存中去
            options.inJustDecodeBounds = true;
            //1.如果通过options.outHeight获取图片的宽高，就必须通过decodestream解析同options赋值,否则options.outheight获取不到宽高
            BitmapFactory.decodeStream(inputStream, null, options);
            //2.通过 btm.getHeight()获取图片的宽高就不需要1的解析，我这里采取第一张方式
//            Bitmap btm = BitmapFactory.decodeStream(inputStream);
            //以屏幕的宽高进行压缩，计算采样率
            options.inSampleSize = calculateSampleSize(context, options);
            //第二次解析到内存中去
            options.inJustDecodeBounds = false;
//            根据uri重新获取流，inputstream在解析中发生改变了
            Stream = context.getContentResolver().openInputStream(uri);
            Bitmap bitmap = BitmapFactory.decodeStream(Stream, null, options);
            return bitmap;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (Stream != null) {
                    Stream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }

        }
        return null;
    }

    /**
     * 按屏幕尺寸计算采样率
     *
     * @param context
     * @param options
     * @return
     */
    public static int calculateSampleSize(Context context, BitmapFactory.Options options) {
        //以屏幕的宽高进行压缩
        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();
        int heightPixels = displayMetrics.heightPixels;
        int widthPixels = displayMetrics.widthPixels;
        //获取图片的宽高
        int outHeight = options.outHeight;
        int outWidth = options.outWidth;
        //heightPixels  widthPixels 就是要要求压缩后的图片高度，宽度
        int a = (int) Math.ceil((outHeight / (float) heightPixels));
        int b = (int) Math.ceil(outWidth / (float) widthPixels);
        //比例计算,一般是图片比较大的情况下进行压缩
        int max = Math.max(a, b);
        int sampleSize = 1;
        if (max > 1) {
            sampleSize = max;
        }
        return sampleSize;
    }

    public static Uri getImageContentUri(Context context, File imageFile) {
        String filePath = imageFile.getAbsolutePath();
        Cursor cursor = context.getContentResolver().query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, new String[]{MediaStore.Images.Media._ID}, MediaStore.Images.Media.DATA + "=? ", new String[]{filePath}, null);
        Uri uri = null;

        if (cursor != null) {
            if (cursor.moveToFirst()) {
                int index = cursor.getColumnIndex(MediaStore.MediaColumns._ID);
                if (index > -1) {
                    int id = cursor.getInt(index);
                    Uri baseUri = Uri.parse("content://media/external/images/media");
                    uri = Uri.withAppendedPath(baseUri, "" + id);
                }
            }
            cursor.close();
        }

        if (uri == null) {
            ContentValues values = new ContentValues();
            values.put(MediaStore.Images.Media.DATA, filePath);
            uri = context.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
        }

        return uri;
    }

    public static Bitmap decodeSampledBitmapFromResource(Resources res, int resId, int reqWidth, int reqHeight) {
        // First decode with inJustDecodeBounds=true to check dimensions
        final BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeResource(res, resId, options);

        // Calculate inSampleSize
        options.inSampleSize = calculateInSampleSize(options, reqWidth, reqHeight);

        // Decode bitmap with inSampleSize set
        options.inJustDecodeBounds = false;
        return BitmapFactory.decodeResource(res, resId, options);
    }

    public static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        // Raw height and width of image
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;

            // Calculate the largest inSampleSize value that is a power of 2 and keeps both
            // height and width larger than the requested height and width.
            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }
        return inSampleSize;
    }

    public static Bitmap scaleBitmap(Bitmap bm, int newWidth,int newHeight,boolean recycle) {
        // 获得图片的宽高
        int width = bm.getWidth();
        int height = bm.getHeight();
        // 设置想要的大小
//        int newWidth = 320;
//        int newHeight = 480;
        // 计算缩放比例
        float scaleWidth = ((float) newWidth) / width;
        float scaleHeight = ((float) newHeight) / height;
        // 取得想要缩放的matrix参数
        Matrix matrix = new Matrix();
        matrix.postScale(scaleWidth, scaleHeight);
        // 得到新的图片
        Bitmap newbm = Bitmap.createBitmap(bm, 0, 0, width, height, matrix, true);
        if (recycle) {
            bm.recycle();
        }
        return newbm;
    }

    public static Bitmap scaleBitmapV2(Bitmap originalBitmap, int targetWidth, int targetHeight) {
        // 创建一个Matrix对象，用于包含缩放和平移的操作
        Matrix matrix = new Matrix();

        // 计算缩放比例
        float scaleWidth = (float) targetWidth / originalBitmap.getWidth();
        float scaleHeight = (float) targetHeight / originalBitmap.getHeight();
        // 使用较小的缩放比例进行缩放
        float scale = Math.min(scaleWidth, scaleHeight);
        matrix.postScale(scale, scale);

        // 使用Matrix创建一个新的Bitmap对象
        return Bitmap.createBitmap(originalBitmap, 0, 0, originalBitmap.getWidth(), originalBitmap.getHeight(), matrix, true);
    }


    public static Bitmap uriToBitmap(Context context, Uri result) {
        if (result != null) {
            int rotate = geExifInterfaceRotate(context, result);
            Bitmap bitmap = imageSizeCompress(context, result);
            if (rotate != 0 && bitmap != null) {
                Matrix matrix = new Matrix();
                matrix.setRotate(rotate);
                Bitmap newBitmap = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
                bitmap.recycle();
                bitmap = newBitmap;
            }
            return bitmap;
        }
        return null;
    }

    @RequiresApi(api = Build.VERSION_CODES.N)
    public static int geExifInterfaceRotate(Context context, Uri result) {
        InputStream inputStream = null;
        int rotate = 0;
        try {
            inputStream = context.getContentResolver().openInputStream(result);
            ExifInterface exifInterface = new ExifInterface(inputStream);
            int orientation = exifInterface.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_UNDEFINED);
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    rotate = 90;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_180:
                    rotate = 180;
                    break;
                case ExifInterface.ORIENTATION_ROTATE_270:
                    rotate = 270;
                    break;
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        return rotate;
    }


    public static String checkAndSaveScaleBitmap(String pngPath, String parentPath, boolean needCopy) {
        String newPath = pngPath;
        ExifInterface originalExif = null;
        try {
            originalExif = new ExifInterface(pngPath);
        } catch (IOException e) {
            Log.e("TAG", "checkAndSaveScaleBitmap outW "+e );
        }
        BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        BitmapFactory.decodeFile(pngPath, options);
        int outW = options.outWidth;
        int outH = options.outHeight;

        float bmpScale = 2f;
        int defWidth = 1800;
        int defHeight = 2880;
        int maxSize = (int) (defWidth * defHeight * bmpScale);
        Log.d("TAG", "checkAndSaveScaleBitmap outW " + outW + " outH " + outH);

        if (outW != 0 && outH != 0) {
            Paint bitmapPaint = new Paint();
            bitmapPaint.setAntiAlias(true);
            bitmapPaint.setStyle(Paint.Style.FILL);
            if ((outW * outH) > maxSize) {
                float scale = (float) maxSize / (options.outWidth * options.outHeight);
                int createW = (int) (outW * scale);
                int createH = (int) (outH * scale);
                if (needCopy) {
                    Bitmap bitmap = Bitmap.createBitmap(createW, createH, Bitmap.Config.ARGB_4444);
                    Canvas canvas = new Canvas(bitmap);

                    options.inJustDecodeBounds = false;
                    Bitmap png = BitmapFactory.decodeFile(pngPath, options);
                    Rect desRect = new Rect(0, 0, bitmap.getWidth(), bitmap.getHeight());
                    Log.d("TAG", "checkAndSaveScaleBitmap desRect " + desRect);

                    canvas.drawBitmap(png, null, desRect, bitmapPaint);
                    png.recycle();
                    canvas.setBitmap(null);
                    String mineType = "";
                    int index = pngPath.lastIndexOf(".");
                    if (index != -1) {
                        mineType = pngPath.substring(index);
                    }
                    String newName = UUID.randomUUID().toString() + mineType;
                    File parentFile = new File(parentPath);
                    newPath = new File(parentFile, newName).getAbsolutePath();
                    File file = new File(parentFile, newName);
                    saveBitmapToFile(bitmap, file);
                    try {
                        ExifInterface newExif = new ExifInterface(file.getAbsolutePath());
                        if(originalExif!=null){
                            newExif.setAttribute(ExifInterface.TAG_ORIENTATION, originalExif.getAttribute(ExifInterface.TAG_ORIENTATION));
                        }
                        newExif.saveAttributes();
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    Log.d("TAG", "checkAndSaveScaleBitmap newPath " + newPath);
                }
            }
        }
        return newPath;
    }


    /**
     * 保存图片到文件
     *
     * @param bitmap 图片
     * @param file   文件
     */
    public static void saveBitmapToFile(Bitmap bitmap, File file) {
        FileOutputStream fileOutputStream = null;
        try {
            if (file.getParentFile() != null && !file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            fileOutputStream = new FileOutputStream(file);
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream);
            fileOutputStream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fileOutputStream != null) {
                try {
                    fileOutputStream.close();
                    bitmap.recycle();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * @param viewGroup viewGroup
     * @return Bitmap
     */
    public static Bitmap getViewGroupBitmapNoBg(ViewGroup viewGroup) {
        // 创建对应大小的bitmap(重点)
        Bitmap bitmap = Bitmap.createBitmap(viewGroup.getWidth(), viewGroup.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        viewGroup.draw(canvas);
        return bitmap;
    }

    /**
     * @param viewGroup viewGroup
     * @return Bitmap
     */
    public static Bitmap getViewGroupBitmap(Activity activity, ViewGroup viewGroup) {
        View screenView = activity.getWindow().getDecorView();
        screenView.setDrawingCacheEnabled(true);
        screenView.buildDrawingCache();

        //获取屏幕整张图片
        Bitmap bitmap = screenView.getDrawingCache();

        if (bitmap != null) {
            //需要截取的长和宽
            int outWidth = viewGroup.getWidth();
            int outHeight = viewGroup.getHeight();

            //获取需要截图部分的在屏幕上的坐标(view的左上角坐标）
            int[] viewLocationArray = new int[2];
            viewGroup.getLocationOnScreen(viewLocationArray);

            //从屏幕整张图片中截取指定区域
            bitmap = Bitmap.createBitmap(bitmap, viewLocationArray[0], viewLocationArray[1], outWidth, outHeight);
        }
        return bitmap;
    }


    /**
     * 根据指定的Activity截图（带空白的状态栏）
     *
     * @param activity 要截图的Activity
     * @return Bitmap
     */
    public static Bitmap shotActivity(Activity activity) {
        View view = activity.getWindow().getDecorView();
        view.setDrawingCacheEnabled(true);
        view.buildDrawingCache();
        Bitmap bitmap = Bitmap.createBitmap(view.getDrawingCache(), 0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
        view.setDrawingCacheEnabled(false);
        view.destroyDrawingCache();
        return bitmap;
    }

    /**
     * 根据指定的Activity截图（去除状态栏）
     *
     * @param activity 要截图的Activity
     * @return Bitmap
     */
    public static Bitmap shotActivityNoStatusBar(Activity activity) {
        // 获取windows中最顶层的view
        View view = activity.getWindow().getDecorView();
        view.buildDrawingCache();
        // 获取状态栏高度
        Rect rect = new Rect();
        view.getWindowVisibleDisplayFrame(rect);
        int statusBarHeights = rect.top;

        DisplayMetrics displayMetrics = activity.getResources().getDisplayMetrics();
        // 获取屏幕长和高
        int widths = displayMetrics.widthPixels;
        int heights = displayMetrics.heightPixels;

//        Display display = activity.getWindowManager().getDefaultDisplay();
//        // 获取屏幕宽和高
//        int widths = display.getWidth();
//        int heights = display.getHeight();
        // 允许当前窗口保存缓存信息
        view.setDrawingCacheEnabled(true);
        // 去掉状态栏
        Bitmap bmp = Bitmap.createBitmap(view.getDrawingCache(), 0, statusBarHeights, widths, heights - statusBarHeights);
        // 销毁缓存信息
        view.destroyDrawingCache();
        return bmp;
    }


    public static Bitmap addWhiteBackground(Bitmap originalBitmap) {
        Bitmap whiteBackgroundBitmap = Bitmap.createBitmap(originalBitmap.getWidth(), originalBitmap.getHeight(), Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(whiteBackgroundBitmap);
        canvas.drawColor(Color.WHITE);
        canvas.drawBitmap(originalBitmap, 0, 0, null);
        originalBitmap.recycle();
        return whiteBackgroundBitmap;
    }

    public static Bitmap base64ToBitmap(String base64Str) {
        // 解码Base64字符串
        byte[] decodedBytes = Base64.decode(base64Str, Base64.DEFAULT);
        // 将字节数组转换为Bitmap对象
        return BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);
    }


    /**
     * 去掉透明白边
     *
     * @param sourceBitmap
     * @return
     * @throws IOException
     */
    public static Bitmap crop(Bitmap sourceBitmap) throws IOException {
        int sourceHeight = sourceBitmap.getHeight();
        int sourceWidth = sourceBitmap.getWidth();

        int[] pixels = new int[sourceHeight * sourceWidth];
        sourceBitmap.getPixels(pixels, 0, sourceWidth, 0, 0, sourceWidth, sourceHeight);

        int top = 0;
        int bot = sourceHeight;
        int left = 0;
        int right = sourceWidth;

        a:
        for (int i = 0; i < sourceHeight; i++) {

            for (int j = 0; j < sourceWidth; j++) {
                int color = pixels[i * sourceWidth + j];
                if (color != 0) {
                    top = i;
                    break a;
                }
            }
        }

        b:
        for (int i = sourceHeight - 1; i >= 0; i--) {

            for (int j = 0; j < sourceWidth; j++) {
                int color = pixels[i * sourceWidth + j];
                if (color != 0) {
                    bot = i;
                    break b;
                }
            }
        }

        c:
        for (int i = 0; i < sourceWidth; i++) {

            for (int j = 0; j < sourceHeight; j++) {
                int color = pixels[j * sourceWidth + i];
                if (color != 0) {
                    left = i;
                    break c;
                }
            }
        }

        d:
        for (int i = sourceWidth - 1; i >= 0; i--) {

            for (int j = 0; j < sourceHeight; j++) {
                int color = pixels[j * sourceWidth + i];
                if (color != 0) {
                    right = i;
                    break d;
                }
            }
        }
        int realWidth = right - left;
        int realHeight = bot - top;

        int[] realColors = new int[realWidth * realHeight];
        sourceBitmap.getPixels(realColors, 0, realWidth, left, top, realWidth, realHeight);
        sourceBitmap.recycle();

        Bitmap bitmap = Bitmap.createBitmap(realColors, realWidth, realHeight, Bitmap.Config.ARGB_8888);
        return bitmap;
    }

    public static Bitmap getBitmapFromView(View view) {
        // 创建一个和该view相同大小的bitmap
        Bitmap bitmap = Bitmap.createBitmap(view.getWidth(), view.getHeight(), Bitmap.Config.ARGB_8888);
        // 使用该bitmap生成一个canvas
        Canvas canvas = new Canvas(bitmap);
        // 将view绘制在canvas上
        view.draw(canvas);
        return bitmap;
    }

    public static Bitmap getTransparentBitmap(int width, int height) {
        // 创建一个和该view相同大小的bitmap
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        bitmap.eraseColor(Color.TRANSPARENT); // 设置Bitmap的背景为透明
        return bitmap;
    }

    public static Bitmap getWhiteBitmap(int width, int height) {
        // 创建一个和该view相同大小的bitmap
        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        bitmap.eraseColor(Color.WHITE); // 设置Bitmap的背景为透明
        return bitmap;
    }

    /**
     * 水平拼接图
     *
     * @param bitmap1
     * @param bitmap2
     * @return
     */
    public Bitmap combineBitmapsHorizontal(Bitmap bitmap1, Bitmap bitmap2) {
        Bitmap combinedBitmap = Bitmap.createBitmap(
                bitmap1.getWidth() + bitmap2.getWidth(),
                bitmap1.getHeight(),
                bitmap1.getConfig());

        Canvas canvas = new Canvas(combinedBitmap);
        canvas.drawBitmap(bitmap1, 0, 0, null);
        canvas.drawBitmap(bitmap2, bitmap1.getWidth(), 0, null);

        return combinedBitmap;
    }

    /**
     * 水垂直拼接图
     *
     * @param bitmap1
     * @param bitmap2
     * @return
     */
    public static Bitmap combineBitmapsVertical(Bitmap bitmap1, Bitmap bitmap2) {
        if (bitmap1.getWidth() >= bitmap2.getWidth()) {
            Bitmap combinedBitmap = Bitmap.createBitmap(
                    bitmap1.getWidth(),
                    bitmap1.getHeight() + bitmap2.getHeight(),
                    bitmap1.getConfig());

            Canvas canvas = new Canvas(combinedBitmap);
            canvas.drawBitmap(bitmap1, 0, 0, null);
            //水平居中对齐
            int left = (bitmap1.getWidth() - bitmap2.getWidth()) / 2;
            canvas.drawBitmap(bitmap2, left, bitmap1.getHeight(), null);
            return combinedBitmap;
        } else {
            Bitmap combinedBitmap = Bitmap.createBitmap(
                    bitmap2.getWidth(),
                    bitmap1.getHeight() + bitmap2.getHeight(),
                    bitmap2.getConfig());

            Canvas canvas = new Canvas(combinedBitmap);
            //水平居中对齐
            //水平居中对齐
            int left = (bitmap2.getWidth() - bitmap1.getWidth()) / 2;
            canvas.drawBitmap(bitmap1, left, 0, null);
            canvas.drawBitmap(bitmap2, 0, bitmap1.getHeight(), null);
            return combinedBitmap;
        }
    }

    /**
     * 保存图片
     *
     * @param webView
     * @return
     */
    public static Bitmap getImage(WebView webView) {
        Picture picture = webView.capturePicture();
        Bitmap bitmap = Bitmap.createBitmap(
                picture.getWidth(), picture.getHeight(), Bitmap.Config.ARGB_8888);
        return bitmap;
//        Canvas c = new Canvas(b);
//        picture.draw(c);
//        File file = new File("/sdcard/" + "page.jpg");
//        if(file.exists()){
//            file.delete();
//        }
//        FileOutputStream fos = null;
//        try {
//            fos = new FileOutputStream(file.getAbsoluteFile());
//            if (fos != null) {
//                b.compress(Bitmap.CompressFormat.JPEG, 90, fos);
//                fos.close();
////                Toast.makeText(getApplicationContext(), "保存成功", Toast.LENGTH_SHORT).show();
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
////            Toast.makeText(getApplicationContext(), "保存失败", Toast.LENGTH_SHORT).show();
//        }
    }

    // 3.getDrawingCache方法
    public static Bitmap screenshotWebView(WebView webView) {
//        // 允许当前窗口保存缓存信息
        webView.setDrawingCacheEnabled(true);
        // 去掉状态栏
        Bitmap bitmap = Bitmap.createBitmap(webView.getDrawingCache());
        // 销毁缓存信息
        webView.destroyDrawingCache();
        return bitmap;
    }
}
