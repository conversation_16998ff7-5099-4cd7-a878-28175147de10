package com.sunia.viewlib.model;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RectF;
import android.util.Log;

import com.sunia.penengine.sdk.operate.edit.LayerMode;
import com.sunia.singlepage.sdk.param.LayoutMode;

public class VerticalBlankBg {
    private static final String TAG = "VerticalBlankBg";
    private float ratio = 1f;
    private float scale = 1f;
    private float offsetX;
    private float offsetY;
    private RectF visibleRectF;
    private RectF leftRectF = new RectF();
    private RectF rightRectF = new RectF();
    private Paint viewBackColorPaint = null;
    LayoutMode layoutMode;

    public VerticalBlankBg() {
        viewBackColorPaint = new Paint();
        viewBackColorPaint.setStyle(Paint.Style.FILL);
        viewBackColorPaint.setColor(0xFFDDDDDD);
    }

    public void setScaleOffset(float ratio, float scale, float offsetX, float offsetY) {
        Log.d(TAG, "setScaleOffset: ratio=" + ratio + ", scale=" + scale + ", offsetX=" + offsetX + ", offsetY=" + offsetY);
        this.ratio = ratio;
        this.scale = scale;
        this.offsetX = offsetX;
        this.offsetY = offsetY;
        if (visibleRectF != null) {
            leftRectF.set(0, 0, Math.max(offsetX, 0), Integer.MAX_VALUE);
            rightRectF.set(Math.min(visibleRectF.width() - offsetX, visibleRectF.width()), 0, visibleRectF.width(), Integer.MAX_VALUE);
        }
    }


    public void setVisibleRectF(RectF visibleRectF) {
        this.visibleRectF = visibleRectF;
    }

    public void onDraw(Canvas canvas) {
        // 更换上屏方案之后不再外面绘制
//        if (layoutMode != LayoutMode.INFINITE_VERTICAL) return;
//        if (leftRectF != null && !leftRectF.isEmpty()) {
//            canvas.drawRect(leftRectF, viewBackColorPaint);
//        }
//        if (rightRectF != null && !rightRectF.isEmpty()) {
//            canvas.drawRect(rightRectF, viewBackColorPaint);
//        }
    }

    public void setLayoutMode(LayoutMode layoutMode) {
        this.layoutMode = layoutMode;
    }
}
