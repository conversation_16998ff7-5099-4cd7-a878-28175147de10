package com.sunia.viewlib.model;

import static com.sunia.viewlib.utils.Util.calcRotatePoint;
import static com.sunia.viewlib.utils.Util.dp2px;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.PointF;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.VectorDrawable;
import android.util.Log;

import androidx.core.content.ContextCompat;

import com.sunia.penengine.sdk.data.GroupDataType;
import com.sunia.penengine.sdk.data.RecoInfoRectF;
import com.sunia.penengine.sdk.operate.edit.SelectRectF;
import com.sunia.singlepage.sdk.InkFunc;
import com.sunia.singlepage.sdk.InkSelectEditFunc;
import com.sunia.singlepage.sdk.param.SelectActionType;
import com.sunia.viewlib.utils.Util;
import com.sunia.viewlib.view.CircleWithSquareGenerator;
import com.tcl.ai.note.sunia.R;
import com.tcl.ai.note.utils.DisplayUtilsKt;
import com.tcl.ai.note.utils.Logger;

import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;


public class SelectViewModel {
    private static final String TAG = "SelectViewModel";
    public static final int ICON_ROTATE_OFFSET_RIGHT = 60;
    public static float ROTATE_SIZE = 48f;
    private static final float CIRCLE_RADIUS = 20;
    private static final float SPANNED_PADDING = 5;

    private static float ROTATE_CIRCLE_OFFSET = 8f;
    private Paint selectPaint;
    private Bitmap mRotateBitmap;
    private Bitmap scaleBitmap;
    private Bitmap scaleDragBitmap;
    private Bitmap mixedBitmap;
    private Bitmap closeBitmap;
    private boolean isSelectPathDraw;
    private int selectType;
    private boolean isSelectedDraw;
    private boolean isSelectEditDraw;
    private boolean isInsertShapeDraw;
    private boolean isScaleDraw;
    private SelectRectF selectRectF = new SelectRectF();
    private final PointF[] keyPointFs = new PointF[5];
    private final Path drawPath = new Path();
    private final RectF scaleRectF = new RectF();
    private final RectF scaleRectF1 = new RectF();
    private final RectF scaleRectF2 = new RectF();
    private final RectF scaleRectF3 = new RectF();
    // 缩放拖拽的位置
    private RectF scaleDragRectF = new RectF();
    private final RectF rotateRectF = new RectF();
    // private final RectF mixedRectF = new RectF();
    private final RectF closeRectF = new RectF();
    private final int MENU_MIN_LIMIT = 1;

    private Paint shapePaint;
    private Paint fillPaint;
    private Path outLinePath = new Path();
    private List<Path> searchPathList = new ArrayList<>();
    private Paint highlightPaint;
    private float ratio = 1f;
    private float scale = 1f;
    private float offsetX;
    private float offsetY;
    private Matrix matrix = new Matrix();

    private Paint mRotateStrokePaint = new Paint();
    private Paint mRotateFillPaint = new Paint();

    private Paint mRactanglePaint = new Paint();

    private InkFunc inkFunc = null;
    private RectF visibleRectF = new RectF();
    private List<PointF> listPoint = new ArrayList<>();
    private int mSelectColor = 0;

    private int scaleCenterType = InkSelectEditFunc.SCALE_CENTER_TYPE_DEFAULT;

    public SelectViewModel(Context context, boolean isTablet) {
        init(context, isTablet);
    }

    public void init(Context context, boolean isTablet) {
        ROTATE_SIZE = Util.dp2px(context, 24);
        if (isTablet) {
            ROTATE_CIRCLE_OFFSET = 8f;
        } else {
            ROTATE_CIRCLE_OFFSET = 11f;
        }
        selectPaint = new Paint();
        mSelectColor = ContextCompat.getColor(context, com.tcl.ai.note.base.R.color.lasso_selected_color);
        selectPaint.setColor(mSelectColor);
        selectPaint.setStyle(Paint.Style.STROKE);
        selectPaint.setStrokeWidth(2);
        selectPaint.setPathEffect(new DashPathEffect(new float[]{4, 4}, 0));

        mRactanglePaint = new Paint();
        mRactanglePaint.setColor(mSelectColor);
        mRactanglePaint.setStyle(Paint.Style.STROKE);
        mRactanglePaint.setStrokeWidth(2);
        mRactanglePaint.setAntiAlias(true);

        mRotateStrokePaint = new Paint();
        mRotateStrokePaint.setColor(mSelectColor);
        mRotateStrokePaint.setStyle(Paint.Style.STROKE);
        mRotateStrokePaint.setStrokeWidth(2);
        mRotateStrokePaint.setAntiAlias(true);
        mRotateFillPaint = new Paint();
        mRotateFillPaint.setColor(DisplayUtilsKt.isDarkMode(context) ? Color.BLACK : Color.WHITE);
        mRotateFillPaint.setStyle(Paint.Style.FILL);
        mRotateFillPaint.setAntiAlias(true);

        shapePaint = new Paint();
        shapePaint.setStyle(Paint.Style.FILL);
        shapePaint.setAntiAlias(true);
        shapePaint.setColor(Color.BLUE);
        fillPaint = new Paint();
        fillPaint.setStyle(Paint.Style.FILL);
        fillPaint.setAntiAlias(true);
        fillPaint.setColor(Color.WHITE);
        scaleBitmap = CircleWithSquareGenerator
                .generateCircle(108, dp2px(context, 12), dp2px(context, 2),
                        DisplayUtilsKt.isDarkMode(context) ?
                                CircleWithSquareGenerator.Mode.NIGHT :
                                CircleWithSquareGenerator.Mode.DAY);
        scaleDragBitmap = Bitmap.createBitmap(
                scaleBitmap.getWidth() * 3 / 2,
                scaleBitmap.getHeight() * 3 / 2,
                Bitmap.Config.ARGB_8888
        );
        Drawable rotateDrawable = ContextCompat.getDrawable(context, com.tcl.ai.note.base.R.drawable.lasso_rotate);
        if (rotateDrawable instanceof VectorDrawable) {
            Bitmap rotateBitmap = Bitmap.createBitmap(
                    rotateDrawable.getIntrinsicWidth(),
                    rotateDrawable.getIntrinsicHeight(),
                    Bitmap.Config.ARGB_8888
            );
            Canvas canvas = new Canvas(rotateBitmap);
            rotateDrawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
            rotateDrawable.draw(canvas);
            mRotateBitmap = rotateBitmap;
        }
        mixedBitmap = BitmapFactory.decodeResource(context.getResources(), R.mipmap.icon_mixed);
        closeBitmap = BitmapFactory.decodeResource(context.getResources(), R.mipmap.icon_close);
        highlightPaint = new Paint();
        highlightPaint.setColor(Color.YELLOW);
        highlightPaint.setAlpha(100);
    }

    public void setInkFunc(InkFunc inkFunc) {
        this.inkFunc = inkFunc;
    }

    public void setVisibleRectF(RectF visibleRectF) {
        this.visibleRectF = visibleRectF;
    }

    public void onDraw(Context context, Canvas canvas) {
        canvas.save();
        updateDarkMode(context);
        if (!isSelectPathDraw && inkFunc != null) {
            //框选过程，不需要裁剪
            RectF showRectF = inkFunc.getCanvasShowRect();
            RectF rectF = getIntersection(visibleRectF, showRectF);
            if (rectF != null && !rectF.isEmpty()) {
                canvas.clipRect(rectF);
            }
        }
        if (isInsertShapeDraw) {
            // 插入图形预绘制
//            if (!isSelectedDraw) {
//                selectPaint.setColor(Color.GREEN);
//            }
//            if (!drawPath.isEmpty()){
//                canvas.drawPath(drawPath, selectPaint);
//                selectPaint.setColor(Color.BLUE);
//            }
        }
        if (isSelectPathDraw) {
            selectPaint.setPathEffect(new DashPathEffect(new float[]{4, 4}, 0));
//            selectPaint.setColor(Color.RED);
            // 绘制框选路径
            canvas.drawPath(drawPath, selectPaint);
        }
        // 选中操作绘制
        if (isSelectedDraw) {
            // 绘制框选区域
            selectPaint.setPathEffect(null);
            selectPaint.setColor(mSelectColor);
            canvas.drawPath(drawPath, mRactanglePaint);
            if (selectType != SelectActionType.SELECT_TYPE_SHAPE) {
                // 绘制缩放图标
                canvas.drawBitmap(scaleBitmap, scaleRectF.left, scaleRectF.top, null);
                canvas.drawBitmap(scaleBitmap, scaleRectF1.left, scaleRectF1.top, null);
                canvas.drawBitmap(scaleBitmap, scaleRectF2.left, scaleRectF2.top, null);
                canvas.drawBitmap(scaleBitmap, scaleRectF3.left, scaleRectF3.top, null);
            }
            if (selectType != SelectActionType.SELECT_TYPE_TABLE) {
                // 绘制旋转图标
                canvas.drawLine(keyPointFs[4].x, keyPointFs[4].y, rotateRectF.centerX(), rotateRectF.centerY(), selectPaint);
                canvas.drawCircle(rotateRectF.centerX(), rotateRectF.centerY(), (rotateRectF.bottom - rotateRectF.top) / 2, mRotateFillPaint);
                canvas.drawCircle(rotateRectF.centerX(), rotateRectF.centerY(), (rotateRectF.bottom - rotateRectF.top) / 2, mRotateStrokePaint);
                canvas.drawBitmap(mRotateBitmap, rotateRectF.left + ROTATE_CIRCLE_OFFSET, rotateRectF.top + ROTATE_CIRCLE_OFFSET, null);
            }
            if (selectType == SelectActionType.SELECT_TYPE_SHAPE) {
                // 图形四个角绘制
                shapePaint.setColor(Color.GREEN);
                if (listPoint.isEmpty()) {
                    canvas.drawCircle(keyPointFs[0].x, keyPointFs[0].y, CIRCLE_RADIUS, shapePaint);
                    canvas.drawCircle(keyPointFs[1].x, keyPointFs[1].y, CIRCLE_RADIUS, shapePaint);
                    canvas.drawCircle(keyPointFs[2].x, keyPointFs[2].y, CIRCLE_RADIUS, shapePaint);
                    canvas.drawCircle(keyPointFs[3].x, keyPointFs[3].y, CIRCLE_RADIUS, shapePaint);
                } else {
                    for (PointF pointF : listPoint) {
                        canvas.drawCircle(pointF.x, pointF.y, CIRCLE_RADIUS, shapePaint);
                    }
                }
                shapePaint.setColor(Color.BLUE);
            }
            //    canvas.drawBitmap(closeBitmap, closeRectF.left, closeRectF.top, null);
        }
        // 框选编辑绘制
        if (isSelectEditDraw) {
            if (selectType == SelectActionType.SELECT_TYPE_SPANNED) {
                // 绘制文本编辑区域
                canvas.drawRect(selectRectF.left, selectRectF.top, selectRectF.right, selectRectF.bottom, selectPaint);
                // 绘制左侧操作区域
                canvas.drawCircle(selectRectF.left, selectRectF.top, CIRCLE_RADIUS, shapePaint);
                // 绘制右侧操作区域
                canvas.drawCircle(selectRectF.right, (selectRectF.top + selectRectF.bottom) / 2f, CIRCLE_RADIUS, shapePaint);
            } else {
                if (selectType == SelectActionType.SELECT_TYPE_SHAPE) {
                    // 绘制旋转图标
                    canvas.drawLine(keyPointFs[4].x, keyPointFs[4].y, rotateRectF.centerX(), rotateRectF.centerY(), selectPaint);
                    canvas.drawCircle(rotateRectF.centerX(), rotateRectF.centerY(), (rotateRectF.bottom - rotateRectF.top) / 2, mRotateFillPaint);
                    canvas.drawCircle(rotateRectF.centerX(), rotateRectF.centerY(), (rotateRectF.bottom - rotateRectF.top) / 2, mRotateStrokePaint);
                    canvas.drawBitmap(mRotateBitmap, rotateRectF.left + ROTATE_CIRCLE_OFFSET, rotateRectF.top + ROTATE_CIRCLE_OFFSET, null);
                }
                // 绘制编辑曲线
                canvas.drawPath(drawPath, selectPaint);
            }
        }
        // 绘制蚂蚁线
        if (isSelectedDraw && outLinePath != null && !outLinePath.isEmpty()) {
            canvas.drawPath(outLinePath, selectPaint);
        }
        if (searchPathList != null && !searchPathList.isEmpty()) {
            matrix.reset();
            matrix.postScale(ratio, ratio);
            matrix.postScale(scale, scale, 0, 0);
            matrix.postTranslate(offsetX, offsetY);
            for (Path searchPath : searchPathList) {
                Path destPath = new Path(searchPath);
                searchPath.transform(matrix, destPath);
                canvas.drawPath(destPath, highlightPaint);
            }
        }

        // 识别分组框
        if (recoInfoRects != null) {
            for (RecoInfoRectF infoRectF : recoInfoRects) {
                if (infoRectF.rectF != null && !infoRectF.rectF.isEmpty()) {
                    changedPaintColorByInfoRect(infoRectF.dataType);
                    canvas.drawRect(infoRectF.rectF, selectPaint);
                }
            }
            selectPaint.setColor(mSelectColor);
        }
        // 缩放时边框和拖动点绘制
        if (isScaleDraw) {
            canvas.drawPath(drawPath, mRactanglePaint);
            canvas.drawBitmap(scaleDragBitmap, scaleDragRectF.left, scaleDragRectF.top, null);
        }
        drawSavePaths(canvas, savePaths);
        canvas.restore();
    }

    public void onSelectPathDraw(Path path) {
        isSelectPathDraw = true;
        drawPath.reset();
        if (path != null) {
            drawPath.addPath(path);
        }
    }

    public void onSelectedDraw(int selectType, SelectRectF rectF) {
        if (rectF == null || rectF.isEmpty()) {
            return;
        }
        Logger.d(TAG, "onSelectedDraw: " + selectType + ",  " + rectF);
        searchPathList.clear();
        isSelectedDraw = true;
        this.selectType = selectType;
        calculateRotateData(rectF);
        PointF centerPoint = new PointF(rectF.centerX(), rectF.centerY());
        calculateRotateRect(centerPoint, rectF);

        if (selectType != SelectActionType.SELECT_TYPE_TABLE) { // 表格无法旋转
//            float rotateSize = 48f;
            // 旋转图标位置
            PointF rotatePoint = new PointF(selectRectF.centerX(), selectRectF.bottom + ICON_ROTATE_OFFSET_RIGHT);
            if (rectF.width() < MENU_MIN_LIMIT || rectF.height() < MENU_MIN_LIMIT) {
                rotatePoint.x += ICON_ROTATE_OFFSET_RIGHT;
            }
            rotatePoint = calcRotatePoint(rotatePoint, new PointF(selectRectF.centerX(), selectRectF.centerY()), selectRectF.angle);
            rotateRectF.left = rotatePoint.x - ROTATE_SIZE / 2f;
            rotateRectF.top = rotatePoint.y - ROTATE_SIZE / 2f;
            rotateRectF.right = rotateRectF.left + ROTATE_SIZE;
            rotateRectF.bottom = rotateRectF.top + ROTATE_SIZE;
        }
    }

    public void onScaleDraw(int selectType, SelectRectF rectF) {
        if (rectF == null || rectF.isEmpty()) {
            return;
        }
        searchPathList.clear();
        isScaleDraw = true;
        this.selectType = selectType;
        calculateRotateData(rectF);
        PointF centerPoint = new PointF(rectF.centerX(), rectF.centerY());
        calculateRotateRect(centerPoint, rectF);
    }

    private void calculateRotateRect(PointF centerPoint, SelectRectF rectF) {
        if (selectType != SelectActionType.SELECT_TYPE_SHAPE) { // 图形缩放由关键点拉伸或单边拉伸
            int scaleBitmapWidth = scaleBitmap.getWidth();
            int scaleBitmapHeight = scaleBitmap.getHeight();
            /*// 缩放图标位置
            if (scaleCenterType == InkSelectEditFunc.SCALE_CENTER_TYPE_RIGHT_TOP ||
                scaleCenterType == InkSelectEditFunc.SCALE_CENTER_TYPE_RIGHT_BOTTOM ||
                scaleCenterType == InkSelectEditFunc.SCALE_CENTER_TYPE_LEFT_BOTTOM ||
                    scaleCenterType == InkSelectEditFunc.SCALE_CENTER_TYPE_LEFT_TOP) {
                scaleRectF.left = keyPointFs[3].x - scaleBitmapWidth / 2f;
                scaleRectF.top = keyPointFs[3].y - scaleBitmapHeight / 2f;
            } else {
                scaleRectF.left = keyPointFs[1].x - scaleBitmapWidth / 2f;
                scaleRectF.top = keyPointFs[1].y - scaleBitmapHeight / 2f + 5;
            }*/

            scaleRectF.left = keyPointFs[1].x - scaleBitmapWidth / 2f;
            scaleRectF.top = keyPointFs[1].y - scaleBitmapHeight / 2f + 5;
            scaleRectF.right = scaleRectF.left + scaleBitmapWidth;
            scaleRectF.bottom = scaleRectF.top + scaleBitmapHeight;
            scaleRectF1.left = keyPointFs[3].x - scaleBitmapWidth / 2f + 5;
            scaleRectF1.top = keyPointFs[3].y - scaleBitmapHeight / 2f;
            scaleRectF1.right = scaleRectF1.left + scaleBitmapWidth;
            scaleRectF1.bottom = scaleRectF1.top + scaleBitmapHeight;
            scaleRectF2.left = keyPointFs[0].x - scaleBitmapWidth / 2f + 5;
            scaleRectF2.top = keyPointFs[0].y - scaleBitmapHeight / 2f + 5;
            scaleRectF2.right = scaleRectF2.left + scaleBitmapWidth;
            scaleRectF2.bottom = scaleRectF2.top + scaleBitmapHeight;
            scaleRectF3.left = keyPointFs[2].x - scaleBitmapWidth / 2f;
            scaleRectF3.top = keyPointFs[2].y - scaleBitmapHeight / 2f;
            scaleRectF3.right = scaleRectF3.left + scaleBitmapWidth;
            scaleRectF3.bottom = scaleRectF3.top + scaleBitmapHeight;
            if (rectF.width() < MENU_MIN_LIMIT || rectF.height() < MENU_MIN_LIMIT) {
                PointF scaleCenter = calcRotatePoint(new PointF(rectF.right + scaleBitmapWidth / 2f, rectF.top - scaleBitmapHeight / 2f), centerPoint, rectF.angle);
                scaleRectF.offset(scaleCenter.x - scaleRectF.centerX(), scaleCenter.y - scaleRectF.centerY());
                PointF scaleCenter1 = calcRotatePoint(new PointF(rectF.left - scaleBitmapWidth / 2f, rectF.bottom + scaleBitmapHeight / 2f), centerPoint, rectF.angle);
                scaleRectF1.offset(scaleCenter1.x - scaleRectF1.centerX(), scaleCenter1.y - scaleRectF1.centerY());
                PointF scaleCenter2 = calcRotatePoint(new PointF(rectF.left - scaleBitmapWidth / 2f, rectF.top - scaleBitmapHeight / 2f), centerPoint, rectF.angle);
                scaleRectF2.offset(scaleCenter2.x - scaleRectF2.centerX(), scaleCenter2.y - scaleRectF2.centerY());
                PointF scaleCenter3 = calcRotatePoint(new PointF(rectF.right + scaleBitmapWidth / 2f, rectF.bottom + scaleBitmapHeight / 2f), centerPoint, rectF.angle);
                scaleRectF3.offset(scaleCenter3.x - scaleRectF3.centerX(), scaleCenter3.y - scaleRectF3.centerY());
            }
        }
    }

    public void onSelectEditDraw(int selectType, Path path, SelectRectF rectF, PointF centerPoint) {
        Logger.d(TAG, "onSelectEditDraw: " + selectType + ",  " + rectF + ",   centerPoint=" + centerPoint);
        isSelectEditDraw = true;
        this.selectType = selectType;
        drawPath.reset();
        if (rectF != null) { // 表格无区域数据
            if (selectType == SelectActionType.SELECT_TYPE_SPANNED) {
                // 文本框右侧往外扩充一个半径的位置, 其他边根据需求设置是否外扩
                selectRectF.left = rectF.left - SPANNED_PADDING;
                selectRectF.top = rectF.top - SPANNED_PADDING;
                selectRectF.right = rectF.right + CIRCLE_RADIUS;
                selectRectF.bottom = rectF.bottom + SPANNED_PADDING;
            } else {
                selectRectF.left = rectF.left;
                selectRectF.top = rectF.top;
                selectRectF.right = rectF.right;
                selectRectF.bottom = rectF.bottom;
            }

            selectRectF.angle = rectF.angle;
        }
        if (selectType == SelectActionType.SELECT_TYPE_SHAPE) {
            // 4个角坐标
            keyPointFs[0] = calcRotatePoint(new PointF(selectRectF.left, selectRectF.top), centerPoint, selectRectF.angle);
            keyPointFs[1] = calcRotatePoint(new PointF(selectRectF.right, selectRectF.top), centerPoint, selectRectF.angle);
            keyPointFs[2] = calcRotatePoint(new PointF(selectRectF.right, selectRectF.bottom), centerPoint, selectRectF.angle);
            keyPointFs[3] = calcRotatePoint(new PointF(selectRectF.left, selectRectF.bottom), centerPoint, selectRectF.angle);
            // 矩形右边中点，用来绘制旋转引导线
            keyPointFs[4] = calcRotatePoint(new PointF(selectRectF.centerX(), selectRectF.bottom), centerPoint, selectRectF.angle);
            // 框选曲线
            drawPath.moveTo(keyPointFs[0].x, keyPointFs[0].y);
            drawPath.lineTo(keyPointFs[1].x, keyPointFs[1].y);
            drawPath.lineTo(keyPointFs[2].x, keyPointFs[2].y);
            drawPath.lineTo(keyPointFs[3].x, keyPointFs[3].y);
            drawPath.lineTo(keyPointFs[0].x, keyPointFs[0].y);
            // 编辑图形，需要旋转图标位置
            PointF rotatePoint = new PointF(selectRectF.centerX(), selectRectF.bottom + ICON_ROTATE_OFFSET_RIGHT);
            rotatePoint = calcRotatePoint(rotatePoint, centerPoint, selectRectF.angle);
            rotateRectF.left = rotatePoint.x - ROTATE_SIZE / 2f;
            rotateRectF.top = rotatePoint.y - ROTATE_SIZE / 2f;
            rotateRectF.right = rotateRectF.left + ROTATE_SIZE;
            rotateRectF.bottom = rotateRectF.top + ROTATE_SIZE;
        }
        if (path != null) { // 文本无曲线数据
            drawPath.addPath(path);
        }
        calculateRotateRect(centerPoint, rectF);
    }

    public void onVertexShapePoint(List<PointF> pointList, SelectRectF selectRectF) {
        listPoint.clear();
        if (pointList.isEmpty()) {
            return;
        }
        this.selectRectF.left = selectRectF.left;
        this.selectRectF.top = selectRectF.top;
        this.selectRectF.right = selectRectF.right;
        this.selectRectF.bottom = selectRectF.bottom;
        this.selectRectF.angle = selectRectF.angle;
        PointF center = new PointF(selectRectF.centerX(), selectRectF.centerY());
        if (!pointList.isEmpty()) {
            for (PointF point : pointList) {
                listPoint.add(calcRotatePoint(point, center, selectRectF.angle));
            }
        }
    }


    public void onInsertShapeDraw(Path path) {
        isInsertShapeDraw = true;
        drawPath.reset();
        drawPath.addPath(path);
    }

    public boolean isDownOnScaleRect(float x, float y) {
        if (!isSelectedDraw) {
            return false;
        }
        if (selectType == SelectActionType.SELECT_TYPE_SHAPE) {
            return false;
        }
        RectF amplifierRectF = new RectF();
        float offset = scaleBitmap.getWidth() / 7f;
        amplifierRectF.left = scaleRectF.left - offset;
        amplifierRectF.top = scaleRectF.top - offset;
        amplifierRectF.right = scaleRectF.right + offset;
        amplifierRectF.bottom = scaleRectF.bottom + offset;

        RectF amplifierRectF1 = new RectF();
        amplifierRectF1.left = scaleRectF1.left - offset;
        amplifierRectF1.top = scaleRectF1.top - offset;
        amplifierRectF1.right = scaleRectF1.right + offset;
        amplifierRectF1.bottom = scaleRectF1.bottom + offset;

        RectF amplifierRectF2 = new RectF();
        amplifierRectF2.left = scaleRectF2.left - offset;
        amplifierRectF2.top = scaleRectF2.top - offset;
        amplifierRectF2.right = scaleRectF2.right + offset;
        amplifierRectF2.bottom = scaleRectF2.bottom + offset;

        RectF amplifierRectF3 = new RectF();
        amplifierRectF3.left = scaleRectF3.left - offset;
        amplifierRectF3.top = scaleRectF3.top - offset;
        amplifierRectF3.right = scaleRectF3.right + offset;
        amplifierRectF3.bottom = scaleRectF3.bottom + offset;

        Logger.d(TAG, "isDownOnScaleRect: " + amplifierRectF.toShortString() + ", x: " + x + ", y: " + y +
                ", scaleRectF: " + scaleRectF.toShortString() + ",scaleRectF1=" + scaleRectF1.toShortString() + ", scaleRectF2=" + scaleRectF2.toShortString() + ", scaleRectF3=" + scaleRectF3.toShortString());

        Logger.d(TAG, "isDownOnScaleRect: " + amplifierRectF.width() + ", " + amplifierRectF.height());
        Logger.d(TAG, "isDownOnScaleRect: " + scaleBitmap.getWidth() + ", " + scaleBitmap.getHeight());

        // 记录拖拽的位置
        if (amplifierRectF.contains(x, y)) {
            scaleDragRectF = scaleRectF;
            scaleCenterType = InkSelectEditFunc.SCALE_CENTER_TYPE_LEFT_BOTTOM;
        }
        if (amplifierRectF1.contains(x, y)) {
            scaleDragRectF = scaleRectF1;
            scaleCenterType = InkSelectEditFunc.SCALE_CENTER_TYPE_RIGHT_TOP;
        }
        if (amplifierRectF2.contains(x, y)) {
            scaleDragRectF = scaleRectF2;
            scaleCenterType = InkSelectEditFunc.SCALE_CENTER_TYPE_RIGHT_BOTTOM;
        }
        if (amplifierRectF3.contains(x, y)) {
            scaleDragRectF = scaleRectF3;
            scaleCenterType = InkSelectEditFunc.SCALE_CENTER_TYPE_LEFT_TOP;
        }
        // 设置缩放点
        inkFunc.getSelectEditFunc().setScaleCenterType(scaleCenterType);

        return amplifierRectF.contains(x, y) || amplifierRectF1.contains(x, y) || amplifierRectF2.contains(x, y) || amplifierRectF3.contains(x, y);
    }

    public boolean isDownOnRotateRect(float x, float y) {
        if (!isSelectedDraw) {
            return false;
        }
        if (selectType == SelectActionType.SELECT_TYPE_TABLE) {
            return false;
        }
        return rotateRectF.contains(x, y);
    }

    public boolean isDownOnMixedRect(float x, float y) {
        return false;
    }

    public boolean isDownOnSpannedLeft(float x, float y) {
        if (!isSelectEditDraw) {
            return false;
        }
        if (selectType != SelectActionType.SELECT_TYPE_SPANNED) {
            return false;
        }
        float offset = 10;
        RectF rectF = new RectF();
        rectF.left = selectRectF.left - CIRCLE_RADIUS - offset;
        rectF.top = selectRectF.top - CIRCLE_RADIUS - offset;
        rectF.right = selectRectF.left + CIRCLE_RADIUS + offset;
        rectF.bottom = selectRectF.top + CIRCLE_RADIUS + offset;
        return rectF.contains(x, y);
    }

    public boolean isDownOnSpannedRight(float x, float y) {
        if (!isSelectEditDraw) {
            return false;
        }
        if (selectType != SelectActionType.SELECT_TYPE_SPANNED) {
            return false;
        }
        float offset = 10;
        RectF rectF = new RectF();
        rectF.left = selectRectF.right - CIRCLE_RADIUS - offset;
        rectF.top = (selectRectF.top + selectRectF.bottom) / 2f - CIRCLE_RADIUS - offset;
        rectF.right = selectRectF.right + CIRCLE_RADIUS + offset;
        rectF.bottom = (selectRectF.top + selectRectF.bottom) / 2f + CIRCLE_RADIUS + offset;
        return rectF.contains(x, y);
    }


    // 自定义点击类型
    private int customClickType;

    public boolean isDownOnCustomClick(float eventX, float eventY) {
        return false;
    }

    public int getCustomClickType() {
        return customClickType;
    }

    private void calculateRotateData(SelectRectF rectF) {
        selectRectF.left = rectF.left;
        selectRectF.top = rectF.top;
        selectRectF.right = rectF.right;
        selectRectF.bottom = rectF.bottom;
        selectRectF.angle = rectF.angle;
        // 旋转中心点
        PointF centerPoint = new PointF(selectRectF.centerX(), selectRectF.centerY());
        // 4个角坐标
        keyPointFs[0] = calcRotatePoint(new PointF(selectRectF.left, selectRectF.top), centerPoint, selectRectF.angle);
        keyPointFs[1] = calcRotatePoint(new PointF(selectRectF.right, selectRectF.top), centerPoint, selectRectF.angle);
        keyPointFs[2] = calcRotatePoint(new PointF(selectRectF.right, selectRectF.bottom), centerPoint, selectRectF.angle);
        keyPointFs[3] = calcRotatePoint(new PointF(selectRectF.left, selectRectF.bottom), centerPoint, selectRectF.angle);
        // 矩形右边中点，用来绘制旋转引导线
        keyPointFs[4] = calcRotatePoint(new PointF(selectRectF.centerX(), selectRectF.bottom), centerPoint, selectRectF.angle);
        // 框选曲线
        drawPath.reset();
        drawPath.moveTo(keyPointFs[0].x, keyPointFs[0].y);
        drawPath.lineTo(keyPointFs[1].x, keyPointFs[1].y);
        drawPath.lineTo(keyPointFs[2].x, keyPointFs[2].y);
        drawPath.lineTo(keyPointFs[3].x, keyPointFs[3].y);
        drawPath.lineTo(keyPointFs[0].x, keyPointFs[0].y);
    }



    public void reset() {
        isSelectPathDraw = false;
        isInsertShapeDraw = false;
        isSelectedDraw = false;
        isSelectEditDraw = false;
        selectType = SelectActionType.SELECT_TYPE_DEFAULT;
        rotateRectF.setEmpty();
        scaleRectF.setEmpty();
        scaleRectF1.setEmpty();
        scaleRectF2.setEmpty();
        scaleRectF3.setEmpty();
        //  mixedRectF.setEmpty();
        outLinePath.reset();
//        searchPathList.clear();
    }

    public void onOutLineDraw(@Nullable Path path) {
        outLinePath.set(path);
    }

    public void setSearchPath(List<Path> sPath) {
        searchPathList.clear();
        if (sPath != null) searchPathList.addAll(sPath);
    }

    private RecoInfoRectF[] recoInfoRects = null;

    public void setRecoInfoRect(RecoInfoRectF[] infoRects) {
        this.recoInfoRects = infoRects;
    }

    private void changedPaintColorByInfoRect(int type) {
        int color = Color.BLUE;
        if (type == GroupDataType.TextLine.value) {
            color = Color.RED;
        } else if (type == GroupDataType.Text.value) {
            color = mSelectColor;
        } else if (type == GroupDataType.MathFormula.value) {
            color = Color.GREEN;
        } else if (type == GroupDataType.FormulaExpression.value) {
            color = Color.LTGRAY;
        } else if (type == GroupDataType.FormulaResult.value) {
            color = Color.LTGRAY;
        } else if (type == GroupDataType.ChemicalFormula.value) {
            color = Color.YELLOW;
        }
        selectPaint.setColor(color);

    }

    public void setScaleOffset(float ratio, float scale, float offsetX, float offsetY) {
        this.ratio = ratio;
        this.scale = scale;
        this.offsetX = offsetX;
        this.offsetY = offsetY;
    }


    public RectF getIntersection(RectF rect1, RectF rect2) {
        if (rect1 == null || rect2 == null) {
            return new RectF(0, 0, 0, 0);
        }
        float x1 = Math.max(rect1.left, rect2.left);
        float y1 = Math.max(rect1.top, rect2.top);
        float x2 = Math.min(rect1.right, rect2.right);
        float y2 = Math.min(rect1.bottom, rect2.bottom);

        if (x1 >= x2 || y1 >= y2) {
            // 没有交集，返回 null 或者一个无效的 RectF
            return new RectF(0, 0, 0, 0);
        }
        // 创建新的 RectF 表示交集
        return new RectF(x1, y1, x2, y2);
    }

    public void clearHighLight() {
        Log.d(TAG, "clearHighLight: ");
        if (searchPathList != null && !searchPathList.isEmpty()) {
            searchPathList.clear();
        }
        if (savePaths != null) {
            savePaths.clear();
        }
    }

    private List<Path> savePaths = null;
    private Paint sPaint = null;

    public void setdrawSavePaths(List<Path> savePaths) {
        this.savePaths = savePaths;
        if (sPaint == null) {
            sPaint = new Paint();
            sPaint.setColor(Color.RED);
            sPaint.setStrokeWidth(3);
            sPaint.setStyle(Paint.Style.STROKE);
        }
    }

    public void drawSavePaths(Canvas canvas, List<Path> savePaths) {
        if (savePaths != null) {
            for (Path path : savePaths) {
                canvas.drawPath(path, sPaint);
            }
        }
    }

    // 更新深色模式
    public void updateDarkMode(Context context) {
       boolean isDarkMode = DisplayUtilsKt.isDarkMode(context);
        // 更新四个角圆形缩放图标的填充色
        scaleBitmap = CircleWithSquareGenerator
                .generateCircle(108, dp2px(context, 12), 2,
                        isDarkMode ?
                                CircleWithSquareGenerator.Mode.NIGHT :
                                CircleWithSquareGenerator.Mode.DAY);
        // 更新旋转图标填充色
        mRotateFillPaint.setColor(DisplayUtilsKt.isDarkMode(context) ? Color.BLACK : Color.WHITE);
    }
}
