package com.sunia.viewlib.model;

import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.RectF;

import com.sunia.penengine.sdk.operate.edit.LayerMode;

public class EraserViewMode {
    private boolean isEraserMode;
    private final float[] eraserData = new float[4];
    private boolean isCircle;
    private Paint eraserPaint;
    private float ratio = 1f;
    private float scale = 1f;
    private float offsetX;
    private float offsetY;
    private RectF rectF = new RectF();
    private LayerMode layerMode;

    public EraserViewMode() {
        init();
    }

    public void init(){
        eraserPaint = new Paint();
        eraserPaint.setStyle(Paint.Style.STROKE);
        eraserPaint.setAntiAlias(true);
        eraserPaint.setColor(Color.BLUE);
    }

    public void onEraserDraw(float[] data, boolean isCircle) {
        isEraserMode = true;
        eraserData[0] = data[0];
        eraserData[1] = data[1];
        eraserData[2] = data[2];
        eraserData[3] = data[3];
        this.isCircle = isCircle;
    }

    public void onDraw(Canvas canvas) {
        if (isEraserMode) {
            // 绘制橡皮擦
            canvas.save();
            if (isCircle) {
                canvas.drawCircle(eraserData[0], eraserData[1], eraserData[2] * scale, eraserPaint);
            } else {
                rectF.set(eraserData[0], eraserData[1], eraserData[2], eraserData[3]);
                float w = rectF.width();
                float h = rectF.height();
                canvas.drawRect(rectF.centerX() - w * scale / 2, rectF.centerY() - h * scale / 2,
                        rectF.centerX() + w * scale / 2, rectF.centerY() + h * scale / 2, eraserPaint);
            }
            canvas.restore();
        }
    }

    public void setScaleOffset(float ratio, float scale, float offsetX, float offsetY) {
        this.ratio = ratio;
        this.scale = scale;
        this.offsetX = offsetX;
        this.offsetY = offsetY;
    }



    public void reset(){
        isEraserMode = false;
    }

    public void setScale(float scale) {
        this.scale = scale;
    }
}
