package com.sunia.viewlib.helper;

import android.content.res.AssetManager;
import android.graphics.*;
import android.text.TextPaint;
import android.text.TextUtils;
import androidx.annotation.Nullable;
import com.blankj.utilcode.util.GsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ObjectUtils;
import com.kspark.custom.bean.AttachmentBean;
import com.kspark.custom.utils.BitmapUtilKT;
import com.sunia.penengine.sdk.data.DataState;
import com.sunia.penengine.sdk.data.IAttachment;
import com.sunia.penengine.sdk.engine.INoteEngine;
import com.sunia.penengine.sdk.operate.edit.IAttachmentCallback;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

public class AttachmentHelper implements IAttachmentCallback {

    private INoteEngine noteEngine;
    private static String TAG = AttachmentHelper.class.getSimpleName();
    //    private List<String> engineConfigList = new ArrayList<>();
    private AssetManager manager;

    public AttachmentHelper(AssetManager manager) {
        this.manager = manager;
    }

//    public void setNoteEngine(INoteEngine noteEngine) {
//        this.noteEngine = noteEngine;
//        setConfig();
//    }
//
//    private void setConfig() {
//        int hashCode = noteEngine.hashCode();
//        if (!engineConfigList.contains(String.valueOf(hashCode))) {
//            engineConfigList.add(String.valueOf(hashCode));
//            if (noteEngine != null) {
//                noteEngine.getEditOperator().setAttachmentCallback(this);
//            }
//        }
//    }

//    public void testAddAttachment(RectF rectF) {
//        if (noteEngine != null) {
//            String elementJson = "{\"id\":12,\"width\":250,\"height\":120,\"suffix\":\"doc\",\"fileName\":\"AI总结.doc\"}"; // "id:12;suffix:doc";
//            byte[] element = elementJson.getBytes(StandardCharsets.UTF_8);
//            if (rectF == null) {
//                rectF = new RectF();
//                rectF.left = 100;
//                rectF.top = 300;
//                rectF.right = rectF.left + 250;
//                rectF.bottom = rectF.top + 120;
//            }
//            noteEngine.getEditOperator().addAttachment(element, rectF, 0);
//        }
//    }

    @Override
    public Bitmap createBitmap(IAttachment attachment, int width, int height) {
        if (width == 0 || height == 0) {
            return null;
        }
        if (ObjectUtils.isEmpty(attachment)) {
            LogUtils.d(TAG, "attachment.getState()=null");
        } else {
            LogUtils.d(TAG, "attachment.getState()=" + attachment.getState()+" w"+width+" h"+height);
        }
        AttachmentBean attachmentBean = parseElement(attachment);
        LogUtils.d(TAG, "attachmentBean " + attachmentBean);
        if (ObjectUtils.isEmpty(attachmentBean)) {
            return null;
        }

        // pdf文件
        if(attachmentBean.getDataType()==1){
            if(attachmentBean.getFileType() ==0){
                Bitmap bitmap = null;
                try {
                    BitmapFactory.Options options = new BitmapFactory.Options();
                    options.inJustDecodeBounds = true;
                    InputStream inputStream = manager.open("pdf_icon.png");
                    BitmapFactory.decodeStream(inputStream, null, options);
                    inputStream.close();
                    inputStream = manager.open("pdf_icon.png");
                    Bitmap originalBitmap = BitmapFactory.decodeStream(inputStream);
                    inputStream.close();
                    Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, width, height, true);
                    int angle = attachmentBean.getAngle();
                    if(angle!=0){
                        Matrix matrix = new Matrix();
                        matrix.postRotate(angle);
                        scaledBitmap = Bitmap.createBitmap(scaledBitmap, 0, 0, scaledBitmap.getWidth(), scaledBitmap.getHeight(), matrix, true);
                    }
                    return scaledBitmap;
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            } else {
                Bitmap bitmap = Bitmap.createBitmap(width,height, Bitmap.Config.ARGB_8888);
                bitmap.eraseColor(Color.RED);
                return bitmap;
            }
        }

        LogUtils.d(TAG, "attachmentBean=" + attachmentBean);
        Bitmap resultBitmap;
        if (attachment.getState() == DataState.Select) {
            resultBitmap = BitmapUtilKT.INSTANCE.createScaledBitmap(BitmapUtilKT.INSTANCE.decodeBitmapFromFile(attachmentBean.getClickedFilePath(), attachmentBean.getWidth(), attachmentBean.getHeight()), attachmentBean.getWidth(), attachmentBean.getHeight());
        } else {
            resultBitmap = BitmapUtilKT.INSTANCE.createScaledBitmap(BitmapUtilKT.INSTANCE.decodeBitmapFromFile(attachmentBean.getDefaultFilePath(), attachmentBean.getWidth(), attachmentBean.getHeight()), attachmentBean.getWidth(), attachmentBean.getHeight());
        }
        LogUtils.d(TAG, "attachment.getState()=null" + attachment.getState() + ",width=" + width + ",height=" + height + ",resultBitmap.getWidth()=" + resultBitmap.getWidth() + ",resultBitmap.getHeight()=" + resultBitmap.getHeight());
        return resultBitmap;


//        Bitmap bitmap = null;
//        Bitmap initBitmap = null;
//        if (attachmentBean != null && attachmentBean.width != 0 && attachmentBean.height != 0) {
//            int w = attachmentBean.width;
//            int h = attachmentBean.height;
//            initBitmap = Bitmap.createBitmap(w, h, Bitmap.Config.ARGB_8888);
//            Canvas canvas = new Canvas(initBitmap);
//            canvas.drawColor(Color.YELLOW);
//            if (!TextUtils.isEmpty(attachmentBean.fileName)) {
//                TextPaint textPaint = getPaintText();
//                Paint.FontMetrics fontMetrics = textPaint.getFontMetrics();
//                float x = w / 2f;
//                float y = h / 2f - (fontMetrics.ascent + fontMetrics.descent) / 2;
//                canvas.drawText(attachmentBean.fileName, x, y, textPaint);
//            }
//        }
//
//        if (initBitmap != null && (initBitmap.getWidth() != width || initBitmap.getHeight() != height)) {
//            Bitmap scaledBitmap = Bitmap.createScaledBitmap(initBitmap, width, height, true);
//            if (scaledBitmap != initBitmap) {
//                initBitmap.recycle();
//                initBitmap = null;
//            }
//            bitmap = scaledBitmap;
//        }
//
//        if (bitmap == null) {
//            bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
//            Canvas canvas = new Canvas(bitmap);
//            canvas.drawColor(Color.YELLOW);
//            if (attachmentBean != null && !TextUtils.isEmpty(attachmentBean.fileName)) {
//                canvas.drawText(attachmentBean.fileName, width / 3.f, height / 3.f, getPaintText());
//            }
//        }
//        return bitmap;
    }

    @Nullable
    public static AttachmentBean parseElement(IAttachment attachment) {
        AttachmentBean attachmentBean = null;
        if (attachment != null) {
            byte[] element = attachment.getElement();
            if (element != null && element.length > 0) {
                String result = new String(element, StandardCharsets.UTF_8);
                if (!TextUtils.isEmpty(result)) {
                    attachmentBean = GsonUtils.fromJson(result, AttachmentBean.class);
//                    try {
//                        attachmentBean = new AttachmentBean();
//                        JSONObject jsonObject = new JSONObject(result);
//                        int id = jsonObject.getInt("id");
//                        attachmentBean.id = id;
//
//                        int init_width = jsonObject.getInt("width");
//                        int init_height = jsonObject.getInt("height");
//                        attachmentBean.width = init_width;
//                        attachmentBean.height = init_height;
//
//                        String suffix = jsonObject.getString("suffix");
//                        attachmentBean.suffix = suffix;
//
//                        String fileName = jsonObject.getString("fileName");
//                        attachmentBean.fileName = fileName;
//
//                        LogUtil.d(TAG, "id:" + id + " init_width:" + init_width + " init_height " + init_height + " suffix:" + suffix + " fileName:" + fileName);
//                    } catch (JSONException e) {
//                        e.printStackTrace();
//                    }
                }
            }
        }
        return attachmentBean;
    }

    private TextPaint getPaintText() {
        TextPaint paint = new TextPaint();
        paint.setTextSize(20);
        paint.setColor(Color.BLACK);
        paint.setAntiAlias(true);
        paint.setTextAlign(Paint.Align.CENTER); // 设置水平对齐方式为居中
        return paint;
    }

    @Override
    public void destroyBitmap(Bitmap bitmap) {

    }

//    public static class AttachmentBean {
//        public int id;
//        public String suffix;
//        public String fileName;
//        public int width;
//        public int height;
//    }
}
