package com.sunia.viewlib.utils;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.text.TextUtils;

import com.sunia.penengine.sdk.data.SimpleTextData;
import com.sunia.penengine.sdk.operate.edit.ISelectObject;
import com.sunia.penengine.sdk.operate.edit.ISelectTextObject;
import com.sunia.penengine.sdk.operate.edit.PasteInfo;
import com.sunia.viewlib.ViewLib;

public class PasteHelper {
    private static final String TAG = PasteHelper.class.getSimpleName();
    // 旧粘贴功能根据是否有引擎复制内容和外部文本内容来控制两种粘贴按钮显示，两种内容无关联
    public static final int PASTE_MODE_OLD = 1;
    // 新粘贴功能可以将外部文本内容粘贴为引擎文本内容，且两种内容相互关联，引擎复制操作和外部文本复制操作都会更改粘贴内容
    public static final int PASTE_MODE_NEW = 2;
    private final int TYPE_DRAW = 1;
    private final int TYPE_TEXT = 2;
    private final int TYPE_PASTE_INFO_TEXT = 1;
    private final int TYPE_PASTE_INFO_OTHER = 2;
    private Context context;
    private PasteBean pasteBean;

    public PasteHelper(Context context) {
        this.context = context;
        // 默认获取粘贴板文字数据
        checkClipboardText();
    }

    public void checkClipboardText() {
        ClipboardManager clipboard = (ClipboardManager) context.getApplicationContext().getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clipData = clipboard.getPrimaryClip();
        if (clipData != null && clipData.getItemCount() > 0) {
            ClipData.Item item = clipData.getItemAt(0);
            CharSequence text = item.getText();
            if (!TextUtils.isEmpty(text)) {
                if (pasteBean != null && TextUtils.equals(text.toString(), pasteBean.text)) {
                    return;
                }
                // 处理粘贴板上的文本内容
                setText(text.toString());
            }
        }
    }

    /**
     * 设置粘贴数据(引擎数据, 是否文本框类型和文本框数据设置在操作前设置)
     *
     * @param pasteInfo 粘贴数据
     */
    public void setPasteInfo(PasteInfo pasteInfo) {
        if (pasteInfo == null) {
            return;
        }
        if (pasteBean == null) {
            pasteBean = new PasteBean();
        }
        pasteBean.type = TYPE_DRAW;
        pasteBean.pasteInfo = pasteInfo;
    }

    /**
     * 设置纯文本数据
     *
     * @param text 纯文本数据
     */
    private void setText(String text) {
        if (TextUtils.isEmpty(text)) {
            return;
        }
        if (pasteBean == null) {
            pasteBean = new PasteBean();
        }
        pasteBean.type = TYPE_TEXT;
        pasteBean.text = text;
        if (ViewLib.Companion.getPASTE_MODE() == PASTE_MODE_OLD) {
            // 旧粘贴功能粘贴板文字数据变化时不需要重置引擎复制内容
            return;
        }
        if (pasteBean.pasteInfo != null) {
            pasteBean.pasteInfo.recycle();
            pasteBean.pasteInfo = null;
        }
    }

    /**
     * 设置文本框数据
     *
     * @param selectObject 框选数据
     */
    public void setSimpleTextData(ISelectObject selectObject) {
        checkClipboardText();
        if (ViewLib.Companion.getPASTE_MODE() == PASTE_MODE_OLD) {
            // 旧粘贴功能不需要记录引擎文本内容
            return;
        }
        if (pasteBean == null) {
            pasteBean = new PasteBean();
        }
        if (selectObject instanceof ISelectTextObject) {
            pasteBean.pasteInfoType = TYPE_PASTE_INFO_TEXT;
            ISelectTextObject selectTextObject = (ISelectTextObject) selectObject;
            // 不能在引擎线程做获取数据操作，会卡死引擎线程
            SimpleTextData textData = selectTextObject.getText();
            pasteBean.textData = new SimpleTextData();
            pasteBean.textData.setContentRectF(textData.getContentRectF());
            pasteBean.textData.setTextColor(textData.getTextColor());
            pasteBean.textData.setText(textData.getText());
            pasteBean.textData.setTextSize(textData.getTextSize());
        } else {
            pasteBean.pasteInfoType = TYPE_PASTE_INFO_OTHER;
            pasteBean.textData = null;
        }
    }

    /**
     * 是否有粘贴数据
     *
     * @return 是否
     */
    public boolean hasPasteData() {
        return pasteBean != null;
    }

    /**
     * 是否有引擎复制数据
     *
     * @return 是否
     */
    public boolean hasEngineData() {
        if (pasteBean == null) {
            return false;
        }
        if (pasteBean.pasteInfo != null && pasteBean.pasteInfo.dataP != 0) {
            return true;
        }
        return false;
    }

    /**
     * 是否有文本数据(纯文本或文本框数据)
     *
     * @return 是否
     */
    public boolean hasTextData() {
        if (pasteBean == null) {
            return false;
        }
        if (ViewLib.Companion.getPASTE_MODE() == PASTE_MODE_OLD) {
            // 旧粘贴功能只需判断粘贴板文字数据
            if (!TextUtils.isEmpty(pasteBean.text)) {
                return true;
            }
            return false;
        }
        if (pasteBean.type == TYPE_TEXT) {
            return true;
        }
        if (pasteBean.type == TYPE_DRAW && pasteBean.pasteInfoType == TYPE_PASTE_INFO_TEXT) {
            return true;
        }
        return false;
    }

    /**
     * 是否纯文本数据
     *
     * @return 是否
     */
    public boolean isText() {
        if (pasteBean == null) {
            return false;
        }
        if (pasteBean.type == TYPE_TEXT) {
            return true;
        }
        return false;
    }

    public String getText() {
        if (pasteBean == null) {
            return "";
        }
        if (pasteBean.type == TYPE_TEXT) {
            return pasteBean.text;
        }
        if (pasteBean.pasteInfoType == TYPE_PASTE_INFO_OTHER || pasteBean.textData == null) {
            return "";
        }
        return pasteBean.textData.getText();
    }

    public void updateClipChanged() {
        ClipboardManager clipboard = (ClipboardManager) context.getApplicationContext().getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clipData = clipboard.getPrimaryClip();
        if (clipData != null && clipData.getItemCount() > 0) {
            ClipData.Item item = clipData.getItemAt(0);
            CharSequence text = item.getText();
            if (!TextUtils.isEmpty(text)) {
                setText(text.toString());
            }
        }
    }

    public static class PasteBean {
        public int type;
        public String text;
        public PasteInfo pasteInfo;
        public SimpleTextData textData;
        public int pasteInfoType;
    }
}
