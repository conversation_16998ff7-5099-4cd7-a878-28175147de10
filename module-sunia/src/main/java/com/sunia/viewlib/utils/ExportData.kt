package com.sunia.viewlib.utils

import com.kspark.custom.utils.FileUtils
import java.util.UUID

/**
 * Created on 2024/4/7.
 * desc: Export Data
 * <AUTHOR> Qin
 */
class ExportData {

    var exportType = 0

    var isIncludeBg = true //是否包含背景

    var savePath: String? = null

    var fileName: String? = null

    var needShare: Boolean = true

    companion object{

        val PNG_TYPE = 0
        val JPEG_TYPE = 1
        val BMP_TYPE = 2
        val ENT_TYPE = 3
        val PDF_TYPE = 4

        var EXPORT_PATH = FilePathConstant.getExportFilePath()

        var mapPostFixType = object : HashMap<Int?, String?>() {
            init {
                put(PNG_TYPE, "png")
                put(JPEG_TYPE, "jpeg")
                put(BMP_TYPE, "bmp")
                put(ENT_TYPE, "zip")
                put(PDF_TYPE, "pdf")
            }
        }

        private var curExport: ExportData? = null

        fun getCurExport():  ExportData {
            if (curExport == null) {
                curExport = ExportData().apply {
                    exportType = PNG_TYPE
                    isIncludeBg = true
                    savePath = EXPORT_PATH
                    fileName = UUID.randomUUID().toString()
                }
            }
            return curExport!!
        }
    }

}