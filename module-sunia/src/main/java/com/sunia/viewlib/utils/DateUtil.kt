package com.sunia.viewlib.utils

import android.text.TextUtils
import java.io.File
import java.io.FileInputStream
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created on 2024/4/7.
 * desc: Date Util
 * <AUTHOR> Qin
 */
class DateUtil {
    companion object {
        fun getTodayDateStr(): String? {
            return SimpleDateFormat("yyyy_MM_dd").format(Date(System.currentTimeMillis()))
        }

        fun getNowTimeStr(): String? {
            return SimpleDateFormat("yyyy-MM-dd_HH-mm-ss-SSS").format(Date(System.currentTimeMillis()))
        }

        fun getTimeStr(currentTimeMilli: Long): String? {
            return SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(Date(currentTimeMilli))
        }

        const val HONOR_LOGO_FLAG = "HWGRAFFI"
        const val FORMAT_HEADER = "HWPENKITDATA" //ent

        const val ASAPENKIT = "ASAPENKIT" //ent

        const val ASAINKEDIT = "ASAINKEDIT" //自研格式
        fun getEntDataFormat(filePath: String?): Int {
            if (filePath == null) {
                return DataFormat.UNKNOWN
            }
            val dataFile = File(filePath)
            if (!dataFile.exists()) {
                return DataFormat.UNKNOWN
            }
            if (dataFile.isDirectory) {
                return DataFormat.UNKNOWN
            }
            val head = ByteArray(12)
            var fileInputStream: FileInputStream? = null
            try {
                fileInputStream = FileInputStream(filePath)
                val len = fileInputStream.read(head)
                if (len == head.size) {
                    val headStr1 = String(head, StandardCharsets.UTF_8).trim { it <= ' ' }
                    val headStr2 = String(head, 0, 8, StandardCharsets.UTF_8).trim { it <= ' ' }
                    if (TextUtils.equals(headStr2, HONOR_LOGO_FLAG)) {
                        return DataFormat.SINGLE_HW
                    }
                    if (TextUtils.equals(headStr2, ASAINKEDIT)) {
                        return DataFormat.SINGLE_ASA
                    }
                    if (TextUtils.equals(headStr1, FORMAT_HEADER)) {
                        return DataFormat.SINGLE_ENT_HW
                    }
                    if (TextUtils.equals(headStr1, ASAPENKIT)) {
                        return DataFormat.SINGLE_ENT_ASA
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            } finally {
                if (fileInputStream != null) {
                    try {
                        fileInputStream.close()
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
            return DataFormat.UNKNOWN
        }
    }
}