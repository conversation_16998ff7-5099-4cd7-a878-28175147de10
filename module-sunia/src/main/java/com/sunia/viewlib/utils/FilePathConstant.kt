package com.sunia.viewlib.utils

import android.text.TextUtils
import com.sunia.singlepage.sdk.InkSDK
import com.sunia.viewlib.ViewLib
import java.io.File
import java.text.SimpleDateFormat
import java.util.*

/**
 * Created on 2024/4/7.
 * desc: File Path Constant
 * <AUTHOR> Qin
 */
class FilePathConstant {


    companion object {
        
        val THUMB_TEMP = "thumb_temp_"
        val TEMP = "temp"
        var crashPath //应用层崩溃日志保存路径
                : String? = null
        var authLogsPath //授权日志保存路径
                : String? = null
        var verifyPath //授权文件保存路径
                : String? = null
        private var configPath //配置文件路径
                : String? = null

        //    private static String engieLogFilePath; //引擎日志保存路径
        private var entFilePath //ent文件保存路径
                : String? = null
        private var exportFilePath //导出文件保存路径
                : String? = null
        private var multiSyncFilePath //多端同步路径
                : String? = null
        private var multiSyncGalleryPath //多端同步接收背景纹理路径
                : String? = null
        private var imgPath: String? = null
        val ENT_MULTI_ENGINE_AND_PAGE = "ent_multi_engine_and_page_"
        
        fun setPath() {
            //沙箱文件目录：/sdcart/Android/data/${packagename}/files/verifyInfos
            verifyPath = ViewLib.instanace?.getExternalFilesDir("verifyInfos")?.getPath()
            //沙箱文件目录：/sdcart/Android/data/${packagename}/cache/crash_logs
            crashPath = ViewLib.instanace?.externalCacheDir?.path + "/crash_logs"
            authLogsPath =
                ViewLib.instanace?.externalCacheDir?.path + "/auth_logs"
            //沙箱文件目录：/sdcart/Android/data/${packagename}/files/ents
            entFilePath = ViewLib.instanace?.getExternalFilesDir("ents")?.getPath()
            exportFilePath = ViewLib.instanace?.getExternalFilesDir("export")?.getPath()
            multiSyncFilePath =
                ViewLib.instanace?.getExternalFilesDir("multiSync")?.getPath()
            multiSyncGalleryPath =
                ViewLib.instanace?.getExternalFilesDir("multiSyncGallery")?.getPath()
            configPath = ViewLib.instanace?.externalCacheDir?.path + "/configs"
        }

        fun getImgPath(): String? {
            if (imgPath == null) {
                imgPath = ViewLib.instanace?.externalCacheDir?.path + "/images"
            }
            return imgPath
        }

        fun getEngineLogFilePath(): String {
            //沙箱文件目录：/sdcart/Android/data/${packagename}/cache/engine_logs/${Date}
            var logFilePath: String =  ViewLib.instanace?.externalCacheDir?.path + "/engine_logs" + File.separator + DateUtil.getTodayDateStr()
            val file = File(logFilePath)
            if (!file.exists()) {
                file.mkdirs()
            }
            logFilePath =
                logFilePath + File.separator + "engineLog_" + DateUtil.getNowTimeStr() + ".txt"
            return logFilePath
        }

        private fun getEntFilePath(): String? {
            return entFilePath
        }

        /**
         * ent文件的临时文件夹
         *
         * @return
         */
        fun getEntFilePathTemp(): String {
            return getEntFilePath() + File.separator + TEMP
        }

        /**
         * 预览图缩略图的临时文件夹
         *
         * @return
         */
        fun getEntFilePathThumbTemp(): String {
            return getEntFilePath() + File.separator + THUMB_TEMP
        }

        fun getExportFilePath(): String? {
            return exportFilePath
        }

        fun getEntFileName(): String {
            return "sunia_" + SimpleDateFormat("yyyy_MM_dd_HH_mm_ss_SSS").format(
                Date(
                    System.currentTimeMillis()
                )
            ) + ".ent"
        }

        /**
         * 多页Ent保存路径
         *
         * @return
         */
        fun getMultiEntDir(): String? {
            val file = File(entFilePath + File.separator + "ent_multi_" + System.currentTimeMillis())
            //        if (!file.exists()) {
//            file.mkdirs();
//        }
            return file.absolutePath
        }

        fun getMultiSyncPath(): String? {
            val file = File(multiSyncFilePath)
            if (!file.exists()) {
                file.mkdirs()
            }
            return file.absolutePath
        }

        fun getMultiSyncGalleryPath(): String? {
            val file = File(multiSyncGalleryPath)
            if (!file.exists()) {
                file.mkdirs()
            }
            return file.absolutePath
        }

        fun getConfigPath(): String? {
            if (TextUtils.isEmpty(configPath)) {
                configPath =
                    ViewLib.instanace?.externalCacheDir?.path + "/configs"
            }
            return configPath
        }


        fun copyConfigFiles() {
            //沙箱文件目录：/sdcart/Android/data/${packagename}/cache/configs
            val tempConfigPath: String =
                ViewLib.instanace?.externalCacheDir?.path + "/configs"
            val dirs = File(tempConfigPath)
            if (!dirs.exists()) {
                dirs.mkdirs()
            }
            val shapeFile = File(tempConfigPath, "shape.conf")
            if (shapeFile.exists()) {
                FileUtil.deleteFile(shapeFile)
            }
            if (!shapeFile.exists()) {
                ViewLib.instanace?.let {
                    FileUtil.copyAssetFile(
                        it,
                        "configs",
                        tempConfigPath,
                        "shape.conf"
                    )
                }
            }
            val shapeModelFile = File(tempConfigPath, "shape.hdb")
            if (shapeModelFile.exists()) {
                FileUtil.deleteFile(shapeModelFile)
            }
            if (!shapeModelFile.exists()) {
                ViewLib.instanace?.let {
                    FileUtil.copyAssetFile(
                        it,
                        "configs",
                        tempConfigPath,
                        "shape.hdb"
                    )
                }
            }
            configPath = tempConfigPath
        }

        /**
         * 获取封面图路径
         *
         * @return
         */
        fun getCoverBitmapName(): String? {
            return "img_cover.png"
        }

        fun createMultiPageEntPathDir(): String? {
            val file =
                File(entFilePath + File.separator + ENT_MULTI_ENGINE_AND_PAGE + System.currentTimeMillis())
            return file.absolutePath
        }
    }
}