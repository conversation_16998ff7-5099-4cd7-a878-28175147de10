package com.sunia.viewlib.utils;

import android.util.Log;

/**
 * <AUTHOR> zeng
 * @date 2024/5/22
 * Description:
 */
public class XLog {

    private static boolean enable = false;
    private static String COMMON_TAG = "XLog - ";

    public static boolean isEnable() {
        return enable;
    }

    public static void setEnable(boolean enable) {
        XLog.enable = enable;
    }

    public static void d(String tag, String msg) {
        if (!enable) {
            return;
        }
        int logLength = 2000; // 你可以根据需要设置每次打印的长度
        for (int i = 0; i < msg.length(); i += logLength) {
            int endIndex = Math.min(msg.length(), i + logLength);
            Log.d(COMMON_TAG + tag, methodNameAndLineNumber() + msg.substring(i, endIndex));
        }

    }

    public static void e(String tag, String msg) {
        if (!enable) {
            return;
        }
        Log.e(COMMON_TAG + tag, methodNameAndLineNumber() + msg);
    }

    private static String methodNameAndLineNumber(){
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        int stackOffset = 4;
        if (stackTrace.length < stackOffset){
            return "";
        }
        StackTraceElement element = stackTrace[stackOffset];
        String methodName = element.getMethodName();
        int lineNumber = element.getLineNumber();
        String className = element.getClassName();
        String fileName = element.getFileName();
        return "["+methodName+"]["+lineNumber+"]";
    }

}
