package com.sunia.viewlib.utils;

import android.content.Context;
import android.graphics.PointF;

public class Util {
    /**
     * 根据角度计算旋转点
     *
     * @param point  当前点
     * @param center 选转框的中心点
     * @param angle  旋转的角度  （需要逆向计算的时候，此处负向）
     * @return 计算出未旋转之前的点
     * x_ = x * cos(角度)-y * sin(角度); y_ = y * sin(角度)+ y * cos(角度);
     */
    public static PointF calcRotatePoint(PointF point, PointF center, float angle) {
        PointF dstPoint = new PointF();
        float xDur = point.x - center.x;
        float yDur = point.y - center.y;
        double rad = -Math.toRadians(angle);
        dstPoint.x = (float) (xDur * Math.cos(rad) + yDur * Math.sin(rad) + center.x);
        dstPoint.y = (float) (yDur * Math.cos(rad) - xDur * Math.sin(rad) + center.y);
        return dstPoint;
    }

    public static int dp2px(Context context, int dpValue) {
        float scale = context.getResources().getDisplayMetrics().density;
        return (int) (dpValue * scale + 0.5f);
    }
}
