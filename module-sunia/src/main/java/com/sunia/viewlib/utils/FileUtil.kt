package com.sunia.viewlib.utils

import android.content.ContentUris
import android.content.Context
import android.database.Cursor
import android.graphics.Bitmap
import android.net.Uri
import android.os.Environment
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.provider.OpenableColumns
import android.text.TextUtils
import android.util.Log
import com.sunia.viewlib.export.DataPoint
import org.json.JSONArray
import org.json.JSONObject
import java.io.*

/**
 * Created on 2024/4/7.
 * desc: File Util
 * <AUTHOR> Qin
 */
class FileUtil {

    companion object {
        fun deleteFile(file: File?): Boolean {
            return file != null && (!file.exists() || file.isFile && file.delete())
        }

        /**
         * 删除文件目录，以及目录下的文件
         * @param file
         */
        fun deleteDirs(file: File?, isIncludeDir: Boolean): Boolean {
            if (file != null) {
                var res = false
                if (file.exists()) {
                    if (file.isDirectory) {
                        val files = file.listFiles()
                        if (files != null && files.size != 0) {
                            for (f in files) {
                                if (f.isDirectory) {
                                    deleteDirs(f, true)
                                } else {
                                    res = f.delete()
                                }
                            }
                        }
                    }
                    if (isIncludeDir) {
                        res = file.delete()
                    }
                    return res
                }
            }
            return false
        }


        /**
         * 拷贝asset资源文件到物理文件中
         * @param context
         * @param assetFileDir
         * @param tagFileDir
         * @param fileName
         */
        fun copyAssetFile(
            context: Context,
            assetFileDir: String,
            tagFileDir: String?,
            fileName: String
        ) {
            val outFile = File(tagFileDir, fileName)
            if (outFile.exists()) {
                val res = outFile.delete()
            }
            var inputStream: InputStream? = null
            var outputStream: OutputStream? = null
            try {
                var tempFileName = fileName
                if (!TextUtils.isEmpty(assetFileDir)) {
                    tempFileName = assetFileDir + File.separator + fileName
                }
                inputStream = context.assets.open(tempFileName)
                outputStream = FileOutputStream(outFile)
                val buffer = ByteArray(1024)
                var len: Int
                while (inputStream.read(buffer).also { len = it } > 0) {
                    outputStream.write(buffer, 0, len)
                }
                inputStream.close()
                outputStream.close()
                inputStream = null
                outputStream = null
            } catch (ioException: IOException) {
                ioException.printStackTrace()
            } finally {
                try {
                    inputStream?.close()
                    outputStream?.close()
                } catch (ioException: IOException) {
                    ioException.printStackTrace()
                }
            }
        }

        fun getRealPath(context: Context?, fileUri: Uri?): String? {
            if (context == null || fileUri == null) {
                return null
            }
            if (DocumentsContract.isDocumentUri(context, fileUri)) {
                if (isExternalStorageDocument(fileUri)) {
                    val docId = DocumentsContract.getDocumentId(fileUri)
                    val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }
                        .toTypedArray()
                    val type = split[0]
                    if ("primary".equals(type, ignoreCase = true)) {
                        return Environment.getExternalStorageDirectory().toString() + "/" + split[1]
                    }
                } else if (isDownloadsDocument(fileUri)) {
                    val id = DocumentsContract.getDocumentId(fileUri)
                    if (id != null && id.startsWith("raw:")) {
                        return id.substring(4)
                    }
                    val contentUriPrefixesToTry = arrayOf(
                        "content://downloads/public_downloads",
                        "content://downloads/my_downloads"
                    )
                    for (contentUriPrefix in contentUriPrefixesToTry) {
                        try {
                            // id 可能为字符串，选择文件后id可能类似为："msf:254"，导致转Long异常
                            val contentUri =
                                ContentUris.withAppendedId(Uri.parse(contentUriPrefix), id!!.toLong())
                            val path = getDataColumn(context, contentUri, null, null)
                            if (path != null && path != "") {
                                return path
                            }
                        } catch (e: Exception) {
                        }
                    }
                    // 路径可能用ContentResolver无法访问, 将文件复制到可访问的缓存中
                    val cacheDir = context.externalCacheDir
                    val file = generateFileName(getFileName(context, fileUri), cacheDir)
                    var toFilePath: String? = null
                    if (file != null) {
                        toFilePath = file.absolutePath
                        copyFileFromUri(context, fileUri, toFilePath)
                    }
                    return toFilePath

                    /*String id = DocumentsContract.getDocumentId(fileUri);
                    Uri contentUri = ContentUris.withAppendedId(Uri.parse("content://downloads/public_downloads"), Long.parseLong(id));
                    return getDataColumn(context, contentUri, null, null);*/
                } else if (isMediaDocument(fileUri)) {
                    val docId = DocumentsContract.getDocumentId(fileUri)
                    val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }
                        .toTypedArray()
                    val type = split[0]
                    var contentUri: Uri? = null
                    if ("image" == type) {
                        contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                    } else if ("video" == type) {
                        contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                    } else if ("audio" == type) {
                        contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                    } else{
                        contentUri = MediaStore.Files.getContentUri("external");
                    }
                    val selection = MediaStore.Images.Media._ID + "=?"
                    val selectionArgs = arrayOf(split[1])
                    return getDataColumn(context, contentUri, selection, selectionArgs)
                }
            } // MediaStore (and general)
            else if ("content".equals(fileUri.scheme, ignoreCase = true)) {
                // Return the remote address
                return if (isGooglePhotosUri(fileUri)) {
                    fileUri.lastPathSegment
                } else getDataColumn(context, fileUri, null, null)
            } else if ("file".equals(fileUri.scheme, ignoreCase = true)) {
                return fileUri.path
            }
            return null
        }

        fun copyFileFromUri(context: Context, uri: Uri?, destinationPath: String?): String? {
            var `is`: InputStream? = null
            var bos: BufferedOutputStream? = null
            try {
                val file = File(destinationPath)
                if (!file.exists()) {
                    file.createNewFile()
                }
                `is` = context.contentResolver.openInputStream(uri!!)
                bos = BufferedOutputStream(FileOutputStream(destinationPath, false))
                val buf = ByteArray(1024)
                var len: Int
                while (`is`!!.read(buf).also { len = it } > 0) {
                    bos.write(buf, 0, len)
                }
            } catch (e: IOException) {
                e.printStackTrace()
            } finally {
                try {
                    `is`?.close()
                    bos?.close()
                } catch (e: IOException) {
                    e.printStackTrace()
                }
            }
            return destinationPath
        }

        /**
         * android7.0以上处理方法
         */
        fun getPath(context: Context, uri: Uri?): String? {
            try {
                val returnCursor = context.contentResolver.query(uri!!, null, null, null, null)
                val nameIndex = returnCursor!!.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                returnCursor.moveToFirst()
                val name = returnCursor.getString(nameIndex)
                val file = File(context.filesDir.toString() + "/Customize", name)
                val inputStream = context.contentResolver.openInputStream(uri)
                val outputStream = FileOutputStream(file)
                var read = 0
                val maxBufferSize = 1 * 1024 * 1024
                val bytesAvailable = inputStream!!.available()
                val bufferSize = Math.min(bytesAvailable, maxBufferSize)
                val buffers = ByteArray(bufferSize)
                while (inputStream.read(buffers).also { read = it } != -1) {
                    outputStream.write(buffers, 0, read)
                }
                returnCursor.close()
                inputStream.close()
                outputStream.close()
                return file.path
            } catch (e: Exception) {
                e.printStackTrace()
            }
            return null
        }

        private fun getDataColumn(
            context: Context,
            uri: Uri?,
            selection: String?,
            selectionArgs: Array<String>?
        ): String? {
            var path: String? = null
            val projection = arrayOf(MediaStore.Images.Media.DATA)
            var cursor: Cursor? = null
            try {
                cursor =
                    context.contentResolver.query(uri!!, projection, selection, selectionArgs, null)
                if (cursor != null && cursor.moveToFirst()) {
                    val columnIndex = cursor.getColumnIndexOrThrow(projection[0])
                    path = cursor.getString(columnIndex)
                }
            } catch (e: Exception) {
                cursor?.close()
            }
            return path
        }

        /**
         * @param uri The Uri to check.
         * @return URI权限是否为ExternalStorageProvider
         * Whether the Uri authority is ExternalStorageProvider.
         */
        fun isExternalStorageDocument(uri: Uri): Boolean {
            return "com.android.externalstorage.documents" == uri.authority
        }

        /**
         * @param uri The Uri to check.
         * @return URI权限是否为google图片
         * Whether the Uri authority is Google Photos.
         */
        fun isGooglePhotosUri(uri: Uri): Boolean {
            return "com.google.android.apps.photos.content" == uri.authority
        }

        /**
         * @param uri The Uri to check.
         * @return URI权限是否为DownloadsProvider.
         * Whether the Uri authority is DownloadsProvider.
         */
        fun isDownloadsDocument(uri: Uri): Boolean {
            return "com.android.providers.downloads.documents" == uri.authority
        }

        /**
         * @param uri The Uri to check.
         * @return URI权限是否为MediaProvider.
         * Whether the Uri authority is MediaProvider.
         */
        fun isMediaDocument(uri: Uri): Boolean {
            return "com.android.providers.media.documents" == uri.authority
        }

        fun generateFileName(name: String?, directory: File?): File? {
            var name = name ?: return null
            var file = File(directory, name)
            if (file.exists()) {
                var fileName = name
                var extension = ""
                val dotIndex = name.lastIndexOf('.')
                if (dotIndex > 0) {
                    fileName = name.substring(0, dotIndex)
                    extension = name.substring(dotIndex)
                }
                var index = 0
                while (file.exists()) {
                    index++
                    name = "$fileName($index)$extension"
                    file = File(directory, name)
                }
            }
            //        try {
//            if (!file.createNewFile()) {
//                return null;
//            }
//        } catch (IOException e) {
//            return null;
//        }
            return file
        }

        fun getFileName(context: Context, uri: Uri): String? {
            val mimeType = context.contentResolver.getType(uri)
            var filename: String? = null
            if (mimeType == null) {
                val path = getPath(context, uri)
                filename = if (path == null) {
                    getName(uri.toString())
                } else {
                    val file = File(path)
                    file.name
                }
            } else {
                val returnCursor = context.contentResolver.query(
                    uri, null,
                    null, null, null
                )
                if (returnCursor != null) {
                    val nameIndex = returnCursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                    returnCursor.moveToFirst()
                    filename = returnCursor.getString(nameIndex)
                    returnCursor.close()
                }
            }
            //        String[] filename_s = filename.split("\\.", 2);
//        return filename_s[0];
            return filename
        }

        private fun getName(filename: String?): String? {
            if (filename == null) {
                return null
            }
            val index = filename.lastIndexOf('/')
            return filename.substring(index + 1)
        }


        /**
         * 判断目录是否存在，不存在则判断是否创建成功
         *
         * @param dirPath 文件路径
         * @return `true`: 存在或创建成功<br></br>`false`: 不存在或创建失败
         */
        fun createOrExistsDir(dirPath: String?): Boolean {
            return createOrExistsDir(getFileByPath(dirPath))
        }

        /**
         * 判断目录是否存在，不存在则判断是否创建成功
         *
         * @param file 文件
         * @return `true`: 存在或创建成功<br></br>`false`: 不存在或创建失败
         */
        fun createOrExistsDir(file: File?): Boolean {
            // 如果存在，是目录则返回true，是文件则返回false，不存在则返回是否创建成功
            return file != null && if (file.exists()) file.isDirectory else file.mkdirs()
        }

        /**
         * 根据文件路径获取文件
         *
         * @param filePath 文件路径
         * @return 文件
         */
        fun getFileByPath(filePath: String?): File? {
            return if ("" == filePath || filePath == null) {
                null
            } else {
                File(filePath)
            }
        }

        fun saveStroke(filePath: String, allPoints: MutableList<MutableList<DataPoint>>) {
            try {
                val file = File(filePath)
                if (!file.exists()) {
                    file.parentFile.mkdirs()
                    file.createNewFile()
                }
                val bufferedWriter = BufferedWriter(FileWriter(filePath))
                val array = JSONArray()
                for (touchPointList in allPoints) {
                    if (touchPointList == null || touchPointList.size == 0) {
                        continue
                    }
                    for (i in touchPointList.indices) {
                        val action = if (i == 0) 0 else if (i == touchPointList.size - 1) 1 else 2
                        val dataPoint = touchPointList[i]
                        // 不保存历史点
                        if (dataPoint.isHistory == 1) {
                            continue
                        }
                        val `object` = JSONObject()
                        `object`.put("action", action)
                        `object`.put("x", dataPoint.getX().toDouble())
                        `object`.put("y", dataPoint.getY().toDouble())
                        `object`.put("pressure", dataPoint.getPressure().toDouble())
                        `object`.put("eventTime", dataPoint.getEventTime())
                        `object`.put("axisTilt", dataPoint.getAxis_tilt().toDouble())
                        `object`.put("axisOrientation", dataPoint.getAxis_orientation().toDouble())
                        `object`.put("axisOrientation", dataPoint.getAxis_orientation().toDouble())
                        `object`.put("axisSize", 0)
                        array.put(`object`)
                    }
                }
                val records = JSONObject()
                records.put("pointRecords", array)
                bufferedWriter.write(records.toString())
                bufferedWriter.flush()
                bufferedWriter.close()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        }

        fun saveBitmapToFile(bitmap: Bitmap, file: File) {
            var fileOutputStream: FileOutputStream? = null
            try {
                if (file.parentFile != null && !file.parentFile.exists()) {
                    file.parentFile.mkdirs()
                }
                fileOutputStream = FileOutputStream(file)
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream)
                fileOutputStream.flush()
            } catch (e: IOException) {
                e.printStackTrace()
            } finally {
                if (fileOutputStream != null) {
                    try {
                        fileOutputStream.close()
                        bitmap.recycle()
                    } catch (e: IOException) {
                        e.printStackTrace()
                    }
                }
            }
        }

        fun saveBitmapToFile(bitmap: Bitmap, file: File, recycle: Boolean) {
            var fileOutputStream: FileOutputStream? = null
            try {
                if (file.parentFile != null && !file.parentFile.exists()) {
                    file.parentFile.mkdirs()
                }
                fileOutputStream = FileOutputStream(file)
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, fileOutputStream)
                fileOutputStream.flush()
            } catch (e: IOException) {
                e.printStackTrace()
            } finally {
                if (fileOutputStream != null) {
                    try {
                        fileOutputStream.close()
                        if (recycle) {
                            bitmap.recycle()
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }

        /**
         * 复制文件从源文件路径到目标文件路径。
         *
         * @param sourcePath 源文件的完整路径
         * @param destPath 目标文件的完整路径
         */
        fun copyFile(sourcePath: String, destPath: String) {
            val sourceFile = File(sourcePath)
            val destFile = File(destPath)

            // 创建目标目录
            val parentDir = destFile.parentFile
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs()
            }

            try {
                FileInputStream(sourceFile).use { inputStream ->
                    FileOutputStream(destFile).use { outputStream ->
                        copyStream(inputStream, outputStream)
                    }
                }
            } catch (e: IOException) {
                Log.e("FileUtil", "Failed to copy file", e)
            }
        }

        private fun copyStream(inputStream: InputStream, outputStream: OutputStream) {
            val bufferSize = 1024
            val buffer = ByteArray(bufferSize)
            var length: Int

            try {
                while (inputStream.read(buffer).also { length = it } > 0) {
                    outputStream.write(buffer, 0, length)
                }
            } catch (e: IOException) {
                throw RuntimeException("Error copying stream", e)
            } finally {
                try {
                    inputStream.close()
                } catch (e: IOException) {
                    Log.e("FileUtil", "Error closing input stream", e)
                }
                try {
                    outputStream.close()
                } catch (e: IOException) {
                    Log.e("FileUtil", "Error closing output stream", e)
                }
            }
        }

    }

}