package com.sunia.viewlib

import android.app.Application
import android.content.Context
import android.content.res.Configuration.ORIENTATION_PORTRAIT
import android.graphics.Color
import android.graphics.Rect
import android.util.DisplayMetrics
import android.view.WindowManager
import com.sunia.penengine.sdk.operate.touch.TouchEffectType
import com.sunia.singlepage.sdk.InkSDK
import com.sunia.singlepage.sdk.InkSelectEditFunc
import com.sunia.viewlib.utils.FilePathConstant
import com.sunia.viewlib.utils.PasteHelper
import kotlin.math.min

/**
 * <AUTHOR> zeng
 * @date 2024/4/16
 * Description:
 */
class ViewLib {

    companion object {

        var TEST_MODE = false
        var SHAPE_PEN_SINGLE = true
        var SHAPE_PEN_MULTI = true
        var ANNOTATION_LAYERED = false
        var ENABLE_CIRCLE_MARKER = false
        var ENABLE_CIRCLE_HIGHLIGHT = false
        var ENABLE_DARKEN_MARKER = true
        var ENABLE_DARKEN_HIGHLIGHT = true
        var ERASE_LINE_MARKER = false
        var ERASE_LINE_HIGHLIGHTER = false
        var ENABLE_BEAUTY_RECOGNIZE = false
        var ENABLE_GRAPHIC_EDITING = true
        var ENABLE_GESTURE_ERASER = true
        var ENABLE_RECO_ANIMATION = true

        var PASTE_MODE = PasteHelper.PASTE_MODE_OLD

        var ENABLE_RECOGNIZE = true

        var connectType: Int = -1

//        var useSurfaceView = true
        var screenMode: Int = 2
        //TODO 多页的上屏模式：0：canvas&SurfaceView置底 ； 1：canvas&SurfaceView置顶
        var screenModeMulti: Int = 1

        var scaleCenterType: Int = InkSelectEditFunc.SCALE_CENTER_TYPE_DEFAULT

        var instanace: Application? = null

        var smallestWidth: Int = 0

        //横屏
        var orientation: Int = ORIENTATION_PORTRAIT

        var colorList: MutableList<Int> = mutableListOf()

        var canvasSize: IntArray = IntArray(2)

        var canvasRect: Rect = Rect()

        var brushMap = HashMap<TouchEffectType, Float>()
        var BRUSH_ENABLE: Boolean = false

        var MAX_PAGE: Int = 100
        var resultColor = Color.parseColor("#FF8514")
        var BITMAP_MAX_MEMORY_SIZE = 600// MB
        var TEXT_MAX_COUNT = 10

        var BEATUTY_CORRENCTRATE = 0.3f
        var BEATUTY_LENGTH = 300
        var MATH_CORRECTRATE = 0.98f
        var BEAUTIFY_PEN_WITH_FACTOR = 0.5f
        var BEAUTIFY_PENCIL_PEN_WITH_FACTOR = 1.5f

        var enable_color_wheel = false
        // 是否开启画布混合 如果开启混合 不能设置背景颜色和空白颜色
        var enable_canvas_blend = false

        fun init(application: Application ) {
            instanace = application
            FilePathConstant.setPath()
            brushMap[TouchEffectType.PRESSURE] = 100f
            brushMap[TouchEffectType.SPEED] = 100f
            brushMap[TouchEffectType.TILT] = 100f
            brushMap[TouchEffectType.TIP_DOWN] = 100f
            brushMap[TouchEffectType.TIP_UP] = 100f
            val sharedPreferences =
                application.getSharedPreferences("share_data", Context.MODE_PRIVATE)
            BRUSH_ENABLE = sharedPreferences.getBoolean("brush_enable", false)
            SHAPE_PEN_SINGLE = sharedPreferences.getBoolean("shape_pen_single", false)
            SHAPE_PEN_MULTI = sharedPreferences.getBoolean("shape_pen_multi", false)
            ENABLE_RECOGNIZE = sharedPreferences.getBoolean("enable_recognize_tool", true)
            ENABLE_GRAPHIC_EDITING = sharedPreferences.getBoolean("graphic_editing_enable", true)
            ENABLE_RECO_ANIMATION = sharedPreferences.getBoolean("enable_reco_animation", true)

            screenMode = sharedPreferences.getInt("screen_mode", screenMode)
            screenModeMulti = sharedPreferences.getInt("screen_mode_multi", screenModeMulti)

            InkSDK.surfaceFullScreenRefresh = sharedPreferences.getBoolean("surface_full_screen_refresh", InkSDK.surfaceFullScreenRefresh)
        }

        /**
         * 获取当前sw限定值
         */
        fun getSmallestWidth(context: Context): Int {
            val displayMetrics = DisplayMetrics()
            val windowManager =
                context.applicationContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            windowManager.defaultDisplay.getMetrics(displayMetrics)
            val widthDpi = (displayMetrics.widthPixels / displayMetrics.density).toInt()
            val heightDpi = (displayMetrics.heightPixels / displayMetrics.density).toInt()
            return min(widthDpi, heightDpi)
        }
    }
}