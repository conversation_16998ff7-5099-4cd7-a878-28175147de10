package com.sunia.viewlib.view;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.Log;
import android.view.View;

import androidx.annotation.Nullable;

import java.io.IOException;
import java.io.InputStream;

public class InkBackgroundView extends View {

    public InkBackgroundView(Context context) {
        this(context, null);
    }

    public InkBackgroundView(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public InkBackgroundView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private Paint paint;
    private Bitmap bitmap;
    private void init(Context context) {
        paint = new Paint();
        paint.setStyle(Paint.Style.FILL_AND_STROKE);
        paint.setStrokeWidth(5);
        paint.setColor(Color.RED);

        bitmap = getImageFromAssetsFile(context, "1.jpeg");
    }

    private final Rect srcRect = new Rect();
    private final RectF dstRectF = new RectF();
    @Override
    protected void onDraw(Canvas canvas) {
        int width = getWidth();
        int height = getHeight();
        if (width == 0 || height == 0) {
            return;
        }
        int bitmapWidth = bitmap.getWidth();
        int bitmapHeight = bitmap.getHeight();
        if (bitmapWidth == 0 || bitmapHeight == 0) {
            return;
        }
        if (bitmap != null && !bitmap.isRecycled()) {
            canvas.save();
            canvas.translate(offsetX, offsetY);
            canvas.scale(scale, scale);
            dstRectF.left = 0;
            dstRectF.top = 0;
            dstRectF.right = dstRectF.left + width;
            dstRectF.bottom = dstRectF.top + width * bitmap.getHeight() * 1f / bitmap.getWidth();
            srcRect.set(0, 0, bitmap.getWidth(), bitmap.getHeight());
            canvas.drawBitmap(bitmap, srcRect, dstRectF, null);
            canvas.restore();
        }
    }

    private float scale = 1f;
    private float offsetX, offsetY;
    public void doScale(float scale, float offsetX, float offsetY) {
        if (this.scale == scale && this.offsetX == offsetX && this.offsetY == offsetY) {
            return;
        }
        this.scale = scale;
        this.offsetX = offsetX;
        this.offsetY = offsetY;
    }

    public void redraw() {
        invalidate();
    }

    public void postRedraw(){
        postInvalidate();
    }

    public Bitmap getImageFromAssetsFile(Context context, String fileName) {
        AssetManager assetManager = context.getAssets();
        InputStream istr = null;
        try {
            istr = assetManager.open(fileName);
            return BitmapFactory.decodeStream(istr);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        } finally {
            if (istr != null) {
                try {
                    istr.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
