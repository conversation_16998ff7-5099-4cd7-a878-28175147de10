package com.sunia.viewlib.view;

import android.graphics.RectF;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import androidx.annotation.NonNull;

/**
 * <AUTHOR>
 */
public class UpperScreenThreadHandler {
   private static final String TAG = "UpperScreenThreadHandle";
   public static final int MSG_UPPER_SCREEN = 0x110;
   private HandlerThread handlerThread;
   private Handler handler;
   private RectF lastRect = new RectF();

   public void init() {
      if (handlerThread == null) {
         handlerThread = new HandlerThread("Upper_Screen_Thread_" + hashCode());
         handlerThread.start();
      }
      if (handler == null) {
         handler = new Handler(handlerThread.getLooper(),new HandlerCallback());
      }
   }

   public void postRendToScreenMsg(IRendToRunnable runnable){
      if (handler == null) {
         return;
      }
      if (handler.hasMessages(MSG_UPPER_SCREEN)) {
         RectF rectF = runnable.getRendToRect();
         if (handler!= null) {
            handler.removeMessages(MSG_UPPER_SCREEN);
         }
         if (!lastRect.isEmpty() && !rectF.isEmpty()) {
            runnable.getRendToRect().union(lastRect);
         }
      }
      lastRect.set(runnable.getRendToRect());

      Message message = new Message();
      message.obj = runnable;
      message.what = MSG_UPPER_SCREEN;
      if (handler!= null) {
         handler.sendMessage(message);
      }
   }

   public void postRunnable(Runnable runnable){
      if (handler == null) {
         return;
      }
      Message message = new Message();
      message.obj = runnable;
      message.what = MSG_UPPER_SCREEN;
      if (handler!= null) {
         handler.sendMessage(message);
      }
   }

   public void quit() {
      if (handler != null) {
         handler.removeCallbacksAndMessages(null);
      }
      if (handlerThread != null) {
         handlerThread.quitSafely();
      }
      handler = null;
      handlerThread = null;
   }

   private static class HandlerCallback implements Handler.Callback {
      @Override
      public boolean handleMessage(@NonNull Message msg) {
         if (msg.what == MSG_UPPER_SCREEN){
            ((Runnable)msg.obj).run();
         }
         return false;
      }
   }

   public interface IRendToRunnable extends Runnable {
      RectF getRendToRect();
   }
}
