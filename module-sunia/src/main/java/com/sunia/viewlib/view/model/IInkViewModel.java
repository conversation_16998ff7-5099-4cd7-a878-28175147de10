package com.sunia.viewlib.view.model;

import android.graphics.PointF;
import android.graphics.RectF;

import com.sunia.penengine.sdk.operate.canvas.ScaleInfo;

public interface IInkViewModel {
    /**
     * 添加绘制控件
     */
    void addDrawView();

    /**
     * 引擎初始化完成
     */
    void onInitFinish();

    /**
     * 控件尺寸变化
     *
     * @param width 宽
     * @param height 高
     */
    void onSizeChanged(int width, int height);

    /**
     * 上屏
     *
     * @param rectF 区域
     * @param state 状态
     */
    void rendToScreen(RectF rectF, int state);

    /**
     * 设置监听
     *
     * @param inkViewListener 监听
     */
    void setInkViewListener(IInkViewListener inkViewListener);

    void onScaleChanged(float scale, float offsetX, float offsetY, PointF center);

    void onCanvasStateChanged(int state);

    /**
     * 释放资源
     */
    void release();

    interface IInkViewListener {
        void onInitAndSetVisibleSizeFinish();
        void onVisibleSizeChanged(int width, int height);
        boolean canVisibleSizeChanged(int width);
        void onViewRefresh(int width ,int height);
    }
}
