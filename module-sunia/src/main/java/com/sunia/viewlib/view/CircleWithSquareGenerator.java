package com.sunia.viewlib.view;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;

public class CircleWithSquareGenerator {

    // 模式枚举：仅区分白天和黑夜
    public enum Mode {
        DAY, NIGHT
    }

    /**
     * 生成带透明正方形背景的圆形Bitmap（根据模式自动切换样式）
     * @param squareSize 正方形尺寸（触摸区域大小）
     * @param circleDiameter 圆形直径
     * @param strokeWidth 描边宽度
     * @param mode 显示模式（日/夜）
     * @return 生成的Bitmap对象
     */
    public static Bitmap generateCircle(
            int squareSize,
            int circleDiameter,
            float strokeWidth,
            Mode mode) {

        // 创建带透明通道的正方形Bitmap
        Bitmap bitmap = Bitmap.createBitmap(squareSize, squareSize, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);

        // 描边画笔（固定黄色）
        Paint strokePaint = new Paint();
        strokePaint.setColor(Color.parseColor("#FF9E00"));
        strokePaint.setStyle(Paint.Style.STROKE);
        strokePaint.setStrokeWidth(strokeWidth);
        strokePaint.setAntiAlias(true);
        strokePaint.setStrokeCap(Paint.Cap.ROUND);
        strokePaint.setStrokeJoin(Paint.Join.ROUND);

        // 填充画笔（根据模式切换颜色）
        Paint fillPaint = new Paint();
        fillPaint.setColor(mode == Mode.DAY ? Color.WHITE : Color.BLACK);
        fillPaint.setStyle(Paint.Style.FILL);
        fillPaint.setAntiAlias(true);

        // 计算半径（考虑描边宽度）
        float radius = (circleDiameter - strokeWidth) / 2f;

        // 先画填充
        canvas.drawCircle(
                squareSize / 2f,
                squareSize / 2f,
                radius - strokeWidth / 2f,
                fillPaint
        );

        // 再画描边
        canvas.drawCircle(
                squareSize / 2f,
                squareSize / 2f,
                radius,
                strokePaint
        );

        return bitmap;
    }
}
