package com.sunia.viewlib.export;


public class DataPoint {
    public float x;
    public float y;
    public long eventTime;
    public int action;
    public float pressure;
    public float axis_tilt;
    public float axis_orientation;
    public int isHistory;
    public float axis_size;

    public float axis_touch_major;
    public float axis_touch_minor;
    public float axis_tool_major;
    public float axis_tool_minor;
    public float axis_vscroll;
    public float axis_hscroll;
    public float axis_z;
    public float axis_rx;
    public float axis_ry;
    public float axis_rz;
    public float axis_hat_x;
    public float axis_hat_y;
    public float axis_ltrigger;
    public float axis_rtrigger;
    public float axis_throttle;
    public float axis_rudder;
    public float axis_wheel;
    public float axis_gas;
    public float axis_brake;
    public float axis_distance;
    public float axis_scroll;
    public float axis_relative_x;
    public float axis_relative_y;
    public float axis_generic_1;
    public float axis_generic_2;
    public float axis_generic_3;
    public float axis_generic_4;
    public float axis_generic_5;
    public float axis_generic_6;
    public float axis_generic_7;
    public float axis_generic_8;
    public float axis_generic_9;
    public float axis_generic_10;
    public float axis_generic_11;
    public float axis_generic_12;
    public float axis_generic_13;
    public float axis_generic_14;
    public float axis_generic_15;
    public float axis_generic_16;

    public DataPoint() {
    }

    public DataPoint(float x, float y, float pressure, long time) {
        this.x = x;
        this.y = y;
        this.eventTime = time;
        this.pressure = pressure;
    }

    public float getX() {
        return x;
    }

    public void setX(float x) {
        this.x = x;
    }

    public float getY() {
        return y;
    }

    public void setY(float y) {
        this.y = y;
    }

    public long getEventTime() {
        return eventTime;
    }

    public void setEventTime(long eventTime) {
        this.eventTime = eventTime;
    }

    public int getAction() {
        return action;
    }

    public void setAction(int action) {
        this.action = action;
    }

    public float getPressure() {
        return pressure;
    }

    public void setPressure(float pressure) {
        this.pressure = pressure;
    }

    public float getAxis_tilt() {
        return axis_tilt;
    }

    public void setAxis_tilt(float axis_tilt) {
        this.axis_tilt = axis_tilt;
    }

    public float getAxis_orientation() {
        return axis_orientation;
    }

    public void setAxis_orientation(float axis_orientation) {
        this.axis_orientation = axis_orientation;
    }

    public int getIsHistory() {
        return isHistory;
    }

    public void setIsHistory(int isHistory) {
        this.isHistory = isHistory;
    }

    public float getAxis_size() {
        return axis_size;
    }

    public void setAxis_size(float axis_size) {
        this.axis_size = axis_size;
    }

    public float getAxis_touch_major() {
        return axis_touch_major;
    }

    public void setAxis_touch_major(float axis_touch_major) {
        this.axis_touch_major = axis_touch_major;
    }

    public float getAxis_touch_minor() {
        return axis_touch_minor;
    }

    public void setAxis_touch_minor(float axis_touch_minor) {
        this.axis_touch_minor = axis_touch_minor;
    }

    public float getAxis_tool_major() {
        return axis_tool_major;
    }

    public void setAxis_tool_major(float axis_tool_major) {
        this.axis_tool_major = axis_tool_major;
    }

    public float getAxis_tool_minor() {
        return axis_tool_minor;
    }

    public void setAxis_tool_minor(float axis_tool_minor) {
        this.axis_tool_minor = axis_tool_minor;
    }

    public float getAxis_vscroll() {
        return axis_vscroll;
    }

    public void setAxis_vscroll(float axis_vscroll) {
        this.axis_vscroll = axis_vscroll;
    }

    public float getAxis_hscroll() {
        return axis_hscroll;
    }

    public void setAxis_hscroll(float axis_hscroll) {
        this.axis_hscroll = axis_hscroll;
    }

    public float getAxis_z() {
        return axis_z;
    }

    public void setAxis_z(float axis_z) {
        this.axis_z = axis_z;
    }

    public float getAxis_rx() {
        return axis_rx;
    }

    public void setAxis_rx(float axis_rx) {
        this.axis_rx = axis_rx;
    }

    public float getAxis_ry() {
        return axis_ry;
    }

    public void setAxis_ry(float axis_ry) {
        this.axis_ry = axis_ry;
    }

    public float getAxis_rz() {
        return axis_rz;
    }

    public void setAxis_rz(float axis_rz) {
        this.axis_rz = axis_rz;
    }

    public float getAxis_hat_x() {
        return axis_hat_x;
    }

    public void setAxis_hat_x(float axis_hat_x) {
        this.axis_hat_x = axis_hat_x;
    }

    public float getAxis_hat_y() {
        return axis_hat_y;
    }

    public void setAxis_hat_y(float axis_hat_y) {
        this.axis_hat_y = axis_hat_y;
    }

    public float getAxis_ltrigger() {
        return axis_ltrigger;
    }

    public void setAxis_ltrigger(float axis_ltrigger) {
        this.axis_ltrigger = axis_ltrigger;
    }

    public float getAxis_rtrigger() {
        return axis_rtrigger;
    }

    public void setAxis_rtrigger(float axis_rtrigger) {
        this.axis_rtrigger = axis_rtrigger;
    }

    public float getAxis_throttle() {
        return axis_throttle;
    }

    public void setAxis_throttle(float axis_throttle) {
        this.axis_throttle = axis_throttle;
    }

    public float getAxis_rudder() {
        return axis_rudder;
    }

    public void setAxis_rudder(float axis_rudder) {
        this.axis_rudder = axis_rudder;
    }

    public float getAxis_wheel() {
        return axis_wheel;
    }

    public void setAxis_wheel(float axis_wheel) {
        this.axis_wheel = axis_wheel;
    }

    public float getAxis_gas() {
        return axis_gas;
    }

    public void setAxis_gas(float axis_gas) {
        this.axis_gas = axis_gas;
    }

    public float getAxis_brake() {
        return axis_brake;
    }

    public void setAxis_brake(float axis_brake) {
        this.axis_brake = axis_brake;
    }

    public float getAxis_distance() {
        return axis_distance;
    }

    public void setAxis_distance(float axis_distance) {
        this.axis_distance = axis_distance;
    }

    public float getAxis_scroll() {
        return axis_scroll;
    }

    public void setAxis_scroll(float axis_scroll) {
        this.axis_scroll = axis_scroll;
    }

    public float getAxis_relative_x() {
        return axis_relative_x;
    }

    public void setAxis_relative_x(float axis_relative_x) {
        this.axis_relative_x = axis_relative_x;
    }

    public float getAxis_relative_y() {
        return axis_relative_y;
    }

    public void setAxis_relative_y(float axis_relative_y) {
        this.axis_relative_y = axis_relative_y;
    }

    public float getAxis_generic_1() {
        return axis_generic_1;
    }

    public void setAxis_generic_1(float axis_generic_1) {
        this.axis_generic_1 = axis_generic_1;
    }

    public float getAxis_generic_2() {
        return axis_generic_2;
    }

    public void setAxis_generic_2(float axis_generic_2) {
        this.axis_generic_2 = axis_generic_2;
    }

    public float getAxis_generic_3() {
        return axis_generic_3;
    }

    public void setAxis_generic_3(float axis_generic_3) {
        this.axis_generic_3 = axis_generic_3;
    }

    public float getAxis_generic_4() {
        return axis_generic_4;
    }

    public void setAxis_generic_4(float axis_generic_4) {
        this.axis_generic_4 = axis_generic_4;
    }

    public float getAxis_generic_5() {
        return axis_generic_5;
    }

    public void setAxis_generic_5(float axis_generic_5) {
        this.axis_generic_5 = axis_generic_5;
    }

    public float getAxis_generic_6() {
        return axis_generic_6;
    }

    public void setAxis_generic_6(float axis_generic_6) {
        this.axis_generic_6 = axis_generic_6;
    }

    public float getAxis_generic_7() {
        return axis_generic_7;
    }

    public void setAxis_generic_7(float axis_generic_7) {
        this.axis_generic_7 = axis_generic_7;
    }

    public float getAxis_generic_8() {
        return axis_generic_8;
    }

    public void setAxis_generic_8(float axis_generic_8) {
        this.axis_generic_8 = axis_generic_8;
    }

    public float getAxis_generic_9() {
        return axis_generic_9;
    }

    public void setAxis_generic_9(float axis_generic_9) {
        this.axis_generic_9 = axis_generic_9;
    }

    public float getAxis_generic_10() {
        return axis_generic_10;
    }

    public void setAxis_generic_10(float axis_generic_10) {
        this.axis_generic_10 = axis_generic_10;
    }

    public float getAxis_generic_11() {
        return axis_generic_11;
    }

    public void setAxis_generic_11(float axis_generic_11) {
        this.axis_generic_11 = axis_generic_11;
    }

    public float getAxis_generic_12() {
        return axis_generic_12;
    }

    public void setAxis_generic_12(float axis_generic_12) {
        this.axis_generic_12 = axis_generic_12;
    }

    public float getAxis_generic_13() {
        return axis_generic_13;
    }

    public void setAxis_generic_13(float axis_generic_13) {
        this.axis_generic_13 = axis_generic_13;
    }

    public float getAxis_generic_14() {
        return axis_generic_14;
    }

    public void setAxis_generic_14(float axis_generic_14) {
        this.axis_generic_14 = axis_generic_14;
    }

    public float getAxis_generic_15() {
        return axis_generic_15;
    }

    public void setAxis_generic_15(float axis_generic_15) {
        this.axis_generic_15 = axis_generic_15;
    }

    public float getAxis_generic_16() {
        return axis_generic_16;
    }

    public void setAxis_generic_16(float axis_generic_16) {
        this.axis_generic_16 = axis_generic_16;
    }
}
