package com.tcl.ai.note.home.components.notelist.title

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.components.HomeTitleTextStyle
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.state.HomeTitleMode
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.widget.HoverProofIconButton

@Composable
internal fun TopTitleEdit(
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    val selectCount = homeNoteUiState.selectedCount
    val isSelectedAll = homeNoteUiState.isSelectedAll
    Logger.d("HomeTabletTopTitle", "EditStateTitle: $selectCount $isSelectedAll")
    // 取消已选中的回到显示模式
    Row(verticalAlignment = Alignment.CenterVertically) {
        HoverProofIconButton(
            onClick = { onAction(HomeNoteListAction.OnChangeTitleMode(HomeTitleMode.Normal)) },
            modifier = Modifier.size(32.dp)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cancel_selected),
                contentDescription = stringResource(id = R.string.cancel),
                tint = colorResource(R.color.transparent_icon)
            )
        }
        Spacer(modifier = Modifier.width(if (isTablet) 16.dp else 4.dp))
        val text =if (selectCount == 0) stringResource(id = R.string.select_notes) else stringResource(id = R.string.selected_count, selectCount)
        Text(
            text = text,
            style = HomeTitleTextStyle,
            color = colorResource(R.color.home_title_color),
            modifier = Modifier.padding(end = 8.dp)
        )
    }

    HoverProofIconButton(
        onClick = {
            onAction(HomeNoteListAction.OnSelectAll(!isSelectedAll))
        },
        modifier = Modifier.size(32.dp)
    ) {
        // 全选
        Image(
            painter = painterResource(
                isSelectedAll.judge(
                    R.drawable.ic_selected_all_checked,
                    R.drawable.ic_selected_all
                )
            ),
            contentDescription = stringResource(id = R.string.item_top_check_all),
        )
    }
}