package com.tcl.ai.note.home.components

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.LocalIndication
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.indication
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.utils.HoverMutableInteractionSource
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.delay

val HomeTitleTextStyle = TextStyle(
    fontSize = 20.sp,
    fontWeight = FontWeight(500),
)
val HomeNoteTitleTextStyle = TextStyle(
    fontSize = 14.sp,
    fontWeight = FontWeight(500),
)

//W400 就是Normal
val HomeCategoryAddCategoryTextStyle = TextStyle(
    fontSize = 16.sp,
    fontWeight = FontWeight(500),
)
val BottomOperateViewHeight = 64.dp



/**
 * Modifier 扩展函数：有条件地添加底部导航栏内边距
 *
 * 只有在存在实际的导航栏按钮时才添加 padding，
 * 手势导航的横线（通常 < 24dp）不需要额外的 padding
 */
fun Modifier.conditionalNavigationBarsPadding(hasNavigationBar: Boolean) =
    if (hasNavigationBar) {
        this.navigationBarsPadding()
    } else {
        this
    }



/**
 * 添加右边描边的 Modifier 扩展函数
 */
fun Modifier.rightBorder(
    width: Dp = 1.dp,
    color: Color = Color(0x1A000000) // 26% 透明度的黑色，对应 #1a000000
): Modifier = this.then(
    Modifier.drawBehind {
        val strokeWidth = width.toPx()
        val x = size.width - strokeWidth / 2

        drawLine(
            color = color,
            start = Offset(x, 0f),
            end = Offset(x, size.height),
            strokeWidth = strokeWidth
        )
    }
)
/**
 * 重组监控器
 * 监控组件的重组频率，帮助发现性能问题
 */
@Composable
fun RecompositionMonitor(
    componentName: String,
    threshold: Int = 10, // 每秒重组次数阈值
    enabled: Boolean = true
) {
    if (!enabled) return

    var recompositionCount by remember { mutableIntStateOf(0) }
    var lastResetTime by remember { mutableLongStateOf(System.currentTimeMillis()) }

    // 每次重组时增加计数
    recompositionCount++

    LaunchedEffect(Unit) {
        while (true) {
            delay(1000) // 每秒检查一次
            val currentTime = System.currentTimeMillis()
            val elapsedSeconds = (currentTime - lastResetTime) / 1000

            if (elapsedSeconds >= 1) {
                val recompositionsPerSecond = recompositionCount / elapsedSeconds

                if (recompositionsPerSecond > threshold) {
                    Logger.w(
                        "PerformanceAnalyzer",
                        "⚠️ $componentName: High recomposition rate detected! " +
                                "$recompositionsPerSecond recompositions/second (threshold: $threshold)"
                    )
                } else {
                    Logger.d(
                        "PerformanceAnalyzer",
                        "✅ $componentName: Normal recomposition rate: $recompositionsPerSecond/sec"
                    )
                }

                // 重置计数器
                recompositionCount = 0
                lastResetTime = currentTime
            }
        }
    }
}