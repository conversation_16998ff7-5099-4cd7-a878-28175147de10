package com.tcl.ai.note.dashboard.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.isTraversalGroup
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.semantics.stateDescription
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.utils.appendSemanticsButton
import com.tcl.ai.note.utils.globalDialogWidth
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton
import com.tct.theme.core.designsystem.component.TclWarningButton
import com.tct.theme.core.designsystem.theme.TclTheme

/**
 * 数据删除对话框组件
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onDelete 当用户选择删除时执行的操作
 */
@Composable
fun DeleteNoteDialog(
    text: String,
    onDismiss: () -> Unit,
    onDelete: () -> Unit
) {
    val context = LocalContext.current
    val contentDescription = context.getString(
        R.string.dialog_semantics_description,
        context.getString(R.string.dialog_type_semantics),                         // 对话框类型
        text,                                                                      // 主要内容
        context.getString(R.string.btn_cancel).appendSemanticsButton(),            // 取消按钮
        context.getString(R.string.menu_delete_recording).appendSemanticsButton()  // 删除按钮
    )

    TclTheme(dynamicColor = false) {
        TclDialog(
            show = true,
            dialogMaxWidth = globalDialogWidth(),
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
            title = {
                Text(
                    text = stringResource(id = R.string.areyou_sure_delete),
                    color = colorResource(id = R.color.dialog_title),
                    fontSize = 20.sp,
                    fontWeight = FontWeight.W500
                )
            },
            content = {
                Text(
                    modifier = Modifier.semantics {
                        this.contentDescription = contentDescription
                    },
                    text = text
                )
            },
            actions = {
                TclTextButton(
                    onClick = { onDismiss.invoke() },
                    contentColor = colorResource(id = R.color.home_title_color)
                ) {
                    Text(stringResource(id = R.string.btn_cancel))
                }
                TclWarningButton(onClick = { onDelete.invoke() }) {
                    Text(stringResource(id = R.string.menu_delete_recording))
                }
            },
        )
    }
}

@Preview
@Composable
private fun PreviewDeleteDataDialog() {
    DeleteNoteDialog(
        text = stringResource(id = R.string.dialog_category_delete_title),
        onDismiss = {},
        onDelete = {},
    )
}

@Preview
@Composable
private fun BottomDeleteCategoryDialogPreview() {
    BottomDeleteCategoryDialog(
        onDelete = {},
        onCancel = {},
        isDeleteNotesSelected = {},
        isShowDeleteNotes = true
    )
}

/**
 * 列表数据删除提示框
 * @param isDeleteNotes 是否显示删除分类里面的note数据
 * @param isDeleteNotesSelected 是否删除分类里面的note数据
 */
@SuppressLint("DesignSystem")
@Composable
internal fun BottomDeleteCategoryDialog(
    onDelete: () -> Unit,
    onCancel: () -> Unit,
    isDeleteNotesSelected: (Boolean) -> Unit,
    isShowDeleteNotes: Boolean
) {

    TclTheme(dynamicColor = false) {
        // 复选框图标是否被选中
        var isSelected by remember { mutableStateOf(false) }
        TclDialog (
            show = true,
            dialogMaxWidth = globalDialogWidth(),
            onDismissRequest = { onCancel() },
            properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
            title = {
                Text(
                    text = stringResource(R.string.dialog_category_delete_title),
                    color = colorResource(R.color.dialog_title),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Normal
                )
            },
            content = {
                if (isShowDeleteNotes) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (isSelected) {
                            Image(
                                modifier = Modifier
                                    .clickable
                                    {
                                        isSelected = !isSelected
                                        isDeleteNotesSelected(isSelected)
                                    },
                                painter = painterResource(id = R.drawable.ic_item_checked),
                                contentDescription = stringResource(R.string.checked_status)
                            )
                        } else {
                            Image(
                                modifier = Modifier
                                    .clickable
                                    {
                                        isSelected = !isSelected
                                        isDeleteNotesSelected(isSelected)
                                    },
                                painter = painterResource(id = R.drawable.ic_item_unchecked),
                                contentDescription = stringResource(R.string.unchecked_status)
                            )
                        }

                        Spacer(modifier = Modifier.width(8.dp))
                        val deleteContent = stringResource(R.string.dialog_category_delete_content)
                        Text(
                            text = deleteContent,
                            fontSize = 16.sp,
                            lineHeight = 20.sp,
                            fontWeight = FontWeight.Normal,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier
                                .padding(0.dp)
                                .weight(1f)
                                .align(Alignment.CenterVertically)
                                .semantics{
                                    contentDescription = deleteContent
                                },
                            color = colorResource(R.color.dialog_title)
                        )
                    }
                }
            },
            actions = {
                TclTextButton(onClick = { onCancel.invoke() }, contentColor = colorResource(id = R.color.home_title_color)) { Text(stringResource(id = R.string.btn_cancel)) }
                TclWarningButton(onClick = { onDelete.invoke() }) { Text(stringResource(id = R.string.menu_delete_recording)) }
            },
        )
    }
}


/**
 * 排序方式选择提示框
 */
@SuppressLint("DesignSystem")
@Composable
internal fun BottomSortOrderDialog(
    onSelected: (isCreateDate: Boolean) -> Unit,
    onCancel: () -> Unit,
    isCreateDate: Boolean
) {
    val context = LocalContext.current
    Dialog(
        onDismissRequest = onCancel,
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Box(
            modifier = Modifier
                .invisibleSemantics()
                .fillMaxSize()
                .padding(bottom = 80.dp, start = 16.dp, end = 16.dp)
                .clickable(onClick = onCancel),
            contentAlignment = Alignment.BottomCenter
        ) {
            Surface(
                color = colorResource(R.color.bg_dialog),
                shape = RoundedCornerShape(20.dp),
                modifier = Modifier
                    .invisibleSemantics()
                    .wrapContentWidth()
                    .sizeIn(minHeight = 76.dp, maxWidth = 440.dp)
                    .fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.padding(
                        start = 24.dp,
                        top = 24.dp,
                        end = 24.dp,
                        bottom = 8.dp
                    ),
                    horizontalAlignment = Alignment.Start,
                    verticalArrangement = Arrangement.Center
                ) {
                    Row(
                        modifier = Modifier
                            .semantics(mergeDescendants = true) {
                                isTraversalGroup = true
                                role = Role.RadioButton
                                if (isCreateDate) {
                                    stateDescription =
                                        context.getString(R.string.sort_by_create_date_selected)
                                }
                            }
                            .clickable {
                                onSelected(true)
                            }
                            .fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(
                                id = if (isCreateDate) R.drawable.ic_item_checked
                                else R.drawable.ic_item_unchecked
                            ),
                            contentDescription = if (isCreateDate) {
                                stringResource(R.string.checked_status)
                            } else {
                                stringResource(R.string.unchecked_status)
                            }
                        )

                        Spacer(modifier = Modifier.width(16.dp))
                        Text(
                            text = stringResource(R.string.create_date),
                            fontSize = 16.sp,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.padding(0.dp),
                            color = colorResource(R.color.text_title)
                        )
                    }
                    Spacer(modifier = Modifier.height(16.dp))

                    Row(
                        modifier = Modifier
                            .semantics(mergeDescendants = true) {
                                isTraversalGroup = true
                                role = Role.RadioButton
                                if (!isCreateDate) {
                                    stateDescription =
                                        context.getString(R.string.sort_by_modify_date_selected)
                                }
                            }
                            .clickable {
                                onSelected(false)
                            }
                            .fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(
                                id = if (!isCreateDate) R.drawable.ic_item_checked
                                else R.drawable.ic_item_unchecked
                            ),
                            contentDescription = if (!isCreateDate) {
                                stringResource(R.string.checked_status)
                            } else {
                                stringResource(R.string.unchecked_status)
                            }
                        )

                        Spacer(modifier = Modifier.width(16.dp))
                        Text(
                            text = stringResource(R.string.modify_date),
                            fontSize = 16.sp,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier.padding(0.dp),
                            color = colorResource(R.color.text_title)
                        )
                    }
                    Spacer(modifier = Modifier.height(16.dp))
                }
            }
        }
    }
}