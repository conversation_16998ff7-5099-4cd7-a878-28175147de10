package com.tcl.ai.note.dashboard.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel

import com.tcl.ai.note.base.R
import com.tcl.ai.note.dashboard.states.ListNoteCategoryState
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.CommonUtils

/**
 * 笔记列表Item
 * @param isActive 当前item是否被选中（主要用于横屏，选中的item添加一个透明背景区分没被选中的）
 * @param searchQuery 搜索框中的内容
 */
@OptIn(ExperimentalFoundationApi::class)
@SuppressLint("StateFlowValueCalledInComposition")
@Composable
internal fun NoteItemRow(
    dashboardModel: DashboardModel = hiltViewModel(),
    item: NoteListItem,
    isCreateTimeSort: Boolean,
    editMode: Boolean,
    isSelected: Boolean,
    isActive :Boolean,
    searchQuery: String,
    onLongClick: () -> Unit,
    onClick: () -> Unit){

    val listNoteCategoryState by dashboardModel.listNoteCategoryState.collectAsState()
    // 缓存上一次的 Success 数据以避免null场景下因重组造成的返回首页item图片闪烁问题 (可能存在更优方案，但没时间研究了)
    val lastValidList = remember { mutableStateOf<List<NoteCategory>>(emptyList()) }

    val listNoteCategory = remember(listNoteCategoryState) {
        val tmpCategoryState = listNoteCategoryState
        if (tmpCategoryState is ListNoteCategoryState.Success) {
            lastValidList.value = tmpCategoryState.items
            lastValidList.value
        } else {
            lastValidList.value
        }
    }

    val targetCategory = remember(listNoteCategory, item.categoryId) {
        listNoteCategory.find { it.categoryId == item.categoryId }
    }

    var title = item.title
    if(title.isEmpty()){
        if(isOnlyImage(item)){
            title = stringResource(R.string.image_title)
        }else if(isOnlyVoice(item)){
            title = stringResource(R.string.audio_title)
        }else if(isOnlyImageVoice(item)){
            title = stringResource(R.string.image_audio_title)
        }else if(!item.summary.isNullOrEmpty()){
            title = generateSmartSummary(item.summary!!)
        }
    }
    val preHeight =if(item.summary!!.isNotEmpty()) 120.dp else 100.dp

    val context = LocalContext.current


    Box(
        modifier = Modifier
            .invisibleSemantics()
            .padding(bottom = 8.dp)
            //.height(preHeight)
            .fillMaxWidth()
            .background(
                color = if (isActive) {
                    Color.LightGray.copy(alpha = 0.3f)
                } else {
                    TclTheme.colorScheme.tctHighlightBgColor
                },
                shape = RoundedCornerShape(8.dp)
            )

    ) {

        Row(modifier = Modifier
            .semantics {
                this.contentDescription = context.getString(R.string.list_item_is_a_note)
            }
            .combinedClickable(onClick = { onClick() }, onLongClick = { onLongClick() })
            .fillMaxWidth()
            .padding(vertical = 22.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                verticalArrangement = Arrangement.Center,
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(
                        start = 16.dp,
                        end = 15.dp,
                    )
            ){
                // 高亮处理
                val highlightedTitle = buildHighlightedText(title.replace("\n", ""), searchQuery)
                Text(
                    text = highlightedTitle,
                    fontSize = 18.sp,
                    lineHeight = 21.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    fontWeight = FontWeight.Medium,
                    color = colorResource(R.color.text_title)
                )
                if(item.summary!!.isNotEmpty()){
                    // 高亮处理
                    val highlightedSummary = buildHighlightedText(item.summary!!, searchQuery)
                    Text(
                        text = highlightedSummary,
                        fontSize = 16.sp,
                        lineHeight = 18.sp,
                        fontWeight = FontWeight.Normal,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.padding(0.dp),
                        color = colorResource(R.color.text_summary)
                    )
                }
                Spacer(modifier = Modifier.height(12.dp))
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = formatDate(item, isCreateTimeSort),
                        fontSize = 12.sp,
                        lineHeight = 14.sp,
                        fontWeight = FontWeight.Normal,
                        modifier = Modifier.padding(0.dp),
                        color = colorResource(R.color.text_summary)
                    )
                    if(item.hasAudio == true){
                        Image(
                            painter = painterResource(id = R.drawable.ic_audio),
                            contentDescription = null
                        )
                    }
                }

            }
            if(!item.firstPicture.isNullOrEmpty() || !item.handwritingThumbnail.isNullOrEmpty()){
//                val imageLoader = ImageLoader.Builder(LocalContext.current)
//                    .components {
//                        add(GifDecoder.Factory()) // Add Gif support
//                    }
//                    .build()
//                Column(
//                    modifier = Modifier
//                        .width(if (!editMode && targetCategory?.colorIndex == 0) 95.dp else 75.dp)
//                        .fillMaxHeight()
//                        .padding(end = 16.dp),
//                    verticalArrangement = Arrangement.Center
//                ){
//                    AsyncImage(
//                        model = ImageRequest.Builder(LocalContext.current)
//                            .data(item.handwritingThumbnail ?: item.firstPicture)
//                            .crossfade(true)
//                            .build(),
//                        imageLoader = imageLoader,
//                        contentScale = ContentScale.Crop, // 保持比例裁剪
//                        contentDescription = "Image",
//                        modifier = Modifier
//                            .size(74.dp)
//                            .clip(RoundedCornerShape(8.dp))
//                    )
//                }
            }
            if(editMode){
                // 编辑模式下显示复选图标
                if(isSelected){
                    Box(Modifier
                        .padding(start = 16.dp, end = 16.dp)
                        .fillMaxHeight(), Alignment.Center) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_item_checked),
                            contentDescription = null
                        )
                    }
                } else {
                    Box(Modifier
                        .padding(start = 16.dp, end = 16.dp)
                        .fillMaxHeight(), Alignment.Center) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_item_unchecked),
                            contentDescription = null
                        )
                    }
                }
            }

        }
        if(!editMode){
            targetCategory.let { category ->
                // 未分类不展示角标分类颜色
                category?.let {
                    if(it.colorIndex>0){
                        val categoryColor = CommonUtils.getCategoryColorArray()[it.colorIndex-1]
                        Image(
                            painter = painterResource(id = R.drawable.label_category_triangle),
                            contentDescription = "Example Image",
                            colorFilter = ColorFilter.tint(colorResource(categoryColor)),
                            modifier = Modifier
                                .align(Alignment.BottomEnd)
                                .size(20.dp),
                        )
                    }
                }
            }
        }
    }
}


