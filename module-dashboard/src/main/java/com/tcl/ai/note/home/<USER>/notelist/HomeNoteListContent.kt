package com.tcl.ai.note.home.components.notelist

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.windowInsetsBottomHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.anim.BottomBarAnim
import com.tcl.ai.note.base.R
import com.tcl.ai.note.dashboard.ui.DeleteNoteDialog
import com.tcl.ai.note.home.components.AdapterStatusBar
import com.tcl.ai.note.home.components.AddNoteFloatingBtn
import com.tcl.ai.note.home.components.BottomNavigationButtons
import com.tcl.ai.note.home.components.NavigationTab
import com.tcl.ai.note.home.components.notelist.title.HomeNoteListTopTitle
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.utils.isButtonNavigation
import com.tcl.ai.note.home.components.base.rememberNoteLayoutConfig
import com.tcl.ai.note.home.components.pop.ShowDropDownMenuCompat
import com.tcl.ai.note.journalhome.components.BottomOperateViewHeight
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getNavigationBarHeight
import com.tcl.ai.note.utils.getStatusBarHeight
import com.tcl.ai.note.utils.getStatusBarHeightAdapter
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge

/**
 * 主页笔记面板组件
 */
@Composable
internal fun HomeNoteListContent(
    modifier: Modifier,
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    if (isTablet) {
        HomeNoteContentTablet(modifier, homeNoteUiState, onAction)
    } else {
        HomeNoteContentPhone(modifier, homeNoteUiState, onAction)
    }
    // 删除对话框
    DeleteDialog(homeNoteUiState, onAction)
}

/**
 * 平板设备的笔记面板组件
 */
@Composable
private fun HomeNoteContentTablet(
    modifier: Modifier,
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    val layoutConfig = rememberNoteLayoutConfig()
    val hasNavigationBar = isButtonNavigation()
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(color = colorResource(R.color.home_note_list_bg_color))
    ) {
        HomeNoteContentPanel(modifier = Modifier.fillMaxWidth(), homeNoteUiState, onAction)
        // 悬浮按钮（在导航按钮上方）
        NoteContentBottomOperateView(
            modifier = Modifier.align(Alignment.BottomEnd),
            homeNoteUiState,
            onAction
        )
        val navigationBarHeight = getNavigationBarHeight()
        val end = layoutConfig.contentPadding.dp
        val paddingBottom = navigationBarHeight + 16.dp           // 添加笔记按钮（在导航按钮上方）
        AddNoteFloatingBtn(
            modifier = Modifier
                .padding(end = end, bottom = paddingBottom)
                .align(Alignment.BottomEnd),
            homeNoteUiState,
            onAction
        )
    }
}

/**
 * 手机设备的笔记面板组件
 */
@Composable
private fun HomeNoteContentPhone(
    modifier: Modifier,
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit,
) {
    val hasNavigationBar = isButtonNavigation() //为true 代表是按键导航，false为手势导航（横线）
    val navigationBarHeight = getNavigationBarHeight()

    Box(
        modifier = modifier
            .fillMaxSize()
            .background(color = colorResource(R.color.home_note_list_bg_color))
    ) {
        Column {
            HomeNoteContentPanel(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f), homeNoteUiState, onAction
            )
            // 底部操作区域（操作按钮和导航按钮）
            if (homeNoteUiState.isShowOperateView || homeNoteUiState.isShowBottomNav) {
                BottomBarAnim(
                    visibleA = homeNoteUiState.isShowOperateView,
                    contentA = {
                        NoteContentBottomOperateView(
                            modifier = Modifier,
                            homeNoteUiState,
                            onAction
                        )
                    },
                    contentB = {
                        // 底部导航按钮（最底部，只在手机上显示）
                        // 只有在显示底部导航且不在操作模式时才显示导航按钮
                        if (homeNoteUiState.isShowBottomNav && !homeNoteUiState.isShowOperateView) {
                            BottomNavigationButtons(
                                modifier = Modifier,
                                onToTab = { route ->
                                    if (route == NavigationTab.JOURNAL) {
                                        onAction(HomeNoteListAction.OnNavigateTo(NavigationTab.JOURNAL))
                                    }
                                }
                            )
                        }
                    }
                )
            }
        }

        val layoutConfig = rememberNoteLayoutConfig()
        val end = layoutConfig.contentPadding.dp

        val paddingBottom = navigationBarHeight + 16.dp + homeNoteUiState.isShowBottomNav.judge(
            BottomOperateViewHeight + 16.dp,
            0.dp
        )

        Logger.i(
            "HomeNoteContent",
            "paddingBottom:$paddingBottom, hasNavigationBar:$hasNavigationBar, navigationBarHeight:$navigationBarHeight"
        )

        // 添加笔记按钮（在导航按钮上方）
        AddNoteFloatingBtn(
            modifier = Modifier
                .padding(end = end, bottom = paddingBottom)
                .align(Alignment.BottomEnd),
            homeNoteUiState,
            onAction
        )
    }
}

/**
 * 笔记面板组件
 */
@Composable
private fun HomeNoteContentPanel(
    modifier: Modifier = Modifier,
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    Column(
        modifier = modifier
    ) {
        AdapterStatusBar()
        // 标题栏（仅平板显示）
        HomeNoteListTopTitle(
            homeNoteUiState = homeNoteUiState,
            onAction = onAction
        )
        HomeNoteList(
            homeNoteUiState = homeNoteUiState,
            onAction = onAction,
        )
    }
}


@Composable
private fun DeleteDialog(
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    if (homeNoteUiState.isShowDeleteDialog) {
        val selectedItemCounts = homeNoteUiState.selectedCount
        val title =
            if (selectedItemCounts == 1) stringResource(R.string.dialog_title_delete_one_items) else String.format(
                stringResource(R.string.dialog_title_delete_multiple_items), selectedItemCounts
            )
        DeleteNoteDialog(
            text = title,
            onDismiss = { onAction(HomeNoteListAction.OnShowDeleteDialog(false)) },
            onDelete = { onAction(HomeNoteListAction.OnDelete) }
        )
    }
}


