package com.tcl.ai.note.home.components.preview

import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.state.HomeTitleMode

/**
 * HomeTabletTopTitle 预览参数提供器
 */
class HomeTabletTopTitlePreviewParameterProvider : PreviewParameterProvider<HomeNoteUiState> {
    override val values = sequenceOf(
        // 正常模式 - 默认状态
        HomeNoteUiState(
            selectedCategoryName = "全部",
            viewType = DataStoreParam.VIEW_TYPE_GRID,
            titleMode = HomeTitleMode.Normal,
            isClickedSort = false,
            selectedCount = 0,
            isSelectedAll = false,
            isCreateTimeSort = true
        ),

        // 正常模式 - 分类选中状态
        HomeNoteUiState(
            selectedCategoryName = "工作笔记",
            viewType = DataStoreParam.VIEW_TYPE_LIST,
            titleMode = HomeTitleMode.Normal,
            isClickedSort = false,
            selectedCount = 0,
            isSelectedAll = false,
            isCreateTimeSort = false
        ),

        // 编辑模式 - 未选中任何项
        HomeNoteUiState(
            selectedCategoryName = "个人笔记",
            viewType = DataStoreParam.VIEW_TYPE_GRID,
            titleMode = HomeTitleMode.Edit,
            isClickedSort = false,
            selectedCount = 0,
            isSelectedAll = false,
            isCreateTimeSort = true
        ),

        // 编辑模式 - 全选状态
        HomeNoteUiState(
            selectedCategoryName = "项目笔记",
            viewType = DataStoreParam.VIEW_TYPE_GRID,
            titleMode = HomeTitleMode.Edit,
            isClickedSort = false,
            selectedCount = 15,
            isSelectedAll = true,
            isCreateTimeSort = true
        ),

        // 搜索模式
        HomeNoteUiState(
            selectedCategoryName = "全部",
            viewType = DataStoreParam.VIEW_TYPE_GRID,
            titleMode = HomeTitleMode.Search,
            isClickedSort = false,
            selectedCount = 0,
            isSelectedAll = false,
            searchText = "搜索关键词",
            isCreateTimeSort = true
        ),

        // 长分类名称测试
        HomeNoteUiState(
            selectedCategoryName = "这是一个非常长的分类名称用来测试UI显示效果",
            viewType = DataStoreParam.VIEW_TYPE_LIST,
            titleMode = HomeTitleMode.Normal,
            isClickedSort = false,
            selectedCount = 0,
            isSelectedAll = false,
            isCreateTimeSort = false
        )
    )
}