package com.tcl.ai.note.utils

import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory

object CommonUtils {

    fun getCategoryColorArray(): IntArray {
        var colorArray: IntArray? = null
        colorArray = intArrayOf(
            R.color.category_color_YELLOW,
            R.color.category_color_ORANGE,
            R.color.category_color_PINK,
            R.color.category_color_PURPLE,
            R.color.category_color_BLUE,
            R.color.category_color_BLACK,
            R.color.category_color_GREEN,
            R.color.category_color_GRAY
        )
        return colorArray
    }

    fun determineIcon(category: NoteCategory): Int {
        // 这里是确定图标的逻辑，可以根据分类的某些属性或预置逻辑设置图标。
        if (category == null) return R.drawable.ic_all_notes
        val colorIndex = category.colorIndex ?: CategoryColors.NONE_COLOR
        return when (colorIndex) {
            CategoryColors.NONE_COLOR -> R.drawable.ic_category_uncategorised // 未分类
            CategoryColors.YELLOW_COLOR -> R.drawable.ic_category_yellow
            CategoryColors.ORANGE_COLOR -> R.drawable.ic_category_orange
            CategoryColors.PINK_COLOR -> R.drawable.ic_category_pink
            CategoryColors.PURPLE_COLOR -> R.drawable.ic_category_purple
            CategoryColors.BLUE_COLOR -> R.drawable.ic_category_blue
            CategoryColors.GREEN_COLOR -> R.drawable.ic_category_green
            else -> R.drawable.ic_all_notes
        }
    }

}