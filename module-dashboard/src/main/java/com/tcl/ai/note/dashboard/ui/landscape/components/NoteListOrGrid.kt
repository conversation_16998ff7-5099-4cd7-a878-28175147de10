package com.tcl.ai.note.dashboard.ui.landscape.components

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.items
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Modifier
import com.tcl.ai.note.dashboard.ui.GridViewItem
import com.tcl.ai.note.dashboard.ui.NoteItemRow
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem

/**
 * 列表数据
 */
@Composable
internal fun NoteListItems(
    dashboardModel: DashboardModel,
    editMode:Boolean,
    selectedItemCounts:Set<Int>,
    selectedNotes:List<NoteListItem>,
    isSearching:Boolean,
    items: List<NoteListItem>,
    onIsSearching:(Boolean) -> Unit,
    onSelectedItemCounts:(Set<Int>) -> Unit,
    onSelectedNotes:(List<NoteListItem>) -> Unit,
    onEditMode:(Boolean) ->Unit,
    onIsSelectedAllMode:(Boolean) ->Unit
){
    val noteState by dashboardModel.noteState.collectAsState()
    // 自动加载更多
    val lazyListState = rememberLazyListState() // 为列表布局添加滚动状态跟踪
    // 跟踪当前选中的noteId
    var selectedNoteId by remember { mutableLongStateOf(0L) }
    if(noteState.note!=null){
        selectedNoteId = noteState.note!!.noteId
    }
    if(!isSearching){
        val listShouldLoadMore = remember {
            derivedStateOf {
                // 检测是否滚动到底部最后3个元素
                val layoutInfo = lazyListState.layoutInfo
                val totalItems = layoutInfo.totalItemsCount
                val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()

                lastVisibleItem?.index != null &&
                        lastVisibleItem.index >= totalItems - 3 &&
                        !dashboardModel.isLoading &&
                        dashboardModel.hasMore
            }
        }

        LaunchedEffect(listShouldLoadMore) {
            snapshotFlow { listShouldLoadMore.value }
                .collect {
                    if (it && !dashboardModel.isLoading && dashboardModel.hasMore) {
                        dashboardModel.loadMoreNotes()
                    }
                }
        }
    }
    LazyColumn(
        state = lazyListState,
        modifier = Modifier.fillMaxSize()
    ) {
        items(items) { item ->
            val index = items.indexOf(item)
            // 列表item展示
            NoteItemRow(
                dashboardModel,
                item,
                isCreateTimeSort = noteState.isCreateTimeSort,
                editMode,
                isSelected = selectedItemCounts.contains(index),
                isActive = item.noteId == selectedNoteId,
                searchQuery = noteState.searchText,
                onClick = {
                    if(editMode){
                        onSelectedItemCounts(setOf(index))
                        onIsSelectedAllMode(selectedItemCounts.size == items.size)
                        onSelectedNotes(if (selectedNotes.contains(item)) {
                            selectedNotes - item
                        } else {
                            selectedNotes + item
                        })
                    }else{
                        selectedNoteId = item.noteId
                        onIsSearching(noteState.isSearching)
                        //更新预览数据
                        dashboardModel.setCurrentNoteId(item.noteId) // 同步设置ID
//                        dashboardModel.handleIntent(ConfigIntent.UpdatePreviewNote(item.noteId))
                    }
                },
                onLongClick = {
                    if(!isSearching){
                        dashboardModel.updateFabVisibleState(false)
                        onEditMode(true)
                        if(!selectedItemCounts.contains(index)){
                            onSelectedItemCounts(setOf(index))
                        }
                        if(!selectedNotes.contains(item)){
                            onSelectedNotes(selectedNotes + item)
                        }
                    }
                }
            )
        }
    }
}


/**
 * 瀑布流数据
 */
@Composable
internal fun NoteStaggeredGridItems(
    dashboardModel: DashboardModel,
    editMode:Boolean,
    selectedItemCounts:Set<Int>,
    selectedNotes:List<NoteListItem>,
    isSearching:Boolean,
    items: List<NoteListItem>,
    onIsSearching:(Boolean) -> Unit,
    onSelectedItemCounts:(Set<Int>) -> Unit,
    onSelectedNotes:(List<NoteListItem>) -> Unit,
    onEditMode:(Boolean) ->Unit,
    onIsSelectedAllMode:(Boolean) ->Unit
){
    val noteState by dashboardModel.noteState.collectAsState()
    // 跟踪当前选中的noteId
    var selectedNoteId by remember { mutableLongStateOf(0L) }
    if(noteState.note!=null){
        selectedNoteId = noteState.note!!.noteId
    }
    val staggeredGridState = rememberLazyStaggeredGridState() // 为瀑布流布局添加滚动状态跟踪
    if(!isSearching){
        val gridShouldLoadMore = remember {
            derivedStateOf {
                // 检测是否滚动到底部最后3个元素
                val layoutInfo = staggeredGridState.layoutInfo
                val totalItems = layoutInfo.totalItemsCount
                val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()

                lastVisibleItem?.index != null &&
                        lastVisibleItem.index >= totalItems - 3 &&
                        !dashboardModel.isLoading &&
                        dashboardModel.hasMore
            }
        }

        LaunchedEffect(gridShouldLoadMore) {
            snapshotFlow { gridShouldLoadMore.value }
                .collect {
                    if (it && !dashboardModel.isLoading && dashboardModel.hasMore) {
                        dashboardModel.loadMoreNotes()
                    }
                }
        }
    }
    LazyVerticalStaggeredGrid(
        state = staggeredGridState,
        columns = StaggeredGridCells.Fixed(2), // Change 2 to the number of columns you want
        modifier = Modifier.fillMaxSize()
    ) {
        items(items) { item ->
            val index = items.indexOf(item)
            // 瀑布流item展示
            GridViewItem(
                dashboardModel,
                item,
                isCreateTimeSort = noteState.isCreateTimeSort,
                editMode,
                isSelected = selectedItemCounts.contains(index),
                isActive = item.noteId == selectedNoteId,
                searchQuery = noteState.searchText,
                onClick = {
                    if(editMode){
                        onSelectedItemCounts(setOf(index))
                        onIsSelectedAllMode(selectedItemCounts.size == items.size)
                        onSelectedNotes(if (selectedNotes.contains(item)) {
                            selectedNotes - item
                        } else {
                            selectedNotes + item
                        })
                    }else{
                        onIsSearching(noteState.isSearching)
                        //更新预览数据
                        dashboardModel.setCurrentNoteId(item.noteId)
//                        dashboardModel.handleIntent(ConfigIntent.UpdatePreviewNote(item.noteId))
                    }
                },
                onLongClick = {
                    if(!isSearching){
                        dashboardModel.updateFabVisibleState(false)
                        onEditMode(true)
                        if(!selectedItemCounts.contains(index)){
                            onSelectedItemCounts(setOf(index))
                        }
                        if(!selectedNotes.contains(item)){
                            onSelectedNotes(selectedNotes + item)
                        }
                    }
                }
            )
        }
    }
}
