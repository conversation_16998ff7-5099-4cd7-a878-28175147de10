package com.tcl.ai.note.home.components.notelist.content

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.hideFromAccessibility
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.viewinterop.AndroidView
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.home.components.base.rememberNoteLayoutConfig
import com.tcl.ai.note.home.model.HomeNoteItemModel

/**
 * 富文本预览组件
 * 用于在首页卡片中显示富文本内容，支持异步加载富文本样式
 */
@Composable
fun HomeRichTextPreview(
    modifier: Modifier = Modifier,
    note: HomeNoteItemModel,
    isNormalMode: Boolean = true
) {

    when {
        !note.content.isNullOrBlank()&& !note.summary.isNullOrBlank() -> {
            val noteLayoutConfig = rememberNoteLayoutConfig()
            RichTextDisplay(
                modifier = modifier,
                content = if (isNormalMode) note.summary else note.content,
                richTextStyleEntity = note.richTextStyleEntity,
                isNormalMode = isNormalMode,
                maxLines = if(isNormalMode) noteLayoutConfig.contentMaxLines else Int.MAX_VALUE,
            )
        }
        
        else -> {
        }
    }
}
/**
 * Compose 版本的富文本显示组件
 * 用于在 Compose UI 中显示富文本内容
 */
@Composable
fun RichTextDisplay(
    modifier: Modifier = Modifier,
    content: String,
    richTextStyleEntity: RichTextStyleEntity?,
    maxLines: Int = 15,
    isNormalMode: Boolean = true
) {
    val context = LocalContext.current
    val darkTheme: Boolean = isSystemInDarkTheme()

    // 记住 RichTextDisplayView 实例，只在关键参数变化时重新创建
    val richTextView = remember(maxLines, isNormalMode) {
        RichTextDisplayView(context, maxLines, isNormalMode)
    }


    // 当夜间模式变化时更新颜色
    LaunchedEffect(darkTheme) {
        richTextView.updateDarkMode()
    }

    AndroidView(
        factory = {  richTextView },
        modifier = modifier.fillMaxSize().semantics(){
            hideFromAccessibility()
        },
        update = { view ->
            view.setRichTextContent(content, richTextStyleEntity)
        }
    )

}

