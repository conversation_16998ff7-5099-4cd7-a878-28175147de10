package com.tcl.ai.note.dashboard.ui.landscape.components

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.theme.LINE_HEIGHT
import com.tcl.ai.note.theme.editorRichTextStyle
import com.tcl.ai.note.utils.judge

/**
 * 显示待办事项
 */
@SuppressLint("RememberReturnType")
@Composable
internal fun ShowTodoBlock(
    modifier: Modifier = Modifier,
    item: EditorContent.TodoBlock,
    darkTheme: Boolean) {
    val textColor = if (item.isDone) {
        colorResource(darkTheme.judge(R.color.todo_block_color_sel,R.color.todo_block_color_nor))
    } else {
        colorResource(darkTheme.judge(R.color.white, R.color.text_edit_color))
    }
    val styledText = remember(item.text,textColor) {
        buildAnnotatedString {
            // 保留原有样式
            append(item.text.annotatedString)

            // 强制覆盖文本颜色
            addStyle(
                style = SpanStyle(color = textColor),
                start = 0,
                end = item.text.annotatedString.length
            )

            // 根据完成状态添加删除线
            if (item.isDone) {
                addStyle(
                    style = SpanStyle(textDecoration = TextDecoration.LineThrough),
                    start = 0,
                    end = item.text.annotatedString.length
                )
            }
        }
    }
    Row(
        modifier = modifier
            .fillMaxWidth()
    ) {
        if (item.isDone) {
            Box(
                Modifier
                    .padding(top = ((LINE_HEIGHT -20)/2).dp, end = 8.dp)
                    .size(20.dp)
                , Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_todo_checked),
                    contentDescription = null
                )
            }
        } else {
            Box(
                Modifier
                    .padding(top = ((LINE_HEIGHT -20)/2).dp, end = 8.dp)
                    .size(20.dp)
                    .fillMaxHeight()
                , Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = darkTheme.judge(R.drawable.ic_todo_unchecked_2, R.drawable.ic_edit_todo_unchecked)),
                    contentDescription = null
                )
            }
        }

        Text(
            text = styledText,
            style = editorRichTextStyle.copy(
                lineHeightStyle = LineHeightStyle(
                    alignment = LineHeightStyle.Alignment.Center,
                    trim = LineHeightStyle.Trim.None
                ),
                platformStyle = PlatformTextStyle(includeFontPadding = false)
            ),
            modifier = Modifier.weight(1f)
        )
    }
}