package com.tcl.ai.note.home.components.categorylist

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.setting.version.UpdateMonitorImpl
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.widget.HoverProofIconButton

@Composable
internal fun UpdateIconWithDot(
    onCheckUpdate: (HomeCategoryAction) -> Unit, updateState: UpdateMonitorImpl.UpdateState, isUpdateVersion: Boolean
) {
    Column(
        modifier = Modifier
            .height(56.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        // 原有 Box 布局
        Box(
            modifier = Modifier
                .size(24.dp),
            contentAlignment = Alignment.Center
        ) {
            HoverProofIconButton(
                modifier = Modifier.size( TclTheme.dimens.btnSize),
                onClick =  {
                    onCheckUpdate(HomeCategoryAction.OnCheckUpdate(true))
                }
            ) {
                Icon(
                    modifier = Modifier.size( TclTheme.dimens.iconSize),
                    painter = painterResource(id = R.drawable.settings),
                    contentDescription = stringResource(R.string.updates)
                )
            }

            // 红点
            if (updateState == UpdateMonitorImpl.UpdateState.NewVersionAvailable && !isUpdateVersion) {
                Box(
                    modifier = Modifier
                        .padding(end = 4.dp)
                        .size(6.dp)
                        .clip(CircleShape)
                        .background(colorResource(id = R.color.edit_palette_color_RED))
                        .align(Alignment.TopEnd)
                )
            }
        }
    }
}