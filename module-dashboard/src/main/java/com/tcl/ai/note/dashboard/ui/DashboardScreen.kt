package com.tcl.ai.note.dashboard.ui

import MoveToNoteCategoryScreen
import android.annotation.SuppressLint
import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.exclude
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.items
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.FloatingActionButton
import androidx.compose.material.FloatingActionButtonDefaults
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material3.FabPosition
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.ScaffoldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.tcl.ai.note.base.R
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.dashboard.BuildConfig
import com.tcl.ai.note.dashboard.intent.ConfigIntent
import com.tcl.ai.note.dashboard.states.ListNotesUiState
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.dashboard.vm.SharedDashboardModel
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.home.HomeActivity
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isFastClick
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.widget.SearchBox
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Locale


/**
 * 首页UI
 */
@SuppressLint("DesignSystem")
@Composable
fun DashboardScreen(
    navController: NavController,
    sharedDashboardModel: SharedDashboardModel,
    dashboardModel: DashboardModel = hiltViewModel(),
    audioToTextViewModel: AudioToTextViewModel,
    onTabClick: (Int) -> Unit = {},
    pageIndex: Int = 0
) {
    val context = LocalContext.current
    val dashboardState by dashboardModel.configState.collectAsState()
    val noteState by dashboardModel.noteState.collectAsState()
    val lifecycleOwner = LocalLifecycleOwner.current
    // 添加生命周期监听
    DisposableEffect(lifecycleOwner) {
        val lifecycleObserver = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                // 只在非搜索状态刷新
                if (noteState.searchText.isEmpty()) {
                    dashboardModel.loadInitialNotes() // 重新加载数据
                    dashboardModel.handleIntent(ConfigIntent.GetCategories)
                }
            }
        }

        lifecycleOwner.lifecycle.addObserver(lifecycleObserver)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(lifecycleObserver)
        }
    }
    val viewType = dashboardState.viewType
    val isFabVisible = dashboardModel.isFabVisible
    val listNotesUiState by dashboardModel.listNotesUiState.collectAsState()
    val searchText = noteState.searchText
    val isSearching = noteState.isSearching

    // 选中的item数据在items中的下标值集合
    val selectedItemCounts by sharedDashboardModel.selectedItemCounts.collectAsState()
    // 是否全选模式
    val isSelectedAllMode by sharedDashboardModel.isSelectedAllMode.collectAsState()
    // 是否显示列表数据删除提示框
    val isDeleteDialogShown by sharedDashboardModel.isDeleteDialogShown.collectAsState()
    // 是否显示分类列表用于迁移已选中的数据至指定的分类中
    val showCategories by dashboardModel.showMoveToCategoryDialog.collectAsState()

    val isNewCategoryVisible = dashboardModel.isNewCategoryVisible
    val isAddCategoryMode = dashboardModel.isAddCategoryMode

    // 是否显示分类删除提示框
    var isDeleteCategoryDialog by remember { mutableStateOf(false) }
    // 是否删除当前分类下的Note
    var isDeleteSelectedCategoryNotes by remember { mutableStateOf(false) }

    // 是否显示排序方式选择提示框
    val isSortOrderDialogShown by dashboardModel.shownSortOrderDialog.collectAsState()

    // 当前分类
    val currentCategoryId  by dashboardModel.currentCategoryId.collectAsState()
    val currentCategoryName by dashboardModel.currentCategoryName.collectAsState()
    val currentColorIndex by dashboardModel.currentColorIndex.collectAsState()

    // 监听返回时的刷新标志
    val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle
    LaunchedEffect(savedStateHandle) {
        savedStateHandle?.get<Boolean>("refresh")?.let { refresh ->
            Logger.d("refresh:", refresh.toString())
            if(isSearching){
                dashboardModel.handleIntent(ConfigIntent.SearchNotes(searchText))
            }else if(refresh){
                dashboardModel.loadInitialNotes() // 重新加载数据
                dashboardModel.handleIntent(ConfigIntent.GetCategories)
                savedStateHandle.remove<Boolean>("refresh") // 清除标志
            }
        }
    }

    // 列表数据
    val items by remember(listNotesUiState) {
        derivedStateOf {
            val tmpUiState = listNotesUiState
            if (tmpUiState is ListNotesUiState.Success) {
                tmpUiState.items
            } else emptyList()
        }
    }

    // 选中的notes数据
//    var selectedNotes by remember { mutableStateOf(listOf<NoteListItem>()) }
    var staggeredGridCells = 2
    if(!BuildConfig.IS_PHONE){
        staggeredGridCells = 3
    }

    Box(modifier = Modifier.fillMaxSize()) {
        Scaffold(
            contentWindowInsets = ScaffoldDefaults
                .contentWindowInsets
                .exclude(WindowInsets.navigationBars),
            topBar = {
                // 显示模式
                if(!noteState.editMode){
                    DisplayModeTopAppBar(
                        dashboardModel.currentCategoryName.value,
                        viewType,onSearchClicked = {
                            // 搜索
                            dashboardModel.updateSearchState(true)
                        },
                        onMoreActionsClicked = {
                            // 显示模式 列表or网格
                            val newViewType = if (viewType == DataStoreParam.VIEW_TYPE_LIST) {
                                DataStoreParam.VIEW_TYPE_GRID
                            } else {
                                DataStoreParam.VIEW_TYPE_LIST
                            }
                            dashboardModel.handleIntent(ConfigIntent.ChangeViewType(newViewType))
                        },
                        onSortClicked = {
                            // 排序
                            dashboardModel.updateShownSortOrderDialog(true)
                        },
                        onRenameCategory = {
                            // 重命名分类
                            dashboardModel.updateNewCategoryVisibleState(true)
                            dashboardModel.updateNewCategoryModeState(false)
                        },
                        onDeleteCategory = {
                            // 删除分类
                            isDeleteCategoryDialog = true
                        },
                        onTabClick = {
                            onTabClick(it)
                        },
                        pageIndex = pageIndex,
                        onTitleClicked = {
                            context.startActivity(Intent(context, HomeActivity::class.java))
                        }
                    )
                } else {
                    // 编辑模式
                    EditModeTopAppBar(
                        selectedItemCount = selectedItemCounts,
                        isSelectedAllMode = isSelectedAllMode,
                        onCancel = {
                            // 取消已选中的回到显示模式
                            dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(false))
                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(emptySet()))
                            dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(listOf()))
                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(false))
                            dashboardModel.updateFabVisibleState(true)
                        },
                        onSelectedAll = {
                            val isAllMode = !isSelectedAllMode
                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(isAllMode))
                            if(isAllMode){
                                // 全选加载所有数据，不然只能选中翻页的10条数据或缓存的多条数据
                                dashboardModel.loadAllNotes { allItems ->
                                    val allIndex = (0 until allItems.size).toSet() // 生成包含所有索引的 Set<Int>
                                    sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(allIndex, true))
                                    dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(allItems))
                                }
                            } else {
                                sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(emptySet()))
                                dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(listOf()))
                            }
                        },
                        onDeleteSelected = {
                            // 删除选中的item
                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsDeleteDialogShown(true))
                        },
                        onMoveTo = {
                            // 移至某一个分类
                            dashboardModel.updateShowMoveToCategoryDialogState(true)
//                            showCategories = true
                        },
                        onTabClick = {
                            onTabClick(it)
                        },
                        pageIndex = pageIndex,
                    )
                }

            },
            floatingActionButton = {
                if(isFabVisible){
                    FloatingActionButton(
                        onClick = {
                            navController.navigate("edit_screen")
                        },
                        elevation = FloatingActionButtonDefaults.elevation(0.dp), // 移除投影
                        backgroundColor = colorResource(R.color.bg_float_action_button), // 设置FloatingActionButton的背景颜色
                        contentColor = Color.White, // 设置图标的颜色
                        modifier = Modifier.navigationBarsPadding(),
                    ) {
                        Icon(
                            Icons.Filled.Add,
                            contentDescription = R.string.btn_add_sticky_note.stringRes(),
                            tint = Color.White)
                    }
                }
            },
            floatingActionButtonPosition = FabPosition.End,
            content = { innerPadding ->
                // Your screen content here
                Box(modifier = Modifier.padding(innerPadding)) {
                    // Content goes here
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(color = TclTheme.colorScheme.tctGlobalBgColor)
                            .padding(
                                bottom = WindowInsets.navigationBars
                                    .asPaddingValues()
                                    .calculateBottomPadding()
                            ),

                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        NoteCategoryScreenPlaceholder(noteState.editMode, isSearching)

                        //列表数据加载
                        when(listNotesUiState){
                            is ListNotesUiState.Loading ->{
                                CircularProgressIndicator(modifier = Modifier.padding(16.dp))
                            }
                            is ListNotesUiState.Success ->{
                                //数据加载成功
                                if (items.isEmpty() && !dashboardModel.isLoading) {
                                    //没有笔记数据
                                    NoNotesScreen()
                                } else {
                                    // 正常列表渲染
                                    if (viewType == DataStoreParam.VIEW_TYPE_LIST) {
                                        // 自动加载更多
                                        val lazyListState = rememberLazyListState() // 为列表布局添加滚动状态跟踪
                                        if (!isSearching) {
                                            val listShouldLoadMore = remember {
                                                derivedStateOf {
                                                    // 检测是否滚动到底部最后3个元素
                                                    val layoutInfo = lazyListState.layoutInfo
                                                    val totalItems = layoutInfo.totalItemsCount
                                                    val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()

                                                    lastVisibleItem?.index != null &&
                                                            lastVisibleItem.index >= totalItems - 3 &&
                                                            !dashboardModel.isLoading &&
                                                            dashboardModel.hasMore
                                                }
                                            }

                                            LaunchedEffect(listShouldLoadMore) {
                                                snapshotFlow { listShouldLoadMore.value }
                                                    .collect {
                                                        if (it && !dashboardModel.isLoading && dashboardModel.hasMore) {
                                                            dashboardModel.loadMoreNotes()
                                                        }
                                                    }
                                            }
                                        }
                                        var paddingSize = 16.dp
                                        if(!BuildConfig.IS_PHONE){
                                            paddingSize = 24.dp
                                        }
                                        //列表展示
                                        LazyColumn(
                                            state = lazyListState,
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .padding(start = paddingSize, end = paddingSize)
                                        ) {
                                            items(items) { item ->
                                                val index = items.indexOf(item)
                                                // 列表item展示
                                                NoteItemRow(
                                                    dashboardModel,
                                                    item,
                                                    isCreateTimeSort = noteState.isCreateTimeSort,
                                                    editMode = noteState.editMode,
                                                    isSelected = selectedItemCounts.contains(index),
                                                    isActive = false,
                                                    searchQuery = searchText,
                                                    onClick = {
                                                        if(noteState.editMode){
                                                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(setOf(index)))
                                                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(selectedItemCounts.size == items.size))
                                                            val selectedNotes = if (noteState.selectedNotes.contains(item)) {
                                                                noteState.selectedNotes - item
                                                            } else {
                                                                noteState.selectedNotes + item
                                                            }
                                                            dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(selectedNotes))
                                                        }else{
                                                            //跳转到详情页
                                                            if (isFastClick()) {
                                                                return@NoteItemRow
                                                            }
                                                            navController.navigate("edit_screen?noteId=${item.noteId}")
//                                                            Toast.makeText(context, "跳转到详情页", Toast.LENGTH_SHORT).show()
                                                        }
                                                    },
                                                    onLongClick = {
                                                        if(!isSearching){
                                                            dashboardModel.updateFabVisibleState(false)
                                                            dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(true))
                                                            if(!selectedItemCounts.contains(index)){
                                                                sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(setOf(index)))
                                                            }
                                                            if(!noteState.selectedNotes.contains(item)){
                                                                dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(noteState.selectedNotes+item))
                                                            }
                                                        }
                                                    }
                                                )
                                            }
                                        }
                                    }else{
                                        //瀑布流展示
                                        // 自动加载更多
                                        val staggeredGridState = rememberLazyStaggeredGridState() // 为瀑布流布局添加滚动状态跟踪
                                        if (!isSearching) {
                                            val gridShouldLoadMore = remember {
                                                derivedStateOf {
                                                    // 检测是否滚动到底部最后3个元素
                                                    val layoutInfo = staggeredGridState.layoutInfo
                                                    val totalItems = layoutInfo.totalItemsCount
                                                    val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()

                                                    lastVisibleItem?.index != null &&
                                                            lastVisibleItem.index >= totalItems - 3 &&
                                                            !dashboardModel.isLoading &&
                                                            dashboardModel.hasMore
                                                }
                                            }

                                            LaunchedEffect(gridShouldLoadMore) {
                                                snapshotFlow { gridShouldLoadMore.value }
                                                    .collect {
                                                        if (it && !dashboardModel.isLoading && dashboardModel.hasMore) {
                                                            dashboardModel.loadMoreNotes()
                                                        }
                                                    }
                                            }
                                        }

                                        var paddingSize = 12.dp
                                        if(!BuildConfig.IS_PHONE){
                                            paddingSize = 18.dp
                                        }
                                        LazyVerticalStaggeredGrid(
                                            state = staggeredGridState,
                                            columns = StaggeredGridCells.Fixed(staggeredGridCells), // Change 2 to the number of columns you want
                                            modifier = Modifier
                                                .fillMaxSize()
                                                .padding(start = paddingSize, end = paddingSize)
                                        ) {
                                            items(items) { item ->
                                                val index = items.indexOf(item)
                                                // 瀑布流item展示
                                                GridViewItem(
                                                    dashboardModel,
                                                    item,
                                                    isCreateTimeSort = noteState.isCreateTimeSort,
                                                    editMode = noteState.editMode,
                                                    isSelected = selectedItemCounts.contains(index),
                                                    isActive = false,
                                                    searchQuery = searchText,
                                                    onClick = {
                                                        if(noteState.editMode){
                                                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(setOf(index)))
                                                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(selectedItemCounts.size == items.size))
                                                            val selectedNotes = if (noteState.selectedNotes.contains(item)) {
                                                                noteState.selectedNotes - item
                                                            } else {
                                                                noteState.selectedNotes + item
                                                            }
                                                            dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(selectedNotes))
                                                        }else{
                                                            //跳转到详情页
                                                            if (isFastClick()) {
                                                                return@GridViewItem
                                                            }
                                                            navController.navigate("edit_screen?noteId=${item.noteId}")
//                                                            Toast.makeText(context, "跳转到详情页", Toast.LENGTH_SHORT).show()
                                                        }
                                                    },
                                                    onLongClick = {
                                                        if(!isSearching){
                                                            dashboardModel.updateFabVisibleState(false)
                                                            dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(true))
                                                            if(!selectedItemCounts.contains(index)){
                                                                sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(setOf(index)))
                                                            }
                                                            if(!noteState.selectedNotes.contains(item)){
                                                                dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(noteState.selectedNotes+item))
                                                            }
                                                        }
                                                    }
                                                )
                                            }

                                        }
                                    }
                                }
                            }
                            is ListNotesUiState.Error ->{
                                //数据加载失败
                                NoNotesScreen()
                            }
                        }

                    }
                }
            }
        )
        // 如果 isSearching 为 true，监听返回键进行处理
        BackHandler(isSearching) {
            dashboardModel.updateFabVisibleState(true)
            dashboardModel.handleIntent(ConfigIntent.SearchNotes(""))
            dashboardModel.updateSearchState(false)
            dashboardModel.loadInitialNotes()
        }
        if(isSearching){
            // 搜索框内容为空则显示半透明遮罩层
            if(searchText.trim().isEmpty()){
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .invisibleSemantics()
                        .padding(top = 56.dp+WindowInsets.statusBars
                            .asPaddingValues()
                            .calculateTopPadding())
                        .background(Color.Black.copy(alpha = 0.5f))
                        .clickable { dashboardModel.updateSearchState(false) } // 点击遮罩层取消搜索
                        .zIndex(0.5f) // 将遮罩层置于搜索框下方，但在内容上方
                )
            }

            // 搜索框
            SearchBox(
                searchText,
                onTextChange = {
                    val isAllSpaces = it.text.isNotEmpty() && it.text.trim().isEmpty()
                    if(!isAllSpaces){
                        dashboardModel.updateFabVisibleState(it.text.isEmpty())
                        dashboardModel.handleIntent(ConfigIntent.SearchNotes(it.text))
                    }else{
                        dashboardModel.updateFabVisibleState(true)
                    }
                },
                onClear = {
                    dashboardModel.updateFabVisibleState(true)
                    dashboardModel.handleIntent(ConfigIntent.SearchNotes(""))
                },
                onBack = {
                    dashboardModel.updateFabVisibleState(true)
                    dashboardModel.handleIntent(ConfigIntent.SearchNotes(""))
                    dashboardModel.updateSearchState(false)
                },
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = WindowInsets.statusBars.asPaddingValues().calculateTopPadding(), start = 0.dp, end = 0.dp)
                    .zIndex(1f) // Ensure the search box is on top
            )
        }

        if (isDeleteDialogShown && selectedItemCounts.isNotEmpty()) {
            DeleteNoteDialog(
                text = if(selectedItemCounts.size==1) stringResource(R.string.dialog_title_delete_one_items) else String.format(stringResource(R.string.dialog_title_delete_multiple_items), selectedItemCounts.size),
                onDelete = {
                    sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsDeleteDialogShown(false))
                    // 删除选中的items
                    val noteIds = noteState.selectedNotes.map { it.noteId }
                    dashboardModel.viewModelScope.launchIO { //删除音频文件
                        dashboardModel.loadNoteContents(noteIds).forEach {
                            if (it is EditorContent.AudioBlock) {
                                // 删除音频文件
                                audioToTextViewModel.deleteAudioFile(it.audioPath)
                            }
                        }
                    }
                    dashboardModel.handleIntent(ConfigIntent.DeleteNotes(noteIds))
                    dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(false))
                    dashboardModel.updateFabVisibleState(true)
                    sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(emptySet()))
                    dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(listOf()))
                    sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(false))
                },
                onDismiss = {
                    sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsDeleteDialogShown(false))
                }
            )
        }

        if(showCategories){
            MoveToNoteCategoryScreen(
                onBack = {
//                    showCategories = false
                    dashboardModel.updateShowMoveToCategoryDialogState(false)
//                    dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(false))
                },
                onMoveToCategory = {categoryId->
                    // 将选中的Notes移至指定的分类
                    val noteIds = noteState.selectedNotes.map { it.noteId }
                    dashboardModel.handleIntent(ConfigIntent.UpdateNotesCategoryId(categoryId,noteIds))
                    dashboardModel.updateFabVisibleState(true)
//                    showCategories = false
                    dashboardModel.updateShowMoveToCategoryDialogState(false)
                    dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(false))
                    sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(emptySet()))
                    dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(listOf()))
                    sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(false))
                },
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(
                        top = WindowInsets.statusBars
                            .asPaddingValues()
                            .calculateTopPadding(), start = 0.dp, end = 0.dp
                    )
                    .zIndex(1f))
        }
        if(isDeleteCategoryDialog){
            BottomDeleteCategoryDialog(
                onCancel = {
                    isDeleteCategoryDialog = false
                },
                onDelete = {
                    val categoryId = currentCategoryId.toLongOrNull()
                    val colorIdx = currentColorIndex.toIntOrNull()
                    if (categoryId == null || colorIdx == null)
                        return@BottomDeleteCategoryDialog
                    val category = NoteCategory(categoryId = categoryId, name = currentCategoryName, colorIndex = colorIdx)
                    dashboardModel.handleIntent(ConfigIntent.DeleteCategory(isDeleteSelectedCategoryNotes,category))
                    isDeleteCategoryDialog = false
                },
                isDeleteNotesSelected = {
                    isDeleteSelectedCategoryNotes = it
                },
                isShowDeleteNotes = items.isNotEmpty()
            )
        }

        if(isSortOrderDialogShown){
            BottomSortOrderDialog(
                onCancel = {
                    dashboardModel.updateShownSortOrderDialog(false)
                },
                onSelected = {
                    dashboardModel.updateSortModeState(it)
                    dashboardModel.updateShownSortOrderDialog(false)
                    // 执行排序查询操作
                    dashboardModel.loadInitialNotes()
                },
                isCreateDate = noteState.isCreateTimeSort
            )
        }

        //添加笔记分类对话框
        if (isNewCategoryVisible) {
            CategoryScreen(
                isPreviewMode = false,
                isAddCategoryMode = isAddCategoryMode,
                onDismissRequest = {
                    //showPopup = false
                    dashboardModel.tempNewCategoryName = ""
                    dashboardModel.tempNewColorIndex = 1
                    dashboardModel.updateNewCategoryVisibleState(false)
                    dashboardModel.updateNewCategoryModeState(true)
                    dashboardModel.updateFabVisibleState(true)
                }
            )
        }

    }

}



// 高亮文本构建函数
@Composable
internal fun buildHighlightedText(text: String, query: String): AnnotatedString {
    return buildAnnotatedString {
        append(text)
        if (query.isNotEmpty()) {
            var startIndex = 0
            while (startIndex < text.length) {
                val matchIndex = text.indexOf(query, startIndex, ignoreCase = true)
                if (matchIndex == -1) break

                // 添加高亮样式
                addStyle(
                    style = SpanStyle(
                        color = colorResource(R.color.search_highlight)
                    ),
                    start = matchIndex.coerceAtLeast(0),
                    end = matchIndex + query.length
                )
                startIndex = matchIndex + query.length
            }
        }
    }
}

/**
 * Note数据是否只有图片
 */
internal fun isOnlyImage(item: NoteListItem):Boolean{
    return (!item.firstPicture.isNullOrEmpty() || !item.handwritingThumbnail.isNullOrEmpty()) && item.summary.isNullOrEmpty() && item.hasAudio == false
}

/**
 * Note数据是否只有音频
 */
internal fun isOnlyVoice(item: NoteListItem):Boolean{
    return item.firstPicture.isNullOrEmpty() && item.summary.isNullOrEmpty() && item.hasAudio == true
}

/**
 * Note数据是否只有图片和录音
 */
internal fun isOnlyImageVoice(item: NoteListItem):Boolean{
    return (!item.firstPicture.isNullOrEmpty() || !item.handwritingThumbnail.isNullOrEmpty()) && item.summary.isNullOrEmpty() && item.hasAudio == true
}

@Composable
fun formatDate(item: NoteListItem, isCreateTimeSort: Boolean): String {
    val currentLocale = LocalConfiguration.current.locales[0] ?: Locale.getDefault()
    // 中文环境下使用 "dd日MM月yyyy年"，否则使用 "dd MMM yyyy"
    val pattern = if (currentLocale.language == "zh") "yyyy年MM月dd日" else "MMM dd, yyyy"
    // "yyyy年MM月dd日 HH:mm:ss" else "MMM dd, yyyy HH:mm:ss" 已使用 时分秒 验证过首页按 修改时间/创建时间 进行排序
    val time = if (isCreateTimeSort) {
        item.createTime
    } else {
        item.modifyTime
    }?: 0L
    // 线程安全且更现代
    val formatter = DateTimeFormatter.ofPattern(pattern).withLocale(currentLocale)
    return Instant.ofEpochMilli(time)   // 时间戳（毫秒） → Instant（UTC）
        .atZone(ZoneId.systemDefault()) // → ZonedDateTime（本地时区）
        .format(formatter)              //  → 格式化字符串
}

/**
 * 智能生成摘要规则：
 * 1. 查找第一个句子结束符（，,。！？.?!)或换行符
 * 2. 找到则截取到该符号位置（包含符号）
 * 3. 未找到则取前15个连续字符
 * 4. 添加省略号处理
 */
internal fun generateSmartSummary(text: String): String {
    if (text.isEmpty()) return ""

    // 定义句子结束符集合（包含中英文常见符号）
    val sentenceEndings = setOf(',','，','。', '！', '？', '.', '!', '?', '\n')

    // 查找第一个句子结束位置
    val endIndex = text.indexOfFirst { it in sentenceEndings }

    return when {
        // 找到结束符的情况
        endIndex != -1 -> {
            val summary = text.substring(0, endIndex)
            // 处理过长内容（超过50字符则截断）
            if (summary.length > 50) summary.take(50) + "…" else summary
        }

        // 无结束符的情况
        else -> {
            // 取前15个连续字符（排除空白符）
            val cleanText = text.trim().replace(Regex("\\s+"), " ")
            val baseSummary = cleanText.take(15)

            // 添加省略号规则
            when {
                cleanText.length <= 15 -> baseSummary
                else -> "$baseSummary…"
            }
        }
    }
}


@Preview(
    widthDp = 1080,// 360 // 设置宽度以模拟横屏
    heightDp = 2340,// 820 // 设置高度
    showBackground = true
)
@Composable
fun PreviewFullConfigScreen() {

    // 调用 DashboardScreen 进行预览
//    DashboardScreen()
}