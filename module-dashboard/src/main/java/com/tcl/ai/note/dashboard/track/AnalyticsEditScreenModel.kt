package com.tcl.ai.note.dashboard.track

import com.tcl.ai.note.handwritingtext.bean.BrushMenu
import com.tcl.ai.note.handwritingtext.bean.DoodlePen
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.PenStyle
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.track.AnalyticsHandWritingTextModel
import com.tcl.ai.note.track.AbsAnalyticsSubModel
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch

/**
 * 统计当前note相关数据
 *
 * 例如，编辑时间，增删字数，笔画次数等等
 */
data class EditStats(
    // 当前note有多少文字
    var totalWordCount: Int = 0,
    // 当前note有多少笔画
    var totalStrokeCount: Int = 0,
    // 当前note有多少录音
    var totalRecordCount: Int = 0,
    // 当前note总录音时长
    var totalRecordDuration: Long = 0,
    // 当前note有多少图片
    var totalImgCount: Int = 0,
    // 当前note的背景id
    var backgroundId: Int = 0,
)

/**
 * 笔刷使用统计
 */
data class PenUsageStats(
    var penType: DoodlePen? = null,
    var penUsageStartMillis: Long = 0L,
    var lastPenStyle: PenStyle? = null
)

/**
 * 分析编辑界面使用情况
 *
 * 用于上报埋点
 */
object AnalyticsEditScreenModel : AbsAnalyticsSubModel() {
    private const val TAG = "AnalyticsEditScreenModel"
    private var richTextState = AnalyticsHandWritingTextModel.richTextStateFlow
    private var strokeStyleStateFlow = AnalyticsHandWritingTextModel.strokeStyleStateFlow
    private var editModeStateFlow = AnalyticsHandWritingTextModel.editModeStateFlow
    private var penSelectionStateFlow = AnalyticsHandWritingTextModel.penSelectionStateFlow

    suspend fun init() = coroutineScope {
        launch { reportEditDurationV2() }  // 使用二期版本的编辑时长统计
        launch { reportNoteContent() }     // 仍使用TEXT模式的内容统计
        launch { reportHandWritingPenDurationV2() } // 使用二期版本的笔刷使用时长统计
    }

    /**
     * 上报画笔使用的时长, 实际上报
     */
    private fun reportPenUsage(penType: DoodlePen?, penUsageStartMillis: Long) {
        when (penType) {
            DoodlePen.FountainPen -> {
                TclAnalytics.reportPenUsageWhenSaving(
                    "0", // 未实现
                    (System.currentTimeMillis() - penUsageStartMillis).toString()
                )
            }

            DoodlePen.Ballpen -> {
                TclAnalytics.reportBallPenUsageWhenSaving(
                    "0",// 未实现
                    (System.currentTimeMillis() - penUsageStartMillis).toString()
                )
            }

            DoodlePen.Markpen -> {
                TclAnalytics.reportMarkPenUsageWhenSaving(
                    "0",// 未实现
                    (System.currentTimeMillis() - penUsageStartMillis).toString()
                )
            }

            null -> {}
        }
    }

    /**
     * 根据PenStyle获取对应的DoodlePen类型
     */
    private fun getDoodlePenFromPenStyle(penStyle: PenStyle?): DoodlePen? {
        return when (penStyle) {
            is PenStyle.PenFountain -> DoodlePen.FountainPen
            is PenStyle.BallPen -> DoodlePen.Ballpen
            is PenStyle.MarkerPen -> DoodlePen.Markpen
            is PenStyle.Pencil -> DoodlePen.Ballpen // 铅笔按圆珠笔统计
            else -> null
        }
    }

    /**
     * 上报画笔使用的时长
     */
    private suspend fun reportHandWritingPenDuration() = coroutineScope {
            var penType: DoodlePen? = null
            var penUsageStartMillis = 0L
            launch {
                strokeStyleStateFlow.collect { strokeStyleState ->
//                    Logger.d(TAG, "strokeStyleStateFlow: $strokeStyleState")
                    // 切换笔时，上报
                    if (penUsageStartMillis > 0
                        && penType != null
                        && penType != strokeStyleState?.doodlePen
                    ) {
                        reportPenUsage(penType, penUsageStartMillis)
                        penUsageStartMillis = 0L
                    }

                    // 设置笔类型
                    penType = strokeStyleState?.doodlePen
                    // 如果是画笔状态，则开始记录使用时间
                    if (richTextState.value?.editMode == true
                        && richTextState.value?.bottomMenuType == MenuBar.BRUSH
                    ) {
                        penUsageStartMillis = System.currentTimeMillis()
                    }
                }
            }

            launch {
                // 结束编辑时，也要上报
                richTextState.collect { richTextState ->
                    if (richTextState?.editMode == true
                        && richTextState.bottomMenuType == MenuBar.BRUSH
                        && richTextState.brushMenuType == BrushMenu.PEN
                    ) {
                        penUsageStartMillis = System.currentTimeMillis()
                    }

                    if (richTextState?.editMode == false && penType != null && penUsageStartMillis > 0) {
                        reportPenUsage(penType, penUsageStartMillis)
                        penType = null
                        penUsageStartMillis = 0L
                    }
                }
            }
        }

    /**
     * 上报画笔使用的时长 - 二期版本，基于PenToolbarViewModel的笔刷选择
     */
    private suspend fun reportHandWritingPenDurationV2() = coroutineScope {
        val penUsageStats = PenUsageStats()
        
        // 监听笔刷选择变化（来自PenToolbarViewModel）
        launch {
            penSelectionStateFlow.collect { selectedPenStyle ->
                //Logger.d(TAG, "penSelectionStateFlow: $selectedPenStyle")
                
                // 切换笔时，上报之前的使用时长
                if (penUsageStats.penUsageStartMillis > 0L
                    && penUsageStats.penType != null
                    && penUsageStats.lastPenStyle != selectedPenStyle
                ) {
                    //Logger.e(TAG, "切换笔刷上报: 从${penUsageStats.penType}切换到${getDoodlePenFromPenStyle(selectedPenStyle)}, 使用时长: ${System.currentTimeMillis() - penUsageStats.penUsageStartMillis}ms")
                    reportPenUsage(penUsageStats.penType, penUsageStats.penUsageStartMillis)
                }
                
                // 更新当前笔刷类型
                penUsageStats.penType = getDoodlePenFromPenStyle(selectedPenStyle)
                penUsageStats.lastPenStyle = selectedPenStyle
                
                // 选择笔刷时就开始计时（如果还没有开始计时的话）
                if (penUsageStats.penUsageStartMillis == 0L && penUsageStats.penType != null) {
                    penUsageStats.penUsageStartMillis = System.currentTimeMillis()
                    //Logger.d(TAG, "选择笔刷开始计时: ${penUsageStats.penType}")
                }
            }
        }
        
        // 监听编辑模式变化，确定笔刷使用时机
        launch {
            editModeStateFlow.collect { editMode ->
                //Logger.d(TAG, "editModeStateFlow for pen: $editMode")
                
                when (editMode) {
                    EditMode.DRAW -> {
                        // 进入绘画模式，开始计时
                        if (penUsageStats.penUsageStartMillis == 0L && penUsageStats.penType != null) {
                            penUsageStats.penUsageStartMillis = System.currentTimeMillis()
                            //Logger.d(TAG, "开始笔刷使用计时，笔刷类型：${penUsageStats.penType}")
                        }
                    }
                    
                    EditMode.TEXT, EditMode.PREVIEW -> {
                        // 退出绘画模式，结束计时并上报
                        if (penUsageStats.penUsageStartMillis > 0L && penUsageStats.penType != null) {
                            reportPenUsage(penUsageStats.penType, penUsageStats.penUsageStartMillis)
                            //Logger.d(TAG, "结束笔刷使用计时，笔刷类型：${penUsageStats.penType}")
                            penUsageStats.penUsageStartMillis = 0L
                        }
                    }
                    
                    is EditMode.LOADING -> {

                    }

                    null -> {
                        // 页面关闭，如果还在使用笔刷则上报
                        if (penUsageStats.penUsageStartMillis > 0L && penUsageStats.penType != null) {
                            reportPenUsage(penUsageStats.penType, penUsageStats.penUsageStartMillis)
                            //Logger.d(TAG, "页面关闭结束笔刷使用，笔刷类型：${penUsageStats.penType}")
                            penUsageStats.penUsageStartMillis = 0L
                        }
                    }

                }
            }
        }
    }

    /**
     * 上报编辑后的内容, 用于上报埋点
     *
     */
    private suspend fun reportNoteContent() {
        var editStats = EditStats()
        var isFirstLoad = true
        
        richTextState.collect { richTextState ->
            //Logger.d(TAG, "reportNoteContent - richTextState: editMode=${richTextState?.editMode}, note=${richTextState?.note?.noteId}, title.length=${richTextState?.title?.length}, contents.size=${richTextState?.contents?.size}")
            
            if (richTextState?.note == null) {
                // 首次进入时，note会为null，应该是bug，直接返回不上报
                editStats = EditStats()
                isFirstLoad = true
                return@collect
            }

            when (richTextState.editMode) {
                true -> {
                    // 编辑模式：如果是首次加载，重置统计对象
                    if (isFirstLoad) {
                        editStats = EditStats()
                        isFirstLoad = false
                        //Logger.d(TAG, "开始编辑模式，重置统计数据")
                    }
                }

                // 编辑结束或预览模式，上报统计数据
                false -> {
                    if (!isFirstLoad) { // 确保不是首次加载就触发上报
                        editStats.totalWordCount = richTextState.title.length

                        richTextState.contents.forEachIndexed { index, content ->
                            //Logger.d(TAG, "内容块[$index]: ${content::class.simpleName}")
                            when (content) {
                                is EditorContent.ImageBlock -> {
                                    editStats.totalImgCount += 1
                                    //Logger.d(TAG, "图片块: ${content.uri}")
                                }

                                is EditorContent.AudioBlock -> {
                                    var audioDuration = content.audioDuration
                                    if (audioDuration <= 0) {
                                        audioDuration = getAudioDuration(content.audioPath)
                                    }
                                    editStats.totalRecordCount += 1
                                    editStats.totalRecordDuration += audioDuration/1000
                                    //Logger.d(TAG, "音频块: ${content.audioPath}")
                                }

                                is EditorContent.TodoBlock -> {
                                    //Logger.d(TAG, "TODO块: ${content.text}")
                                }

                                is EditorContent.TextBlock -> {
                                    val textLength = content.text.text.length
                                    editStats.totalWordCount += textLength
                                    //Logger.d(TAG, "文本块: 长度=$textLength, 内容='${content.text.text.take(50)}...'")
                                }

                                is EditorContent.RichTextV2 -> {
                                    // 二期版本：RichTextV2的实际内容已经被转换为TextBlock
                                    // 在loadRichTextViewModel2中处理，这里不需要额外统计
                                    //Logger.d(TAG, "RichTextV2块: 已转换为TextBlock处理")
                                }
                                
                                else -> {
                                    //Logger.d(TAG, "未知内容块类型: ${content::class.simpleName}")
                                }
                            }
                        }
                        
                        val bgMode = AnalyticsHandWritingTextModel.getSkinName()
                        //Logger.d(TAG, "上报笔记内容统计：wordCount=${editStats.totalWordCount}, imgCount=${editStats.totalImgCount}, audioCount=${editStats.totalRecordCount}, audioDuration=${editStats.totalRecordDuration}, bgMode=$bgMode")

                        TclAnalytics.reportNoteContentWhenSaving(
                            editStats.totalWordCount.toString(),
                            AnalyticsHandWritingTextModel.getSkinName().toString(),
                            editStats.totalImgCount.toString(),
                            editStats.totalRecordCount.toString(),
                            (editStats.totalRecordDuration).toString(),
                        )
                        editStats = EditStats()
                    }
                }
            }
        }
    }

    /**
     * 上报编辑时长 - 二期版本，基于原始EditMode统计总编辑时长（TEXT + DRAW）
     */
    private suspend fun reportEditDurationV2() {
        var editStartMillis = 0L
        editModeStateFlow.collect { editMode ->
            when (editMode) {
                EditMode.TEXT,
                EditMode.DRAW -> {
                    // 开始编辑（TEXT或DRAW模式）
                    if (editStartMillis == 0L) {
                        editStartMillis = System.currentTimeMillis()
                        //Logger.d(TAG, "开始编辑计时，模式：$editMode")
                    }
                }
                
                EditMode.PREVIEW -> {
                    // 结束编辑，进入预览模式
                    if (editStartMillis > 0L) {
                        val duration = System.currentTimeMillis() - editStartMillis
                        //Logger.d(TAG, "预览模式结束编辑，编辑时长：${duration}ms")
                        TclAnalytics.reportEditDuration(duration.toString())
                        editStartMillis = 0L
                    }
                }

                is EditMode.LOADING -> {

                }

                null -> {
                    // 页面关闭或ViewModel销毁，如果还在编辑状态则上报编辑时长
                    if (editStartMillis > 0L) {
                        val duration = System.currentTimeMillis() - editStartMillis
                        //Logger.d(TAG, "页面关闭结束编辑，编辑时长：${duration}ms")
                        TclAnalytics.reportEditDuration(duration.toString())
                        editStartMillis = 0L
                    }
                }

            }
        }
    }

    /**
     * 上报编辑时长 - 一期版本（保留作为备用）
     */
    private suspend fun reportEditDuration() {
        var editStartMillis = 0L
        richTextState.distinctUntilChanged { old, new ->
            old?.editMode == new?.editMode
        }.collect { richTextState ->
            if (richTextState?.note == null) {
                // 首次进入时，note会为null，应该是bug，直接返回不上报
                editStartMillis = 0L
                return@collect
            }

            when (richTextState.editMode) {
                true -> {
                    editStartMillis = System.currentTimeMillis()
                }

                false -> {
                    if (editStartMillis > 0L) {
                        // 结束编辑，上报编辑时长
                        TclAnalytics.reportEditDuration((System.currentTimeMillis() - editStartMillis).toString())
                        editStartMillis = 0L
                    }
                }

                null -> editStartMillis = 0L
            }
        }
    }
}