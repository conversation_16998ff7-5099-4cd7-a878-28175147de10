package com.tcl.ai.note.home.components.notelist

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.components.BottomOperateViewHeight
import com.tcl.ai.note.home.components.HomeAnimatedVisibility
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.getNavigationBarHeight
import com.tcl.ai.note.utils.isButtonNavigation
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.clickableHover

@Composable
internal fun NoteContentBottomOperateView(
    modifier: Modifier = Modifier,
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    if (!homeNoteUiState.isShowOperateView) {
        return
    }
    val hasNavigationBar = isButtonNavigation()
    var navigationBarHeight = getNavigationBarHeight()
    val backgroundColor = if (isTablet)  colorResource(R.color.home_note_list_bg_color) else TclTheme.colorScheme.bottomBarColor
    // 底部菜单
    HomeAnimatedVisibility(
        visible = homeNoteUiState.isShowOperateView,
        enableFadeEffect = false,
        modifier = modifier
            .fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .background(color = backgroundColor)
                .padding(bottom = navigationBarHeight), // 增加底部间距，为导航按钮留出空间
            horizontalArrangement = Arrangement.Center
        ) {
            val move = stringResource(R.string.move)
            BottomActionButton(
                iconResId = R.drawable.ic_home_move,
                text = move,
                isEnabled = homeNoteUiState.isButtonOperateEnable,
                contentDescription = move,
                onClick = { onAction(HomeNoteListAction.OnMoveToCategory) }
            )
            val delete = stringResource(R.string.delete)
            BottomActionButton(
                iconResId = R.drawable.ic_home_delete,
                text = delete,
                isEnabled = homeNoteUiState.isButtonOperateEnable,
                contentDescription = delete,
                onClick = { onAction(HomeNoteListAction.OnShowDeleteDialog(true)) }
            )
        }
    }
}


/**
 * 自定义悬浮操作按钮
 */
@Composable
private fun BottomActionButton(
    iconResId: Int,
    text: String,
    isEnabled: Boolean = true,
    contentDescription: String? = null,
    onClick: () -> Unit
) {
    // 动画颜色过渡
    val enabledColor = colorResource(R.color.home_note_list_bottom_icon_hint_color)
    val disabledColor = colorResource(R.color.home_note_list_bottom_icon_hint_disable_color)

    val animatedIconColor by animateColorAsState(
        targetValue = if (isEnabled) enabledColor else disabledColor,
        animationSpec = tween(durationMillis = 300),
        label = "icon_color_animation"
    )

    Column(
        modifier = Modifier
            .size(180.dp, BottomOperateViewHeight)
            .clickableHover(
                onClick = onClick,
                role = Role.Button,
                enabled = isEnabled
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Image(
            painterResource(id = iconResId),
            contentDescription = contentDescription,
            modifier = Modifier.size(24.dp),
            colorFilter = ColorFilter.tint(animatedIconColor)
        )
        Text(
            text = text,
            fontSize = 12.sp,
            color = animatedIconColor
        )
    }
}