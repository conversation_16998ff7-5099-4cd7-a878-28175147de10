package com.tcl.ai.note.dashboard.intent

import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem


/**
 * 配置界面意图
 */
sealed class ConfigIntent {
    /**
     * 切换模式
     */
    data class ChangeViewType(val viewType: String) : ConfigIntent()

    /**
     * 获取首页笔记数据列表
     */
    object GetNotes : ConfigIntent()

    /**
     * 获取初始化的分类数据
     */
    object GetCategories : ConfigIntent()

    /**
     * 添加一个分类
     */
    data class AddCategory(val category: NoteCategory, val isPreviewMode: Boolean) : ConfigIntent()

    /**
     * 删除一个分类
     */
    data class DeleteCategory(
        val isDeleteSelectedCategoryNotes: Boolean,
        val category: NoteCategory
    ) : ConfigIntent()

    /**
     * 重命名一个分类
     */
    data class RenameCategory(val category: NoteCategory) : ConfigIntent()

    /**
     * 删除一个分类下的Note数据
     */
    data class DeleteNotesByCategoryId(val categoryId: Long) : ConfigIntent()

    /**
     * 删除指定的Notes
     */
    data class DeleteNotes(val listNotes: List<Long>) : ConfigIntent()

    /**
     * 删除一条Note
     */
    data class DeleteOneNote(val noteId: Long) : ConfigIntent()

    /**
     * 将选中的Notes移至指定的分类
     */
    data class UpdateNotesCategoryId(val categoryId: Long, val listNotes: List<Long>) :
        ConfigIntent()

    /**
     * 搜索笔记
     */
    data class SearchNotes(val text: String) : ConfigIntent()


    /**
     * 切换分类
     */
    data class UpdateSelectedCategory(val noteCategory: NoteCategory) : ConfigIntent()

    /**
     * 更新当前预览Note
     */
    data class UpdatePreviewNote(val noteId:Long) : ConfigIntent()
    /**
     * 更新单条指定 Note 的 categoryId
     */
    data class UpdateNoteCategoryId(val noteId: Long, val categoryId: Long) : ConfigIntent()


    /**
     * 更新编辑模式状态
     */
    data class UpdateEditMode(val enabled: Boolean) : ConfigIntent()

    /**
     * 更新长按下选中的notes数据
     */
    data class UpdateSelectedNotes(val selectedNotes: List<NoteListItem>) : ConfigIntent()

    data class UpdateSelectedItemCounts(val selectedItemCounts: Set<Int>, var isSelectAll: Boolean = false) : ConfigIntent()

    data class UpdateIsSelectedAllMode(val isSelectedAllMode: Boolean) : ConfigIntent()

    data class UpdateIsDeleteDialogShown(val isDeleteDialogShown: Boolean) : ConfigIntent()

}

