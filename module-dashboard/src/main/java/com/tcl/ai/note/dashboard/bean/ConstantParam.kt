package com.tcl.ai.note.dashboard.bean

// 编辑页面底部一级菜单类型常量
object MenuTypes {
    const val TODO = "TODO" // 待办事项
    const val FONT = "FONT" // 字体
    const val BRUSH = "BRUSH" // 画笔
    const val SKIN = "SKIN" // 皮肤
    const val PICTURE = "PICTURE" // 选图
    const val AUDIO = "AUDIO" // 录音
    const val AI = "AI" // AI
}

// 文字样式常量
object FontStyles {
    const val BULLETED = "BULLETED" // 黑点段落符
    const val NUMBERED = "NUMBERED" // 序号段落符
    const val BOLD = "BOLD" // 粗体
    const val ITALIC = "ITALIC" // 斜体
    const val UNDERLINE = "UNDERLINE" // 下划线
}

// 画笔菜单
object BrushMenu{
    const val HANDWRITING_BEAUTIFICATION = "HANDWRITING_BEAUTIFICATION" // 笔迹美化
    const val PEN = "PEN" // 钢笔
    const val COLOR_SELECTOR = "COLORS_ELECTOR" // 颜色选择器
    const val ERASER = "ERASER" // 橡皮擦
    const val CONVERT_TEXT = "CONVERT_TEXT" // 转文字
    const val BRUSH_TOOL = "BRUSH_TOOL" // 画笔工具
}

// 画笔类型常量
object PenTypes {
    const val PEN = "PEN" // 钢笔
    const val MARKER = "MARKER" // 马克笔
    const val BALLPOINT = "BALLPOINT" // 圆珠笔
}



