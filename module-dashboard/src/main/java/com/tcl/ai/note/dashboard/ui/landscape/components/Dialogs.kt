package com.tcl.ai.note.dashboard.ui.landscape.components

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.window.DialogProperties
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.appendSemanticsButton
import com.tcl.ai.note.utils.globalDialogWidth
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton
import com.tct.theme.core.designsystem.component.TclWarningButton
import com.tct.theme.core.designsystem.theme.TclTheme

/**
 * 数据删除对话框组件
 * @param onDismiss 当对话框被关闭时执行的操作
 * @param onDelete 当用户选择删除时执行的操作
 */
@Composable
fun DeleteDataDialog(
    text: String,
    onDismiss: () -> Unit,
    onDelete: () -> Unit
) {
    val context = LocalContext.current
    val contentDescription = context.getString(
        R.string.dialog_semantics_description,
        context.getString(R.string.dialog_type_semantics),                         // 对话框类型
        text,                                                                      // 主要内容
        context.getString(R.string.btn_cancel).appendSemanticsButton(),            // 取消按钮
        context.getString(R.string.menu_delete_recording).appendSemanticsButton()  // 删除按钮
    )
    TclTheme(dynamicColor = false) {
        TclDialog (
            show = true,
            dialogMaxWidth = globalDialogWidth(),
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(dismissOnClickOutside = true, dismissOnBackPress = true),
            content = {
                Text(
                    modifier = Modifier.semantics {
                        this.contentDescription = contentDescription
                    },
                    text = text
                )
            },
            actions = {
                TclTextButton(onClick = { onDismiss.invoke() }, contentColor = colorResource(id = R.color.home_title_color)) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel)) }
                TclWarningButton(onClick = { onDelete.invoke() }) { Text(stringResource(id = R.string.menu_delete_recording)) }
            },
        )
    }
}

@Preview
@Composable
private fun DeleteDataDialogPreview() {
    DeleteDataDialog(
        text = stringResource(id = R.string.delete),
        onDismiss = {},
        onDelete = {}
    )
}