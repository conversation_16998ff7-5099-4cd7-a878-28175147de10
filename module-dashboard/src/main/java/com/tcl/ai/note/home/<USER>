package com.tcl.ai.note.home

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.ui.Modifier
import com.tcl.ai.note.theme.NoteTclTheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class HomeActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            NoteTclTheme {
//                Scaffold(
//                    modifier = Modifier
//                        .fillMaxSize(),
//                    // 添加全局背景
//                ) { innerPadding ->
                    Box(modifier = Modifier.fillMaxSize()) {
//                        HomeScreenRoute()
                    }
//                }
            }
        }
    }
}

