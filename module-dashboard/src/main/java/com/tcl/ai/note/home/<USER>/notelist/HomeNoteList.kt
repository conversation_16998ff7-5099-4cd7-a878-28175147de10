package com.tcl.ai.note.home.components.notelist

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyGridState
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.dashboard.ui.NoNotesScreen
import com.tcl.ai.note.home.components.BottomOperateViewHeight
import com.tcl.ai.note.home.components.base.rememberGridColumnCount
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.home.vm.state.HomeContentListNotesUiState
import com.tcl.ai.note.utils.getNavigationBarHeight
import com.tcl.ai.note.home.components.base.rememberNoteLayoutConfig
import com.tcl.ai.note.home.vm.state.HomeTitleMode
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet

/**
 * 首页笔记列表组件
 * 
 * 根据不同的状态显示笔记列表内容，包括加载中、错误、搜索模式、空状态和成功状态
 * 
 * @param homeNoteUiState 首页笔记UI状态，包含笔记列表状态和其他界面相关状态
 * @param onAction 处理笔记列表操作的回调函数
 */
@Composable
fun HomeNoteList(
    modifier: Modifier = Modifier,
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    val listNotesUiState = homeNoteUiState.listNotesUiState
    Box(
        modifier = modifier
            .fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        when (listNotesUiState) {
            is HomeContentListNotesUiState.Loading -> {
                // 显示加载状态
                CircularProgressIndicator()
            }

            is HomeContentListNotesUiState.Error -> {
                // 显示错误状态
            }
            is HomeContentListNotesUiState.SearchMode -> {
                // 搜索模式 不显示任何内容
                Logger.d("HomeNoteList","SearchMode")
            }

            is HomeContentListNotesUiState.Empty -> {
                // 显示空状态给分类下列表为空的情况
                Logger.d("HomeNoteList","Empty")
                NoNotesScreen(homeNoteUiState.isSearchMode,isDialogShowing = homeNoteUiState.isCategoryDialogShowing)
            }

            is HomeContentListNotesUiState.Success -> {
                val homeNoteItems = listNotesUiState.homeContentListState.notes
                if (homeNoteItems.isEmpty()) {
                    Logger.d("HomeNoteList","homeNoteItems Empty")
                    NoNotesScreen(homeNoteUiState.isSearchMode,isDialogShowing = homeNoteUiState.isCategoryDialogShowing)
                } else {
                    // 显示成功状态
                    SuccessNoteList(homeNoteUiState, homeNoteItems, onAction)
                }
            }

        }

    }
}

@Composable
private fun SuccessNoteList(
    homeNoteUiState: HomeNoteUiState,
    homeNoteItemEntities: List<HomeNoteItemModel>,
    onAction: (HomeNoteListAction) -> Unit
) {
    val listState = rememberLazyListState()
    val gridState = rememberLazyGridState()
    val layoutConfig = rememberNoteLayoutConfig()
    val columnCount = rememberGridColumnCount()

    ScrollToListTop(homeNoteUiState, gridState, listState)
    val contentPadding = layoutConfig.contentPadding.dp
    val navigationBarHeight = getNavigationBarHeight()
    // 列表顶部间距
    val topSpacing = if (isTablet) 6.dp else 8.dp
    val bottomPadding = when {
        isTablet -> {
            val base = contentPadding + navigationBarHeight
            if (homeNoteUiState.isShowOperateView) base + BottomOperateViewHeight else base
        }
        else     -> {
            val base = contentPadding
            if (homeNoteUiState.isShowOperateView) base else base + navigationBarHeight
        }
    }

    if (homeNoteUiState.viewType == DataStoreParam.VIEW_TYPE_GRID) {
        // 笔记网格
        LazyVerticalGrid(
            columns = GridCells.Fixed(columnCount),
            state = gridState,
            horizontalArrangement = Arrangement.spacedBy(layoutConfig.gridItemSpacing.dp),
            verticalArrangement = Arrangement.spacedBy(layoutConfig.gridItemSpacing.dp),
            contentPadding = PaddingValues(
                start = contentPadding,
                end = contentPadding,
                top = topSpacing,
                bottom = bottomPadding,
            ),
            modifier = Modifier
                .fillMaxSize()
//                .editModeClickHandler(
//                    isEditMode = homeNoteUiState.isEditMode,
//                    onExitEditMode = { onAction(NoteListAction.OnChangeTitleMode(HomeTitleMode.Normal)) }
//                )
        ) {
            items(homeNoteItemEntities,
                key = { it.noteId }
            ) { note ->
                HomeNoteItemGridType(
                    modifier = if (homeNoteUiState.isEditMode) Modifier.fillMaxSize().animateItem() else Modifier.fillMaxSize(),
                    note = note,
                    editMode = homeNoteUiState.isEditMode,
                    checked = note.isChecked,
                    onItemClick = { _, isPen ->
                        onAction(HomeNoteListAction.OnAddHomeNoteClick(note.noteId, isPen))
                    },
                    onLongClick = { onAction(HomeNoteListAction.OnItemLongClick(note.noteId)) },
                    onCheckedChange = { checked ->
                        onAction(
                            HomeNoteListAction.OnItemCheckedChange(
                                note.noteId,
                                checked
                            )
                        )
                    }
                )
            }
        }
    } else {
        // item 列表
        LazyColumn(
            state = listState,
            verticalArrangement = Arrangement.spacedBy(layoutConfig.listItemSpacing.dp),
            contentPadding = PaddingValues(
                start = contentPadding,
                end = contentPadding,
                top = topSpacing,
                bottom = bottomPadding
            ),
            modifier = Modifier
                .fillMaxSize()
//                .editModeClickHandler(
//                    isEditMode = homeNoteUiState.isEditMode,
//                    onExitEditMode = { onAction(NoteListAction.OnChangeTitleMode(HomeTitleMode.Normal)) }
//                )
        ) {
            items(homeNoteItemEntities) { note ->
                NoteListItemListType(
                    note = note,
                    editMode = homeNoteUiState.isEditMode,
                    checked = note.isChecked,
                    onItemClick = { onAction(HomeNoteListAction.OnAddHomeNoteClick(note.noteId)) },
                    onCheckedChange = { checked ->
                        onAction(
                            HomeNoteListAction.OnItemCheckedChange(
                                note.noteId,
                                checked
                            )
                        )
                    },
                    onLongClick = { onAction(HomeNoteListAction.OnItemLongClick(note.noteId)) }
                )
            }
        }
    }
}

@Composable
private fun ScrollToListTop(
    homeNoteUiState: HomeNoteUiState,
    gridState: LazyGridState,
    listState: LazyListState
) {
    // 分类ID不同 滑动到顶部
    val previousCategoryId = rememberSaveable { mutableStateOf(homeNoteUiState.selectedCategoryId) }
    // 标题模式不同 滑动到顶部
    val previousTitleMode = rememberSaveable { mutableStateOf(homeNoteUiState.titleMode) }

    // 监听分类变化，只有前后不一样才滚动到顶部
    LaunchedEffect(homeNoteUiState.selectedCategoryId, homeNoteUiState.titleMode) {
        val currentCategoryId = homeNoteUiState.selectedCategoryId
        val currentTitleMode = homeNoteUiState.titleMode
        if (previousTitleMode.value != currentTitleMode&&currentTitleMode == HomeTitleMode.Normal) {
            Logger.d(
                "HomeNoteList",
                "TitleMode changed from ${previousTitleMode.value} to $currentTitleMode, scrolling to top"
            )
            previousTitleMode.value = currentTitleMode
            scrollToTop(homeNoteUiState, gridState, listState)
        }
        if (previousCategoryId.value != currentCategoryId) {
            Logger.d(
                "HomeNoteList",
                "Category changed from ${previousCategoryId.value} to $currentCategoryId, scrolling to top"
            )
            previousCategoryId.value = currentCategoryId
            scrollToTop(homeNoteUiState, gridState, listState)
        } else {
            Logger.d("HomeNoteList", "Category unchanged, skip scrolling: $currentCategoryId")
        }
    }
}

private fun scrollToTop(
    homeNoteUiState: HomeNoteUiState,
    gridState: LazyGridState,
    listState: LazyListState
) {
    if (homeNoteUiState.viewType == DataStoreParam.VIEW_TYPE_GRID) {
        gridState.requestScrollToItem(0)
    } else {
        listState.requestScrollToItem(0)
    }
}

/**
 * 编辑模式下点击空白区域回到正常模式的扩展函数
 */
@Composable
private fun Modifier.editModeClickHandler(
    isEditMode: Boolean,
    onExitEditMode: () -> Unit
): Modifier {
    return this.then(
        if (isEditMode) {
            Modifier.pointerInput(Unit) {
                detectTapGestures(
                    onTap = {
                        // 编辑模式下点击空白区域回到正常模式
                        onExitEditMode()
                    }
                )
            }
        } else {
            Modifier
        }
    )
}

