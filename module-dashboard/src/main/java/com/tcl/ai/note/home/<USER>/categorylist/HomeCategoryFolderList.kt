package com.tcl.ai.note.home.components.categorylist

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.home.vm.action.HomeCategoryAction
import com.tcl.ai.note.home.vm.state.HomeCategoryUiState

/**
 * 文件夹 列表行 过渡版不上
 */
@Composable
fun ColumnScope.FolderList(
    modifier: Modifier = Modifier,
    homeCategoryUiState: HomeCategoryUiState,
    onAction: (HomeCategoryAction) -> Unit,
    onCategorySelected: (String) -> Unit
) {
    // 控制文件夹区域展开/收起状态
    var isFoldersExpanded by remember { mutableStateOf(true) }
    Spacer(modifier = Modifier.height(24.dp))

    // Folders 部分标题（可点击）
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { isFoldersExpanded = !isFoldersExpanded }
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.Companion.CenterVertically
    ) {
        Text(
            text = "Folders",
            style = MaterialTheme.typography.titleMedium,
            color = Color.Companion.Gray
        )
//        Icon(
//            if (isFoldersExpanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
//            contentDescription = if (isFoldersExpanded) "Collapse" else "Expand",
//            tint = Color.Gray,
//            modifier = Modifier.size(20.dp)
//        )

    }

    // 文件夹列表（带动画）
    AnimatedVisibility(
        visible = isFoldersExpanded,
    ) {
        Column {
            Spacer(modifier = Modifier.height(16.dp))

            homeCategoryUiState.folders.forEach { folder ->
                CategoryItem(
                    category = folder,
                    isSelected = homeCategoryUiState.selectedCategoryId == folder.id,
                    onClick = { onCategorySelected(folder.name ?: "") },
                    onLongClick = { /* Handle long click */ }
                )
            }

            Spacer(modifier = Modifier.height(16.dp))
        }
    }

    Spacer(modifier = Modifier.weight(1f))
}