package com.tcl.ai.note.dashboard.view

import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.ui.geometry.Rect
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.tcl.ai.note.handwritingtext.ui.EditScreen2
import com.tcl.ai.note.home.screen.HomeScreenPager
import com.tcl.ai.note.journaldashboard.ui.ROUTE_ADD_PAGE_SCREEN
import com.tcl.ai.note.journaldashboard.ui.ROUTE_JOURNAL_CONTENT_SCREEN
import com.tcl.ai.note.navigation.NavigationUtils.ROUTE_EDIT_SCREEN_WITH_PARAMS
import com.tcl.ai.note.navigation.NavigationUtils.ROUTE_MAIN_SCREEN
import com.tcl.ai.note.setting.version.VersionRoute
import com.tcl.ai.note.utils.getCurCoverRect

private const val PARAM_NOTE_ID = "noteId"
private const val PARAM_IS_LANDSCAPE = "isLandscape"
const val ROUTE_CHECK_UPDATE = "check_update"

fun NavGraphBuilder.homeScreen(navController: NavController, easing: CubicBezierEasing) {
    var showBlur = false
    composable(
        route = ROUTE_MAIN_SCREEN,
        exitTransition = {
            if (targetState.destination.route?.contains(ROUTE_JOURNAL_CONTENT_SCREEN) == true) {
                fadeOut(targetAlpha = 1f, animationSpec = tween(600))
            } else {
                null
            }
        },
        popEnterTransition = {
            if (getCurCoverRect() != Rect.Zero
                && (initialState.destination.route?.contains(ROUTE_ADD_PAGE_SCREEN) == true
                        || initialState.destination.route?.contains(ROUTE_JOURNAL_CONTENT_SCREEN) == true)
            ) {
                showBlur = true
                fadeIn(initialAlpha = 1f, animationSpec = tween(600))
            } else {
                null
            }
        }
    ) { backStackEntry ->
        HomeScreenPager(
            navController,
            showBlur = showBlur
        )
        showBlur = false
    }
}

fun NavGraphBuilder.editScreen(navController: NavController) {
    composable(
        route = ROUTE_EDIT_SCREEN_WITH_PARAMS,
        arguments = listOf(
            navArgument(PARAM_NOTE_ID) {
                type = NavType.StringType
                defaultValue = null  // 设置为可空参数
                nullable = true
            },
            navArgument("isPen") {
                type = NavType.BoolType
                defaultValue = false // 默认值设为false
            },
            navArgument(PARAM_IS_LANDSCAPE) {
                type = NavType.BoolType
                defaultValue = false // 默认值设为false
            },
        )
    ) { backStackEntry ->

        // 获取参数（可能为null表示新建）
        val isPen = backStackEntry.arguments?.getBoolean("isPen")
        val noteIdString = backStackEntry.arguments?.getString(PARAM_NOTE_ID)
        val noteId = noteIdString?.toLongOrNull()

        EditScreen2(
            noteId = noteId,
            isPen = isPen == true,
        )
    }
}

fun NavGraphBuilder.checkUpdateScreen(navController: NavController) {
    composable(
        route = ROUTE_CHECK_UPDATE,
    ) {
        VersionRoute(onBackClick = { navController.navigateUp()})
    }
}