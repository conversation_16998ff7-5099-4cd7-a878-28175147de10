package com.tcl.ai.note.dashboard.ui

import NoteCategoryScreen
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.unit.dp

@Composable
fun NoteCategoryScreenPlaceholder(editMode: <PERSON><PERSON>an, isSearching: <PERSON>olean) {
    var measuredHeight by remember { mutableStateOf(0.dp) }
    SubcomposeLayout { constraints ->
        if (measuredHeight == 0.dp) {
            // 1. 先测量 NoteCategoryScreen 的高度
            val noteCategoryPlaceable = subcompose("NoteCategoryScreen") {
                NoteCategoryScreen()
            }.first().measure(constraints)
            measuredHeight = noteCategoryPlaceable.height.toDp()
        }

        // 2. 根据 editMode 决定渲染内容
        val contentPlaceable = subcompose("Content") {
            if (!editMode && !isSearching) {
                NoteCategoryScreen()
            } else {
                // 渲染一个相同高度的空 Box
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(measuredHeight)
                )
            }
        }.first().measure(constraints)

        // 3. 布局
        layout(contentPlaceable.width, contentPlaceable.height) {
            contentPlaceable.place(0, 0)
        }
    }
}