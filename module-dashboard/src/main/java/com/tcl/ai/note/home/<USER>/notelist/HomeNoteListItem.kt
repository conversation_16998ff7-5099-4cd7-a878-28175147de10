package com.tcl.ai.note.home.components.notelist

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tct.theme.core.designsystem.component.TclCheckbox
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.Dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.components.HighlightText
import com.tcl.ai.note.home.components.HomeNoteTitleTextStyle
import com.tcl.ai.note.home.components.HomeTitleTextStyle
import com.tcl.ai.note.home.components.notelist.content.TopCardContentContainer
import com.tcl.ai.note.home.components.preview.HomeNoteItemPreviewParameterProvider
import com.tcl.ai.note.home.utils.rememberScaledCardSize
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.combinedClickable

/**
 * 列表模式下的笔记Item
 */
@Composable
fun NoteListItemListType(
    note: HomeNoteItemModel,
    modifier: Modifier = Modifier,
    editMode: Boolean = false,
    checked: Boolean = false,
    onItemClick: (String) -> Unit,
    onCheckedChange: ((Boolean) -> Unit)? = null,
    onLongClick: (() -> Unit)? = null
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White)
            .combinedClickable(
                onClick = {
                    onItemClick(note.noteId)
                },
                onLongClick = onLongClick
            )
            .padding(12.dp)
    ) {
        if (editMode) {
            TclCheckbox(
                checked = checked,
                onCheckedChange = onCheckedChange,
                modifier = Modifier.padding(end = 8.dp)
            )
        }
        Column(modifier = Modifier.weight(1f)) {
            // 标题高亮显示
            HighlightText(
                text = note.noteTitle,
                normalStyle = HomeTitleTextStyle,
                highlights = note.highlightInfo?.titleHighlights ?: emptyList()
            )
            Spacer(modifier = Modifier.height(4.dp))
            // 内容摘要高亮显示
            HighlightText(
                text = note.summary ?: "",
                normalStyle = MaterialTheme.typography.bodyMedium,
                highlights = note.highlightInfo?.contentHighlights ?: emptyList(),
                maxLines = 1
            )
        }
    }
}

/**
 * 网格模式下的笔记Item
 */
@Composable
fun HomeNoteItemGridType(
    modifier: Modifier = Modifier,
    note: HomeNoteItemModel,
    editMode: Boolean = false,
    checked: Boolean = false,
    onItemClick: (String, Boolean) -> Unit = { _, _ -> },
    onCheckedChange: ((Boolean) -> Unit)? = null,
    onLongClick: (() -> Unit)? = null
) {
    val (scaleCardWidth, scaleCardHeight) = rememberScaledCardSize()
    Column(
        modifier = modifier
    ) {
        TopCardContentContainer(
            modifier = Modifier
                .fillMaxWidth()
                .height(scaleCardHeight.dp),
            editMode,
            onCheckedChange,
            checked,
            onItemClick,
            note,
            onLongClick
        )
        Spacer(modifier = Modifier.height(8.dp))
        ItemTitle(note, scaleCardWidth.dp)
    }
}

@Composable
private fun ItemTitle(note: HomeNoteItemModel, cardWidth: Dp) {
    // 获取显示标题：优先使用 titleResId，否则使用 title
    val title = note.titleResId?.let { stringResource(it) } ?: note.noteTitle

    // 标题
    HighlightText(
        text = title,
        normalStyle = HomeNoteTitleTextStyle,
        highlights = note.highlightInfo?.titleHighlights ?: emptyList()
    )

    val density = LocalDensity.current

    Spacer(modifier = Modifier.height(4.dp))
    // 为音频图标预留固定空间，避免被文本挤压
    val iconSize = if (isTablet) 14.dp else 10.dp
    val audioIconSpace = if (note.showAudioIcon) iconSize+4.dp else 0.dp // 图标14dp + 间距4dp
    val textMaxWidth = cardWidth - audioIconSpace

    // 日期和音频图标 - 使用固定布局避免挤压
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth()
    ) {
        Text(
            text = note.date,
            fontSize = 12.sp,
            lineHeight = with(density) { 14.dp.toSp() },
            color = colorResource(R.color.text_summary),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.widthIn(max = textMaxWidth)
        )

        if (note.showAudioIcon) {
            Spacer(modifier = Modifier.width(2.dp))
            Icon(
                painter = painterResource(id = R.drawable.ic_note_audio),
                contentDescription = stringResource(R.string.audio_title),
                modifier = Modifier
                    .size(iconSize)
                    .wrapContentSize(), // 确保图标不被压缩
                tint = colorResource(R.color.text_summary)
            )
        }
    }
}


@Preview(showSystemUi = false, showBackground = true)
@Composable
private fun HomeNoteItemCardParameterizedPreview(
    @PreviewParameter(HomeNoteItemPreviewParameterProvider::class) note: HomeNoteItemModel
) {
    Box(modifier = Modifier.size(100.dp, 200.dp)) {
        HomeNoteItemGridType(
            note = note,
            editMode = true,
            checked = note.isChecked,
            onItemClick = { _, _ -> },
            onCheckedChange = {},
            onLongClick = {}
        )

    }
}