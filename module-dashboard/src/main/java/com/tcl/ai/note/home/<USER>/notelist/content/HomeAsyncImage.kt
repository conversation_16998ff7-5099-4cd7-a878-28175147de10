package com.tcl.ai.note.home.components.notelist.content
import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import coil3.compose.rememberAsyncImagePainter
import coil3.compose.rememberConstraintsSizeResolver
import coil3.gif.GifDecoder
import coil3.imageLoader
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.tcl.ai.note.base.R
import com.tcl.ai.note.home.model.HomeNoteItemModel
import com.tcl.ai.note.home.utils.rememberScaledCardSize
import com.tcl.ai.note.utils.Logger

/**
 * 异步加载图片组件
 * contentScale 默认为 FillBounds，能两边对齐,
 * ContentScale.Fit
 * ContentScale.Crop 更圆一点
 *
 */
@Composable
fun HomeAsyncImage(
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.FillBounds,
    data: Any?,
    imageCacheKey: String,
    contentDescription: String? = stringResource(R.string.image_title)
) {
    if (data != null) {
        val context = LocalContext.current
        Logger.d("HomeAsyncImage", "Loading image with URI: $data, cacheKey: $imageCacheKey")
        // 使用全局的ImageLoader，避免在每个列表项中重复创建
        val imageLoader = context.imageLoader.newBuilder().components {
            GifDecoder.Factory()
        }.build()
        val imageRequest = imageRequestBuilder(context, data, imageCacheKey).build()
        AsyncImage(
            model = imageRequest,
            imageLoader = imageLoader,
            contentDescription = contentDescription,
            contentScale = ContentScale.FillWidth, // 填充父容器
            alignment =Alignment.TopStart,
            modifier = modifier,
        )
    } else {
        Logger.w("HomeAsyncImage", "Image URI is null, skipping image loading")
    }
}

@Composable
internal fun HomeSizeImage(
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.FillBounds,
    data: Any?,
    imageCacheKey: String,
    contentDescription: String? = stringResource(R.string.image_title)
) {
    val context = LocalContext.current
    val sizeResolver = rememberConstraintsSizeResolver()
    val containerSize = rememberScaledCardSize()
    sizeResolver.size(containerSize.width.dp,containerSize.height.dp)
    val painter = rememberAsyncImagePainter(
        model = ImageRequest.Builder(context)
            .data(data)
            .crossfade(true)
            .memoryCacheKey(imageCacheKey)
            .diskCacheKey(imageCacheKey)
            .size(sizeResolver)
            .build(),
    )

    Image(
        painter = painter,
        contentDescription = contentDescription,
        modifier = modifier.then(sizeResolver),
        contentScale = contentScale,
    )
}

private fun imageRequestBuilder(
    context: Context,
    data: Any,
    imageCacheKey: String
): ImageRequest.Builder {
    val imageRequestBuilder = ImageRequest.Builder(context)
        .data(data)
        .crossfade(true)
        .memoryCacheKey(imageCacheKey)
        .diskCacheKey(imageCacheKey)
        .listener(
            onSuccess = { _, _ ->
                Logger.d("HomeAsyncImage", "Image loaded successfully: $data")
            },
            onError = { _, error ->
                Logger.e(
                    "HomeAsyncImage",
                    "Image loading failed for: $data, error: ${error.throwable.message}"
                )
            }
        )

    return imageRequestBuilder
}

/**
 * 根据夜间模式选择合适的缩略图
 */
@Composable
internal fun getImageUrlAndCacheKey(note: HomeNoteItemModel): Pair<String?, String> {
    var handwritingThumbnail = if (isSystemInDarkTheme()) {
        note.handwritingThumbnailDark ?: note.handwritingThumbnail
    } else {
        note.handwritingThumbnail
    }
    if (handwritingThumbnail.isNullOrEmpty()){
        handwritingThumbnail=note.image
    }
    val modifyTime= note.thumbnailLastModifyMillis

    val imageCacheKey = "${handwritingThumbnail}_${modifyTime}"
    Logger.d("getImageUrlAndCacheKey", "imageCacheKey: $imageCacheKey thumbnailLastModifyMillis: ${note.thumbnailLastModifyMillis} modifyTime:  ${note.modifyTime}")
    return Pair(handwritingThumbnail, imageCacheKey)
}

/**
 * 获取图片缩略图
 * ContentScale.Fit 图片不会变形 但手绘不会挨着边
 * ContentScale.FillBounds 手绘能挨着边
 */
internal fun getContentScale(note: HomeNoteItemModel): ContentScale {
    val isImageType = note.isOnlyImageType
    val isHasImgAndHandwriting = note.isHasImgAndHandwritingType
    return if (isHasImgAndHandwriting||isImageType) ContentScale.Fit else ContentScale.FillBounds
}