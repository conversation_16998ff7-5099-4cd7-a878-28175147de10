package com.tcl.ai.note.dashboard.repo


import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.dashboard.states.ConfigState
import com.tcl.ai.note.utils.SPUtils


object DashboardRepository {

    /**
     * 获取首页列表视图类型的方法
     * @return 视图类型字符串
     * DataStoreParam.KEY_VIEW_TYPE = "LIST" 列表形态
     * DataStoreParam.VIEW_TYPE_GRID = "GRID" 网络形态
     */
    private fun getViewType(): String? {
        return SPUtils.getString(
            key = DataStoreParam.KEY_VIEW_TYPE,
            defValue = DataStoreParam.VIEW_TYPE_LIST
        )
    }

    /**
     * 保存视图类型的方法
     * @param viewType 要保存的视图类型字符串
     */
    fun saveViewType(viewType: String){
        SPUtils.setSync(
            key = DataStoreParam.KEY_VIEW_TYPE,
            value = viewType
        )
    }

    /**
     * 加载默认配置的方法
     * @return 配置状态对象
     */
    fun loadDefaultConfig() : ConfigState {
        val storedViewType =  getViewType() ?: DataStoreParam.VIEW_TYPE_LIST
        return ConfigState(
            viewType = storedViewType,
        )
    }

}