package com.tcl.ai.note.setting.version

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import androidx.appcompat.app.AppCompatActivity
import com.tcl.ai.note.utils.Logger
import com.tct.smart.aota.check.ICheck
import com.tct.smart.aota.check.ICheckResult
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class UpdateMonitorImpl@Inject constructor(
    @ApplicationContext private val context: Context) : UpdateMonitor {
    private var checkService: ICheck? = null
    private var onUpdateResult: ((UpdateState) -> Unit)? = null
    @OptIn(DelicateCoroutinesApi::class)
    override fun checkUpdate(pageName: String, onUpdateResult: (UpdateState) -> Unit) {
        Logger.d("UpdateMonitorImpl", "checkUpdate: $pageName")
        GlobalScope.launch {
            withContext(Dispatchers.IO) {
                checkService?.let {
                    Logger.d("UpdateMonitorImpl", "checkUpdate: startCheck")
                    startCheck(pageName,onUpdateResult)
                }?: run {
                    //延时一秒模拟检查效果
                    Thread.sleep(1000)
                    Logger.d("UpdateMonitorImpl", "checkUpdate: bindAotaCheckService")
                    bindAotaCheckService(context,onUpdateResult)
                }
            }
        }
    }
    private fun startCheck(pageName: String,  onUpdateResult: (UpdateState) -> Unit) {
        Logger.d("UpdateMonitorImpl", "startCheck: $pageName")
        //启动Aota服务检查更新，参数 checkPackage: 检查的包名，checkResult: 检查结果回调
        checkService?.checkUpdate(pageName,   object : ICheckResult.Stub() {
            override fun onCheckResult(result: Int) {
                Logger.d("UpdateMonitorImpl", "onCheckResult: $result")
                val message = when (result) {
                    1 -> UpdateState.NetworkError
                    2 -> UpdateState.ServerError
                    3 -> UpdateState.LatestVersion
                    4 -> UpdateState.NewVersionAvailable
                    else -> UpdateState.Idle
                }
                onUpdateResult(message)
                unbindAotaCheckService() //获取结果之后解绑服务
            }
        })
    }
    private fun bindAotaCheckService(context: Context, onUpdateResult: (UpdateState) -> Unit){
        var result = false
        try {
            val intent = Intent("com.tct.smart.aota.action.CHECK_UPDATE")
            intent.`package` = "com.tct.smart.aota"
            result = context.bindService(intent, serviceConnection, AppCompatActivity.BIND_AUTO_CREATE)
            if(result){
                this.onUpdateResult = onUpdateResult
                Logger.d("UpdateMonitorImpl", "bindAotaCheckService success")
            }
        } catch (ex: Exception) {
            Logger.e("UpdateMonitorImpl", "bindAotaCheckService error: $ex")
        }
        finally {
            if(!result){
                onUpdateResult(UpdateState.NewVersionAvailable)
            }
        }
    }

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            GlobalScope.launch(Dispatchers.IO) {
                service?.let {
                    //绑定成功后，启动Aota服务检查更新，参数 checkPackage: 检查的包名，checkResult: 检查结果回调
                    checkService = ICheck.Stub.asInterface(it)
                    Logger.d("UpdateMonitorImpl", "onServiceConnected: $checkService")
                    checkService?.let {
                        Logger.d("UpdateMonitorImpl", "onServiceConnected: startCheck")
                        onUpdateResult?.let { resultCallback ->
                            startCheck(context.packageName, resultCallback)
                        } ?: run {
                            Logger.e("UpdateMonitorImpl", "onUpdateResult is null")
                        }
                    }
                }
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            checkService = null
        }
    }
    // 解除绑定
    fun unbindAotaCheckService() {
        try {
            onUpdateResult = null
            checkService?.let {
                context.unbindService(serviceConnection)
            }
            checkService = null
            Logger.d(TAG, "Service Unbound")
        } catch (ex: Exception) {
            Logger.e(TAG, "unbindAotaCheckService error: $ex")
        }
    }
    companion object {
        private const val TAG = "UpdateMonitorImpl"
    }
    enum class UpdateState {
        Idle,
        NetworkError,
        ServerError,
        LatestVersion,
        NewVersionAvailable
    }
}