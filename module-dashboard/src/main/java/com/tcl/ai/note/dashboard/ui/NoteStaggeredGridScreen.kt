package com.tcl.ai.note.dashboard.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel

import com.tcl.ai.note.base.R
import com.tcl.ai.note.dashboard.BuildConfig
import com.tcl.ai.note.dashboard.states.ListNoteCategoryState
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.handwritingtext.utils.invisibleSemantics
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.CommonUtils

/**
 * 瀑布流列表Item
 */
@OptIn(ExperimentalFoundationApi::class)
@SuppressLint("StateFlowValueCalledInComposition")
@Composable
internal fun GridViewItem(
    dashboardModel: DashboardModel = hiltViewModel(),
    item: NoteListItem,
    isCreateTimeSort: Boolean,
    editMode: Boolean,
    isSelected: Boolean,
    isActive:Boolean,
    searchQuery: String,
    onLongClick: () -> Unit,
    onClick: () -> Unit){
    val listNoteCategoryState by dashboardModel.listNoteCategoryState.collectAsState()

    // 安全获取分类列表数据
    val listNoteCategory = remember(listNoteCategoryState) {
        when (val tmpState =  listNoteCategoryState) {
            is ListNoteCategoryState.Success -> tmpState.items
            else -> emptyList()
        }
    }

    // 查找当前分类
    val targetCategory = remember(listNoteCategory, item.categoryId) {
        listNoteCategory.find { it.categoryId == item.categoryId }
    }
    var paddingSize = 4.dp
    if(!BuildConfig.IS_PHONE){
        paddingSize = 6.dp
    }


    val context = LocalContext.current



    Box(
        modifier = Modifier
            .semantics {
                this.contentDescription = context.getString(R.string.list_item_is_a_note)
            }
            .combinedClickable(onClick = {onClick()}, onLongClick = {onLongClick()})
            .padding(start = paddingSize, end = paddingSize, bottom = 8.dp)
            .wrapContentHeight()
            .fillMaxWidth()
            .background(
                color =if(isActive){Color.LightGray.copy(alpha = 0.3f)}else{TclTheme.colorScheme.tctHighlightBgColor},
                shape = RoundedCornerShape(8.dp)
            )
    ) {

        Column (
            modifier = Modifier.fillMaxWidth()
                .wrapContentHeight()
        ){
            if(!item.firstPicture.isNullOrEmpty() || !item.handwritingThumbnail.isNullOrEmpty()){
//                val imageLoader = ImageLoader.Builder(LocalContext.current)
//                    .components {
//                        add(GifDecoder.Factory()) // Add Gif support
//                    }
//                    .build()
//                AsyncImage(
//                    model = ImageRequest.Builder(LocalContext.current)
//                        .data(item.handwritingThumbnail ?: item.firstPicture)
//                        .crossfade(true)
//                        .build(),
//                    imageLoader = imageLoader,
//                    contentScale = ContentScale.Crop, // 保持比例裁剪
//                    contentDescription = "Image",
//                    modifier = Modifier
//                        .fillMaxWidth()
//                        .aspectRatio(1f)
//                        .clip(
//                            RoundedCornerShape(
//                                topStart = 8.dp,
//                                topEnd = 8.dp,
//                                bottomStart = 0.dp,
//                                bottomEnd = 0.dp
//                            )
//                        )// 设置顶部两边圆角，圆角半径为16dp
//                )
            }
            var title = item.title
            if(title.isEmpty()){
                if(isOnlyImage(item)){
                    title = stringResource(R.string.image_title)
                }else if(isOnlyVoice(item)){
                    title = stringResource(R.string.audio_title)
                }else if(isOnlyImageVoice(item)){
                    title = stringResource(R.string.image_audio_title)
                }else if(!item.summary.isNullOrEmpty()){
                    title = generateSmartSummary(item.summary!!)
                }
            }

            // 高亮处理
            val highlightedTitle = buildHighlightedText(title, searchQuery)
            Spacer(modifier =Modifier.height(10.dp))
            Text(
                text = highlightedTitle,
                fontSize = 18.sp,
                lineHeight = 21.sp,
                maxLines = 2,
                modifier = Modifier.padding(start = 15.dp, end = 15.dp, top = 15.dp, bottom = 12.dp),
                overflow = TextOverflow.Ellipsis,
                fontWeight = FontWeight.Medium,
                color = colorResource(R.color.text_title)
            )
            if(item.summary!!.isNotEmpty()){
                // 高亮处理
                val highlightedSummary = buildHighlightedText(item.summary!!, searchQuery)
                Text(
                    text = highlightedSummary,
                    fontSize = 16.sp,
                    lineHeight = 18.sp,
                    fontWeight = FontWeight.Normal,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.padding(horizontal = 15.dp),
                    color = colorResource(R.color.text_summary)
                )
            }

            if(editMode){
                Row(modifier = Modifier.padding(bottom = 16.dp,end = 16.dp)) {
                    Text(
                        text = formatDate(item, isCreateTimeSort),
                        fontSize = 12.sp,
                        lineHeight = 14.sp,
                        fontWeight = FontWeight.Normal,
                        modifier = Modifier.padding(
                            start = 15.dp,
                            top = 0.dp,
                            end = 15.dp,
                            bottom = 0.dp
                        ).weight(1f),
                        color = colorResource(R.color.text_summary)
                    )
                    if(isSelected){
                        Box(Modifier.fillMaxHeight().padding(top = 5.dp)) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_item_checked),
                                contentDescription = null
                            )
                        }

                    } else {
                        Box(Modifier.fillMaxHeight().padding(top = 5.dp)) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_item_unchecked),
                                contentDescription = null
                            )
                        }
                    }

                }
            }else{
                NotetDateAndAudioLogo(item,isCreateTimeSort)
                Row(Modifier.height(20.dp).fillMaxWidth()){
                    Text(text = "", Modifier.weight(1f))
                    // 未分类不展示角标分类颜色
                    targetCategory?.let { category ->
                        if(category.colorIndex>0){
                            val categoryColor = CommonUtils.getCategoryColorArray()[category.colorIndex-1]
                            Image(
                                painter = painterResource(id = R.drawable.label_category_triangle),
                                contentDescription = "Example Image",
                                colorFilter = ColorFilter.tint(colorResource(categoryColor)),
                                modifier = Modifier
                                    .size(20.dp),
                            )
                        }
                    }
                }
            }
        }

    }
}


/**
 * Note日期及录音图标
 */
@Composable
private fun NotetDateAndAudioLogo(item: NoteListItem,isCreateTimeSort:Boolean){



    val imageId = "imageId"

    val text = buildAnnotatedString {
        append(formatDate(item, isCreateTimeSort))
        // 添加内联内容标记
        appendInlineContent(imageId)
    }

    Row(
        modifier = Modifier.padding(
            start = 15.dp,
            top = 0.dp,
            end = 15.dp,
            bottom = 0.dp
        ),
        verticalAlignment = Alignment.CenterVertically) {
        Text(
            text = text,
            fontSize = 12.sp,
            color = colorResource(R.color.text_summary),
            inlineContent = mapOf(
                imageId to InlineTextContent(
                    Placeholder(
                        width = 12.sp,
                        height = 12.sp,
                        placeholderVerticalAlign = PlaceholderVerticalAlign.TextCenter
                    )
                ) {
                    if(item.hasAudio == true){
                        Image(
                            modifier = Modifier.size(12.dp).align(Alignment.CenterVertically).invisibleSemantics(),
                            painter = painterResource(id = R.drawable.ic_audio),
                            contentDescription = null
                        )
                    }
                }
            )
        )

    }
}

