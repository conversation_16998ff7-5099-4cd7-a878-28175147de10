package com.tcl.ai.note.dashboard.vm

import androidx.lifecycle.ViewModel
import com.tcl.ai.note.dashboard.intent.ConfigIntent
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import javax.inject.Inject
import kotlin.collections.setOf

@HiltViewModel
class SharedDashboardModel @Inject constructor() : ViewModel() {

    // 选中的item数据在items中的下标值集合
    private val _selectedItemCounts = MutableStateFlow(setOf<Int>())
    val selectedItemCounts: StateFlow<Set<Int>> = _selectedItemCounts.asStateFlow()

    // 是否全选模式
    private val _isSelectedAllMode = MutableStateFlow(false)
    val isSelectedAllMode: StateFlow<Boolean> = _isSelectedAllMode.asStateFlow()

    // 是否显示列表数据删除提示框
    private val _isDeleteDialogShown = MutableStateFlow(false)
    val isDeleteDialogShown: StateFlow<Boolean> = _isDeleteDialogShown.asStateFlow()


    fun handleIntent(intent: ConfigIntent) {
        when(intent) {
            is ConfigIntent.UpdateSelectedItemCounts -> {
                val itemsToToggle = intent.selectedItemCounts  // 要切换的元素集合
                val isSelectAll = intent.isSelectAll  // 是否全选
                val currentSet = _selectedItemCounts.value.toMutableSet()  // 当前选中的集合

                if (itemsToToggle.isEmpty()) {
                    currentSet.clear()
                } else {
                    // 遍历要切换的元素，存在则移除，不存在则添加
                    itemsToToggle.forEach { item ->
                        if (currentSet.contains(item)) {
                            if (!isSelectAll) { // 如果不是全选模式，移除元素
                                currentSet.remove(item)
                            }
                        } else {
                            currentSet.add(item)
                        }
                    }
                }

                _selectedItemCounts.value = currentSet
            }
            is ConfigIntent.UpdateIsSelectedAllMode -> {
                _isSelectedAllMode.value = intent.isSelectedAllMode
            }
            is ConfigIntent.UpdateIsDeleteDialogShown -> {
                _isDeleteDialogShown.value = intent.isDeleteDialogShown
            }
            else -> {} // 其他的不用处理
        }
    }

}