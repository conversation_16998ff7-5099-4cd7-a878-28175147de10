
import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInWindow
import androidx.compose.ui.platform.LocalConfiguration
import com.tcl.ai.note.widget.debounceClickable
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.dashboard.intent.ConfigIntent
import com.tcl.ai.note.dashboard.states.ListNoteCategoryState
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.adaptUIModifier
import com.tcl.ai.note.utils.adaptUIValue
import com.tcl.ai.note.utils.toPx
import com.tcl.ai.note.widget.AutoScrollText


/**
 * 笔记分类（分类列表，新增分类）
 */
@Composable
internal fun NoteCategoryScreen(dashboardModel: DashboardModel = hiltViewModel()) {
    // 分类选择弹窗
    val showPopup by dashboardModel.showCategoryPopup.collectAsState()
    //笔记分类默认分类为“全部便签”
    val initSelectedCategory = stringResource(R.string.database_preset_category_all)
    // 当前分类名称
    val currentCategoryName by dashboardModel.currentCategoryName.collectAsState()
    var rowPosition by remember { mutableStateOf(Offset.Zero) }
    var rowSize by remember { mutableStateOf(Size.Zero) }
//    val isNewCategoryVisible = dashboardModel.isNewCategoryVisible
//    val isAddCategoryMode = dashboardModel.isAddCategoryMode
    val listNoteCategoryState by dashboardModel.listNoteCategoryState.collectAsState()
    val tmpCategoryState = listNoteCategoryState
    val currentSysLanguage by dashboardModel.currentSysLanguage.collectAsState()

    val configuration = LocalConfiguration.current
    //切换系统语言的时候重新加载分类数据
    LaunchedEffect(configuration) {
        val newLanguage = configuration.locales[0].language
        if (newLanguage != currentSysLanguage) {
            dashboardModel.updateSysLanguage()
            dashboardModel.handleIntent(ConfigIntent.GetCategories)
        }

    }

    Box(modifier = Modifier.fillMaxWidth()){
        Column {
            //是否能显示Pop，防止误触
            val isCanShowPop=showPopup&&rowPosition!=Offset.Zero
            Spacer(modifier = Modifier.height(8.dp))
            Row(
                modifier = Modifier
                    .padding(start = adaptUIValue(18.dp, 0.dp, 18.dp))
                    .debounceClickable(
                        indication = ripple(color = colorResource(R.color.text_category_title))
                    ) { dashboardModel.updateShowCategoryPopupState(true) }
                    .onGloballyPositioned { coordinates ->
                        rowPosition = coordinates.positionInWindow()
                        rowSize = coordinates.size.toSize()
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                var textString = initSelectedCategory
                if (currentCategoryName.isNotEmpty()) {
                    textString = currentCategoryName
                }

                AutoScrollText(
                    text = textString,
                    color = colorResource(R.color.text_category_title),
                    modifier = Modifier
                        .weight(1f, fill = false)
                        .wrapContentWidth()
                        .padding(end = 8.dp)
                )

                Image(
                    painter = painterResource(
                        id = if (isCanShowPop) R.drawable.ic_arrow_drop_up_title
                        else R.drawable.ic_arrow_drop_down_title
                    ),
                    contentDescription = null
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            Logger.d("NoteCategoryScreen:showPopup", "$showPopup currentCategoryName:$currentCategoryName rowPosition:$rowPosition rowSize:$rowSize")

            if (isCanShowPop&& tmpCategoryState is ListNoteCategoryState.Success) {
                Popup(
                    alignment = Alignment.TopStart,
                    offset = IntOffset(
                        adaptUIValue(rowPosition.x.toInt(), 0, rowPosition.x.toInt()),
                        adaptUIValue((rowPosition.y.toInt()/3), rowSize.height.toInt() + 14.dp.toPx.toInt()) // 顶部offset+底部offset=8+6)
                    ),
                    onDismissRequest = { dashboardModel.updateShowCategoryPopupState(false) },
                    properties = PopupProperties(focusable = true),
                ) {
                    Surface(
                        elevation = 8.dp,
                        shape = RoundedCornerShape(8.dp),
                        modifier = Modifier.width(adaptUIValue(226, 206).dp)
                    ) {
                        Box(modifier = Modifier
                            .fillMaxWidth()
                            .clip(RoundedCornerShape(8.dp))
                            .background(colorResource(R.color.bg_dialog))
                        ) {
                            Column(modifier = Modifier) {
                                //Category list
                                LazyColumn(
                                    modifier = Modifier
                                        .padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 0.dp)
                                        .heightIn(max = 292.dp)// 限制弹出菜单高度，使其可以滚动
                                ) {
                                    items(tmpCategoryState.items) { item ->
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(vertical = adaptUIValue(8, 12).dp)
                                                .clickable {
                                                    dashboardModel.handleIntent(ConfigIntent.UpdateSelectedCategory(item))
                                                    dashboardModel.updateShowCategoryPopupState(false)
                                                }
                                        ) {
                                            Image(
                                                painter = painterResource(id = item.icon),
                                                contentDescription = null,
                                            )
                                            Spacer(modifier = Modifier.width(adaptUIValue(8, 16).dp))
                                            var  textColor = colorResource(R.color.text_title)
                                            var name = item.name
                                            if(item.name.isEmpty()){
                                                name = stringResource( R.string.database_preset_category_all)
                                            }
                                            if(item.name == currentCategoryName){
                                                textColor = colorResource(R.color.text_category_list_selected)
                                            }
                                            AutoScrollText(
                                                text = name+"(${item.noteCounts})",
                                                color = textColor,
                                                modifier = Modifier
                                                    .weight(1f, fill = false)
                                                    .wrapContentWidth()
                                            )
                                        }
                                    }
                                }

                                Divider(color = colorResource(R.color.bg_outline_10), thickness = 1.dp)

                                Box(
                                    modifier = Modifier
                                        .adaptUIModifier(Modifier.padding(16.dp), Modifier.padding(start = 16.dp, end = 16.dp, top = 12.dp, bottom = 16.dp))
                                        .fillMaxWidth()
                                        .clickable {
//                                            Toast.makeText(context, "成功创建分类", Toast.LENGTH_SHORT).show()
                                            dashboardModel.updateShowCategoryPopupState(false)
                                            dashboardModel.updateNewCategoryVisibleState(true)
                                            dashboardModel.updateNewCategoryModeState(true)
                                            dashboardModel.updateFabVisibleState(false)
                                        }
                                ) {
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Image(
                                            painter = painterResource(id = R.drawable.ic_category_add),
                                            contentDescription = null,
                                        )
                                        Spacer(modifier = Modifier.width(adaptUIValue(8, 16).dp))
                                        Text(
                                            text = stringResource(R.string.dialog_category_name_title),
                                            color = colorResource(R.color.text_title),
                                            fontSize = 14.sp
                                        )
                                    }
                                }
                            }
                        }

                    }
                }
            }
        }

    }
}


/**
 * 分类列表（用于迁移已选中的Note数据至指定分类中）
 */
@Composable
internal fun MoveToNoteCategoryScreen(
    dashboardModel: DashboardModel = hiltViewModel(),
    onBack: () -> Unit,
    onMoveToCategory: (Long) -> Unit,
    modifier: Modifier = Modifier) {
    //笔记分类默认分类为“全部便签”
    val initSelectedCategory = stringResource(R.string.database_preset_category_all)
    // 当前分类名称
//    val selectedCategory = remember { dashboardModel.selectedCategory }
    val currentCategoryId  by dashboardModel.currentCategoryId.collectAsState()
    val currentCategoryName by dashboardModel.currentCategoryName.collectAsState()

    val listNoteCategoryState by dashboardModel.listNoteCategoryState.collectAsState()
    Box(modifier = modifier.fillMaxWidth()){
        Column {
            Box(modifier = Modifier.background(colorResource(R.color.bg_dialog))) {
                Column(modifier = Modifier) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier
                            .height(56.dp)
                            .fillMaxWidth()
                            .padding(start = 16.dp)){
                        Image(
                            painter = painterResource(id = R.drawable.ic_menu_back),
                            contentDescription = null,
                            modifier = Modifier.clickable { onBack() }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        androidx.compose.material3.Text(
                            text = stringResource(R.string.choose_category),
                            fontSize = TclTheme.textSizes.titleMedium,
                            fontWeight = FontWeight.Medium,
                            color = colorResource(R.color.text_title),
                        )
                    }
                    val context = LocalContext.current
                    //Category list
                    LazyColumn(
                        modifier = Modifier
                            .heightIn(max = 336.dp)// 限制弹出菜单高度，使其可以滚动
                            .padding(start = 16.dp, end = 20.dp, top = 0.dp, bottom = 0.dp)
                    ) {
                        // state作为flow存在并发可能，因此需要用临时变量，保证类型转换安全
                        when (val listNoteState = listNoteCategoryState) {
                            is ListNoteCategoryState.Loading -> {
                                item {
                                    CircularProgressIndicator(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(16.dp)
                                    )
                                }
                            }
                            is ListNoteCategoryState.Success -> {
                                items(listNoteState.items) { item ->
                                    var tip = stringResource(R.string.category_moveto_already)
                                    if(item.categoryId.toString()!=currentCategoryId){
                                        tip = String.format(stringResource(R.string.category_moveto_success), item.name)
                                    }
                                    if(item.categoryId>0){
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(vertical = 16.dp)
                                                .clickable {
                                                    if(item.categoryId.toString()==currentCategoryId){
                                                        Toast.makeText(context, tip, Toast.LENGTH_SHORT).show()
                                                    }else{
                                                        onMoveToCategory(item.categoryId)
                                                        Toast.makeText(context, tip, Toast.LENGTH_SHORT).show()
                                                    }

                                                }
                                        ) {
                                            Image(
                                                painter = painterResource(id = item.icon),
                                                contentDescription = null,
                                            )
                                            Spacer(modifier = Modifier.width(8.dp))
                                            var  textColor = colorResource(R.color.text_title)
                                            var name = item.name
                                            if(item.name.isEmpty()){
                                                name = stringResource( R.string.database_preset_category_all)
                                            }
                                            if(item.name == currentCategoryName){
                                                textColor = colorResource(R.color.text_category_list_selected)
                                            }
                                            Text(
                                                text = name,
                                                color = textColor,
                                                fontSize = 14.sp,
                                            )
                                            Spacer(modifier = Modifier.weight(1f))
                                            Text(
                                                text = "${item.noteCounts}",
                                                color = textColor,
                                                fontSize = 14.sp
                                            )
                                        }
                                    }
                                }
                            }

                            is ListNoteCategoryState.Error -> {}
                        }
                    }

                    Divider(color = colorResource(R.color.bg_outline_10), thickness = 1.dp)

                    //New Category
                    Box(
                        modifier = Modifier
                            .padding(16.dp)
                            .fillMaxWidth()
                            .clickable {
//                                onBack()
                                dashboardModel.updateNewCategoryVisibleState(true)
                                dashboardModel.updateNewCategoryModeState(true)
                                dashboardModel.updateFabVisibleState(false)
                            }
                    ) {
                        Row(verticalAlignment = Alignment.CenterVertically) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_category_add),
                                contentDescription = null,
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = stringResource(R.string.dialog_category_name_title),
                                color = colorResource(R.color.text_title),
                                fontSize = 14.sp
                            )
                        }
                    }
                }
            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black.copy(alpha = 0.5f))
                    .clickable { onBack() } // 点击遮罩层取消搜索
                    .zIndex(0.5f) // 将遮罩层置于列表下方，但在内容上方
            )
        }

    }
}