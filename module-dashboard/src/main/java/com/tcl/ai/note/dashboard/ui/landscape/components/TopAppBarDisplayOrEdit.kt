package com.tcl.ai.note.dashboard.ui.landscape.components

import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.dashboard.intent.ConfigIntent
import com.tcl.ai.note.dashboard.ui.DisplayModeTopAppBar
import com.tcl.ai.note.dashboard.ui.EditModeTopAppBar
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.home.HomeActivity

/**
 * 左侧顶部导航栏
 */
@Composable
internal fun TopAppBarDisplayOrEdit(
    dashboardModel: DashboardModel,
    editMode:Boolean,
    viewType:String,
    isSelectedAllMode: Boolean,
    selectedItemCounts: Set<Int>,
    items: List<NoteListItem>,
    onSelectedItemCounts:(set: Set<Int>, isSelAll: Boolean) -> Unit,
    onSelectedNotes:(List<NoteListItem>) -> Unit,
    onEditMode:(Boolean) ->Unit,
    onIsSelectedAllMode:(Boolean) ->Unit,
    onIsSearching:(Boolean) -> Unit,
    onIsSortOrderDialogShown:(Boolean) -> Unit,
    onIsDeleteCategoryDialog:(Boolean) -> Unit,
    onIsDeleteDialogShown:(Boolean) -> Unit,
    onShowCategories:(Boolean) -> Unit,
){
    val context = LocalContext.current
    if(!editMode){
        DisplayModeTopAppBar(
            dashboardModel.currentCategoryName.value,
            viewType,onSearchClicked = {
                // 搜索
                onIsSearching(true)
            },
            onMoreActionsClicked = {
                // 显示模式 列表or网格
                val newViewType = if (viewType == DataStoreParam.VIEW_TYPE_LIST) {
                    DataStoreParam.VIEW_TYPE_GRID
                } else {
                    DataStoreParam.VIEW_TYPE_LIST
                }
                dashboardModel.handleIntent(ConfigIntent.ChangeViewType(newViewType))
            },
            onSortClicked = {
                // 排序
                onIsSortOrderDialogShown(true)
            },
            onRenameCategory = {
                // 重命名分类
                dashboardModel.updateNewCategoryVisibleState(true)
                dashboardModel.updateNewCategoryModeState(false)
            },
            onDeleteCategory = {
                // 删除分类
                onIsDeleteCategoryDialog(true)
            },
            onTitleClicked = {
                context.startActivity(Intent(context, HomeActivity::class.java))
            }
        )
    } else {
        // 编辑模式
        EditModeTopAppBar(
            selectedItemCount = selectedItemCounts,
            isSelectedAllMode = isSelectedAllMode,
            onCancel = {
                // 取消已选中的回到显示模式
                onEditMode(false)
                onSelectedItemCounts(emptySet(), false)
                onSelectedNotes(listOf())
                onIsSelectedAllMode(false)
                dashboardModel.updateFabVisibleState(true)
            },
            onSelectedAll = {
                // 全选
                val isSelectedAll = !isSelectedAllMode
                onIsSelectedAllMode(isSelectedAll)
                onSelectedItemCounts(emptySet(), false)
                onSelectedNotes(listOf())
                if(isSelectedAll){
                    // 生成包含所有索引的 Set<Int>
                    val allIndex = (0 until items.size).toSet()
                    onSelectedItemCounts(allIndex, true)
                    onSelectedNotes(items)
                }
            },
            onDeleteSelected = {
                // 删除选中的item
                onIsDeleteDialogShown(true)
            },
            onMoveTo = {
                // 移至某一个分类
                onShowCategories(true)
            }
        )
    }
}
