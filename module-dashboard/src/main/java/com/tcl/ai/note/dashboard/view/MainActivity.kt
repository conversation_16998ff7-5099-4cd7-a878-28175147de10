package com.tcl.ai.note.dashboard.view

import android.app.ActivityManager
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalConfiguration
import androidx.lifecycle.lifecycleScope
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.tcl.ai.note.base.BaseActivity
import com.tcl.ai.note.dashboard.track.AnalyticsEditScreenModel
import com.tcl.ai.note.journaldashboard.ui.addPageScreen
import com.tcl.ai.note.journaldashboard.ui.journalContentNoAnimScreen
import com.tcl.ai.note.journaldashboard.ui.journalContentScreen
import com.tcl.ai.note.journaldashboard.ui.settingsScreen
import com.tcl.ai.note.navigation.AnimatedNaHost
import com.tcl.ai.note.navigation.NavigationUtils.ROUTE_MAIN_SCREEN
import com.tcl.ai.note.navigation.navigateToNewNote
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.startEditActivity
import com.tcl.ai.note.utils.startEditActivityIfNotExists
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.delay

@AndroidEntryPoint
class MainActivity : BaseActivity() {

    companion object {
        private const val TAG = "MainActivity"

        private const val KEY_NEW_NOTE = "new_note"
        private const val VALUE_WRITE_A_NOTE = "write_a_note"
    }

    private val shouldNavigateToEdit = mutableStateOf(false)
    private var mCurrentRoute = ROUTE_MAIN_SCREEN

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        Logger.d(TAG, "onCreate: $this")

        enableEdgeToEdge()

        handleExternalIntent(intent)

        // 分析编辑界面的埋点情况
        lifecycleScope.launchIO {
            AnalyticsEditScreenModel.init()
        }


        setContent {
            NoteTclTheme {
                val navController = rememberNavController()
                val easing = remember { CubicBezierEasing(0.22f, 1f, 0.36f, 1f) }

                // 是否处于转场动画运行中
                var isTransitionRunning by remember { mutableStateOf(false) }
                val navBackStackEntry by navController.currentBackStackEntryAsState()
                LaunchedEffect(navBackStackEntry) {
                    isTransitionRunning = true
                    delay(350)
                    isTransitionRunning = false
                }

                val currentBackStackEntry = navController.currentBackStackEntryAsState()
                val currentRoute =
                    currentBackStackEntry.value?.destination?.route ?: ROUTE_MAIN_SCREEN
                mCurrentRoute = currentRoute
                val shouldNavigate by remember { shouldNavigateToEdit }

                // 处理导航逻辑, 接收外部 Intent 并决定是否导航到编辑页
                HandleNavigation(
                    shouldNavigate = shouldNavigate,
                    currentRoute = currentRoute,
                    navController = navController,
                    onNavigationComplete = {
                        shouldNavigateToEdit.value = false
                    }
                )

                Box(modifier = Modifier.fillMaxSize().background(color = TclTheme.tclColorScheme.tctStanderBgBasic)) {
                    AnimatedNaHost(
                        navController, startDestination = ROUTE_MAIN_SCREEN,
                    ) {
                        homeScreen(navController = navController, easing = easing)
//                        editScreen(navController = navController)
                        addPageScreen(navController, easing)
                        journalContentScreen(navController, easing)
                        journalContentNoAnimScreen(navController)
                        settingsScreen(navController)
                        checkUpdateScreen(navController)
                    }
                    TouchBlockerOverlay(show = isTransitionRunning)
                }
            }
        }
    }


    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        Logger.d(TAG, "onNewIntent: $this")
        handleExternalIntent(intent)
    }

    /**
     *重新设置enableEdgeToEdge() 不然暗黑模式切换成白天模式时 会导致看不清状态栏了
     */
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        enableEdgeToEdge()
    }

    /**
     * 处理外部 Intent，检查是否需要导航到编辑页
     */
    private fun handleExternalIntent(intent: Intent?) {
        intent?.let {
            val hasNewNoteParam = it.hasExtra(KEY_NEW_NOTE)
            if (hasNewNoteParam) {
                val value = it.getStringExtra(KEY_NEW_NOTE)
                if (VALUE_WRITE_A_NOTE == value && mCurrentRoute.startsWith(ROUTE_MAIN_SCREEN)) {
                    shouldNavigateToEdit.value = true
                }
            }
        }
    }


    /**
     *处理导航逻辑,是否导航到编辑页
     */
    @Composable
    private fun HandleNavigation(
        shouldNavigate: Boolean,
        currentRoute: String,
        navController: NavController,
        onNavigationComplete: () -> Unit
    ) {
        Logger.d(TAG, "HandleNavigation: $shouldNavigate, $currentRoute")
        val context = LocalContext.current
        if (shouldNavigate && currentRoute.startsWith(ROUTE_MAIN_SCREEN)) {
            // 如果已经存在 EditActivity 则不跳转
            context.startEditActivityIfNotExists()
            LaunchedEffect(Unit) {
                onNavigationComplete()
            }
        }
    }

    @Composable
    fun TouchBlockerOverlay(show: Boolean) {
        if (show) {
            Box(
                Modifier
                    .fillMaxSize()
                    .pointerInput(Unit) {
                        // 持续消费所有事件
                        while (true) {
                            awaitPointerEventScope {
                                awaitPointerEvent()
                            }
                        }
                    }
            )
        }
    }
}

