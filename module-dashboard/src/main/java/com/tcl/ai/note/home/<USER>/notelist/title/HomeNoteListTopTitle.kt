package com.tcl.ai.note.home.components.notelist.title

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.home.components.HomeTitleTextStyle
import com.tcl.ai.note.home.components.pop.ShowDropDownMenuCompat
import com.tcl.ai.note.home.components.preview.HomeTabletTopTitlePreviewParameterProvider
import com.tcl.ai.note.home.components.base.rememberNoteLayoutConfig
import com.tcl.ai.note.home.vm.state.HomeNoteUiState
import com.tcl.ai.note.home.vm.state.HomeTitleMode
import com.tcl.ai.note.home.vm.action.HomeNoteListAction
import com.tcl.ai.note.theme.GlobalDimens
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.testUIBorder

@Composable
fun HomeNoteListTopTitle(
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    val mode = homeNoteUiState.titleMode
    val contentConfig = rememberNoteLayoutConfig()
    val padding = contentConfig.contentPadding.dp
    //GD 觉得菜单有点偏左了，往右靠一点
    val paddingEnd = padding - if (homeNoteUiState.isSearchMode) 0.dp else 6.dp
    //手机上的搜索模式下不需要左边的padding没那么宽
    val paddingStart = if (homeNoteUiState.isSearchMode&&!isTablet) 8.dp else padding
    Box() {
        Row(
            modifier = Modifier
                .height(56.dp)
                .background(colorResource(R.color.home_note_list_bg_color))
                .padding(start = paddingStart, end = paddingEnd)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            when (mode) {
                HomeTitleMode.Edit -> {
                    TopTitleEdit(
                        homeNoteUiState = homeNoteUiState,
                        onAction = onAction
                    )
                }

                HomeTitleMode.Search -> {
                    // 可根据需求自定义搜索模式下的标题栏
                    HomeNoteListTopTitleSearch(
                        onTextChange = { text, isSearchInputFocused ->
                            onAction(HomeNoteListAction.OnSearchTextChange(text))
                        },
                        onBack = {
                            onAction(HomeNoteListAction.OnChangeTitleMode(HomeTitleMode.Normal))
                        }
                    )
                }

                HomeTitleMode.Normal -> {
                    TopTitleNormal(
                        homeNoteUiState = homeNoteUiState,
                        onAction = onAction
                    )
                }
            }
        }
        Box(modifier = Modifier.align(alignment = Alignment.BottomEnd)) {
            ShowDropDownMenuCompat(
                homeNoteUiState.isShowMorePop,
                homeNoteUiState = homeNoteUiState,
                onAction
            ) { isShow ->
                onAction(HomeNoteListAction.OnShowMoreMenu(isShow))
                Logger.i("BounceScalePopup", "ShowDropDownMenuCompat, isShow:$isShow")
                if (!isShow) {
                    onAction(HomeNoteListAction.OnClickSort(clickSorted = false))
                }
            }
        }
    }
}



@Composable
private fun RowScope.TopTitleNormal(
    homeNoteUiState: HomeNoteUiState,
    onAction: (HomeNoteListAction) -> Unit
) {
    val title = homeNoteUiState.selectedCategoryName
    val viewType = homeNoteUiState.viewType
    Row(modifier = Modifier.weight(1f, fill = false),verticalAlignment = Alignment.CenterVertically) {
        if (!isTablet) {
            HoverProofIconButton(
                modifier = Modifier.size(32.dp),
                onClick = {
                    onAction(HomeNoteListAction.OnOpenDrawer)
                }) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_drawer_setting),
                    contentDescription = stringResource(R.string.edit_top_menu_category),
                )
            }
            Spacer(modifier = Modifier.width(8.dp))
        }
        Text(
            text = if (title.isNullOrEmpty()) {
                stringResource(R.string.database_preset_category_all)
            } else {
                title
            },
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = colorResource(R.color.home_title_color),
            style = HomeTitleTextStyle
        )
    }
    Box() {
        Row(verticalAlignment = Alignment.CenterVertically) {
            HoverProofIconButton(
                onClick = {
                    onAction(HomeNoteListAction.OnChangeTitleMode(HomeTitleMode.Search))
                    onAction(HomeNoteListAction.OnClickSort(clickSorted = false))
                },
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_search),
                    contentDescription = stringResource(R.string.search_icon),
                    modifier = Modifier.size(24.dp)
                )
            }

            Spacer(modifier = Modifier.width(8.dp))
            HoverProofIconButton(
                onClick = {
                    onAction(HomeNoteListAction.OnShowMoreMenu(true))
                },
                modifier = Modifier.size(32.dp)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_navigator_more),
                    contentDescription = stringResource(R.string.more_actions),
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    }

}


@Composable
private fun ChangeTypeIcon(
    onChangeTypeClick: () -> Unit,
    dimens: GlobalDimens,
    viewType: String
) {
    HoverProofIconButton(
        onClick = onChangeTypeClick,
        modifier = Modifier.size(24.dp)
    ) {
        // 根据 viewType 切换图标 反着的
        val iconId = if (viewType != DataStoreParam.VIEW_TYPE_GRID) {
            R.drawable.ic_staggered_grid_show
        } else {
            R.drawable.ic_list_show
        }
        Icon(
            painter = painterResource(id = iconId),
            contentDescription = if (viewType == DataStoreParam.VIEW_TYPE_GRID) stringResource(
                R.string.view_type_list
            ) else stringResource(R.string.view_type_staggered_grid),
        )
    }
}

@Preview(
    device = "id:pixel_5",
    showBackground = true,
    name = "HomeTabletTopTitle - All States"
)
@Composable
private fun HomeTabletTopTitlePreview(
    @PreviewParameter(HomeTabletTopTitlePreviewParameterProvider::class)
    homeNoteUiState: HomeNoteUiState
) {
    HomeNoteListTopTitle(
        homeNoteUiState = homeNoteUiState,
        onAction = {}
    )
}