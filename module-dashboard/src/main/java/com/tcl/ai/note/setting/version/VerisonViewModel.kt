package com.tcl.ai.note.setting.version

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.net.NetworkMonitor
import com.tcl.ai.note.utils.AppDataStore
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tct.smart.aota.check.ICheck
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import java.time.Instant
import java.time.ZoneId
import javax.inject.Inject

@HiltViewModel
class VerisonViewModel @Inject constructor(
    private val application: Application,
    private val networkMonitor: NetworkMonitor,
    private val updateMonitor: UpdateMonitor) : ViewModel() {
    private var checkService: ICheck? = null
    private val checkPackage = application.packageName
    val isOffline = networkMonitor.isOnline.map(Boolean::not).stateIn(scope = viewModelScope, started = SharingStarted.Eagerly, initialValue = false)
    private val _updateState = MutableStateFlow(UpdateMonitorImpl.UpdateState.Idle)
    val updateState = _updateState.asStateFlow()

    val _isLoading = MutableStateFlow(true)
    val isLoading = _isLoading.asStateFlow()

    private val _isCheckUpdate = MutableStateFlow(false)
    val isCheckUpdate = _isCheckUpdate.asStateFlow()

    private val _effect = EventSharedFlow<VerisonEffect>()
    val effect: SharedFlow<VerisonEffect> by lazy { _effect.asSharedFlow() }

    init {
        viewModelScope.launch {
            val lastCheckTimestamp = withContext(Dispatchers.IO) {
                AppDataStore.getLong("last_check_timestamp", 0)
            }
            Logger.d("lastCheckTimestamp", "lastCheckTimestamp==="+lastCheckTimestamp)
            val currentTimestamp = System.currentTimeMillis()
            if (!isToday(currentTimestamp, lastCheckTimestamp)) {
                onUpdateClick(false)
            }
        }
    }

    private fun isToday(lastTimestamp: Long, currentTimestamp: Long): Boolean {
        val currentDate = Instant.ofEpochMilli(currentTimestamp)
            .atZone(ZoneId.systemDefault())
            .toLocalDate()

        val lastDate = Instant.ofEpochMilli(lastTimestamp)
            .atZone(ZoneId.systemDefault())
            .toLocalDate()

        return lastDate == currentDate
    }

    fun onUpdateClick(isClick: Boolean = false) {
        if (!isOffline.value) {
            // _isLoading.value = true
        }
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                Logger.d("lastCheckTimestamp", "lastCheckTimestamp1===" + System.currentTimeMillis())
                AppDataStore.putLong("last_check_timestamp", System.currentTimeMillis())
            }
        }

        if (isOffline.value) {
            _isLoading.value = false
            if (isClick) {
                _effect.tryEmit(VerisonEffect.ShowAota)
            } else {
                _effect.tryEmit(VerisonEffect.ShowToastRes(com.tcl.ai.note.base.R.string.network_error))
            }
            return
        }

        viewModelScope.launch {
            try {
                withTimeout(10000) { // 10秒超时
                    val completableDeferred = CompletableDeferred<UpdateMonitorImpl.UpdateState>()

                    updateMonitor.checkUpdate(checkPackage) { result ->
                        completableDeferred.complete(result)
                    }

                    val result = completableDeferred.await()

                    _updateState.value = result
                    _isLoading.value = false

                    if (result == UpdateMonitorImpl.UpdateState.NewVersionAvailable) {
                        if (isClick) {
                            _effect.tryEmit(VerisonEffect.ShowAota)
                        }
                        withContext(Dispatchers.IO) {
                            AppDataStore.putBoolean("has_check_update_flag", true)
                        }
                    } else if (result == UpdateMonitorImpl.UpdateState.NetworkError ||
                        result == UpdateMonitorImpl.UpdateState.ServerError) {
                        _effect.tryEmit(VerisonEffect.ShowToastRes(com.tcl.ai.note.base.R.string.network_error))
                    }
                    Logger.d("has_check_update_flag", "has_check_update_flag===" + isCheckUpdate.value)
                }
            } catch (e: TimeoutCancellationException) {
                // 10秒内没有收到回调，执行超时处理
                Logger.e(TAG, "Update check timeout")
                _isLoading.value = false
                _effect.tryEmit(VerisonEffect.ShowToastRes(com.tcl.ai.note.base.R.string.network_error))
            } catch (e: Exception) {
                // 捕获其他可能的异常
                Logger.e(TAG, "Update check failed")
                _isLoading.value = false
                _effect.tryEmit(VerisonEffect.ShowToastRes(com.tcl.ai.note.base.R.string.network_error))
            }
        }
    }


    companion object {
        private const val TAG = "VerisonViewModel"
    }

    fun getIsCheckUpdate(): Boolean {
        viewModelScope.launch {
            _isCheckUpdate.value = withContext(Dispatchers.IO) {
                AppDataStore.getBoolean("has_check_update_flag", false)
            }
        }
        return isCheckUpdate.value
    }
}


sealed class VerisonEffect {
    data object ShowAota : VerisonEffect()
    data class ShowToastRes(val resourceId: Int) : VerisonEffect()
}