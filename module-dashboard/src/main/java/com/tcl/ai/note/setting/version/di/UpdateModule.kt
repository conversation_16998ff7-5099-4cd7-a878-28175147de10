package com.tcl.ai.feature.personalcenter.ui.version.di

import com.tcl.ai.note.setting.version.UpdateMonitor
import com.tcl.ai.note.setting.version.UpdateMonitorImpl
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
interface UpdateModule {


    @Singleton
    @Binds
    fun bindUpdateMonitor(
        updateMonitor: UpdateMonitorImpl
    ): UpdateMonitor


}
