package com.tcl.ai.note.dashboard.ui

import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.isTabletLandscape
import com.tcl.ai.note.utils.rememberIsInMultiWindowMode

/**
 * 笔记列表数据为空时UI
 * @param isSearchMode 搜索模式需要动画， 是否需要动画
 * @param isDialogShowing 是否有dialog正在显示，用于过滤dialog中输入框的键盘事件
 */
@Composable
internal fun NoNotesScreen(
    isSearchMode: Boolean = false,
    isDialogShowing: Boolean = false,
){
    val isMultiWindow= rememberIsInMultiWindowMode()
    val isNeedScrollAnima=isSearchMode&&!isMultiWindow
    // 根据参数决定是否应用动画，但都支持键盘监听
    val modifier = if (isNeedScrollAnima) {
        // 需要动画模式：监听键盘并应用动画
        val imeBottomPadding = WindowInsets.ime.asPaddingValues().calculateBottomPadding()
        // 当有dialog显示时，忽略键盘事件，保持原有状态
        val isKeyboardVisible = if (isDialogShowing) false else imeBottomPadding > 0.dp

        val targetPaddingTop = calculateTopPadding(isTablet, isTabletLandscape, isKeyboardVisible)

        val innerPadding by animateDpAsState(
            targetValue = targetPaddingTop,
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioLowBouncy,
                stiffness = Spring.StiffnessLow
            ),
            label = "paddingTopAnimation"
        )

        Logger.d("NoNotesScreen", "targetPaddingTop:$targetPaddingTop, isDialogShowing:$isDialogShowing")

        Modifier
            .fillMaxSize()
            .padding(top = innerPadding)
            .clipToBounds() // 防止动画过程中内容溢出
    } else {
        // 默认模式：居中显示，不监听键盘
        Modifier.fillMaxSize()
    }

    Column(
        verticalArrangement = if (isNeedScrollAnima) Arrangement.Top else Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier
    ) {
        NoNoteContent(isSearchMode)
    }
}

@Composable
private fun NoNoteContent(isSearchMode: Boolean) {
    val noNoteText = if (isSearchMode) stringResource(R.string.no_result) else stringResource(id = R.string.no_notes)
    Image(
        painter = painterResource(id = R.drawable.ic_home_notelist_no_data),
        contentDescription = noNoteText,
        modifier = Modifier.size(120.dp)
    )
    Text(noNoteText, fontSize = 14.sp, color = colorResource(R.color.text_summary))
}

@Composable
fun calculateTopPadding(
    isTablet: Boolean,
    isLandScape: Boolean,
    isKeyboardVisible: Boolean
): Dp {
    return when {
        // 手机 竖屏
        !isTablet && !isLandScape -> 191.dp

        // 平板-竖屏
        isTablet && !isLandScape -> 360.dp

        // 平板-横屏且键盘可见
        isTablet && isLandScape && isKeyboardVisible -> 75.dp

        // 平板-横屏且键盘不可见
        isTablet && isLandScape && !isKeyboardVisible -> 212.dp

        else -> 191.dp
    }
}
