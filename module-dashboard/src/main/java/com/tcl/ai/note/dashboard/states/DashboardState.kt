package com.tcl.ai.note.dashboard.states

import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem


/**
 * 页面状态
 */
data class ConfigState(
    /**
     * 首页列表界面，初始默认状态， LIST
     * */
    val viewType: String = DataStoreParam.VIEW_TYPE_LIST,

)

/**
 * 笔记列表数据状态
 */
sealed class ListNotesUiState {
    object Loading : ListNotesUiState()
    data class Success(val items: List<NoteListItem>, val hasMore: Boolean) : ListNotesUiState()
    data class Error(val message: String) : ListNotesUiState()
}

/**
 * 笔记分类列表数据状态
 */
sealed class ListNoteCategoryState {
    object Loading : ListNoteCategoryState()
    data class Success(val items: List<NoteCategory>) : ListNoteCategoryState()
    data class Error(val message: String) : ListNoteCategoryState()
}

/**
 * 笔记分类数据状态
 */
sealed class NoteCategoryState {
    object Loading : NoteCategoryState()
    data class Success(val item: NoteCategory) : NoteCategoryState()
    data class Error(val message: String) : NoteCategoryState()
}

/**
 * 当前显示的Note数据状态
 */
data class NoteState(
    val note: Note? = null, // 当前笔记
    val noteId : Long =-1L, // 当前预览noteId
    val contents: List<EditorContent> = emptyList(), // 内容块列表
    val newCategory: NoteCategory? = null, // 预览新建的分类
    val currentNoteCategory:NoteCategory? = null, // 当前预览note的分类
    val searchText:String = "", // 搜索框中输入的内容
    val isSearching: Boolean = false, // 是否处于搜索状态
    val editMode:Boolean = false, // 列表长按编辑模式状态(默认非长按状态)
    val selectedNotes: List<NoteListItem> = emptyList(), // 长按状态下选中的notes数据
    val isCreateTimeSort:Boolean = true, // 是否按照创建时间排序
)


//data class OtherConfigState(
//
//)





