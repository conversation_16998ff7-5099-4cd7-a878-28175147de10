package com.tcl.ai.note.dashboard.ui.landscape.components

import MoveToNoteCategoryScreen
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.NavController
import com.tcl.ai.note.base.R
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.dashboard.intent.ConfigIntent
import com.tcl.ai.note.dashboard.states.ListNotesUiState
import com.tcl.ai.note.dashboard.ui.BottomDeleteCategoryDialog
import com.tcl.ai.note.dashboard.ui.BottomSortOrderDialog
import com.tcl.ai.note.dashboard.ui.CategoryScreen
import com.tcl.ai.note.dashboard.ui.DeleteNoteDialog
import com.tcl.ai.note.dashboard.ui.NoNotesScreen
import com.tcl.ai.note.dashboard.ui.NoteCategoryScreenPlaceholder
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.dashboard.vm.SharedDashboardModel
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel

/**
 * 横屏首页左侧区域
 */
@Composable
fun LandScapeListSection(
    navController: NavController,
    dashboardModel: DashboardModel = hiltViewModel(),
    audioToTextViewModel: AudioToTextViewModel,
    sharedDashboardModel: SharedDashboardModel,
    modifier: Modifier,
    isSearching:Boolean,
    searchText:String,
    onIsSearching:(Boolean) -> Unit){
    val dashboardState by dashboardModel.configState.collectAsState()
    val noteState by dashboardModel.noteState.collectAsState()
    val viewType = dashboardState.viewType
    val listNotesUiState by dashboardModel.listNotesUiState.collectAsState()

    // 选中的item数据在items中的下标值集合
    val selectedItemCounts by sharedDashboardModel.selectedItemCounts.collectAsState()
    // 是否全选模式
    val isSelectedAllMode by sharedDashboardModel.isSelectedAllMode.collectAsState()
    // 是否显示列表数据删除提示框
    val isDeleteDialogShown by sharedDashboardModel.isDeleteDialogShown.collectAsState()
    // 是否显示分类列表用于迁移已选中的数据至指定的分类中
    val showCategories by dashboardModel.showMoveToCategoryDialog.collectAsState()

    val isNewCategoryVisible = dashboardModel.isNewCategoryVisible
    val isAddCategoryMode = dashboardModel.isAddCategoryMode
    // 是否显示分类删除提示框
    var isDeleteCategoryDialog by remember { mutableStateOf(false) }
    // 是否删除当前分类下的Note
    var isDeleteSelectedCategoryNotes by remember { mutableStateOf(false) }

    // 是否显示排序方式选择提示框
    val isSortOrderDialogShown by dashboardModel.shownSortOrderDialog.collectAsState()

    // 当前分类
    val currentCategoryId  by dashboardModel.currentCategoryId.collectAsState()
    val currentCategoryName by dashboardModel.currentCategoryName.collectAsState()
    val currentColorIndex by dashboardModel.currentColorIndex.collectAsState()

    // 列表数据
    var items by remember { mutableStateOf(listOf<NoteListItem>()) }
    val tmpUiState = listNotesUiState
    if(tmpUiState is ListNotesUiState.Success){
        items = tmpUiState.items
    }

    // 选中的notes数据
//    var selectedNotes by remember { mutableStateOf(listOf<NoteListItem>()) }

    // 监听返回时的刷新标志
    val savedStateHandle = navController.currentBackStackEntry?.savedStateHandle
    LaunchedEffect(savedStateHandle) {
        savedStateHandle?.get<Boolean>("refresh")?.let { refresh ->
            if(isSearching){
                dashboardModel.handleIntent(ConfigIntent.SearchNotes(searchText))
            }else if(refresh){
                dashboardModel.loadInitialNotes() // 重新加载数据
                dashboardModel.handleIntent(ConfigIntent.GetCategories)
                savedStateHandle.remove<Boolean>("refresh") // 清除标志
            }
        }
    }

    Box(modifier = modifier
        .fillMaxHeight()
        .padding(
            bottom = WindowInsets.navigationBars
                .asPaddingValues()
                .calculateBottomPadding()
        )
        .background(TclTheme.colorScheme.tctGlobalBgColor)
    ) {
        Column(Modifier.fillMaxSize()) {
            // 动态切换 TopAppBar
            TopAppBarDisplayOrEdit(
                dashboardModel=dashboardModel,
                editMode=noteState.editMode,
                viewType=viewType,
                isSelectedAllMode=isSelectedAllMode,
                selectedItemCounts=selectedItemCounts,
                items=items,
                onSelectedItemCounts={ it, isSelAll -> sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(it, isSelAll))},
                onSelectedNotes={dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(it))},
                onEditMode={dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(it))},
                onIsSelectedAllMode={sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(it))},
                onIsSearching={onIsSearching(it)},
                onIsSortOrderDialogShown={dashboardModel.updateShownSortOrderDialog(it)},
                onIsDeleteCategoryDialog={isDeleteCategoryDialog=it},
                onIsDeleteDialogShown={sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsDeleteDialogShown(it))},
                onShowCategories={dashboardModel.updateShowMoveToCategoryDialogState(it)},
            )

            // 列表内容区域
            Box{
                // Content goes here
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(start = 18.dp, end = 18.dp)
                        .background(TclTheme.colorScheme.tctGlobalBgColor),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    //分类选择
                    NoteCategoryScreenPlaceholder(noteState.editMode, isSearching)

                    //列表数据加载
                    when(tmpUiState){
                        is ListNotesUiState.Loading ->{
                            CircularProgressIndicator(modifier = Modifier.padding(16.dp))
                        }
                        is ListNotesUiState.Success ->{
                            //数据加载成功
                            items = tmpUiState.items
                            if (items.isEmpty() && !dashboardModel.isLoading) {
                                //没有笔记数据
                                NoNotesScreen()
                            } else {
                                // 正常列表渲染

                                // 自动加载更多
                                val lazyListState = rememberLazyListState() // 为列表布局添加滚动状态跟踪
                                if(!isSearching){
                                    val listShouldLoadMore = remember {
                                        derivedStateOf {
                                            // 检测是否滚动到底部最后3个元素
                                            val layoutInfo = lazyListState.layoutInfo
                                            val totalItems = layoutInfo.totalItemsCount
                                            val lastVisibleItem = layoutInfo.visibleItemsInfo.lastOrNull()

                                            lastVisibleItem?.index != null &&
                                                    lastVisibleItem.index >= totalItems - 3 &&
                                                    !dashboardModel.isLoading &&
                                                    dashboardModel.hasMore
                                        }
                                    }

                                    LaunchedEffect(listShouldLoadMore) {
                                        snapshotFlow { listShouldLoadMore.value }
                                            .collect {
                                                if (it && !dashboardModel.isLoading && dashboardModel.hasMore) {
                                                    dashboardModel.loadMoreNotes()
                                                }
                                            }
                                    }
                                }

                                if (viewType == DataStoreParam.VIEW_TYPE_LIST) {
                                    //列表展示
                                    NoteListItems(
                                        dashboardModel=dashboardModel,
                                        editMode=noteState.editMode,
                                        selectedItemCounts=selectedItemCounts,
                                        selectedNotes=noteState.selectedNotes,
                                        isSearching=isSearching,
                                        items=items,
                                        onSelectedItemCounts={
                                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(it))
                                        },
                                        onSelectedNotes={
                                            dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(it))
                                        },
                                        onEditMode={
                                            dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(it))
                                        },
                                        onIsSelectedAllMode={
                                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(it))
                                        },
                                        onIsSearching = { onIsSearching(it) }
                                    )
                                }else{
                                    //瀑布流展示
                                    NoteStaggeredGridItems(
                                        dashboardModel=dashboardModel,
                                        editMode=noteState.editMode,
                                        selectedItemCounts=selectedItemCounts,
                                        selectedNotes=noteState.selectedNotes,
                                        isSearching=isSearching,
                                        items=items,
                                        onSelectedItemCounts={
                                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(it))
                                        },
                                        onSelectedNotes={
                                            dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(it))
                                        },
                                        onEditMode={
                                            dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(it))
                                        },
                                        onIsSelectedAllMode={
                                            sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(it))
                                        },
                                        onIsSearching = { onIsSearching(it) }
                                    )
                                }
                            }
                        }
                        is ListNotesUiState.Error ->{
                            //数据加载失败
                            NoNotesScreen()
                        }
                    }

                }
            }

        }
    }

    if (isDeleteDialogShown && selectedItemCounts.isNotEmpty()) {
        DeleteNoteDialog(
            text = if(selectedItemCounts.size==1) stringResource(R.string.dialog_title_delete_one_items) else String.format(stringResource(R.string.dialog_title_delete_multiple_items), selectedItemCounts.size),
            onDelete = {
                sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsDeleteDialogShown(false))
                // 删除选中的items
                val noteIds = noteState.selectedNotes.map { it.noteId }
                dashboardModel.viewModelScope.launchIO { // 删除音频文件
                    dashboardModel.loadNoteContents(noteIds).forEach {
                        if (it is EditorContent.AudioBlock) {
                            // 删除音频文件
                            audioToTextViewModel.deleteAudioFile(it.audioPath)
                        }
                    }
                }
                dashboardModel.handleIntent(ConfigIntent.DeleteNotes(noteIds))
                dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(false))
                dashboardModel.updateFabVisibleState(true)
                sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(emptySet()))
                dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(listOf()))
                sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(false))
            },
            onDismiss = {
                sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsDeleteDialogShown(false))
            }
        )
    }

    if(showCategories){
        MoveToNoteCategoryScreen(
            onBack = {
//                showCategories = false
                dashboardModel.updateShowMoveToCategoryDialogState(false)
//                dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(false))
            },
            onMoveToCategory = {categoryId->
                // 将选中的Notes移至指定的分类
                val noteIds = noteState.selectedNotes.map { it.noteId }
                dashboardModel.handleIntent(ConfigIntent.UpdateNotesCategoryId(categoryId,noteIds))
                dashboardModel.updateFabVisibleState(true)
//                showCategories = false
                dashboardModel.updateShowMoveToCategoryDialogState(false)
                dashboardModel.handleIntent(ConfigIntent.UpdateEditMode(false))
                sharedDashboardModel.handleIntent(ConfigIntent.UpdateSelectedItemCounts(emptySet()))
                dashboardModel.handleIntent(ConfigIntent.UpdateSelectedNotes(listOf()))
                sharedDashboardModel.handleIntent(ConfigIntent.UpdateIsSelectedAllMode(false))
            },
            modifier = Modifier
//                .align(Alignment.TopCenter)
                .padding(
                    top = WindowInsets.statusBars
                        .asPaddingValues()
                        .calculateTopPadding(), start = 0.dp, end = 0.dp
                )
                .zIndex(1f))
    }
    if(isDeleteCategoryDialog){
        BottomDeleteCategoryDialog(
            onCancel = {
                isDeleteCategoryDialog = false
            },
            onDelete = {
                val categoryId = currentCategoryId.toLongOrNull()
                val colorIdx = currentColorIndex.toIntOrNull()
                if (categoryId == null || colorIdx == null)
                    return@BottomDeleteCategoryDialog
                val category = NoteCategory(categoryId = categoryId, name = currentCategoryName, colorIndex = colorIdx)
                dashboardModel.handleIntent(ConfigIntent.DeleteCategory(isDeleteSelectedCategoryNotes,category))
                isDeleteCategoryDialog = false
            },
            isDeleteNotesSelected = {
                isDeleteSelectedCategoryNotes = it
            },
            isShowDeleteNotes = items.isNotEmpty()
        )
    }

    if(isSortOrderDialogShown){
        BottomSortOrderDialog(
            onCancel = {
                dashboardModel.updateShownSortOrderDialog(false)
            },
            onSelected = {
                dashboardModel.updateSortModeState(it)
                dashboardModel.updateShownSortOrderDialog(false)
                // 执行排序查询操作
                dashboardModel.loadInitialNotes()
            },
            isCreateDate = noteState.isCreateTimeSort
        )
    }

    //添加笔记分类对话框
    if (isNewCategoryVisible) {
        CategoryScreen(
            isPreviewMode = false,
            isAddCategoryMode = isAddCategoryMode,
            onDismissRequest = {
                //showPopup = false
                dashboardModel.tempNewCategoryName = ""
                dashboardModel.tempNewColorIndex = 1
                dashboardModel.updateNewCategoryVisibleState(false)
                dashboardModel.updateNewCategoryModeState(true)
                dashboardModel.updateFabVisibleState(true)
            }
        )
    }
}