package com.tcl.ai.note.dashboard.ui

import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.res.stringResource
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.base.R
import com.tcl.ai.note.dashboard.intent.ConfigIntent
import com.tcl.ai.note.dashboard.states.ListNoteCategoryState
import com.tcl.ai.note.dashboard.vm.DashboardModel
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.utils.isExistCategory
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.findLeastUsedColor
import com.tcl.ai.note.widget.CategoryDialog


@Composable
fun CategoryScreen(
    viewModel: DashboardModel = hiltViewModel(),
    isPreviewMode: Boolean,
    isAddCategoryMode: Boolean,
    onDismissRequest: () -> Unit
) {
    // Get category states from ViewModel
    val listNoteCategoryState by viewModel.listNoteCategoryState.collectAsState()
    val currentCategoryId  by viewModel.currentCategoryId.collectAsState()
    val currentCategoryName by viewModel.currentCategoryName.collectAsState()
    val currentColorIndex by viewModel.currentColorIndex.collectAsState()

    // 分类列表数据
    var items by remember { mutableStateOf(listOf<NoteCategory>()) }
    listNoteCategoryState.let { state->
        if (state is ListNoteCategoryState.Success) {
            items = state.items
        }
    }

    val initialCategoryName = if (!isAddCategoryMode) currentCategoryName else viewModel.tempNewCategoryName
    var categoryName by remember { mutableStateOf(initialCategoryName) }

    val tmpColorIdx = currentColorIndex.toIntOrNull()
    val initialColorIndex = if (!isAddCategoryMode && tmpColorIdx != null) {
        tmpColorIdx
    } else {
        findLeastUsedColor(items){ it.colorIndex }?: CategoryColors.PINK_COLOR
    }
    var selectedColorIndex by remember { mutableIntStateOf(initialColorIndex) }

    val originalColorIndex by remember(currentColorIndex) {
        mutableIntStateOf(
            currentColorIndex.toIntOrNull() ?: 1
        )
    }

    var isOnChanged by remember { mutableStateOf(false) }

    // Compute dialog states for validation
    val isNotEmpty = categoryName.trim().isNotEmpty()
    val isExistCategory =
        isExistCategory(items, categoryName, selectedColorIndex, isAddCategoryMode)
    val isRepeated = isExistCategory && isNotEmpty
    val isDiffColor = selectedColorIndex != originalColorIndex
    val isEnabled = if (isAddCategoryMode) {
        !isExistCategory && isNotEmpty
    } else {
        (isDiffColor || !isExistCategory) && isNotEmpty
    }

    val toastText = stringResource(R.string.category_add_success)
    CategoryDialog(
        isAddCategoryMode = isAddCategoryMode,
        categoryName = categoryName,
        onCategoryNameChange = { newName ->
            categoryName = newName
            viewModel.tempNewCategoryName = newName
            isOnChanged = true
        },
        selectedColorIndex = selectedColorIndex,
        onColorIndexSelected = { colorIndex ->
            selectedColorIndex = colorIndex
            viewModel.tempNewColorIndex = colorIndex
            isOnChanged = true
        },
        isEnabled = isEnabled,
        isRepeated = isRepeated,
        isOnChanged = isOnChanged,
        onDismiss = onDismissRequest,
        onConfirm = {
            if (isEnabled) {
                onDismissRequest()
                if (isPreviewMode) {
                    // Preview mode
                    val category = NoteCategory(
                        name = categoryName,
                        colorIndex = selectedColorIndex,
                        createTime = System.currentTimeMillis(),
                        modifyTime = System.currentTimeMillis()
                    )
                    viewModel.handleIntent(ConfigIntent.AddCategory(category, true))
                } else {
                    viewModel.updateShowMoveToCategoryDialogState(false)
                    viewModel.handleIntent(ConfigIntent.UpdateEditMode(false))
                    if (isAddCategoryMode) {
                        // Add new category
                        val category = NoteCategory(
                            name = categoryName,
                            colorIndex = selectedColorIndex,
                            createTime = System.currentTimeMillis(),
                            modifyTime = System.currentTimeMillis()
                        )
                        viewModel.handleIntent(ConfigIntent.AddCategory(category, false))
                        ToastUtils.makeWithCancel(toastText, Toast.LENGTH_SHORT)
                    } else {
                        // Rename category
                        currentCategoryId.toLongOrNull()?.let { categoryId ->
                            val newCategory = NoteCategory(
                                categoryId = categoryId,
                                name = currentCategoryName
                            )
                            newCategory.modifyTime = System.currentTimeMillis()
                            newCategory.name = categoryName
                            newCategory.colorIndex = selectedColorIndex
                            newCategory.isRename = currentCategoryName != categoryName
                            viewModel.handleIntent(ConfigIntent.RenameCategory(newCategory))
                        }
                    }
                }
            }
        }
    )
}


