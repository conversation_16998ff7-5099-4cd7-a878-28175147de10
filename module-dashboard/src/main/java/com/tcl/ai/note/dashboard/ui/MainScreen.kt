package com.tcl.ai.note.dashboard.ui

import android.annotation.SuppressLint
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.navigation.NavController
import com.tcl.ai.note.dashboard.vm.SharedDashboardModel
//import com.tcl.ai.note.journaldashboard.ui.JournalDashboard
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel

@SuppressLint("DesignSystem")
@Composable
fun MainScreen(
    navController: NavController,
    sharedDashboardModel: SharedDashboardModel,
    audioToTextViewModel: AudioToTextViewModel,
    pageIndex: Int,
    showBlur: Boolean,
    onTabClick: (Int) -> Unit,
    onCreateJournalClick: () -> Unit = {},
    onResumeRefresh: (() -> Unit) -> Unit = {}
) {
    if (pageIndex == 0) {
        Logger.d("MainScreen", "pageIndex: $pageIndex, note screen")
        DashboardScreen(
            navController = navController,
            sharedDashboardModel = sharedDashboardModel,
            audioToTextViewModel = audioToTextViewModel,
            onTabClick = onTabClick,
            pageIndex = pageIndex
        )
    } else if (pageIndex == 1) {
        /*Logger.d("MainScreen", "pageIndex: $pageIndex, journal screen")
        JournalDashboard(
            navController = navController,
            showBlur = showBlur,
            onTabClick = onTabClick,
            pageIndex = pageIndex,
            onCreateJournalClick = onCreateJournalClick,
            onResumeRefresh = onResumeRefresh
        )*/
    }
}