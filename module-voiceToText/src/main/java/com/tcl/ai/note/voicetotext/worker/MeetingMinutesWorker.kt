package com.tcl.ai.note.voicetotext.worker

import android.content.Context
import android.content.pm.ServiceInfo
import android.widget.RemoteViews
import androidx.core.app.NotificationCompat
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.ForegroundInfo
import androidx.work.WorkManager
import androidx.work.WorkerParameters
import com.google.gson.Gson
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.voicetotext.util.AudioToTextConstant.EXTRA_AUDIO_RECORD
import com.tcl.ai.note.voicetotext.data.AudioRepository
import com.tcl.ai.note.voicetotext.data.di.AppEntryPoint
import com.tcl.ai.note.voicetotext.states.GenerateResultState
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.voicetotext.BuildConfig
import com.tcl.ai.note.voicetotext.R
import com.tcl.ai.note.voicetotext.bean.translationLanguageList
import com.tcl.ai.note.voicetotext.data.AudioTransferEntity
import com.tcl.ai.note.voicetotext.states.ErrorState
import com.tcl.ai.note.voicetotext.util.languageCode
import com.tcl.ai.sdk.assistant.AssistantServiceManager
import com.tcl.ai.sdk.assistant.ICallResult
import com.tcl.ai.sdk.assistant.callback.ITransferCallback
import com.tcl.ai.sdk.assistant.entity.TransferInfo
import dagger.hilt.android.EntryPointAccessors
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.io.File
import javax.inject.Inject
import kotlin.coroutines.resume

/**
 * 录音的Worker
 */
class MeetingMinutesWorker(appContext: Context, params: WorkerParameters) :
    CoroutineWorker(appContext, params) {

    @Inject
    lateinit var recordRepository: AudioRepository
    //private lateinit var audioPath: String

    init {
        initializeDependencies()
    }

    private fun initializeDependencies() {
        val entryPoint =
            EntryPointAccessors.fromApplication(applicationContext, AppEntryPoint::class.java)
        recordRepository = entryPoint.recordRepository()
    }

    companion object {
        const val TAG = "MeetingMinutesWorker"
        const val OUTPUT_KEY = "OUTPUT_KEY"

        private const val START_MEETING_MINUTES_NOTIFICATION_ID = 999
        private const val MEETING_MINUTES_SUCCEED_NOTIFICATION_ID = 998
        private const val MEETING_MINUTES_FAILED_NOTIFICATION_ID = 997

        const val DEFAULT_EXPECTED_COST = 0
        val expectedCostFlow = MutableStateFlow(DEFAULT_EXPECTED_COST)
        private val _generateResultFlow = MutableStateFlow(GenerateResultState())
        val generateResultFlow = _generateResultFlow.asStateFlow()

        fun resetGenerateResult() {
            _generateResultFlow.value = GenerateResultState()
        }
    }

    private fun getServiceInfo(): ForegroundInfo {
        val remoteViews = updateSmallNotification(
            //0,
            applicationContext,
            applicationContext.getString(com.tcl.ai.note.base.R.string.tips_notification_generating)
        )
        return ForegroundInfo(
            START_MEETING_MINUTES_NOTIFICATION_ID, createNotification(applicationContext) {
                setSmallIcon(R.drawable.ic_tcl_ai)
                setStyle(NotificationCompat.DecoratedCustomViewStyle())
                setCustomContentView(remoteViews)
                setCustomBigContentView(remoteViews)
                priority = NotificationCompat.PRIORITY_DEFAULT
                //setContentIntent(getPendingIntent(applicationContext, audioRecordGsonString!!))
                mNotificationBuilder = this
            }, ServiceInfo.FOREGROUND_SERVICE_TYPE_SPECIAL_USE
        )
    }

    private lateinit var mNotificationBuilder: NotificationCompat.Builder



    //Worker执行任务
    // todo 添加问答任务逻辑
    override suspend fun doWork(): Result {
        expectedCostFlow.emit(DEFAULT_EXPECTED_COST)
        val audioPath = inputData.getString(EXTRA_AUDIO_RECORD)?: ""
        Logger.d(TAG, "doWork: $audioPath, file exist: ${File(audioPath).exists()}")
        val coroutineScope = CoroutineScope(Dispatchers.Main)
        recordRepository.observeAudioDeletion(audioPath).onEach { isDeleted ->
            if (isDeleted) {
                Logger.i(TAG, "cancelAllWork")
                WorkManager.getInstance(applicationContext).cancelAllWorkByTag(audioPath)
                coroutineScope.cancel()
            }
        }.launchIn(coroutineScope)

        setForeground(getServiceInfo())
        _generateResultFlow.value = GenerateResultState(false, audioPath)

        withContext(Dispatchers.IO) {
            recordRepository.deleteTransfersByAudioPath(audioPath)
            recordRepository.saveCurrentTranscriptionFile(audioPath)
        }

        val startTranscripeMillis = System.currentTimeMillis()
        val data = startTask(audioPath)
        if (data.isNotEmpty()) {
            // 转写成功埋点
            TclAnalytics.reportRecordToTextCompletionState("0", (System.currentTimeMillis() - startTranscripeMillis).toString())

            Logger.i(TAG, "result data: $data")
            showSuccessNotification()
            withContext(Dispatchers.IO) {
                recordRepository.saveTranscription(audioPath, data)
                recordRepository.saveCurrentTranscriptionFile("")
            }
            _generateResultFlow.value = GenerateResultState(
                true,
                audioPath,
                result = data
            )
            Logger.d(TAG, "startTask success end")
        } else {
            // 转写失败埋点
            TclAnalytics.reportRecordToTextCompletionState("1", (System.currentTimeMillis() - startTranscripeMillis).toString())

            WorkManager.getInstance(applicationContext).cancelAllWorkByTag(audioPath)
            showFailedNotification()
            Logger.d(TAG, "startTask fail")
            _generateResultFlow.value = GenerateResultState(
                true,
                audioPath,
                errorState = ErrorState.GenerateError
            )
            withContext(Dispatchers.IO) {
                recordRepository.saveCurrentTranscriptionFile("")
            }
            Logger.d(TAG, "startTask fail end")
        }

        val outData = Data.Builder().putString(OUTPUT_KEY, Gson().toJson(data)).build()

        coroutineScope.cancel()
        return Result.success(outData)
    }

    //创建ForegroundInfo Worker将会作为前台服务运行
    override suspend fun getForegroundInfo(): ForegroundInfo {
        Logger.d(TAG, "getForegroundInfo")
        return getServiceInfo()
    }


    // {"res":{"endTime":"2024-08-05 15:48:37","id":"1820366106557157378","languageCode":"CN","manufacturer":"azure","meeting":true,"meetingMinutes":"**提取关键词**  \n金属小圆片、翻译器、礼物、外星使者、植物镇、传统、大餐、菜肴、胡萝卜、烹饪\n\n**会议总结**  \n外星使者在植物镇受到热情款待，带来了金属翻译器和礼物。植物们对外星人充满好奇，准备提供丰盛的大餐。使者对菜肴和烹饪过程表现出极大的兴趣。\n\n**待办事项**  \n1. 准备丰盛的大餐给外星使者。  \n2. 解释植物镇的传统和菜肴制作过程。  \n3. 收集外星使者关于他们家乡的烹饪信息。","message":"Success","name":"azureTaranscription-8d2f94e2-d34e-4b12-bf4e-ddee859ef65d","startTime":"2024-08-05 15:47:55","status":3,"taskId":"8d2f94e2-d34e-4b12-bf4e-ddee859ef65d","textLength":"260","transfers":[{"content":"有一个金属小圆片。","endTime":1440,"personNum":"1","startTime":80},{"content":"那一定是他们的翻译器哇，他还带了这么多礼物。","endTime":6320,"personNum":"2","startTime":1760},{"content":"植物们伸长了脖子，对外星使者充满了好奇。外星人也觉得地球上的一切都很新鲜。按照植物镇的传统，他们要请远道而来。","endTime":21360,"personNum":"3","startTime":7280},{"content":"的客。","endTime":21840,"personNum":"1","startTime":21360},{"content":"人享用一顿。","endTime":23280,"personNum":"3","startTime":21840},{"content":"丰盛的大餐。","endTime":24560,"personNum":"2","startTime":23360},{"content":"M16星的使者看到桌上的菜肴。","endTime":28800,"personNum":"3","startTime":25520},{"content":"问题一个接一个的往外冒我。","endTime":33280,"personNum":"3","startTime":29360},{"content":"们也种植各种工作。","endTime":34800,"personNum":"4","startTime":33280},{"content":"不。","endTime":35680,"personNum":"3","startTime":35520},{"content":"过和这些不太一样这是什么形状怎么这么奇怪？","endTime":40880,"personNum":"5","startTime":35680},{"content":"使者指着盘子里做成花朵形的胡萝卜说，伙计。梳妆。老师笑了笑，解释。","endTime":49200,"personNum":"3","startTime":41760},{"content":"道。","endTime":49320,"personNum":"5","startTime":49200},{"content":"吼。","endTime":50080,"personNum":"4","startTime":49840},{"content":"吼。","endTime":50320,"personNum":"3","startTime":50080},{"content":"他们原本不是这样的，是做菜时加工而成的。","endTime":55520,"personNum":"4","startTime":50720},{"content":"使者更好奇了。","endTime":58560,"personNum":"3","startTime":56880},{"content":"哦。","endTime":59280,"personNum":"3","startTime":59160},{"content":"做菜。","endTime":60240,"personNum":"5","startTime":59600},{"content":"是指。","endTime":61280,"personNum":"4","startTime":60640},{"content":"烹饪吗？","endTime":61920,"personNum":"3","startTime":61280},{"content":"地球上的烹饪。","endTime":63680,"personNum":"5","startTime":62560},{"content":"听。","endTime":64160,"personNum":"3","startTime":63920},{"content":"起来好复杂，在我的家乡。","endTime":67360,"personNum":"5","startTime":64160}]}}
    private suspend fun startTask(audioPath: String): List<AudioTransferEntity> {
        var result = emptyList<AudioTransferEntity>()
        suspendCancellableCoroutine { continuation ->
            val selectLanguagePair = translationLanguageList[recordRepository.getLanguage()]
            val transferCallback = object : ITransferCallback {
                override fun onFailure(errCode: Int) {
                    Logger.d(TAG, "asr callback failed: $errCode")
                    //WorkManager.getInstance(applicationContext).cancelAllWork()
                    if (errCode == 430) {
                        _generateResultFlow.value = GenerateResultState(
                            true,
                            audioPath,
                            errorState = ErrorState.SensitiveWords
                        )
                    }
                    showFailedNotification()
                    continuation.resume("Asr process failed")
                }

                override fun onSuccess(transfers: List<TransferInfo>) {
                    result = transfers.map {
                        AudioTransferEntity(
                            audioPath = audioPath,
                            content = it.content,
                            startTime = it.startTime,
                            endTime = it.endTime,
                            personNum = it.personNum
                        )
                    }
                    continuation.resume("Asr process Succeed")
                }

                override fun onUsageLimit(remainCredit: Long) {
                    //WorkManager.getInstance(applicationContext).cancelAllWork()
                    Logger.d(TAG, "onUsageLimit: $remainCredit")
                    showFailedNotification()
                    _generateResultFlow.value = GenerateResultState(
                        true,
                        audioPath,
                        errorState = ErrorState.UsageLimit
                    )
                    continuation.resume("Asr process onUsageLimit")
                }

            }

            val assistantResult = object : ICallResult.Stub() {
                override fun onSuccess(p0: Int) {
                    val remoteViews = updateSmallNotification(
                        //progress,
                        applicationContext,
                        applicationContext.getString(com.tcl.ai.note.base.R.string.tips_notification_generating)
                    )
                    mNotificationBuilder .setStyle(NotificationCompat.DecoratedCustomViewStyle())
                        .setCustomContentView(remoteViews )
                        .setCustomBigContentView(remoteViews)
                    notifyNotification(
                        applicationContext,
                        START_MEETING_MINUTES_NOTIFICATION_ID,
                        mNotificationBuilder.build()
                    )
                    /*Logger.d(TAG, "obtain assistant success: $p0, token: ${AccountController.token}, " +
                            "baseHost: ${AccountController.baseHost}, languageCode: ${languageCode}, " )*/
                    AssistantServiceManager.instance.processAsr(
                        callId = p0,
                        authorization = AccountController.token,
                        serverUrl = AccountController.baseHost,
                        fileSaasUrl = AccountController.fileSaasBaseUrl,
                        file = File(audioPath),
                        langCode = languageCode,
                        meeting = false,
                        countryCode = AccountController.countryCode,
                        functionName = "AudioToText",
                        appId = BuildConfig.appId,
                        secretKey = BuildConfig.secretKey,
                        transferCallback,
                        callResult = object : ICallResult.Stub() {
                            override fun onSuccess(p0: Int) {
                                Logger.d(TAG, "process asr success: $p0")
                            }

                            override fun onFailure(p0: Int) {
                                Logger.d(TAG, "process asr failed: $p0")
                            }
                        }
                    )
                }

                override fun onFailure(p0: Int) {
                    Logger.i(TAG, "obtain assistant failed: $p0")
                    continuation.resume("Assistant obtain failed")
                }

            }

            AssistantServiceManager.instance.obtainAssistant(assistantResult)
        }

        Logger.d(TAG, "startTask finish $result")
        return result
    }

    private fun showSuccessNotification() {
        val remoteViews = updateSmallNotification(
            //100,
            applicationContext,
            applicationContext.getString(com.tcl.ai.note.base.R.string.tips_notification_generated)
        )
        notifyNotification(applicationContext, MEETING_MINUTES_SUCCEED_NOTIFICATION_ID) {
            setSmallIcon(R.drawable.ic_tcl_ai)
            setStyle(NotificationCompat.DecoratedCustomViewStyle())
            setCustomContentView(remoteViews)
            setCustomBigContentView(remoteViews)
            setContentText(applicationContext.getString(com.tcl.ai.note.base.R.string.tips_notification_generated))
            priority = NotificationCompat.PRIORITY_DEFAULT
            setAutoCancel(true)
            //setContentIntent(getPendingIntent(applicationContext, audioRecordGsonString))
        }
    }

    private fun showFailedNotification() {
        val remoteViews = updateSmallNotification(
            //100,
            applicationContext,
            applicationContext.getString(com.tcl.ai.note.base.R.string.text_audio_to_text_failed)
        )
        // 覆盖转写中的进度通知
        notifyNotification(applicationContext, MEETING_MINUTES_FAILED_NOTIFICATION_ID) {
            setSmallIcon(R.drawable.ic_tcl_ai)
            setStyle(NotificationCompat.DecoratedCustomViewStyle())
            setCustomContentView(remoteViews)
            setCustomBigContentView(remoteViews)
            priority = NotificationCompat.PRIORITY_DEFAULT
            setAutoCancel(true)
            /*addAction(
                NotificationCompat.Action.Builder(
                    R.drawable.icon_retry,
                    applicationContext.getString(R.string.retry),
                    getRetryPendingIntent(applicationContext, audioRecordGsonString!!)
                ).build()
            )*/
            //setContentIntent(getPendingIntent(applicationContext, audioRecordGsonString!!))
        }
    }

    private fun updateSmallNotification(
        //progress: Int,
        applicationContext: Context,
        tips: String
    ): RemoteViews {
        val remoteViews = RemoteViews(
            applicationContext.packageName,
            R.layout.notification_meeting_minutes
        )
        /*remoteViews.setTextViewText(
            R.id.meet_audio_name,
            audioPath
        )*/
        remoteViews.setTextViewText(
            R.id.meet_audio_tips,
            tips
        )
        /*remoteViews.setProgressBar(
            R.id.meet_progress_bar,
            100,
            progress,
            false
        )*/
        return remoteViews
    }
}

