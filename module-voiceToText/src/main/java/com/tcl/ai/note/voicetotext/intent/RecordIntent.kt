package com.tcl.ai.note.voicetotext.intent


/**
 * 录音意图
 */
sealed class RecordIntent {
    /**
     * 用于开始录音的意图
     */
    data class StartRecord(val audioPath: String) : RecordIntent()

    /**
     * 检查是否正在录音
     */
    //data object CheckRecording : RecordIntent()

    /**
     * 停止录音
     */
    data object StopRecord : RecordIntent()

    /**
     * 保存录音
     */
    data object SaveRecord : RecordIntent()
}

