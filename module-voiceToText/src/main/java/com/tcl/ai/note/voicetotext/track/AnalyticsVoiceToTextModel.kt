package com.tcl.ai.note.voicetotext.track

import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.track.AbsAnalyticsSubModel
import com.tcl.ai.note.track.TclAnalytics
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.voicetotext.states.ErrorState
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.voicetotext.worker.MeetingMinutesWorker
import kotlinx.coroutines.flow.filter
import java.lang.ref.WeakReference

/**
 * 分析录音转写
 *
 * 用于上报埋点
 */
object AnalyticsVoiceToTextModel: AbsAnalyticsSubModel() {

    internal fun loadAudioToTextViewModel(audioToTextViewModel: AudioToTextViewModel): Unit {
        reportRecordToTextUsage(audioToTextViewModel)
    }

    /**
     * 上报录音转写埋点
     */
    private fun reportRecordToTextUsage(audioToTextViewModel: AudioToTextViewModel) {
        // 这里有个bug，每次viewModelScope销毁重建时，都会上报一次埋点
        // collectWithScope的audioToTextViewModel.viewModelScope改成App的Scope能解决问题
        MeetingMinutesWorker.generateResultFlow
            .filter { it.isGenerateComplete }
            .collectWithScope(audioToTextViewModel.viewModelScope){ result ->
                if (result.filePath.isNullOrEmpty()) {
                    return@collectWithScope
                }
                // 上报录音转写埋点
                TclAnalytics.reportRecordToTextUsage(
                    result.filePath,
                    getAudioDuration(result.filePath).toString(),
                    when (result.errorState) {
                        ErrorState.AudioDurationError -> "1"
                        ErrorState.GenerateError -> ""
                        ErrorState.NetworkError -> ""
                        ErrorState.OtherFileInTranscription -> ""
                        ErrorState.SensitiveWords -> ""
                        ErrorState.UsageLimit -> "3"
                        null -> "0"
                    }
                )
        }

    }
}