package com.tcl.ai.note.voicetotext.view.widget

import android.text.format.DateUtils
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import com.tcl.ai.note.controller.LoginErrorResult
import com.tcl.ai.note.controller.isAIServiceEnable
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.HoverMutableInteractionSource
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.voicetotext.R
import com.tcl.ai.note.voicetotext.util.formatAudioTimeHourMinuteSecond
import com.tcl.ai.note.voicetotext.util.getAudioDuration
import com.tcl.ai.note.voicetotext.util.getAudioFileFolder
import com.tcl.ai.note.voicetotext.util.getFileCreateFormatTime
import com.tcl.ai.note.voicetotext.vm.AudioToTextViewModel
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.MarqueeText
import com.tcl.ai.note.widget.VerticalLine
import com.tcl.ai.note.widget.components.AIServiceDialog
import com.tcl.ai.note.widget.verticalScrollbar
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.io.File

@Composable
fun RecordingList(
    noteId: Long,
    audioPaths: List<String>,
    audioDurations: List<Long>,
    currentDisplayedAudioPath: String? = null,
    onDeleteAudios: (List<String>) -> Unit,
    onDismissRequest: () -> Unit,
    onAudioToTextClick: () -> Unit,
    onItemClick: (String) -> Unit,
    onConfirmRename: (String, String) -> Unit,
    audioToTextViewModel: AudioToTextViewModel,
) {
    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    val showAiServiceDialog = remember { mutableStateOf(false) }
    val loginHandler = rememberLoginHandler(onFailure = {
        if (it == LoginErrorResult.LunchError) {
            showAiServiceDialog.value = true
            Logger.d("AudioBlock", "loginHandler onFailure")
        }
    })
    AIServiceDialog(
        isShow = showAiServiceDialog.value,
        title = context.getString(com.tcl.ai.note.base.R.string.audio_to_text),
        onDismiss = { showAiServiceDialog.value = false },
        onGoToSettings = {
            showAiServiceDialog.value = false
        }
    )

    val audioToTextState by audioToTextViewModel.audioToTextState.collectAsState()
    var isEditMode by remember { mutableStateOf(false) }
    var selectedAudios by remember { mutableStateOf(emptyList<String>()) }
    var showDeleteAudioDialog by remember { mutableStateOf(false) }
    val durationMap = remember { mutableStateOf(mutableMapOf<String, String>()) }
    val tintColor = if (isSystemInDarkTheme()) Color(0xFFE9E9E9) else Color(0xFF191919)
    val lazyListState = rememberLazyListState()

    // 计算Popup的最大高度（屏幕高度的60%）
    val screenHeight = LocalDensity.current.run { context.resources.displayMetrics.heightPixels.toDp() }
    val maxPopupHeight = screenHeight * 0.6f

    // 计算Popup的偏移量
    val density = LocalDensity.current
    val alignment = if (isTablet) Alignment.TopEnd else Alignment.TopCenter
    val xOffset = if (isTablet) with(density) { (-24).dp.toPx().toInt() } else 0
    val yOffset = with(density) { 102.dp.toPx().toInt() }

    // 使用rememberUpdatedState确保onDismissRequest引用最新值
    val currentOnDismiss = rememberUpdatedState(onDismissRequest)


    // 动画状态初始化为false，通过LaunchedEffect延迟触发入场动画
    val isVisible = remember { mutableStateOf(false) }
    // 用于跟踪内容是否已完成首次测量
    var isContentMeasured by remember { mutableStateOf(false) }
    // 修复：确保关闭逻辑正确触发动画
    fun handleDismiss() {
        coroutineScope.launch {
            isVisible.value = false // 触发退出动画
            delay(300) // 等待动画结束（与动画时长一致）
            currentOnDismiss.value() // 调用原始关闭回调
        }
    }

    LaunchedEffect(audioToTextState) {
        handleAudioToTextState(audioToTextState, audioToTextViewModel)
    }
    val interactionSource = remember { HoverMutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()


    // 先加载数据，再触发动画
    LaunchedEffect(audioPaths) {
        // 等待durationMap初始化完成（确保列表项高度可计算）
        delay(50) // 给数据初始化留一点时间（根据实际情况调整）
        isVisible.value = true
    }

    Popup(
        alignment = alignment,
        offset = IntOffset(xOffset, yOffset),
        onDismissRequest = { handleDismiss() },
//        properties = PopupProperties(focusable = true)
    ) {
        AnimatedVisibility(
            visible = isVisible.value,
            // 明确动画参数，避免高度计算异常
            enter = expandVertically(
                initialHeight = { 0 }, // 从0开始展开
                animationSpec = tween(
                    durationMillis = 300, // 动画时长缩短，减少视觉异常
                    easing = LinearEasing
                )
            ),
            exit = shrinkVertically(
                animationSpec = tween(300)
            )
        ) {
            Box(
                modifier = if (isTablet) {
                    Modifier
                        .width(328.dp)
                        .heightIn(max = maxPopupHeight) // 限制最大高度
                        .shadow(4.dp, RoundedCornerShape(16.dp))
                        .background(TclTheme.colorScheme.reWriteExpandBg, RoundedCornerShape(16.dp))
                        .onGloballyPositioned { coordinates ->
                            // 标记内容已完成测量
                            isContentMeasured = true
                        }
                } else {
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .heightIn(max = maxPopupHeight) // 限制最大高度
                        .shadow(4.dp, RoundedCornerShape(16.dp))
                        .background(TclTheme.colorScheme.reWriteExpandBg, RoundedCornerShape(16.dp))
                }
            ) {
                Column(modifier = Modifier.padding(vertical = 24.dp)) {
                    // 顶部区域
                    Row(
                        Modifier
                            .fillMaxWidth()
                            .padding(start = 18.dp, end = 15.dp)
                            .height(40.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (isEditMode) {
                            Text(
                                text = String.format(
                                    stringResource(com.tcl.ai.note.base.R.string.selected_audios),
                                    selectedAudios.size
                                ),
                                color = TclTheme.colorScheme.tctStanderTextPrimary,
                                fontSize = 20.sp,
                                fontWeight = FontWeight.W500,
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(start = 8.dp)
                            )

                            val selectAllDescription = stringResource(
                                if (audioPaths.size == selectedAudios.size)
                                    com.tcl.ai.note.base.R.string.deselect_all_audios
                                else
                                    com.tcl.ai.note.base.R.string.select_all_audios
                            )
                            HoverProofIconButton(
                                modifier = Modifier
                                    .size(24.dp)
                                    .semantics {
                                        contentDescription = selectAllDescription
                                    },
                                onClick = {
                                    selectedAudios = if (audioPaths.size == selectedAudios.size) {
                                        selectedAudios - selectedAudios.toSet()
                                    } else {
                                        audioPaths
                                    }
                                },
                            ) {
                                Image(
                                    painter = painterResource(
                                        id =
                                            if (audioPaths.size == selectedAudios.size)
                                                com.tcl.ai.note.base.R.drawable.ic_selected_all_checked
                                            else com.tcl.ai.note.base.R.drawable.ic_selected_all
                                    ),
                                    contentDescription = null,
                                    alpha = if (selectedAudios.isNotEmpty()) 1f else 0.5f
                                )
                            }

                            Spacer(modifier = Modifier.width(18.dp))

                            val deleteDescription =
                                stringResource(com.tcl.ai.note.base.R.string.delete_selected_audios)
                            HoverProofIconButton(
                                modifier = Modifier
                                    .size(24.dp)
                                    .semantics {
                                        contentDescription = deleteDescription
                                    },
                                enabled = selectedAudios.isNotEmpty(),
                                onClick = { showDeleteAudioDialog = true },
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.ic_audio_delete),
                                    contentDescription = null,
                                    alpha = if (selectedAudios.isNotEmpty()) 1f else 0.5f
                                )
                            }
                        } else {
                            MarqueeText(
                                text = stringResource(id = com.tcl.ai.note.base.R.string.recording_audio),
                                fontSize = 20.sp,
                                fontWeight = FontWeight.W500,
                                modifier = Modifier.weight(1f),
                                color = tintColor
                            )
                            // 编辑按钮
                            val editDescription =
                                stringResource(com.tcl.ai.note.base.R.string.accessibility_edit_recordings)
                            Text(
                                text = stringResource(id = com.tcl.ai.note.base.R.string.edit),
                                fontSize = 14.sp,
                                color = tintColor,
                                modifier = Modifier
                                    .semantics {
                                        contentDescription = editDescription
                                        role = Role.Button
                                    }
                                    .then(
                                        if (isPressed) {
                                            Modifier.background(
                                                color = Color(0x1A000000), // 背景颜色，透明度10%
                                                shape = RoundedCornerShape(18.dp)
                                            )
                                        } else {
                                            Modifier
                                        }
                                    )
                                    .padding(start = 8.dp, end = 8.dp)
                                    .clickable(
                                        interactionSource = interactionSource,
                                        indication = null,
                                        onClick = { isEditMode = true }
                                    ),
                            )
                        }
                    }

                    // 列表区域
                    LazyColumn(
                        state = lazyListState,
                        modifier = Modifier
                            .verticalScrollbar(
                                state = lazyListState,
                                alwaysShowScrollBar = false,
                                scrollbarWidth = 3.dp,
                                offsetX = 8f
                            ).heightIn(min = 48.dp) // 给一个最小高度，避免初始高度为0
                    ) {
                        items(audioPaths.size) { index ->
                            val audioPath = audioPaths[index]
                            var audioDuration = if (audioDurations[index] == 0L) getAudioDuration(audioPath) else audioDurations[index]
                            val durationText = formatAudioTimeHourMinuteSecond(audioDuration)

                            if (audioDuration >= 2000L) {
                                RecordingItem(
                                    noteId = noteId,
                                    editMode = isEditMode,
                                    isSelected = selectedAudios.contains(audioPath),
                                    isDisplayed = currentDisplayedAudioPath == audioPath,
                                    audioPath = audioPath,
                                    audioDuration = audioDuration,
                                    durationText = durationText,
                                    onItemClick = { path ->
                                        Logger.d(
                                            "RecordingList",
                                            "onItemClick: $path, isEditMode: $isEditMode"
                                        )
                                        if (isEditMode) {
                                            selectedAudios = if (selectedAudios.contains(path)) {
                                                selectedAudios - path
                                            } else {
                                                selectedAudios + path
                                            }
                                        } else {
                                            onItemClick(path)
                                        }
                                    },
                                    onConfirmRename = { oldPath, newPath ->
                                        audioToTextViewModel.renameAudioFile(oldPath, newPath)
                                        onConfirmRename(oldPath, newPath)
                                    },
                                    onAudioToTextClick = {
                                        onAudioToTextClick()
                                    },
                                    onHandleAudioToText = {
                                        handleAudioToTextClick(
                                            audioPath,
                                            audioToTextViewModel,
                                            loginHandler
                                        )
                                    },
                                    coroutineScope = coroutineScope
                                )
                            }

                        }
                    }
                }

                if (showDeleteAudioDialog) {
                    DeleteRecordingListAudioDialog(
                        audioCount = selectedAudios.size,
                        isDeleteAll = selectedAudios.size == audioPaths.size,
                        onDismiss = {
                            showDeleteAudioDialog = false
                        },
                        onDelete = {
                            onDeleteAudios(selectedAudios)
                            selectedAudios = emptyList()
                            showDeleteAudioDialog = false
                        },
                    )
                }
            }
        }
    }
}

@Composable
private fun RecordingItem(
    noteId: Long,
    editMode: Boolean = false,
    isSelected: Boolean = false,
    isDisplayed: Boolean = false, // 是否为当前在播放条中显示的录音
    audioPath: String,
    audioDuration: Long,
    durationText: String,
    onItemClick: (String) -> Unit = {},
    onConfirmRename: (String, String) -> Unit,
    onAudioToTextClick: () -> Unit = {},
    onHandleAudioToText: suspend () -> Unit = {},
    coroutineScope: CoroutineScope,
) {
    val dimens = getGlobalDimens()
    var showRenameDialog by remember { mutableStateOf(false) }
    var hasSameNameAudioFile by remember { mutableStateOf(false) }
    var renamedFile by remember { mutableStateOf(File(audioPath)) }

    // 判断是否应该显示选中状态（编辑模式下的选中或当前显示在播放条中）
//    val isHighlighted = (editMode && isSelected) || (!editMode && isDisplayed)

    // 录音名称颜色：选中/当前显示时为橙色，默认为黑色
    val titleColor = if (isDisplayed) {
        Color(0xFFFF9E00) // 橙色
    } else {
        TclTheme.colorScheme.tctStanderTextPrimary // 默认黑色，透明度90%
    }

    val interactionSource = remember { HoverMutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    val itemDescription = if (editMode) {
        stringResource(
            if (isSelected) com.tcl.ai.note.base.R.string.audio_item_selected_description
            else com.tcl.ai.note.base.R.string.audio_item_unselected_description,
            File(audioPath).nameWithoutExtension
        )
    } else {
        stringResource(
            com.tcl.ai.note.base.R.string.audio_item_description,
            File(audioPath).nameWithoutExtension,
            durationText
        )
    }

    Row(
        Modifier
            .fillMaxWidth()
            .height(48.dp)
            .padding(horizontal = 12.dp)
            .semantics {
                contentDescription = itemDescription
                role = if (editMode) Role.Checkbox else Role.Button
            }
            .then(
                if (isPressed) {
                    Modifier.background(
                        color = Color(0x1A000000), // 背景颜色，透明度10%
                        shape = RoundedCornerShape(8.dp)
                    )
                } else {
                    Modifier
                }
            )
            .clickable(
                interactionSource = interactionSource,
                indication = ripple(),
                onClick = { onItemClick(audioPath) }),
        verticalAlignment = Alignment.CenterVertically
    ) {

        if(editMode){
            // 编辑模式下显示复选图标
            if(isSelected){
                Box(
                    Modifier
                        .padding(start = 12.dp)
                        .size(20.dp), Alignment.Center) {
                    Image(
                        painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_note_checkbox_selected),
                        contentDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_recording_selected)
                    )
                }
            } else {
                Box(
                    Modifier
                        .padding(start = 12.dp)
                        .size(20.dp), Alignment.Center) {
                    Image(
                        painter = painterResource(id = com.tcl.ai.note.base.R.drawable.ic_note_checkbox_normal),
                        contentDescription = stringResource(com.tcl.ai.note.base.R.string.accessibility_recording_not_selected)
                    )
                }
            }
        }

        Column(
            modifier = Modifier
                .fillMaxHeight()
                .padding(start = 8.dp, end = 12.dp, top = 5.dp, bottom = 9.dp)
                .weight(1f),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = File(audioPath).nameWithoutExtension.trim(),
                fontSize = 14.sp,
                color = titleColor,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.wrapContentHeight(unbounded = true)
            )
            Text(
                text = getFileCreateFormatTime(audioPath),
                fontSize = 10.sp,
                color = TclTheme.colorScheme.tctStanderTextSecondary,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.wrapContentHeight(unbounded = true)
            )
        }
        val textFontSize = if (audioDuration != null && audioDuration!! >= DateUtils.HOUR_IN_MILLIS) 12.sp else 14.sp
        Text(
            text = durationText,
            fontSize = textFontSize,
            color = TclTheme.colorScheme.tctStanderTextPrimary,
            textAlign = TextAlign.End,
            modifier = Modifier
                .padding(end = 16.dp)
        )

        if (!editMode) {
            VerticalLine(modifier = Modifier.padding(vertical = 18.dp))
            val renameDescription = stringResource(com.tcl.ai.note.base.R.string.rename_audio_button)
            HoverProofIconButton(
                modifier = Modifier
                    .padding(start = 12.dp)
                    .size(dimens.btnSize)
                    .semantics {
                        contentDescription = renameDescription
                    },
                enabled = true,
                onClick = {
                    showRenameDialog = true
                }
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_audio_rename),
                    contentDescription = stringResource(id = com.tcl.ai.note.base.R.string.accessibility_rename_recording),
                    modifier = Modifier.size(dimens.iconSize),
                )
            }
            if (isAIServiceEnable) {
                val audioToTextDescription = stringResource(com.tcl.ai.note.base.R.string.audio_to_text_button)
                HoverProofIconButton(
                    modifier = Modifier
                        .size(dimens.btnSize)
                        .semantics {
                            contentDescription = audioToTextDescription
                        },
                    enabled = true,
                    onClick = {
                        coroutineScope.launch {
                            onHandleAudioToText()
                        }
                        onAudioToTextClick()
                    }
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_audio_to_text_new),
                        contentDescription = null,
                        modifier = Modifier.size(dimens.iconSize),
                    )
                }
            }
        }

    }
    if (showRenameDialog) {
        Logger.d("RecordingList", "showRenameDialog: $audioPath")
        InputDialog(
            title = stringResource(id = com.tcl.ai.note.base.R.string.rename_audio),
            text = File(audioPath).nameWithoutExtension.trim(),
            onValueChange = {
                renamedFile = File(getAudioFileFolder(noteId = noteId), "$it.amr")
                hasSameNameAudioFile = renamedFile.exists() && renamedFile != File(audioPath)
            },
            isError = hasSameNameAudioFile,
            error = stringResource(id = com.tcl.ai.note.base.R.string.text_same_audio_file),
            maxLength = 50,
            onConfirm = {
                coroutineScope.launch {
                    onConfirmRename(audioPath, renamedFile.absolutePath)
                }
                showRenameDialog = false
            },
            onDismissRequest = {
                showRenameDialog = false
                hasSameNameAudioFile = false
                //renamedFile = File("")
            })
    }
}

//private fun getRecordingItemDurationText(
//    audioPath: String,
//    durationMap: MutableState<MutableMap<String, String>>
//): String {
//    if (!durationMap.value.containsKey(audioPath)) {
//        durationMap.value[audioPath] = formatAudioTimeHourMinuteSecond(getAudioDuration(audioPath))
//    }
//    return durationMap.value[audioPath]!!
//}