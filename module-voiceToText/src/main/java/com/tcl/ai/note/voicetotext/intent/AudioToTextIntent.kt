package com.tcl.ai.note.voicetotext.intent

import com.tcl.ai.note.voicetotext.data.AudioTransferEntity


/**
 * 录音转文本意图
 */
sealed class AudioToTextIntent {
    /**
     * 用于开始音频转录的意图
     */
    data object StartTranscription : AudioToTextIntent()

    /**
     * 检查音频时长
     */
    data object CheckAudioDuration : AudioToTextIntent()

    /**
     * 从本地数据库获取转录文本的意图
     */
    data class GetLocalTranscription(val transfers: List<AudioTransferEntity>?) : AudioToTextIntent()

    /**
     * 从AI接口获取转录文本的意图
     */
    data object GetAITranscription : AudioToTextIntent()

    /**
     * 语言变化时重新获取录音转文
     * @param index 选择语言index
     * */
    data class GetTranscriptionOnLanguageChanged(val index: Int) : AudioToTextIntent()

    /**
     * 删除录音记录
     * */
    data object DeleteAudioRecord : AudioToTextIntent()
}

