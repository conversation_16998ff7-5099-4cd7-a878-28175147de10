package com.tcl.ai.note.voicetotext.states

import com.tcl.ai.note.voicetotext.data.AudioTransferEntity

/**
 * 音频流消息的数据类
 * @property status 音频流的状态
 * @property transfers 音频传输实体的列表
 * @property threadId 服务器的对话ID，用于标识单轮对话
 * @property userMessageId 服务器返回的用户消息ID，用于retry时上传
 */
data class AudioStreamingMsg(
    val status : AudioStreamingStatus,
    val transfers : List<AudioTransferEntity>,
    val threadId : String, // 服务器的对话ID，用于标识单轮对话
    val userMessageId : String = "" // 服务器返回的用户消息ID，用于retry时上传
)

/**
 * 音频流状态的枚举类
 */
enum class AudioStreamingStatus {
    IN_PROGRESS, // 进行中
    COMPLETED,   // 已完成
    STOPPED      // 已停止
}

/**
 * 音频结果的密封接口，包含三种状态：成功、错误和加载中
 */
sealed interface AudioResult<out T> {
    /**
     * 成功状态的数据类
     * @property data 成功返回的数据
     */
    data class Success<T>(val data: T) : AudioResult<T>

    /**
     * 错误状态的数据类
     * @property exception 抛出的异常
     * @property code 可选的错误代码
     */
    data class Error(val exception: Throwable, val code: Int? = null) : AudioResult<Nothing>

    /**
     * 加载中状态的对象
     */
    data object Loading : AudioResult<Nothing>
}

/**
 * 音频网络错误类
 * 用于没有网络的时候抛出异常 对应的是MsgStatus.NET_ERROR
 */
class AudioNetError(message: String) : Throwable(message) {
    // 用于没有网络的时候抛出异常 对应的是MsgStatus.NET_ERROR
}


