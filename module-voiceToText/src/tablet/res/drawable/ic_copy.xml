<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="27dp"
    android:height="27dp"
    android:viewportWidth="27"
    android:viewportHeight="27">
  <group>
    <clip-path
        android:pathData="M0.695,0.389h26v26h-26z"/>
    <path
        android:pathData="M13.695,26.387C20.875,26.387 26.695,20.566 26.695,13.387C26.695,6.207 20.875,0.387 13.695,0.387C6.515,0.387 0.695,6.207 0.695,13.387C0.695,20.566 6.515,26.387 13.695,26.387Z"
        android:fillColor="#ffffff"/>
    <path
        android:pathData="M13.695,26.387C20.875,26.387 26.695,20.566 26.695,13.387C26.695,6.207 20.875,0.387 13.695,0.387C6.515,0.387 0.695,6.207 0.695,13.387C0.695,20.566 6.515,26.387 13.695,26.387Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="13.695"
            android:startY="0.387"
            android:endX="13.695"
            android:endY="26.387"
            android:type="linear">
          <item android:offset="0" android:color="#FFE4F4FF"/>
          <item android:offset="1" android:color="#FFF7F6FF"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M15.945,10.324H8.82C8.371,10.324 8.007,10.688 8.007,11.137V18.262C8.007,18.711 8.371,19.074 8.82,19.074H15.945C16.394,19.074 16.757,18.711 16.757,18.262V11.137C16.757,10.688 16.394,10.324 15.945,10.324Z"
        android:strokeWidth="1.21875"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="12.382"
            android:startY="10.324"
            android:endX="12.382"
            android:endY="19.074"
            android:type="linear">
          <item android:offset="0" android:color="#FF4968EE"/>
          <item android:offset="1" android:color="#FF7E65F7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M10.632,9.23V8.574C10.632,8.091 11.024,7.699 11.507,7.699H18.507C18.99,7.699 19.382,8.091 19.382,8.574V15.574C19.382,16.058 18.99,16.449 18.507,16.449H17.851"
        android:strokeWidth="1.21875"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="15.007"
            android:startY="7.699"
            android:endX="15.007"
            android:endY="16.449"
            android:type="linear">
          <item android:offset="0" android:color="#FF4968EE"/>
          <item android:offset="1" android:color="#FF7E65F7"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
