plugins {
    alias(libs.plugins.android.library)
    alias(libs.plugins.jetbrains.kotlin.android)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.google.dagger.hilt)
    alias(libs.plugins.google.devtools.ksp)
}
val standaloneHelpWriting = project.findProperty("standaloneHelpWriting")?.toString()?.toBoolean() ?: false
android {
    namespace = "com.tcl.ai.note.helpwriting"
    compileSdk = libs.versions.compileSdk.get().toIntOrNull() ?: 35

    defaultConfig {
        minSdk = libs.versions.minSdk.get().toIntOrNull() ?: 34

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles("consumer-rules.pro")
    }

    buildTypes {
        release {
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
    buildFeatures {
        buildConfig = true
    }
    flavorDimensions.add("device")
    productFlavors {
        create("phone") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "true")
        }
        create("tablet") {
            dimension = "device"
            buildConfigField("boolean", "IS_PHONE", "false")
        }
    }

    sourceSets {
        getByName("phone") {
            java.srcDirs("src/phone/java")
            res.srcDirs("src/phone/res")
            manifest.srcFile("src/phone/AndroidManifest.xml")
        }
        getByName("tablet") {
            java.srcDirs("src/tablet/java")
            res.srcDirs("src/tablet/res")
            manifest.srcFile("src/tablet/AndroidManifest.xml")
        }
    }
    /*sourceSets {
        getByName("main") {
            if (standaloneHelpWriting) {
                manifest.srcFile("src/main/moduleManifest/AndroidManifest.xml")
            } else {
                manifest.srcFile("src/main/AndroidManifest.xml")
            }
        }
    }*/
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
    buildFeatures {
        compose = true
    }

}

dependencies {
    implementation(libs.ai.common)
    implementation(libs.ai.assistant)
    implementation(project(":sdk:lib-account"))

    implementation(libs.androidx.core.ktx)
    implementation(libs.androidx.lifecycle.runtime.ktx)
    implementation(platform(libs.androidx.compose.bom))
    implementation(libs.androidx.ui.graphics)
    implementation(libs.androidx.ui.tooling.preview)
    implementation(libs.compose.material3)
    implementation(libs.androidx.adaptive)
    implementation(libs.androidx.lifecycle.service)
    implementation(libs.dagger.hilt)
    ksp(libs.dagger.hilt.compiler)
    implementation(libs.androidx.hilt.navigation.compose)

    implementation(libs.androidx.activity.compose)
    implementation(libs.androidx.lifecycle.viewmodel.compose)
    implementation(libs.androidx.ui)
    androidTestImplementation(platform(libs.androidx.compose.bom))
    androidTestImplementation(libs.androidx.ui.test.junit4)
    debugImplementation(libs.androidx.ui.tooling)
    debugImplementation(libs.androidx.ui.test.manifest)
    implementation(libs.tclui.compose)
    implementation(libs.tclui.compose.icons)
    lintChecks(libs.tclui.compose.lint)

    implementation("androidx.appcompat:appcompat:1.7.0")
    implementation(libs.airbnb.lottie.compose)


    val markwonVersion = "4.6.2"
    implementation("io.noties.markwon:core:$markwonVersion")
    implementation("io.noties.markwon:ext-strikethrough:$markwonVersion")
    implementation("io.noties.markwon:ext-tables:$markwonVersion")
    implementation("io.noties.markwon:html:$markwonVersion")

    implementation("io.coil-kt:coil:2.6.0")

    testImplementation(libs.junit)
    androidTestImplementation(libs.androidx.junit)
    androidTestImplementation(libs.androidx.espresso.core)

    implementation(project(":module-base"))
    implementation(project(":module-handwritingText"))
    //implementation(libs.ai.common)

}