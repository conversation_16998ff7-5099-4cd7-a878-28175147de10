# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx5120m -XX:MaxMetaspaceSize=512m -Dfile.encoding=UTF-8
# When configured, <PERSON>rad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true


moduleDashboardModule=true
handwritingTextModule=true
handwritingToTextModule=true
textBeautifyModule=false
voiceToTextModule=true
summaryModule=true
polishModule=true
helpWritingModule=true
moduleJournalModule=false


standaloneModuleDashboard=false
standaloneHandwritingText=false
standaloneHandwritingToText=false
standaloneTextBeautify=false
standaloneVoiceToText=false
standaloneSummary=false
standalonePolish=false
standaloneHelpWriting=false
standaloneJournal=false

appId=28575a96052d425e9fa2fc606ea6905c
#test key
#secretKey=sFcE?KZCWFhK49N^8!Cu?2Dlg^oEH#5k

#release key
secretKey=GzOVOOfS2DQ%mE0jcgNY^sMN0zgal6CR

remote.sign.group=tcl_release_new
remote.sign.type=release
remote.sign.project=TCL_GooglePlay_AAB_release
