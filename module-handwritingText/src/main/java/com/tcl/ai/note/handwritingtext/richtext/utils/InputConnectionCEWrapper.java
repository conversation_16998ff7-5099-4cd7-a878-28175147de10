package com.tcl.ai.note.handwritingtext.richtext.utils;

import android.annotation.SuppressLint;
import android.os.Bundle;
import android.os.Handler;
import android.view.KeyEvent;
import android.view.inputmethod.CompletionInfo;
import android.view.inputmethod.CorrectionInfo;
import android.view.inputmethod.ExtractedText;
import android.view.inputmethod.ExtractedTextRequest;
import android.view.inputmethod.InputConnection;
import android.view.inputmethod.InputContentInfo;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.tcl.ai.note.utils.Logger;


/**
 * for monkey test
 * ignore some ims binder call exception
 */
public class InputConnectionCEWrapper implements InputConnection {

    private final static String TAG = "InputConnectionCEWrapper";

    private final InputConnection mBase;

    public InputConnectionCEWrapper(InputConnection base) {
       this.mBase = base;
    }

    @Override
    public CharSequence getTextBeforeCursor(int n, int flags) {
        return mBase.getTextBeforeCursor(n, flags);
    }

    @Override
    public CharSequence getTextAfterCursor(int n, int flags) {
        return mBase.getTextAfterCursor(n, flags);
    }

    @Override
    public CharSequence getSelectedText(int flags) {
        return mBase.getSelectedText(flags);
    }

    @Override
    public int getCursorCapsMode(int reqModes) {
        return mBase.getCursorCapsMode(reqModes);
    }

    @Override
    public ExtractedText getExtractedText(ExtractedTextRequest request, int flags) {
        return mBase.getExtractedText(request, flags);
    }

    @Override
    public boolean deleteSurroundingText(int beforeLength, int afterLength) {
        try {
            return mBase.deleteSurroundingText(beforeLength, afterLength);
        } catch (IndexOutOfBoundsException e) {
            Logger.e(TAG, e.getMessage());
            return false;
        }
    }

    @SuppressLint("NewApi")
    @Override
    public boolean deleteSurroundingTextInCodePoints(int beforeLength, int afterLength) {
        try {
            return mBase.deleteSurroundingTextInCodePoints(beforeLength, afterLength);
        }catch (NoSuchMethodError e) {
            return false;
        }
    }

    @Override
    public boolean setComposingText(CharSequence text, int newCursorPosition) {
        return mBase.setComposingText(text, newCursorPosition);
    }

    @Override
    public boolean setComposingRegion(int start, int end) {
        return mBase.setComposingRegion(start, end);
    }

    @Override
    public boolean finishComposingText() {
        return mBase.finishComposingText();
    }

    @Override
    public boolean commitText(CharSequence text, int newCursorPosition) {
        return mBase.commitText(text, newCursorPosition);
    }

    @Override
    public boolean commitCompletion(CompletionInfo text) {
        return mBase.commitCompletion(text);
    }

    @Override
    public boolean commitCorrection(CorrectionInfo correctionInfo) {
        return mBase.commitCorrection(correctionInfo);
    }

    @Override
    public boolean setSelection(int start, int end) {
        return mBase.setSelection(start, end);
    }

    @Override
    public boolean performEditorAction(int editorAction) {
        return mBase.performEditorAction(editorAction);
    }

    @Override
    public boolean performContextMenuAction(int id) {
        return mBase.performContextMenuAction(id);
    }

    @Override
    public boolean beginBatchEdit() {
        return mBase.beginBatchEdit();
    }

    @Override
    public boolean endBatchEdit() {
        return mBase.endBatchEdit();
    }

    @Override
    public boolean sendKeyEvent(KeyEvent event) {
        return mBase.sendKeyEvent(event);
    }

    @Override
    public boolean clearMetaKeyStates(int states) {
        return mBase.clearMetaKeyStates(states);
    }

    @Override
    public boolean reportFullscreenMode(boolean enabled) {
        return mBase.reportFullscreenMode(enabled);
    }

    @Override
    public boolean performPrivateCommand(String action, Bundle data) {
        return mBase.performPrivateCommand(action, data);
    }

    @Override
    public boolean requestCursorUpdates(int cursorUpdateMode) {
        return mBase.requestCursorUpdates(cursorUpdateMode);
    }

    @SuppressLint("NewApi")
    @Override
    public Handler getHandler() {
        try {
            return mBase.getHandler();
        }catch (NoSuchMethodError e) {
            Logger.e(TAG, e.getMessage());
            return null;
        }
    }

    @SuppressLint("NewApi")
    @Override
    public void closeConnection() {
        try {
            mBase.closeConnection();
        } catch (NoSuchMethodError e) {
            Logger.e(TAG, e.getMessage());
        }
    }

    @SuppressLint("NewApi")
    @Override
    public boolean commitContent(@NonNull InputContentInfo inputContentInfo, int flags, @Nullable Bundle opts) {
        try {
            return mBase.commitContent(inputContentInfo, flags, opts);
        }catch (NoSuchMethodError e) {
            Logger.e(TAG, e.getMessage());
            return false;
        }
    }
}
