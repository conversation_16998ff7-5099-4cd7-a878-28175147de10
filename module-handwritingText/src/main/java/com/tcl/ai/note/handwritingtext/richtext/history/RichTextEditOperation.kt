package com.tcl.ai.note.handwritingtext.richtext.history

import android.text.Spannable

/**
 * 编辑操作记录
 */
data class RichTextEditOperation(
    val beforeSpanned: Spannable,  // 操作前的状态
    val afterSpanned: Spannable,   // 操作后的状态
    val start: Int,              // 操作开始位置
    val end: Int,                // 操作结束位置
    val operationType: OperationType,  // 操作类型
    val spanClass: Class<*>?,    // 样式类型
    val indentLevel: Int = 0,    // 缩进级别
    val isChecked: Boolean = false, // 待办事项选中状态
    val timestamp: Long          // 操作时间戳
)

/**
 * 操作类型枚举
 */
enum class OperationType {
    TEXT_INSERT,      // 插入文本
    TEXT_DELETE,      // 删除文本
    STYLE_APPLY,      // 应用样式
    STYLE_REMOVE,     // 移除样式
    PARAGRAPH_CHANGE, // 段落格式变化
    INDENT_CHANGE,    // 缩进变化（通用）
    INDENT_RIGHT,     // 右缩进（增加缩进）
    INDENT_LEFT       // 左缩进（减少缩进）
}