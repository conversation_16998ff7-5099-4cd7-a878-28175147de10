package com.tcl.ai.note.handwritingtext.richtext.Commands;

import android.os.Handler;

/*
asynchronous communication between presenter and model,
/ */
public class PM_Commands{

    //model_ui_action
    public static final int EVENT_DATA_UI_BASE = 12300;
    public static final int EVENT_DATA_MAIN_NOTES_LOADED = EVENT_DATA_UI_BASE + 1;
    public static final int EVENT_DATA_ACTION_NOTES_DELETE = EVENT_DATA_UI_BASE + 2;
    public static final int EVENT_DATA_SHOW_NOTES_GET = EVENT_DATA_UI_BASE + 3;
    public static final int EVENT_DATA_SHOW_NOTES_SAVE = EVENT_DATA_UI_BASE + 4;
    public static final int EVENT_DATE_NOTES_QUERY_FINISH = EVENT_DATA_UI_BASE + 5;
    public static final int EVENT_DATE_NOTES_CONTENT_REFRESH = EVENT_DATA_UI_BASE + 6;
    public static final int EVENT_DATA_MAIN_NOTECATEGORIES_LOADED = EVENT_DATA_UI_BASE + 7;
    public static final int EVENT_DATA_MAIN_NOTES_BYCID_LOADED = EVENT_DATA_UI_BASE + 8;
    public static final int EVENT_DATA_MAIN_HAND_WRITING_LOADED = EVENT_DATA_UI_BASE + 9;
    public static final int EVENT_DATA_MAIN_UPDATA_NOTECATEGORIES = EVENT_DATA_UI_BASE + 10;
    public static final int EVENT_DATA_ACTION_NOTES_MOVE_TO_TRASH = EVENT_DATA_UI_BASE + 11;
    public static final int EVENT_DATA_ACTION_NOTES_RECOVERY = EVENT_DATA_UI_BASE + 12;

    //todo actions
    public static final int EVENT_TODO_BASE = 1000;
    public static final int EVENT_TODO_SPAN_CLICK = EVENT_TODO_BASE + 1;
    public static final int EVENT_TODO_IMAGE_CLICK = EVENT_TODO_BASE + 2;


    private static PM_Commands INSTANCE;

    private RegistrantList mNotesDataRegistrants = new RegistrantList();
    private RegistrantList mTextRegistrants = new RegistrantList();

    private PM_Commands(){}

    public synchronized static PM_Commands getInstance(){
        if (INSTANCE == null) {
            INSTANCE = new PM_Commands();
        }
        return INSTANCE;
    }

    public void setNotesDataRegistrant(Handler h, int what, Object obj){ mNotesDataRegistrants.add(h,what,obj); }
    public void unSetNotesDataRegistrant(Handler h){ mNotesDataRegistrants.remove(h); }
    public void notifyNotesData(int what, Object result, Throwable exception){ mNotesDataRegistrants.internalNotifyRegistrantsWithEvent(what,result,exception); }

    public void setTextRegistrant(Handler h, int what, Object obj) {
        if (what == EVENT_TODO_SPAN_CLICK) {
            mTextRegistrants.add(h, what, obj, true);
        } else {
            mTextRegistrants.add(h, what, obj);
        }
    }
    public void unSetTodoRegistrant(Handler h){ mTextRegistrants.remove(h); }
    public void notifyText(int what, Object result, Throwable exception){ mTextRegistrants.internalNotifyRegistrantsWithEvent(what,result,exception); }


}
