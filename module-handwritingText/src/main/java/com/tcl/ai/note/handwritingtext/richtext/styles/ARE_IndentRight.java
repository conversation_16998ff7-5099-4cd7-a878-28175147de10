package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.text.Editable;
import android.text.Spannable;
import android.text.Spanned;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.converter.MarginStyleConverter;
import com.tcl.ai.note.handwritingtext.richtext.history.OperationType;
import com.tcl.ai.note.handwritingtext.richtext.inner.Constants;
import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.spans.AreLeadingMarginSpan;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

/**
 * 右缩进
 */
public class ARE_IndentRight extends ARE_ABS_FreeStyle {

	public ARE_IndentRight(AREditText editText) {
        super(editText.getContext());
        setEditText(editText);
	}

	@Override
	public void setListenerForImageView(ImageView imageView) {

	}

	public void setIndentRight(Integer level,boolean isFollowMove) {
		EditText editText = getEditText();
		int currentLine = Util.getCurrentCursorLine(editText);
		int start = Util.getThisLineStart(editText, currentLine);
		int end = Util.getThisLineEnd(editText, currentLine);

		Editable editable = editText.getText();
		// Checks if current line has leading margin already
		// If any, remove;
		// Then apply new leading margin.
		AreLeadingMarginSpan[] existingLeadingSpans = editable.getSpans(start, end, AreLeadingMarginSpan.class);
		int maxLevel = MarginStyleConverter.INSTANCE.calculateMaxLevelPerLine(editText);
		int currentLevel = 0;
		int newLevel = 0;
		Logger.d("AreLeadingMarginSpan", "setIndentRight() called start = " + start + ", end=" + end + ", level=" + level + ", currentLevel=" + currentLevel + ", newLevel=" + newLevel + ", maxLevel=" + maxLevel);
		if (null != existingLeadingSpans && existingLeadingSpans.length == 1) {
			AreLeadingMarginSpan currentLeadingMarginSpan = existingLeadingSpans[0];
			int originalEnd = editable.getSpanEnd(currentLeadingMarginSpan);
			editable.removeSpan(currentLeadingMarginSpan);
			if (level != null) {
				currentLeadingMarginSpan.setLevel(level);
				newLevel = level;
			} else {
				currentLeadingMarginSpan.increaseLevel();
				newLevel = currentLeadingMarginSpan.getLevel();
			}
			if (isFollowMove){
				start=Math.max(0, start - 1); // 包含可能的行首符号
			}
			Logger.d("AreLeadingMarginSpan", "setIndentRight() called start = " + start + ", end=" + end + ",  originalEnd = " + originalEnd + ",level=" + level + ", currentLevel=" + currentLevel + ", newLevel=" + newLevel + ", maxLevel=" + maxLevel);
			editable.setSpan(currentLeadingMarginSpan, start, originalEnd, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
			currentLevel = currentLeadingMarginSpan.getLevel();
		}
		else {
			editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
			start = Util.getThisLineStart(editText, currentLine);
			if (isFollowMove){
				start=Math.max(0, start - 1); // 包含可能的行首符号
			}
			end = Util.getThisLineEnd(editText, currentLine);
			//mlevel 是从0开始的，maxLevel是从1开始的
			AreLeadingMarginSpan leadingMarginSpan = new AreLeadingMarginSpan();
			if (level != null) {
				leadingMarginSpan.setLevel(level);
				newLevel = level;
			} else {
				int leadingMarginSpanLevel = leadingMarginSpan.getLevel();
				if (leadingMarginSpanLevel < maxLevel-2) {
					leadingMarginSpan.increaseLevel();
					newLevel = leadingMarginSpan.getLevel();
				}
			}
			editable.setSpan(leadingMarginSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
			currentLevel = leadingMarginSpan.getLevel();
		}

		logAllLeadingSpans(editable);

		// 记录操作到撤销重做管理器
		// 判断操作类型：如果新级别大于旧级别，则是右缩进；否则是左缩进
		OperationType operationType = (newLevel > currentLevel) ?
			OperationType.INDENT_RIGHT : OperationType.INDENT_LEFT;

		if (mEditText != null) {
			AREditText arEditText = mEditText;
			arEditText.getRichTextUndoRedoManager().recordOperation(
				start,
				end,
				operationType,
				AreLeadingMarginSpan.class,
				newLevel,
				false
			);
		}
	}

	@Override
	public void applyStyle(Editable editable, int start, int end, Boolean isRecordToHistory) {
		AreLeadingMarginSpan[] leadingSpans = editable.getSpans(start, end, AreLeadingMarginSpan.class);
		if (null == leadingSpans || leadingSpans.length == 0) {
			return;
		}

		if (end > start) {
			//
			// User inputs
			//
			// To handle the \n case
			char c = editable.charAt(end - 1);
			if (c == Constants.CHAR_NEW_LINE) {
				int leadingSpanSize = leadingSpans.length;
				int previousLeadingSpanIndex = leadingSpanSize - 1;
				if (previousLeadingSpanIndex > -1) {
					AreLeadingMarginSpan previousLeadingSpan = leadingSpans[previousLeadingSpanIndex];
					int lastLeadingItemSpanStartPos = editable.getSpanStart(previousLeadingSpan);

					//
					// Handle this case:
					//
					// -> A
					// 
					// User types \n after 'A'
					// Then
					// We should see:
					// -> A
					// -> 
					//
					// We need to end the first span
					// Then start the 2nd span
					// Then reNumber the following list item spans
					if (end > lastLeadingItemSpanStartPos) {
						editable.removeSpan(previousLeadingSpan);
						editable.setSpan(previousLeadingSpan, lastLeadingItemSpanStartPos, end - 1, Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
					}
				
					makeLineAsLeadingSpan(previousLeadingSpan.getLevel());
				} // #End of if it is in ListItemSpans..
			} // #End of user types \n
		} 
		else {
			//
			// User deletes
			int spanStart = editable.getSpanStart(leadingSpans[0]);
			int spanEnd = editable.getSpanEnd(leadingSpans[0]);

			Util.log("Delete spanStart = " + spanStart + ", spanEnd = " + spanEnd);

			if (spanStart >= spanEnd) {
				//
				// User deletes the last char of the span
				// So we think he wants to remove the span
				editable.removeSpan(leadingSpans[0]);

				//
				// To delete the previous span's \n
				// So the focus will go to the end of previous span
				if (spanStart > 0) {
					editable.delete(spanStart - 1, spanEnd);
				}
			}
		}

		logAllLeadingSpans(editable);
	}

	@Override
	public Boolean needApplyStyle() {
		return false;
	}


	private AreLeadingMarginSpan makeLineAsLeadingSpan(int level) {
		EditText editText = getEditText();
		int currentLine = Util.getCurrentCursorLine(editText);
		int start = Util.getThisLineStart(editText, currentLine);
		int end = Util.getThisLineEnd(editText, currentLine);
		Editable editable = editText.getText();
		editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
		start = Util.getThisLineStart(editText, currentLine);
		end = Util.getThisLineEnd(editText, currentLine);

		if (editable.charAt(end - 1) == Constants.CHAR_NEW_LINE) {
			end--;
		}

		AreLeadingMarginSpan leadingMarginSpan = new AreLeadingMarginSpan();
		leadingMarginSpan.setLevel(level);
		editable.setSpan(leadingMarginSpan, start, end,	Spannable.SPAN_INCLUSIVE_INCLUSIVE);
		return leadingMarginSpan;
	}

	private void logAllLeadingSpans(Editable editable) {
		AreLeadingMarginSpan[] leadingSpans = editable.getSpans(0,
				editable.length(), AreLeadingMarginSpan.class);
		for (AreLeadingMarginSpan span : leadingSpans) {
			int ss = editable.getSpanStart(span);
			int se = editable.getSpanEnd(span);
			Util.log("List All: Level = " + span.getLevel() + " :: start == " + ss + ", end == " + se);
		}
	}
	
	
	@Override
	public ImageView getImageView() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setChecked(boolean isChecked) {
		// TODO Auto-generated method stub

	}

	@Override
	public void setisValid(boolean isValid) {

	}

	@Override
	public boolean getIsValid() {
		return false;
	}

	@Override
	public void updateCheckStatus(boolean checked) {

	}

}
