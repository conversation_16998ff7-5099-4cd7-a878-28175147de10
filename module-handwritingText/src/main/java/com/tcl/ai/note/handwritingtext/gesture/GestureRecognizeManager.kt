package com.tcl.ai.note.handwritingtext.gesture

import com.sunia.HTREngine.sdk.ERecognitionMode
import com.sunia.HTREngine.sdk.Engine
import com.sunia.HTREngine.sdk.Params
import com.sunia.HTREngine.sdk.RecognizeListener
import com.sunia.HTREngine.sdk.RecognizePoint
import com.sunia.HTREngine.sdk.editor.Editor
import com.sunia.HTREngine.utils.LogUtil
import com.tcl.ai.note.utils.Logger

object GestureRecognizeManager {

    private val TAG = "GestureRecognizeManager"

    private var editor: Editor? = null

    fun init(engine: Engine?) {
        initEditor(engine)
    }

    private fun initEditor(engine: Engine?) {
        Logger.d(TAG, "initEngine, engine:$engine, editor=$editor")
        if (engine == null || (!engine.isValidate)) {
            Logger.w(TAG, "initEditor, engine is null or isValidate, return")
            return
        }
        val start = System.currentTimeMillis()
        Logger.d(TAG, " initEditor start")

        if (editor == null) {
            val params = getParams()
            editor = engine.createEditor(params)
            Logger.d(TAG, "initEditor end, editor:${editor}, use time:${System.currentTimeMillis() - start}, params:$params")
            editor?.setRecognizeEngineListener(object : RecognizeListener {
                override fun onLoaded(p0: Editor?) {
                    Logger.d(TAG, "editor onLoaded,  cost time:" + (System.currentTimeMillis() - start))
                }

                override fun onError(p0: Editor?, p1: Int, p2: Exception?) {
                    Logger.e(TAG, " editor onError, p1:$p1, p2:$p2")
                }

                override fun onContentChanged(p0: Editor, result: String?) {
                    Logger.d(TAG, "editor onContentChanged, result: $result")

                }

                override fun onAssociationalChanged(p0: Editor?, p1: String?) {
                    Logger.d(TAG, "editor onAssociationalChanged, p1: $p1")
                }

                override fun onCandidateChanged(p0: Editor?, p1: String?) {
                    Logger.d(TAG, "editor onCandidateChanged, p1: $p1")
                }
            })

            editor?.open(params)
        } else {
            Logger.d(TAG, "initEditor, The recognizer is not empty, no need to be initialized repeatedly")
        }
        Logger.d(TAG, "initEditor, editor:${editor}")
    }

    private fun getParams(): Params {
        val params = Params.createInstance()
        params.setDataDir("gesture")
        params.setConfigName("gesture.conf")
        params.mode = ERecognitionMode.MODE_ECR.value
        params.setResultCalculate(false)
        params.setResultCoordinate(true)
        params.setWordSplitTimeLot(500)
        params.setResultAssociational(true)
        params.setResultCandidate(true)
        params.setResultPartitionCoordinate(true)
        params.setResultSpanProcessed(true)
        return params
    }

    fun getGesture(x: DoubleArray?, y: DoubleArray?, time: DoubleArray?): String {
        if (x == null || y == null || time == null) return ""
        val recognizePointList: MutableList<RecognizePoint> = ArrayList()
        for (i in x.indices) {
            val point = RecognizePoint()
            if (i == 0) {
                point.down()
            } else if (i == x.size - 1) {
                point.up()
            } else {
                point.move()
            }
            point.x = x[i].toFloat()
            point.y = y[i].toFloat()
            point.t = time[i].toLong()
            recognizePointList.add(point)
        }
        var result: String? = null
        if (editor != null) {
            result = editor?.syncRecognize(recognizePointList)
        }
        if (result == null) {
            result = ""
        }
        LogUtil.d(TAG, "getGesture: $result")
        return result
    }

    fun release() {
        editor?.setRecognizeEngineListener(null)
        editor = null
    }
}