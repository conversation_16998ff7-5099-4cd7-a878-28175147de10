package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Popup
import kotlinx.coroutines.delay


/**
 * 带有缩放和透明度动画的弹出 Popup 组件，支持阴影效果
 *
 * @param onDismissRequest 关闭弹窗的回调
 * @param offset 弹窗的偏移量
 * @param content 弹窗内容
 */
@Composable
fun AnimatedPopupWithShadow(
    alignment: Alignment = Alignment.TopStart,
    onDismissRequest: () -> Unit,
    offset: IntOffset,
    content: @Composable (closePopup: () -> Unit) -> Unit
) {

    var showContent by remember { mutableStateOf(false) }


    val shadowThreshold = 1f

    val transition = updateTransition(targetState = showContent, label = "popup")
    val scale by transition.animateFloat(
        transitionSpec = { tween(300, easing = FastOutSlowInEasing) },
        label = "scale"
    ) { if (it) 1f else 0.85f }
    val alpha by transition.animateFloat(
        transitionSpec = { tween(300, easing = FastOutSlowInEasing) },
        label = "alpha"
    ) { if (it) 1f else 0f }

    LaunchedEffect(alpha, showContent) {
        if (!showContent && alpha == 0f) {
            onDismissRequest()
        }
    }

    Popup(
        alignment =alignment,
        onDismissRequest = {
            showContent = false
        },
        offset = offset
    ) {

        DisposableEffect(Unit) {
            showContent = true
            onDispose {}
        }
        Box(
            modifier = Modifier

        ) {
            Box(
                modifier = Modifier
                    .graphicsLayer {
                        scaleX = scale
                        scaleY = scale
                        this.alpha = alpha
                    }
                    .then(
                        if (scale >= shadowThreshold)
                            Modifier.shadow(
                                elevation = 6.dp,
                                shape = RoundedCornerShape(20.dp),
                                ambientColor = Color.Black.copy(alpha = 0.37f),
                                spotColor = Color.Black.copy(alpha = 0.37f)
                            )
                        else
                            Modifier
                    )
            ) {
                content {
                    showContent = false
                }
            }

        }


    }
}
