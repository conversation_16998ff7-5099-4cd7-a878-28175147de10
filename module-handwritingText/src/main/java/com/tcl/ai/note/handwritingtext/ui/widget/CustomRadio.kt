package com.tcl.ai.note.handwritingtext.ui.widget

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge

/**
 *  author : junze.liu
 *  date : 2025-06-16 17:14
 *  description :
 */

@Composable
fun CustomRadioButton(
    isDarkTheme: Boolean = isSystemInDarkTheme(),
    selected: Boolean,
    onClick: () -> Unit,
    color: Color = MaterialTheme.colorScheme.primary,
    modifier: Modifier = Modifier,
    outerSize: Dp = 20.dp,
    innerSize: Dp = 10.dp
) {
    val strokeWidth = 2.dp

    val animatedSelection by animateFloatAsState(
        targetValue = if (selected) 1f else 0f,
        animationSpec = tween(durationMillis = 200)
    )
    val darkThemeCircleColor = R.color.dark_theme_circle.colorRes()
    val radioSelColor =R.color.radio_sel.colorRes()
    Box(
        modifier = modifier.focusable()
            .size(outerSize)
            .clickable(
                interactionSource = null,
                indication = null,
                onClick = onClick
            )
            .drawWithContent {

                if(selected){
                    //深色模式下黑色选择状态需要特殊处理
                    if (color == Color.Black && isDarkTheme || color == Color.White && !isDarkTheme ){
                        drawCircle(
                            color = radioSelColor,
                            radius = (outerSize-strokeWidth).toPx() / 2,
                            style = Stroke(width = strokeWidth.toPx())
                        )
                    }else{
                        drawCircle(
                            color = color,
                            radius =(outerSize-strokeWidth).toPx() / 2,
                            style = Stroke(width = strokeWidth.toPx()) ,
                        )
                    }
                    drawCircle(
                        color = color.copy(alpha = animatedSelection),
                        radius = innerSize.toPx() / 2 * animatedSelection,
                        center = center
                    )
                }else{
                    //白色或者深色模式下黑色，外圈需要特殊处理
                    if( color == Color.White || (color == Color.Black && isDarkTheme)){
                        drawCircle(
                            color = darkThemeCircleColor,
                            radius = (outerSize-1.5.dp).toPx() / 2,
                            style =  Stroke(width = 1.5.dp.toPx())
                        )
                    }
                    drawCircle(
                        color = color,
                        radius =  (outerSize-1.5.dp).toPx() / 2,
                        center = center
                    )
                }

            }
    )
}


@Composable
fun FillRadioButton(
    isDarkTheme: Boolean = isSystemInDarkTheme(),
    selected: Boolean,
    onClick: () -> Unit,
    color: Color = MaterialTheme.colorScheme.primary,
    modifier: Modifier = Modifier,
    outerSize: Dp = 20.dp,
){
    val strokeWidth = 2.dp


    val darkThemeCircleColor = R.color.dark_theme_circle.colorRes()
    val radioSelColor =R.color.radio_sel.colorRes()
    Box(
        modifier = modifier.focusable()
            .size(outerSize)
            .clickable(
                interactionSource = null,
                indication = null,
                onClick = onClick
            )
            .drawWithContent {
                //白色或者深色模式下黑色，外圈需要特殊处理
                val isSpecialColor = color == Color.White || (color == Color.Black && isDarkTheme)

                if(selected){
                    val outerRingColor =  radioSelColor
                    drawCircle(
                        color = outerRingColor,
                        radius = (outerSize-strokeWidth).toPx() / 2,
                        style = Stroke(width = strokeWidth.toPx())
                    )
                    drawCircle(
                        color = color,
                        radius =  (outerSize-strokeWidth*2).toPx() / 2,
                        center = center
                    )

                }else{
                    if(isSpecialColor){
                        drawCircle(
                            color = darkThemeCircleColor,
                            radius = (outerSize-1.5.dp).toPx() / 2,
                            style =  Stroke(width = 1.5.dp.toPx())
                        )
                    }
                    drawCircle(
                        color = color,
                        radius =  (outerSize-1.5.dp).toPx() / 2,
                        center = center
                    )
                }

            }
    )
}




