package com.tcl.ai.note.handwritingtext.bean

import android.view.MotionEvent
import androidx.compose.ui.geometry.Offset
import kotlinx.serialization.Serializable

@Serializable
data class DrawPoint(
    val x: Float,
    val y: Float,
    private val pressure: Float = 1f,
    val timestamp: Long = System.currentTimeMillis(),
    val tooltype: Int = MotionEvent.TOOL_TYPE_FINGER,
) {
    fun toOffset() = Offset(x, y)
    val pressureScale: Float
        get() = pressure * if (tooltype == MotionEvent.TOOL_TYPE_STYLUS) PRESSURE_SCALE else 1f
    val pressureOrigin: Float
        get() = this.pressure

    operator fun minus(other: Offset) = this.copy(
        x = x - other.x,
        y = y - other.y
    )

    operator fun plus(other: Offset) = this.copy(
        x = x + other.x,
        y = y + other.y
    )

    companion object {
        private const val PRESSURE_DEVIATION = 0.0f
        const val PRESSURE_SCALE = 3f

        fun centerOf(a: DrawPoint, b: DrawPoint) =
            DrawPoint(
                x = (a.x + b.x) / 2,
                y = (a.y + b.y) / 2,
                pressure = (a.pressure + b.pressure) / 2,
                timestamp = b.timestamp,
                tooltype = b.tooltype
            )
    }
}

fun Offset.toDrawPoint() = DrawPoint(x = x, y = y)