package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.text.Editable;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.spans.AreBoldSpan;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;


public class ARE_FirtTextShow extends ARE_ABS_Style<AreBoldSpan> {

    private ImageView mShowImageView;
    private View mBottomSecondText;
    private boolean mShowChecked;

    public ARE_FirtTextShow(ImageView showImage, View bottomSecondText) {
        super(AreBoldSpan.class);
        mBottomSecondText = bottomSecondText;
        mShowImageView = showImage;
    }

    @Override
    public void setListenerForImageView(ImageView imageView) {

    }

    @Override
    public AreBoldSpan newSpan() {
        return null;
    }

    @Override
    public void applyStyle(Editable editable, int start, int end, Boolean isRecordToHistory) {

    }

    @Override
    public ImageView getImageView() {
        return mShowImageView;
    }

    @Override
    public void setChecked(boolean isChecked) {
        mShowChecked = isChecked;
    }

    @Override
    public boolean getIsChecked() {
        return mShowChecked;
    }

    @Override
    public void setisValid(boolean isValid) {

    }

    @Override
    public boolean getIsValid() {
        return false;
    }

    @Override
    public void setEditText(AREditText editText) {
        this.mEditText = editText;
    }

    @Override
    public EditText getEditText() {
        return null;
    }

    @Override
    public void updateCheckStatus(boolean checked) {
        if(mBottomSecondText.getVisibility() == View.GONE){
            setChecked(checked);
            getImageView().setSelected(checked);
        }else {
            setChecked(true);
            getImageView().setSelected(true);
        }
    }
}
