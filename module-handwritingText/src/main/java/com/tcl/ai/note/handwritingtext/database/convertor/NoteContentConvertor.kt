package com.tcl.ai.note.handwritingtext.database.convertor

import androidx.room.TypeConverter
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.decodeFromByteArray
import kotlinx.serialization.encodeToByteArray
import kotlinx.serialization.protobuf.ProtoBuf

@OptIn(ExperimentalSerializationApi::class)
class NoteContentConvertor {
    private val protoFormat = ProtoBuf

    @TypeConverter
    fun contentListToByteArray(contents: List<EditorContent>) =
        protoFormat.encodeToByteArray(contents.map { it.toNoteContentData() })

    @TypeConverter
    fun byteArrayToContentList(byteArray: ByteArray) =
        protoFormat.decodeFromByteArray<List<NoteContentData>>(byteArray).map {
            it.toEditorContent()
        }
}