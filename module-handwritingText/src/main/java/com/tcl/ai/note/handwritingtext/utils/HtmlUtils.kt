package com.tcl.ai.note.handwritingtext.utils

import android.graphics.Typeface
import android.text.Layout
import android.text.Spanned
import android.text.TextUtils
import android.text.style.AbsoluteSizeSpan
import android.text.style.AlignmentSpan
import android.text.style.BackgroundColorSpan
import android.text.style.CharacterStyle
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.text.style.ParagraphStyle
import android.text.style.RelativeSizeSpan
import android.text.style.StrikethroughSpan
import android.text.style.StyleSpan
import android.text.style.SubscriptSpan
import android.text.style.SuperscriptSpan
import android.text.style.TypefaceSpan
import android.text.style.URLSpan
import com.tcl.ai.note.handwritingtext.richtext.spans.ARE_Span
import com.tcl.ai.note.handwritingtext.richtext.spans.AreLeadingMarginSpan
import com.tcl.ai.note.handwritingtext.richtext.spans.AreListSpan
import com.tcl.ai.note.handwritingtext.richtext.spans.AreUnderlineSpan
import com.tcl.ai.note.handwritingtext.richtext.spans.ListBulletSpan
import com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan
import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan

/**
 * This class processes HTML strings into displayable styled text.
 * Not all HTML tags are supported.
 * 参考官方的Html类修改
 */
object HtmlUtils {
    private var escapeCJK: Boolean = false
    private const val OL: String = "ol"
    private const val UL: String = "ul"
    private const val TODO: String = "ul class=\"todo\""

    private val head = """
        <head>
            <meta charset="UTF-8">
        </head>
        
    """.trimIndent()
    private val style = """
        <style>
            ul, ol {
                padding-left: 1.5em;
            }
            .todo {
                list-style: none;
                padding-left: 0em;
            }
        </style>
        
        """.trimIndent()

    /**
     * Returns an HTML representation of the provided Spanned text. A best effort is
     * made to add HTML tags corresponding to spans. Also note that HTML metacharacters
     * (such as "&lt;" and "&amp;") within the input text are escaped.
     *
     * @param text input text to convert
     * @return string containing input converted to HTML
     */
    fun toHtml(text: Spanned): String {
        val out = StringBuilder()
        out.append(head)
        out.append(style)
        withinHtml(out, text)
        // Constants.ZERO_WIDTH_SPACE_STR_ESCAPE
        return out.replace(Regex("(<br>\n)+$|(<br>)+\$|&#8203;"), "")
    }

    private fun withinHtml(out: StringBuilder, text: Spanned) {
        // option等于TO_HTML_PARAGRAPH_LINES_INDIVIDUAL时，走分段逻辑
        // <<<------ 重点：这里要走复杂段逻辑，支持li/p/代办/对齐
        withinBlockquote(out, text, 0, text.length)
    }

    private fun getTextDirection(text: Spanned, start: Int, end: Int): String {
//        final int len = end - start;
//        final byte[] levels = ArrayUtils.newUnpaddedByteArray(len);
//        final char[] buffer = TextUtils.obtain(len);
//        TextUtils.getChars(text, start, end, buffer, 0);
//
//        int paraDir = AndroidBidi.bidi(Layout.DIR_REQUEST_DEFAULT_LTR, buffer, levels, len,
//                false /* no info */);
//    	int paraDir = Layout.DIR_LEFT_TO_RIGHT;
//
//        switch (paraDir) {
//          case Layout.DIR_RIGHT_TO_LEFT:
//            return "<p dir=\"rtl\">";
//          case Layout.DIR_LEFT_TO_RIGHT:
//          default:
//            return "<p>";
//        }
        return ""
    }

    private fun getTodoCheck(text: Spanned, start: Int, end: Int): String {
        var todoCheck = ""
        val upcomingListSpans = text.getSpans(
            start, end,
            UpcomingListSpan::class.java
        )
        for (upcomingListSpan in upcomingListSpans) {
            if (upcomingListSpan.isChecked)
                todoCheck = "><input type=\"checkbox\" checked /"
            else
                todoCheck = "><input type=\"checkbox\" /"
        }
        return todoCheck
    }

    private fun getTextStyles(
        text: Spanned, start: Int, end: Int,
        forceNoVerticalMargin: Boolean, includeTextAlign: Boolean
    ): String {
        var textAlign: String? = null
        var marginLeft: String? = null

        if (includeTextAlign) { // 只有需要时才查找对齐
            val alignmentSpans = text.getSpans(
                start, end,
                AlignmentSpan::class.java
            )

            // 只取当前段落内最后一个 AlignmentSpan
            for (i in alignmentSpans.indices.reversed()) {
                val s = alignmentSpans[i]
                val alignment = s.alignment
                if (alignment == Layout.Alignment.ALIGN_NORMAL) {
                    textAlign = "text-align:start;"
                } else if (alignment == Layout.Alignment.ALIGN_CENTER) {
                    textAlign = "text-align:center;"
                } else if (alignment == Layout.Alignment.ALIGN_OPPOSITE) {
                    textAlign = "text-align:end;"
                }
                break // 已找到则跳出
            }
        }

        // 处理缩进
        val leadingMarginSpans = text.getSpans(
            start, end,
            AreLeadingMarginSpan::class.java
        )
        for (i in leadingMarginSpans.indices.reversed()) {
            val s = leadingMarginSpans[i]
            if (s.level > 0) {
                marginLeft = String.format("margin-left:%dem;", s.level * 2)
                break // 只取最后一个有效的缩进
            }
        }

        // 组合样式
        val styleBuilder = StringBuilder()
        if (textAlign != null) {
            styleBuilder.append(textAlign)
        }
        if (marginLeft != null) {
            styleBuilder.append(marginLeft)
        }

        if (styleBuilder.length == 0) {
            return ""
        }
        return " style=\"$styleBuilder\""
    }

    private fun withinBlockquote(out: StringBuilder, text: Spanned, start: Int, end: Int) {
        withinBlockquoteIndividual(out, text, start, end)
    }

    private fun withinBlockquoteIndividual(
        out: StringBuilder,
        text: Spanned,
        start: Int,
        end: Int
    ) {
        // 是否在列表项中
        var isInList = false
        // 下一个换行
        var next: Int
        // 标记当前的列表类型
        var listType = ""
        var i = start
        while (i <= end) {
            next = TextUtils.indexOf(text, '\n', i, end)
            if (next < 0) {
                next = end
            }

            // 如果当前就是换行
            if (next == i) {
                if (isInList) {
                    // Current paragraph is no longer a list item; close the previously opened list
                    // 当前处于列表中，列表项的最后是个换行，说明后面都不是列表了，加个标记结束
                    isInList = false
                    out.append("</$listType>\n")
                }
                // 加个换行
                out.append("\n")
            } else {
                // 末尾不是换行，说明不是列表项，设置false
                var isListItem = false
                val paragraphStyles = text.getSpans(
                    i, next,
                    ParagraphStyle::class.java
                )
                for (paragraphStyle in paragraphStyles) {
                    if (paragraphStyle is AreListSpan) {
                        // 如果是自定义的列表项
                        var closed = false
                        if (paragraphStyle is ListNumberSpan) {
                            // 如果是有序列表，确认上一个列表类型是不是有序列表，不是则往out输出闭合
                            closed = checkToClosePreviousList(out, listType, OL)
                            // 修改列表项标记为有序列表
                            listType = OL
                        } else if (paragraphStyle is ListBulletSpan) {
                            // 无序列表
                            closed = checkToClosePreviousList(out, listType, UL)
                            listType = UL
                        } else if (paragraphStyle is UpcomingListSpan) {
                            // 待办项
                            closed = checkToClosePreviousList(out, listType, TODO)
                            listType = TODO
                        }

                        // 如果闭合，说明不在列表项中
                        if (closed) {
                            // If the list item has been closed,
                            // It will no longer be in list.
                            // So set it as false then the following
                            // logic can start a new list item again
                            isInList = false
                        }
                        // 设置当前为列表项
                        isListItem = true
                        break
                    }
                }

                if (isListItem && !isInList) {
                    // Current paragraph is the first item in a list
                    isInList = true
                    out.append("<$listType")
                        .append(getTextStyles(text, i, next, true, false))
                        .append(">\n")
                }

                if (isInList && !isListItem) {
                    // Current paragraph is no longer a list item; close the previously opened list
                    isInList = false
                    out.append("</$listType>\n")
                }

                // 原本会添加多余的p，直接移除对p的添加
                if (isListItem) {
                    out.append("<")
                        .append("li")
                        .append(getTextDirection(text, i, next))
                        .append(getTodoCheck(text, i, next))
                        .append(getTextStyles(text, i, next, false, true))
                        .append(">")
                }

                withinParagraph(out, text, i, next)

                if(isListItem) {
                    out.append("</li>\n")
                }

                if (next == end && isInList) {
                    isInList = false
                    out.append("</$listType>\n")
                }
            }

            if (!isInList && next != end) {
                out.append("<br>")
            }

            next++
            i = next
        }
    }

    /**
     * 如果列表项与前一项不同，则往out输出对应的闭合类型
     * 返回是否添加了闭合
     */
    private fun checkToClosePreviousList(
        out: StringBuilder,
        srcListType: String,
        targetListType: String
    ): Boolean {
        if (srcListType != targetListType && !TextUtils.isEmpty(srcListType)) {
            out.append("</$srcListType>")
            return true
        }
        return false
    }

    private fun withinParagraph(out: StringBuilder, text: Spanned, start: Int, end: Int) {
        var next: Int
        var i = start
        while (i < end) {
            next = text.nextSpanTransition(i, end, CharacterStyle::class.java)
            val style = text.getSpans(
                i, next,
                CharacterStyle::class.java
            )

            for (j in style.indices) {
                if (style[j] is ARE_Span) {
                    out.append((style[j] as ARE_Span).html)
                    i = next
                    continue
                }
                if (style[j] is StyleSpan) {
                    val s = (style[j] as StyleSpan).style

                    if ((s and Typeface.BOLD) != 0) {
                        out.append("<b>")
                    }
                    if ((s and Typeface.ITALIC) != 0) {
                        out.append("<i>")
                    }
                }
                if (style[j] is TypefaceSpan) {
                    val s = (style[j] as TypefaceSpan).family

                    if ("monospace" == s) {
                        out.append("<tt>")
                    }
                }
                if (style[j] is SuperscriptSpan) {
                    out.append("<sup>")
                }
                if (style[j] is SubscriptSpan) {
                    out.append("<sub>")
                }
                if (style[j] is AreUnderlineSpan) {
                    out.append("<u>")
                }
                if (style[j] is StrikethroughSpan) {
                    out.append("<span style=\"text-decoration:line-through;\">")
                }
                if (style[j] is URLSpan) {
                    out.append("<a href=\"")
                    out.append((style[j] as URLSpan).url)
                    out.append("\">")
                }
                if (style[j] is ImageSpan) {
                    out.append("<img src=\"")
                    out.append((style[j] as ImageSpan).source)
                    out.append("\" />")

                    // Don't output the dummy character underlying the image.
                    i = next
                }
                if (style[j] is AbsoluteSizeSpan) {
                    val s = (style[j] as AbsoluteSizeSpan)
                    var sizeDip = s.size.toFloat()
                    if (!s.dip) {
                        // Application application = ActivityThread.currentApplication();
                        // float density = application.getResources().getDisplayMetrics().density;
                        val density = 1.5f
                        sizeDip /= density
                    }

                    // px in CSS is the equivalance of dip in Android
                    out.append(String.format("<span style=\"font-size:%.0fpx;\">", sizeDip))
                }
                if (style[j] is RelativeSizeSpan) {
                    val sizeEm = (style[j] as RelativeSizeSpan).sizeChange
                    out.append(String.format("<span style=\"font-size:%.2fem;\">", sizeEm))
                }
                if (style[j] is ForegroundColorSpan) {
                    val color = (style[j] as ForegroundColorSpan).foregroundColor
                    out.append(String.format("<span style=\"color:#%06X;\">", 0xFFFFFF and color))
                }
                if (style[j] is BackgroundColorSpan) {
                    val color = (style[j] as BackgroundColorSpan).backgroundColor
                    out.append(
                        String.format(
                            "<span style=\"background-color:#%06X;\">",
                            0xFFFFFF and color
                        )
                    )
                }
            }

            withinStyle(out, text, i, next)

            for (j in style.indices.reversed()) {
                if (style[j] is BackgroundColorSpan) {
                    out.append("</span>")
                }
                if (style[j] is ForegroundColorSpan) {
                    out.append("</span>")
                }
                if (style[j] is RelativeSizeSpan) {
                    out.append("</span>")
                }
                if (style[j] is AbsoluteSizeSpan) {
                    out.append("</span>")
                }
                if (style[j] is URLSpan) {
                    out.append("</a>")
                }
                if (style[j] is StrikethroughSpan) {
                    out.append("</span>")
                }
                if (style[j] is AreUnderlineSpan) {
                    out.append("</u>")
                }
                if (style[j] is SubscriptSpan) {
                    out.append("</sub>")
                }
                if (style[j] is SuperscriptSpan) {
                    out.append("</sup>")
                }
                if (style[j] is TypefaceSpan) {
                    val s = (style[j] as TypefaceSpan).family

                    if (s == "monospace") {
                        out.append("</tt>")
                    }
                }
                if (style[j] is StyleSpan) {
                    val s = (style[j] as StyleSpan).style

                    if ((s and Typeface.BOLD) != 0) {
                        out.append("</b>")
                    }
                    if ((s and Typeface.ITALIC) != 0) {
                        out.append("</i>")
                    }
                }
            }
            i = next
        }
    }

    private fun withinStyle(
        out: StringBuilder, text: CharSequence,
        start: Int, end: Int
    ) {
        var i = start
        while (i < end) {
            val c = text[i]

            if (c == '<') {
                out.append("&lt;")
            } else if (c == '>') {
                out.append("&gt;")
            } else if (c == '&') {
                out.append("&amp;")
            } else if (c.code >= 0xD800 && c.code <= 0xDFFF) {
                if (c.code < 0xDC00 && i + 1 < end) {
                    val d = text[i + 1]
                    if (d.code >= 0xDC00 && d.code <= 0xDFFF) {
                        i++
                        val codepoint = 0x010000 or (c.code - 0xD800 shl 10) or d.code - 0xDC00
                        out.append("&#").append(codepoint).append(";")
                    }
                }
            } else if (c.code > 0x7E || c < ' ') {
                if (escapeCJK) {
                    out.append("&#").append(c.code).append(";")
                } else {
                    out.append(c)
                }
            } else if (c == ' ') {
                while (i + 1 < end && text[i + 1] == ' ') {
                    out.append("&nbsp;")
                    i++
                }
                if (start == 0 && end == 1) {
                    out.append("&nbsp;")
                } else {
                    out.append(' ')
                }
                // out.append(' ');
            } else {
                out.append(c)
            }
            i++
        }
    }
}

