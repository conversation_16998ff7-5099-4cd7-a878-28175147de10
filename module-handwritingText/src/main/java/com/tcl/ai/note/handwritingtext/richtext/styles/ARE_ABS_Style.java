package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.content.Context;
import android.text.Editable;
import android.text.Spanned;
import android.util.Log;
import android.widget.ImageView;


import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.utils.RichTextKTUtilsKt;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

import java.lang.reflect.ParameterizedType;

/**
 * 斜体
 * 删除线
 * 加粗
 * 下划线
 * 继承
 * @param <E>
 */
public abstract class ARE_ABS_Style<E> implements IARE_Style {

	protected final Class<E> clazzE;
	protected AREditText mEditText;
	protected boolean mIsRecordToHistory = false;
	public ARE_ABS_Style() {
		this(null);
	}

	public ARE_ABS_Style(Class<E> clazzE) {
		this.clazzE = clazzE;
	}

	@Override
	public void applyStyle(Editable editable, int start, int end, Boolean isRecordToHistory) {
		Logger.d("applyStyle, checked=" + getIsChecked() + " start/end=" + start + "/" + end + " class=" + clazzE);

		// 用户想要应用样式
		if (getIsChecked()) {
            E[] spans = editable.getSpans(start, end, clazzE);
			// 用户输入
            if (end >= start) {
				// 获取选区内的所有选中的类型样式
                E existingESpan = null;
				if (spans.length > 0) {
					existingESpan = spans[0];
				}

				if (existingESpan == null) {
					checkAndMergeSpan(editable, start, end, clazzE);
				} else {
					int existingESpanStart = editable.getSpanStart(existingESpan);
					int existingESpanEnd = editable.getSpanEnd(existingESpan);
					if (existingESpanStart < start && existingESpanEnd > end) {
						// The selection is just within an existing E span
						// Do nothing for this case
						changeSpanInsideStyle(editable, start, end, existingESpan);
					} else {
						checkAndMergeSpan(editable, start, end, clazzE);
					}
				}
			} else {
				// 用户删除
                if (spans.length > 0) {
					E span = spans[0];
					//找到最靠后的样式（通常是用户正在删除的位置）
					int lastSpanStart = editable.getSpanStart(span);
					for (E e : spans) {
						int lastSpanStartTmp = editable.getSpanStart(e);
						if (lastSpanStartTmp > lastSpanStart) {
							lastSpanStart = lastSpanStartTmp;
							span = e;
						}
					}
					// 检查样式是否变为无效（开始位置>=结束位置）
					int eStart = editable.getSpanStart(span);
					int eEnd = editable.getSpanEnd(span);
					Logger.d("ABS_Style","eSpan start == " + eStart + ", eSpan end == " + eEnd);

					if (eStart >= eEnd) {
						// 样式变为无效，移除它
						editable.removeSpan(span);
						extendPreviousSpan(editable, eStart);

						updateCheckStatus(false);
					} else {
						//
						// Do nothing, the default behavior is to extend
						// the span's area.
					}
				}
			}
		} else {
			// 用户想要移除样式
			if (end >= start) {
				//
				// User inputs or user selects a range
				E[] spans = editable.getSpans(start, end, clazzE);
				if (spans.length > 0) {
					E span = spans[0];
					if (null != span) {
						//
						// User stops the style, and wants to show
						// un-UNDERLINE characters
						int ess = editable.getSpanStart(span); // ess == existing span start
						int ese = editable.getSpanEnd(span); // ese = existing span end
						Logger.d("ABS_Style","calss=" + clazzE +" spans Length=" + spans.length + " start/end=" + start + "/" + end);
						Logger.d("ABS_Style"," es start/end=" + ess + "/" + ese);
						if (start >= ese) {
							Util.logSpan(editable);
							// User inputs to the end of the existing e span
							// End existing e span
							editable.removeSpan(span);
//							if((start-1) > ess)
//								editable.setSpan(span, ess, start - 1, Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
							editable.setSpan(span, ess, start, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
						} else if (start == ess && end == ese) {
							// Case 1 desc:
							// *BBBBBB*
							// All selected, and un-check e
							editable.removeSpan(span);
						} else if (start > ess && end < ese) {
							// Case 2 desc:
							// BB*BB*BB
							// *BB* is selected, and un-check e
							editable.removeSpan(span);
							E spanLeft = newSpan();
							editable.setSpan(spanLeft, ess, start, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
							E spanRight = newSpan();
							editable.setSpan(spanRight, end, ese, Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
						} else if (start == ess && end < ese) {
							// Case 3 desc:
							// *BBBB*BB
							// *BBBB* is selected, and un-check e
							editable.removeSpan(span);
							E newSpan = newSpan();
							editable.setSpan(newSpan, end, ese, Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
						} else if (start > ess && end == ese) {
							// Case 4 desc:
							// BB*BBBB*
							// *BBBB* is selected, and un-check e
							editable.removeSpan(span);
							E newSpan = newSpan();
							editable.setSpan(newSpan, ess, start, Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
						}
					}
				}
			} else if (end == start) {
				//
				// User changes focus position
				// Do nothing for this case
			} else {
				//
				// 用户在删除文本
				E[] spans = editable.getSpans(start, end, clazzE);
				if (spans.length > 0) {
					E span = spans[0];
					if (null != span) {
						int eStart = editable.getSpanStart(span);
						int eEnd = editable.getSpanEnd(span);

						if (eStart >= eEnd) {
							//
							// Invalid case, this will never happen.
						} else {
							// 删除操作通常会自动扩展样式区间
							// 我们需要确保样式状态保持一致
							updateCheckStatus(true);
						}
					}
				}
			}
		}
		// 记录样式变化操作
		if (null == mEditText) {
			return;
		}
		if (isRecordToHistory) {
			RichTextKTUtilsKt.recordApplyStyleToUndoRedo(start, end, getIsChecked(), mEditText, clazzE);
		}
	}

	protected void changeSpanInsideStyle(Editable editable, int start, int end, E e) {
		// Do nothing by default
		Log.e("ARE", "in side a span!!");
	}

	private void checkAndMergeSpan(Editable editable, int start, int end, Class<E> clazzE) {
		E leftSpan = null;
		E[] leftSpans = editable.getSpans(start, start, clazzE);
		if (leftSpans.length > 0) {
			leftSpan = leftSpans[0];
		}

		E rightSpan = null;
		E[] rightSpans = editable.getSpans(end, end, clazzE);
		if (rightSpans.length > 0) {
			rightSpan = rightSpans[0];
		}


		int leftSpanStart = editable.getSpanStart(leftSpan);
		int rightSpanEnd = editable.getSpanEnd(rightSpan);
		removeAllSpans(editable, start, end, clazzE);
		E eSpan = newSpan();
		if (leftSpan != null && rightSpan != null) {
			Logger.d("ABS_Style", "checkAndMergeSpan ls=" + leftSpanStart + " re=" + rightSpanEnd);
			int spanned;
			if (start == end) {
				spanned = Spanned.SPAN_INCLUSIVE_INCLUSIVE;
			} else {
				spanned = Spanned.SPAN_EXCLUSIVE_INCLUSIVE;
			}
			editable.setSpan(eSpan, leftSpanStart, rightSpanEnd, spanned);
		} else if (leftSpan != null) {
			Logger.d("ABS_Style", "checkAndMergeSpan ls=" + leftSpanStart + " end=" + end);
			editable.setSpan(eSpan, leftSpanStart, end, Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
		} else if (rightSpan != null) {
			Logger.d("ABS_Style", "checkAndMergeSpan start=" + start + " re=" + rightSpanEnd);
			editable.setSpan(eSpan, start, rightSpanEnd, Spanned.SPAN_EXCLUSIVE_INCLUSIVE);
		} else {
			Logger.d("ABS_Style", "checkAndMergeSpan start=" + start + " end=" + end);
			int spanned;
			if (start == end) {
				spanned = Spanned.SPAN_INCLUSIVE_INCLUSIVE;
			} else {
				spanned = Spanned.SPAN_EXCLUSIVE_INCLUSIVE;
			}
			editable.setSpan(eSpan, start, end, spanned);
		}
	}

	private void removeAllSpans(Editable editable, int start, int end, Class<E> clazzE) {
		E[] allSpans = editable.getSpans(start, end, clazzE);
		for (E span : allSpans) {
			editable.removeSpan(span);
		}
	}

	@Override
	public void removeStyle(Editable editable, int start, int end) {

	}

	protected void extendPreviousSpan(Editable editable, int pos) {
		// Do nothing by default
	}

	public abstract void setListenerForImageView(ImageView imageView);

	public abstract E newSpan();

	@Override
	public Boolean needApplyStyle() {
		return false;
	}

    protected void triggerSaveContent(AREditText editText){
        Runnable task = editText.getSaveContentToMemoryTask();
        if (task != null) {
            task.run();
        }
    }

	@Override
	public void setStyleStatusListener(StyleStatusListener listener) {
	}

	/**
	 * 获取样式类
	 */
	protected Class<?> getSpanClass() {
		return clazzE; // 子类需要重写此方法
	}
}
