package com.tcl.ai.note.handwritingtext.richtext.utils

import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.richtext.inner.Constants
import com.tcl.ai.note.handwritingtext.ui.richtext.RichTextController
import com.tcl.ai.note.handwritingtext.vm.state.RichTextDataState
import com.tcl.ai.note.utils.Logger

object NoteContentUtil {

    /**
     * 判断Note中富文本内容是否为空
     * 包括：空文本、只有换行符、只有列表符号/待办符号但无实际内容
     */
    fun isContentEmpty(state: RichTextDataState): Boolean {
        // 检查标题
        if (state.title.isNotBlank()) {
            return false
        }

        // 检查图片和音频
        if (state.images.isNotEmpty() || state.audios.isNotEmpty()) {
            return false
        }

        // 检查文本内容是否只包含列表符号
        return isTextContentEmpty(state.content)
    }

    /**
     * 检查文本内容是否为空（考虑列表符号）
     */
    private fun isTextContentEmpty(content: String): Boolean {
        // 移除换行符
        val contentWithoutLineBreaks = content.replace(Regex("[\\n\\r]"), "")

        if (contentWithoutLineBreaks.isBlank()) {
            return true
        }

        // 移除零宽字符（列表符号）后检查是否还有实际内容
        val contentWithoutListSymbols = contentWithoutLineBreaks
            .replace(Constants.ZERO_WIDTH_SPACE_STR, "")
            .trim()

        return contentWithoutListSymbols.isEmpty()
    }

    /**
     * 判断插入内容后是否超出长度限制
     */
    fun isInsertContentLengthValid(originContent: String,needAddContent: String):Boolean{
        val isCanInsert =
            originContent.length + needAddContent.length <= RichTextController.MAX_CONTENT_LENGTH
        Logger.d("isInsertContentLengthValid","isCanInsert:$isCanInsert originContent.length:${originContent.length} needAddContent.length:${needAddContent.length}")
        return isCanInsert
    }
    /**
     *笔记转文本，给AI使用,
     * 主要处理 待办事项的断句问题
     */
    fun dealWithNoteContentForAI(note: Note?): String {
        // 如果 contents 列表不为空，则从中获取内容并在每个 item 后添加句号
        if (note?.contents?.isNotEmpty() == true) {
//            val isHasTodo = note.contents.any { it is EditorContent.TodoBlock }
            val content = buildString {
                note.contents.forEach { content ->
                    when (content) {
                        is EditorContent.TextBlock -> {
//                            if (isHasTodo) {
//                                append("[text] ")
//                            }
                            append(content.text.text)
                        }

                        is EditorContent.TodoBlock -> {
//                            // 对待办事项添加特殊标记，使用逗号标记
//                            val status = if (content.isDone) "[todolist] " else "[todolist] "
//                            append(status)
                            append(content.text.text).append("。")
                        }
                        // 图片和音频内容不添加文本
                        else -> {}
                    }
                }
            }
            if (content.isEmpty()){//一期数据，为空则是二期数据
                return note.content
            }
        }

        // 如果 contents 为空，则使用原来的 content 字段
        return note?.content ?: ""
    }
}