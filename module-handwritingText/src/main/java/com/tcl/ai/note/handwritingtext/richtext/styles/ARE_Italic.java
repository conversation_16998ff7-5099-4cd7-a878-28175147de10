package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.spans.AreItalicSpan;
import com.tcl.ai.note.utils.Logger;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

/**
 * 斜体
 */
public class ARE_Italic extends ARE_ABS_Style<AreItalicSpan> {

	private ImageView mItalicImageView;

	private boolean mItalicChecked;
	private boolean mItalicValided = false;


	public ARE_Italic() {
		super(AreItalicSpan.class);
	}

	@Override
	public void setisValid(boolean isValid) {
		mItalicValided = isValid;
	}

	@Override
	public boolean getIsValid() {
		return mItalicValided;
	}

	/**
	 * 
	 * @param editText
	 */
	public void setEditText(AREditText editText) {
		this.mEditText = editText;
	}

	@Override
	public EditText getEditText() {
		return this.mEditText;
	}

	@Override
	public void setListenerForImageView(final ImageView imageView) {
	}

	public void setItalic(){
		if(!mItalicValided) {
			return;
		}
		Logger.d("datahub, text_italic_click");
		mItalicChecked = !mItalicChecked;
		updateCheckStatus(mItalicChecked);
		// 清除保存的样式状态
		if (mEditText != null) {
			mEditText.clearLastDeletedStyleState();
		}

		if (null != mEditText) {
			int start = mEditText.getSelectionStart();
			int end = mEditText.getSelectionEnd();
			mIsRecordToHistory = start != end;
			applyStyle(mEditText.getEditableText(),
					start,
					end, mIsRecordToHistory);
			triggerSaveContent(mEditText);
		}
	}

	@Override
	public void updateCheckStatus(boolean checked) {
		setChecked(checked);
	}

	@Override
	public ImageView getImageView() {
		return this.mItalicImageView;
	}

	@Override
	public void setChecked(boolean isChecked) {
		this.mItalicChecked = isChecked;
	}

	@Override
	public boolean getIsChecked() {
		return this.mItalicChecked;
	}

	@Override
	public AreItalicSpan newSpan() {
		return new AreItalicSpan();
	}
}
