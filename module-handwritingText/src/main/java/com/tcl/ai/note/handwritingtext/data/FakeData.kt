package com.tcl.ai.note.handwritingtext.data

import com.tcl.ai.note.handwritingtext.bean.DrawPoint

const val defaultText =
    """
@Composable
fun TextBoard(
    enabled: Boolean = true,
    viewModel: DrawViewModel = viewModel(),
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .layout { measurable, constraints ->
                // 重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际重新计算子布局大小，先设置成无限用于测量实际
                val childConstraints = constraints.copy(
                    maxHeight = Constraints.Infinity
                )
                // 测量子布局
                val placeable = measurable.measure(childConstraints)
                // 确认子布局会在屏幕上显示多少，子布局的实际大小和屏幕大小取最大
                val width = placeable.width.coerceAtMost(constraints.maxWidth)
                val height = placeable.height.coerceAtMost(constraints.maxHeight)
                val scrollMaxHeight = placeable.height - height
                // 设置组件的宽高
                layout(width, height) {
                    // 放置布局，参数为偏移量
                    placeable.placeRelativeWithLayer(
                        viewModel.canvasState.translation.x.roundToInt(),
                        viewModel.canvasState.translation.y.roundToInt(),
                    )
                }
            }
            .graphicsLayer {
                // translationY = viewModel.canvasState.translation.y
                scaleX = viewModel.canvasState.scale
                scaleY = viewModel.canvasState.scale
                transformOrigin = TransformOrigin(0f, 0f)
            }
    ) {
        BasicTextField(
            value = viewModel.text,
            onValueChange = {
                viewModel.text = it
            },
            enabled = enabled,
            modifier = Modifier
                .fillMaxSize(),
        )
    }
}
    """

val fakePoints = listOf(
    DrawPoint(100f,100f),
    DrawPoint(500f,100f),
    DrawPoint(500f,1000f),
    DrawPoint(1000f,1000f),
    DrawPoint(1500f,1500f),
    DrawPoint(1000f,2000f),
    DrawPoint(1000f,1000f),
    DrawPoint(100f,100f),
)