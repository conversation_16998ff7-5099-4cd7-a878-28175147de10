package com.tcl.ai.note.handwritingtext.ui.popup

import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.ui.swatches.BottomButton
import com.tcl.ai.note.handwritingtext.ui.swatches.ColorSwatchGrid
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.isInMultiWindowMode
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes


/**
 * 单一纯颜色选择Popup
 */
@Composable
internal fun PureColorPalettePopup(
    isDarkTheme:Boolean = isSystemInDarkTheme(),
    modifier: Modifier = Modifier,
    curColor: Color = Color.White,
    popupWidth:Dp = 328.dp,
    popupHeight:Dp =  381.dp,
    onConfirm:(color:Color) ->Unit,
    onClosePopup: () -> Unit) {
    var selectedColor by remember (curColor) {
        mutableStateOf(curColor)
    }

    Box(
        modifier = modifier
            .background(
                color = Color.Transparent,
                shape = RoundedCornerShape(20.dp)
            )
            .width(popupWidth)
            .height(popupHeight)
            .then(
                isInMultiWindowMode.judge(
                    Modifier.verticalScroll(rememberScrollState()),
                    Modifier
                )
            )
    ) {
        Column(
            modifier = Modifier
                .width(popupWidth)
                .wrapContentHeight()
                .background(color = TclTheme.colorScheme.tertiaryBackground),
            horizontalAlignment = Alignment.CenterHorizontally
        ){
            Spacer(modifier = Modifier.height(24.dp))
            Text(
                modifier = Modifier.fillMaxWidth().padding(horizontal = 24.dp),
                fontSize = 20.sp,
                lineHeight = 24.sp,
                textAlign = TextAlign.Start,
                fontFamily = FontFamily.Default,
                fontWeight = FontWeight.Medium,
                color = TclTheme.colorScheme.textDialogTitle,
                text = R.string.swatches_popup_tool.stringRes(),
            )
            Spacer(modifier = Modifier.height(20.dp))

            ColorSwatchGrid(
                selColor = selectedColor
            ) { color ->
                selectedColor = color
            }
            Spacer(modifier = Modifier.height(4.dp))



            BottomButton(
                modifier = Modifier.fillMaxWidth().padding(horizontal = 20.dp)
            ) { isOk ->
                if(isOk){
                    val color = isDarkTheme.judge(
                        selectedColor.inverseColor(),
                        selectedColor
                    )
                    onConfirm(color)
                }
                onClosePopup()
            }
            Spacer(modifier = Modifier.height(16.dp))

        }
    }

}