package com.tcl.ai.note.handwritingtext.controller

import android.os.CancellationSignal
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.channels.ChannelResult
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.transformLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.selects.select
import java.util.concurrent.Executors

@OptIn(ExperimentalCoroutinesApi::class)
object NoteDataSaveController {
    private const val TAG = "NoteDataSaveController"
    private val singleDispatcher = Executors.newSingleThreadExecutor().asCoroutineDispatcher()
    private val scope = CoroutineScope(SupervisorJob() + singleDispatcher)

    // 保存的类型
    enum class SaveType {
        RichText, // 富文本
        DrawStroke, // 手绘
        Thumbnail // 首页缩略图
    }
    // 需要执行的任务集合
    private val saveBatchMap = HashMap<SaveType, suspend () -> Unit>(SaveType.entries.size)

    // 是否保存完成, 默认没有保存任务，所以为true
    // 如果saveFinishState变成false，代表有内容需要保存
    private val mSaveFinishState = MutableStateFlow(true)
    val saveFinishState = mSaveFinishState.asStateFlow()

    /**
     * 执行保存操作
     */
    private var saveBatchJob: Job? = null
    private fun applyAndSave(forceSave: Boolean = false): Job {
        saveBatchJob?.cancel()
        saveBatchJob = scope.launch {
            Logger.d(TAG, "save start")
            mSaveFinishState.value = false
            if (!forceSave) {
                // delay一段时间，再触发保存。 避免高频保存内存，性能消耗高
                delay(1000)
            }
            Logger.d(TAG, "save in progress")
            // 开始执行真正的内容保存操作
            // 执行结束才能remove元素，避免执行到一半，协程取消了。
            saveBatchMap.get(SaveType.RichText)?.invoke()
            saveBatchMap.remove(SaveType.RichText)
            saveBatchMap.get(SaveType.DrawStroke)?.invoke()
            saveBatchMap.remove(SaveType.DrawStroke)
            saveBatchMap.get(SaveType.Thumbnail)?.invoke()
            saveBatchMap.remove(SaveType.Thumbnail)
            mSaveFinishState.value = true
            Logger.d(TAG, "save end")
        }
        return saveBatchJob!!
    }

    /**
     * 添加富文本保存操作，并更新SharedFlow（调用了updateSaveInfoSharedFlow）
     * 需要保证SuspendRunnable内是同步操作。否则保存失败
     *
     * @param runnable 由业务侧传入保存方法
     */
    fun saveRichText(runnable: suspend () -> Unit) {
        Logger.d(TAG, "saveRichText")
        saveBatchMap.put(SaveType.RichText, runnable)
        applyAndSave()
    }

    /**
     * 添加手绘保存操作，并更新SharedFlow（调用了updateSaveInfoSharedFlow）
     * 需要保证SuspendRunnable内是同步操作。否则保存失败
     *
     * @param runnable 由业务侧传入保存方法
     */
    fun saveDrawStroke(runnable: suspend () -> Unit) {
        Logger.d(TAG, "saveDrawStroke")
        saveBatchMap.put(SaveType.DrawStroke, runnable)
        applyAndSave()
    }

    /**
     * 添加缩略图保存操作，并更新SharedFlow（调用了updateSaveInfoSharedFlow）
     * 需要保证SuspendRunnable内是同步操作。否则保存失败
     *
     * @param runnable 由业务侧传入保存方法
     */
    fun saveThumbnail(runnable: suspend () -> Unit) {
        Logger.d(TAG, "saveThumbnail")
        saveBatchMap.put(SaveType.Thumbnail, runnable)
        applyAndSave()
    }

    /**
     * 强制保存，常用于“返回逻辑”，并更新SharedFlow（调用了updateSaveInfoSharedFlow）
     *
     * @return 返回协程，是否阻塞等待保存完成
     */
    fun forceSave(): Deferred<Unit> {
        Logger.d(TAG, "forceSave")
        val job = applyAndSave(forceSave = true)
        job.invokeOnCompletion {
            Logger.d(TAG, "forceSave job end! reason: ${it ?: "success"}")
        }
        return scope.async(Dispatchers.IO) { job.join() }
    }
}