package com.tcl.ai.note.handwritingtext.richtext.utils

import android.app.Activity
import android.content.Context
import android.os.Build
import android.util.DisplayMetrics
import android.util.TypedValue
import android.view.WindowInsets
import android.view.WindowManager
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.Logger

object DisplayUtils {

    private var SCREEN_WIDTH = 0
    private var SCREEN_HEIGHT = 0

    /**
     * 获取屏幕相关信息(不含系统栏)，deprecated接口已兼容
     */
    @JvmStatic
    fun getScreenRelatedInformation(context: Context): IntArray {
        return getScreenWidthAndHeight(context)
    }

    /**
     * 获取屏幕真实像素(含系统栏)，deprecated接口已兼容
     */
    @JvmStatic
    fun getRealScreenRelatedInformation(context: Context): IntArray {
        return getScreenWidthAndHeight(context)
    }

    /**
     * 获取状态栏高度
     */
    @JvmStatic
    fun getStatusBarHeight(context: Context): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        Logger.i("StatusBarHeight= $result")
        return result
    }

    /**
     * 获取导航栏高度
     */
    @JvmStatic
    fun getNavigationBarHeight(context: Context): Int {
        var naH = 0
        val rId = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
        if (rId > 0) {
            naH = context.resources.getDimensionPixelSize(rId)
        }
        Logger.i("NavigationBarHeight= $naH")
        return naH
    }

    @JvmStatic
    fun dp2px(context: Context, dp: Float): Int {
        return (context.resources.displayMetrics.density * dp + 0.5f).toInt()
    }

    @JvmStatic
    fun dp2px(dpValue: Int): Int {
        val scale = GlobalContext.instance.resources.displayMetrics.density
        return (dpValue * scale + 0.5f).toInt()
    }

    @JvmStatic
    fun px2dip(pxValue: Float): Int {
        val scale = GlobalContext.instance.resources.displayMetrics.density
        return (pxValue / scale + 0.5f).toInt()
    }

    // sp->px
    @JvmStatic
    fun sp2px(spVal: Float): Float {
        return TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_SP, spVal,
            GlobalContext.instance.resources.displayMetrics
        )
    }

    /**
     * 获取屏幕最小边长
     */
    @JvmStatic
    fun getScreenMinSize(context: Context): Int {
        val sizes = getScreenWidthAndHeight(context)
        return sizes.minOrNull() ?: 0
    }

    /**
     * 获取屏幕宽高，自动根据API做兼容
     */
    @JvmStatic
    fun getScreenWidthAndHeight(context: Context): IntArray {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            val windowManager = context.getSystemService(WindowManager::class.java)
            val metrics = windowManager!!.currentWindowMetrics
            val insets = metrics.windowInsets.getInsetsIgnoringVisibility(WindowInsets.Type.systemBars())
            val bounds = metrics.bounds
            val width = bounds.width() - insets.left - insets.right
            val height = bounds.height() - insets.top - insets.bottom
            intArrayOf(width, height)
        } else {
            val windowManager = context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val displayMetrics = DisplayMetrics()
            @Suppress("DEPRECATION")
            windowManager.defaultDisplay.getMetrics(displayMetrics)
            intArrayOf(displayMetrics.widthPixels, displayMetrics.heightPixels)
        }
    }

    /**
     * 获取屏幕宽度
     */
    @JvmStatic
    fun getScreenWidth(): Int {
        if (SCREEN_WIDTH == 0) setWH(GlobalContext.instance)
        return SCREEN_WIDTH
    }

    /**
     * 获取屏幕高度
     */
    @JvmStatic
    fun getScreenHeight(): Int {
        if (SCREEN_HEIGHT == 0) setWH(GlobalContext.instance)
        return SCREEN_HEIGHT
    }

    /**
     * 获取屏幕最小长度（短边）
     */
    @JvmStatic
    fun getScreenMinSize(): Int {
        if (SCREEN_WIDTH == 0 || SCREEN_HEIGHT == 0) setWH(GlobalContext.instance)
        return minOf(SCREEN_WIDTH, SCREEN_HEIGHT)
    }

    private fun setScreenWidth(screenWidth: Int) {
        SCREEN_WIDTH = screenWidth
    }

    private fun setScreenHeight(screenHeight: Int) {
        SCREEN_HEIGHT = screenHeight
    }

    /**
     * 更新静态全局宽高缓存
     */
    @JvmStatic
    fun setWH(context: Context) {
        val sizes = getScreenWidthAndHeight(context)
        setScreenWidth(sizes[0])
        setScreenHeight(sizes[1])
        Logger.d("set fixed WH w/h=${sizes[0]}/${sizes[1]}")
    }

    /**
     * 设置状态栏颜色
     */
    @JvmStatic
    fun setStatusBarColor(activity: Activity, color: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            activity.window.statusBarColor = color
        }
    }
}