package com.tcl.ai.note.handwritingtext.database.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.TypeConverters
import androidx.room.Update
import com.tcl.ai.note.handwritingtext.bean.DrawStroke
import com.tcl.ai.note.handwritingtext.database.convertor.DrawConvertor
import com.tcl.ai.note.handwritingtext.database.entity.DBConst
import com.tcl.ai.note.handwritingtext.database.entity.Draw
import kotlinx.coroutines.flow.Flow

@Dao
@TypeConverters(DrawConvertor::class)
interface DrawDao {
    @Query("select * from ${DBConst.TABLE_NAME_DRAW} where noteId = :noteId")
    fun getFlowByNoteId(noteId: Long): Flow<Draw?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrReplace(draw: Draw): Long

    @Query("select * from ${DBConst.TABLE_NAME_DRAW} where id = :drawId")
    suspend fun getByDrawId(drawId: Long): Draw?

    @Query("select * from ${DBConst.TABLE_NAME_DRAW} where noteId = :noteId")
    suspend fun getByNoteId(noteId: Long): Draw?

    @Update
    suspend fun update(draw: Draw): Int

    @Query("update ${DBConst.TABLE_NAME_DRAW} set strokes = :strokes, modifyTime = :modifyTime where id = :drawId")
    suspend fun updateStrokes(drawId: Long, strokes: List<DrawStroke>, modifyTime: Long)
}