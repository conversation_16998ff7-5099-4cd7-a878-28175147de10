package com.tcl.ai.note.handwritingtext.state

import androidx.compose.runtime.Stable
import com.tcl.ai.note.handwritingtext.bean.MenuBarItem

/**
 * 菜单工具弹窗状态管理
 * 使用sealed class管理不同的弹窗状态，确保类型安全
 */
@Stable
sealed class MenuPopupState {
    /**
     * 无弹窗显示状态
     */
    data object None : MenuPopupState()
    
    /**
     * 橡皮擦工具弹窗状态
     * @param menuBarItem 触发弹窗的菜单项
     */
    data class EraserTool(val menuBarItem: MenuBarItem) : MenuPopupState()
    
    /**
     * 笔刷工具弹窗状态
     * @param menuBarItem 触发弹窗的菜单项
     */
    data class PenTool(val menuBarItem: MenuBarItem) : MenuPopupState()
    
    /**
     * 颜色选择器弹窗状态
     * @param menuBarItem 触发弹窗的菜单项
     */
    data class ColorPalette(val menuBarItem: MenuBarItem) : MenuPopupState()
}

/**
 * 菜单工具事件定义
 * 使用sealed class管理不同的用户交互事件
 */
@Stable
sealed class MenuEvent {
    /**
     * 键盘按钮点击事件
     */
    data object KeyboardClick : MenuEvent()
    
    /**
     * 笔刷按钮点击事件
     * @param menuBarItem 被点击的菜单项
     */
    data class BrushClick(val menuBarItem: MenuBarItem) : MenuEvent()
    
    /**
     * 橡皮擦按钮点击事件
     * @param menuBarItem 被点击的菜单项
     */
    data class EraserClick(val menuBarItem: MenuBarItem) : MenuEvent()
    
    /**
     * 打开颜色选择器事件
     */
    data object OpenColorPicker : MenuEvent()
    
    /**
     * 关闭弹窗事件
     */
    data object DismissPopup : MenuEvent()
} 