package com.tcl.ai.note.handwritingtext.bean
import androidx.annotation.DrawableRes
import androidx.annotation.IntDef
import androidx.annotation.IntRange
import androidx.compose.ui.graphics.Color
import com.sunia.penengine.sdk.operate.touch.PenType
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.handwritingtext.utils.ColorUtils.inverseColor
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes

/**
 *  画笔样式数据类
 */
sealed class PenStyle(
    open var width: Float,
    open var color: Color,
    @DrawableRes open val hullResId: Int,
    @DrawableRes open val nibResId: Int,
    @IntRange(1, 100) open var alpha: Int = 100,
    open var minWidth: Float = 1f,
    open var maxWidth: Float = 20f,
    @ColorSourceAnnotation open var colorSource: Int = ColorSource.COLOR_SOURCE_INTERNAL,
    val minProgress: Int = 1,
    val maxProgress: Int = 100,
    open val curProgress: Int = 1

) {
    data class BallPen(
        override var width: Float = isTablet.judge(10f,18f),
        override var color: Color = Color(0xFF1764FE),
        override var alpha: Int = 100,
        override var colorSource: Int = ColorSource.COLOR_SOURCE_INTERNAL,
        override var minWidth: Float = 1f,
        override var maxWidth: Float = isTablet.judge(20f,60f),
        override var curProgress: Int = isTablet.judge(50,30)
    ) : PenStyle(width, color, R.drawable.ic_tool_ball_pen_hull, R.drawable.ic_tool_ball_pen_nib)



    data class MarkerPen(
        override var width: Float = isTablet.judge(9f,30f),
        override var color: Color = Color(0xFFFFB916),
        override var alpha: Int = 45,
        override var colorSource: Int = ColorSource.COLOR_SOURCE_INTERNAL,
        override var minWidth: Float = 1f,
        override var maxWidth: Float = isTablet.judge(30f,60f),
        override var curProgress: Int =isTablet.judge(30,50)
    ) : PenStyle(
        width,
        color,
        R.drawable.ic_tool_marker_pen_hull,
        R.drawable.ic_tool_marker_pen_nib
    )

    data class Pencil(
        override var width: Float = isTablet.judge(16f,30f),
        override var color: Color = Color(0xFFE74780),
        override var alpha: Int = 100,
        override var colorSource: Int = ColorSource.COLOR_SOURCE_INTERNAL,
        override var minWidth: Float = 1f,
        override var maxWidth: Float = isTablet.judge(20f,60f),
        override var curProgress: Int =isTablet.judge(80,50)
    ) : PenStyle(width, color, R.drawable.ic_tool_pencil_hull, R.drawable.ic_tool_pencil_nib)

    data class PenFountain(
        override var width: Float =  isTablet.judge(6f,24f),
        override var color: Color = Color(0xFF000000),
        override var alpha: Int = 100,
        override var colorSource: Int = ColorSource.COLOR_SOURCE_INTERNAL,
        override var minWidth: Float = 1f,
        override var maxWidth: Float = isTablet.judge(20f,80f),
        override var curProgress: Int = 30
    ) : PenStyle(
        width,
        color,
        R.drawable.ic_tool_pen_fountain_hull,
        R.drawable.ic_tool_pen_fountain_nib
    )


}



fun PenStyle.toPenType(): PenType =
    when (this) {
        is PenStyle.BallPen -> PenType.PEN_BALLPOINT
        is PenStyle.MarkerPen -> PenType.PEN_MARK
        is PenStyle.Pencil -> PenType.PEN_PENCIL
        is PenStyle.PenFountain -> PenType.PEN_FOUNTAIN
    }

fun PenStyle.menHullIcon(): Int = when (this) {
    is PenStyle.BallPen -> R.drawable.ic_menu_hull_ballpen
    is PenStyle.MarkerPen -> R.drawable.ic_menu_hull_markerpen
    is PenStyle.Pencil -> R.drawable.ic_menu_hull_pencil
    is PenStyle.PenFountain -> R.drawable.ic_menu_hull_pen_fountain


}

fun PenStyle.isMarkerPen(): Boolean = this is PenStyle.MarkerPen


fun PenStyle.menNibIcon(): Int = when (this) {
    is PenStyle.BallPen -> R.drawable.ic_menu_nib_ballpen
    is PenStyle.MarkerPen -> R.drawable.ic_menu_nib_markerpen
    is PenStyle.Pencil -> R.drawable.ic_menu_nib_pencil
    is PenStyle.PenFountain -> R.drawable.ic_menu_nib_pen_fountain

}


object ColorSource {
    const val COLOR_SOURCE_INTERNAL = 1
    const val COLOR_SOURCE_EXTERNAL = 2
}

@Retention(AnnotationRetention.SOURCE)
@IntDef(ColorSource.COLOR_SOURCE_INTERNAL, ColorSource.COLOR_SOURCE_EXTERNAL)
annotation class ColorSourceAnnotation

fun PenStyle.copyWith(
    width: Float? = null,
    color: Color? = null,
    alpha: Int? = null,
    curProgress: Int? = null,
    @ColorSourceAnnotation colorSource: Int? = null
): PenStyle =
    when (this) {
        is PenStyle.BallPen -> this.copy(
            width = width ?: this.width,
            color = color ?: this.color,
            alpha = alpha ?: this.alpha,
            curProgress =curProgress ?: this.curProgress,
            colorSource = colorSource ?: this.colorSource
        )

        is PenStyle.MarkerPen -> this.copy(
            width = width ?: this.width,
            color = color ?: this.color,
            alpha = alpha ?: this.alpha,
            curProgress =curProgress ?: this.curProgress,
            colorSource = colorSource ?: this.colorSource
        )

        is PenStyle.Pencil -> this.copy(
            width = width ?: this.width,
            color = color ?: this.color,
            alpha = alpha ?: this.alpha,
            curProgress =curProgress ?: this.curProgress,
            colorSource = colorSource ?: this.colorSource
        )


        is PenStyle.PenFountain -> this.copy(
            width = width ?: this.width,
            color = color ?: this.color,
            alpha = alpha ?: this.alpha,
            curProgress =curProgress ?: this.curProgress,
            colorSource = colorSource ?: this.colorSource
        )

    }


fun PenStyle.progressToPenSize(progress: Int): Float {
    val clampedProgress = progress.coerceIn(minProgress, maxProgress)

    val result = ((clampedProgress - minProgress).toFloat() / (maxProgress - minProgress) *
            (maxWidth - minWidth) + minWidth)


   Logger.d("PenStyle","${this.toPenType()},progress:$progress,result:$result,maxWidth:$maxWidth,minWidth:${minWidth}")

    return result
}
fun PenStyle.getDescription(): Int {
    return when (this) {
        is PenStyle.BallPen -> com.tcl.ai.note.base.R.string.pen_selector_ball_pen
        is PenStyle.MarkerPen ->  com.tcl.ai.note.base.R.string.pen_selector_marker_pen
        is PenStyle.Pencil -> com.tcl.ai.note.base.R.string.pen_selector_pencil
        is PenStyle.PenFountain ->  com.tcl.ai.note.base.R.string.pen_selector_fountain_pen
    }
}



