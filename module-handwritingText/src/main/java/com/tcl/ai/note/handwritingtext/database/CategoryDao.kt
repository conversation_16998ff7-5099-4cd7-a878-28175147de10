package com.tcl.ai.note.handwritingtext.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Update
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import kotlinx.coroutines.flow.Flow

@Dao
interface CategoryDao {
    @Query("SELECT * FROM categories ORDER BY modifyTime DESC")
    fun getAllCategories(): List<NoteCategory>

    @Query("SELECT * FROM categories WHERE categoryId = :categoryId")
    fun getCategoryById(categoryId: Long): NoteCategory?

    @Insert
    fun insert(category: NoteCategory): Long

    @Update
    fun update(category: NoteCategory): Int

    @Delete
    fun delete(category: NoteCategory): Int

    /**
     * 获取分类列表的Flow
     * DESC 表示按照修改时间降序排列 新增的分类会排在前面
     * 未分类 是在第一个 降序之后 排在了最后面
     */
    @Query("SELECT * FROM categories ORDER BY modifyTime DESC")
    fun getAllCategoriesFlow(): Flow<List<NoteCategory>>
}