package com.tcl.ai.note.handwritingtext.vm.event

import android.util.Log
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarState
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.toHex
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow

/**
 * 富文本样式事件管理器
 * 用于处理富文本编辑器的样式事件传递
 * 将事件传递给RichTextViewHolder里的EditText
 * 同时将工具栏状态传递给RichTextToolBarViewModel
 */
object RichTextEventManager {
    // 富文本样式点击事件流
    private val _richTextStyleActionEvent = MutableStateFlow<RichTextStyleActionEvent?>(null)
    val richTextStyleActionEvent: StateFlow<RichTextStyleActionEvent?> = _richTextStyleActionEvent.asStateFlow()

    // 工具栏状态
    private val _toolBarState = MutableStateFlow(RichTextToolBarState())
    val toolBarState: StateFlow<RichTextToolBarState> = _toolBarState.asStateFlow()
    // 富文本操作事件流
    private val _richTextOperateEvent = MutableSharedFlow<RichTextOperateEvent>(extraBufferCapacity = 1)
    val richTextOperateEvent = _richTextOperateEvent.asSharedFlow()

    /**
     * 触发样式事件，给富文本RichTextViewHolder里的EditText
     */
    fun triggerStyleActionEvent(event: RichTextStyleActionEvent) {
        _richTextStyleActionEvent.value = null // 先clear
        _richTextStyleActionEvent.value = event // 再发
    }

    // 触发富文本操作事件，给RichTextToolBarViewModel
    fun triggerTextOperateEvent(event: RichTextOperateEvent) {
        _richTextOperateEvent.tryEmit(event) // 非挂起，立即生效且线程安全
    }

    // 清除当前事件，防止重复处理
    fun clearStyleEvent() {
        _richTextStyleActionEvent.value = null
    }


    /**
     * 还原工具栏到默认状态
     */
    fun restoreToolBarStyleState() {
        _toolBarState.value = RichTextToolBarState()
    }
    // 更新样式状态
    fun updateToolBarStyleState(update: (RichTextToolBarState) -> RichTextToolBarState) {
        _toolBarState.value = update(_toolBarState.value)
    }

    fun updateManagerStyleState(state: RichTextToolBarState){
        _toolBarState.value = state
    }

}