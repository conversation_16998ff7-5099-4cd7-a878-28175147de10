package com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.renderer

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarConfig
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarItem
import com.tcl.ai.note.handwritingtext.bean.RichTextToolBarState
import com.tcl.ai.note.handwritingtext.bean.RichTextToolGroup
import com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.group.RenderActionButton
import com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.group.RenderColorPicker
import com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.group.RenderDropdownSelector
import com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.group.RenderToggleButton
import com.tcl.ai.note.handwritingtext.vm.text.RichTextToolBarViewModel
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.widget.VerticalLine


/**
 * 富文本工具栏渲染器: 根据配置和状态渲染工具栏UI
 * 1. RichTextToolBarRenderer - 主入口函数，负责整体布局和状态管理
 * 2. RenderToolGroups - 负责渲染工具组集合
 * 3. RenderGroupDivider - 负责渲染组间分隔符
 * 4. RenderGroupItems - 负责渲染单个组内的工具项
 * 5. 各种具体的渲染函数 - 负责渲染特定类型的工具项
 *
 * @param config 工具栏配置，包含显示的工具项、间距等设置
 * @param state 工具栏状态，包含各工具项的激活状态
 * @param modifier 修饰符
 */
@SuppressLint("DesignSystem")
@Composable
fun RichTextToolBarRenderer(
    config: RichTextToolBarConfig,
    state: RichTextToolBarState,
    modifier: Modifier = Modifier,
    viewModel: RichTextToolBarViewModel = viewModel()
) {
    // 计算偏移量的像素值
    val density = LocalDensity.current
    val offsetYPx = with(density) { (12.dp).roundToPx() } // 上方间距
    val scrollState = rememberScrollState()

    // 工具栏容器
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(TclTheme.dimens.richTextToolBarHeight)
            .horizontalScroll(scrollState)  // 添加水平滚动能力
        ,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        val groupedItems = config.getItemsByGroup()

        // 渲染所有工具组
        RenderToolGroups(groupedItems, config, state, offsetYPx)
    }
}

/**
 * 渲染所有工具组
 *
 * @param groupedItems 按组分类的工具项Map
 * @param config 工具栏配置
 * @param state 工具栏状态
 * @param offsetYPx 弹出框Y轴偏移量（像素）
 */
@Composable
private fun RenderToolGroups(
    groupedItems: Map<RichTextToolGroup, List<RichTextToolBarItem>>,
    config: RichTextToolBarConfig,
    state: RichTextToolBarState,
    offsetYPx: Int
) {
    groupedItems.entries.forEachIndexed { groupIndex, (group, items) ->
        // 处理组间分隔符
        if (groupIndex > 0) {
            RenderGroupDivider(groupIndex, groupedItems, config)
        }

        // 渲染组内工具项
        RenderGroupItems(items, config, state, offsetYPx)
    }
}

/**
 * 渲染组间分隔符
 * 根据组类型决定是否显示分隔符，特定组之间不显示分隔符
 *
 * @param groupIndex 当前组的索引
 * @param groupedItems 按组分类的工具项Map
 * @param config 工具栏配置
 */
@Composable
private fun RenderGroupDivider(
    groupIndex: Int,
    groupedItems: Map<RichTextToolGroup, List<RichTextToolBarItem>>,
    config: RichTextToolBarConfig
) {
    if (config.showGroupDividers) {
        val prevGroup = groupedItems.entries.elementAt(groupIndex - 1).key
        val currentGroup = groupedItems.entries.elementAt(groupIndex).key

        val shouldShowDivider = !(
                // 删除线(TEXT_FORMAT)和字体颜色(COLOR)之间
                (prevGroup == RichTextToolGroup.TEXT_FORMAT && currentGroup == RichTextToolGroup.COLOR) ||
                        // 字体颜色(COLOR)和字号大小(FONT_SIZE)之间
                        (prevGroup == RichTextToolGroup.COLOR && currentGroup == RichTextToolGroup.FONT_SIZE)
                )

        if (shouldShowDivider) {
            // 图标与分割线的间距
            Spacer(modifier = Modifier.width(config.groupSpacing))

            // 添加组间分割线，固定高度20dp
            Box(modifier = Modifier.height(20.dp), contentAlignment = Alignment.Center) {
                VerticalLine()
            }

            // 分割线与下一个图标的间距
            Spacer(modifier = Modifier.width(config.groupSpacing))
        } else {
            // 只添加间距，不添加分割线
            Spacer(modifier = Modifier.width(config.groupSpacing))
        }
    }
}

/**
 * 渲染组内工具项
 * 渲染一个工具组内的所有工具项，并处理它们之间的间距
 *
 * @param items 组内工具项列表
 * @param config 工具栏配置
 * @param state 工具栏状态
 * @param offsetYPx 弹出框Y轴偏移量（像素）
 */
@Composable
private fun RenderGroupItems(
    items: List<RichTextToolBarItem>,
    config: RichTextToolBarConfig,
    state: RichTextToolBarState,
    offsetYPx: Int
) {
    items.forEachIndexed { itemIndex, item ->
        // 组内图标之间的间距
        if (itemIndex > 0) {
            Spacer(modifier = Modifier.width(config.itemSpacing))
        }

        Box {
            // 渲染工具栏项目
            when (item) {
                is RichTextToolBarItem.ToggleButton -> {
                    RenderToggleButton(item, state, isEnabled = config.isEnabled && item.isEnabled)
                }

                is RichTextToolBarItem.ActionButton -> {
                    RenderActionButton(item, isEnabled = config.isEnabled && item.isEnabled)
                }

                is RichTextToolBarItem.DropdownSelector<*> -> {
                    RenderDropdownSelector(
                        item,
                        state,
                        isEnabled = config.isEnabled && item.isEnabled,
                        offsetYPx
                    )
                }

                is RichTextToolBarItem.ColorPicker -> {
                    RenderColorPicker(
                        item,
                        state,
                        offsetYPx,
                        isEnabled = config.isEnabled && item.isEnabled
                    )
                }

                is RichTextToolBarItem.Divider -> {
                    // 分割线在组级别处理，这里不需要渲染
                }
            }
        }
    }
}






