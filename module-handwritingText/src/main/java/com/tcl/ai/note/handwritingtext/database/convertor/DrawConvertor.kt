package com.tcl.ai.note.handwritingtext.database.convertor

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.tcl.ai.note.handwritingtext.bean.DrawStroke
import com.tcl.ai.note.utils.stringToType
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.decodeFromByteArray
import kotlinx.serialization.encodeToByteArray
import kotlinx.serialization.protobuf.ProtoBuf

@OptIn(ExperimentalSerializationApi::class)
class DrawConvertor {
    private val protoFormat = ProtoBuf

    @TypeConverter
    fun drawStrokeListToByteArray(strokes: List<DrawStroke>) =
        protoFormat.encodeToByteArray(strokes)

    @TypeConverter
    fun byteArrayToDrawStrokeList(byteArray: ByteArray) =
        protoFormat.decodeFromByteArray<List<DrawStroke>>(byteArray)
}

