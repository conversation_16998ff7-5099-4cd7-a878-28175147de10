package com.tcl.ai.note.handwritingtext.richtext.viewholder

import android.text.Layout
import androidx.compose.ui.graphics.Color
import com.tcl.ai.note.handwritingtext.richtext.listener.StyleStatusListener
import com.tcl.ai.note.handwritingtext.vm.event.RichTextEventManager
import com.tcl.ai.note.utils.Logger

/**
 * 富文本样式状态委托类
 * 集中处理所有样式状态回调，避免在ViewHolder中重复代码
 */
class StyleStatusDelegate : StyleStatusListener {


     val  TAG ="StyleStatusDelegate"

    override fun onTodoToggled(isActive: Boolean) {
        Logger.d(TAG,"onTodoToggled")
        // 更改待办事项状态
        RichTextEventManager.updateToolBarStyleState { it.copy(isTodoActive = isActive) }
    }

    override fun onBoldToggled(isActive: Boolean) {
        Logger.d(TAG,"onBoldToggled")
        // 应用粗体样式
        RichTextEventManager.updateToolBarStyleState { it.copy(isBoldActive = isActive) }
    }

    override fun onItalicToggled(isActive: Boolean) {
        Logger.d(TAG,"onItalicToggled")

        // 应用斜体样式
        RichTextEventManager.updateToolBarStyleState { it.copy(isItalicActive = isActive) }
    }

    override fun onUnderlineToggled(isActive: Boolean) {
        Logger.d(TAG,"onUnderlineToggled")
        RichTextEventManager.updateToolBarStyleState { it.copy(isUnderlineActive = isActive) }
    }

    override fun onStrikethroughToggled(isActive: Boolean) {
        Logger.d(TAG,"onStrikethroughToggled")
        RichTextEventManager.updateToolBarStyleState { it.copy(isStrikethroughActive = isActive) }
    }

    override fun onAlignmentApplied(alignment: Layout.Alignment?) {
        Logger.d(TAG,"onAlignmentApplied")

        // 将实际的对齐状态写入Compose状态，并间接影响Toolbar按钮的高亮状态
        // 应用对齐方式
        if (alignment != null) {
            RichTextEventManager.updateToolBarStyleState {
                it.copy(alignmentState = alignment)
            }
        }
    }

    override fun onIndentLeftApplied(isActive: Boolean) {
        Logger.d(TAG,"onAlignmentApplied")
        RichTextEventManager.updateToolBarStyleState {
            it.copy(isIndentLeftActive = false)
        }
    }

    override fun onIndentRightApplied(isActive: Boolean) {
        Logger.d(TAG,"onIndentRightApplied")
        // 应用右缩进
        RichTextEventManager.updateToolBarStyleState {
            it.copy(isIndentRightActive = true)
        }
    }

    override fun onNumberedListToggled(isActive: Boolean) {
        Logger.d(TAG,"onNumberedListToggled")
        // 实现有序列表
        RichTextEventManager.updateToolBarStyleState {
            it.copy(
                isNumberListActive = isActive,
                isBulletListActive = if (isActive) false else it.isBulletListActive
            )
        }
    }

    override fun onBulletedListToggled(isActive: Boolean) {
        Logger.d(TAG,"onBulletedListToggled")
        // 实现无序列表
        RichTextEventManager.updateToolBarStyleState {
            it.copy(
                isBulletListActive = isActive,
                isNumberListActive = if (isActive) false else it.isNumberListActive
            )
        }
    }

    override fun onFontSizeApplied(size: Int) {
        // 实现字体大小
        RichTextEventManager.updateToolBarStyleState { it.copy(selectedFontSize = size) }
    }

    override fun onFontColorApplied(color: Int) {
        // 实现字体颜色
        RichTextEventManager.updateToolBarStyleState { it.copy(textColor = Color(color)) }
    }

    override fun onBackgroundColorApplied(color: Int) {
        // 实现背景颜色
        RichTextEventManager.updateToolBarStyleState { it.copy(textBgColor = Color(color)) }
    }
}