package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.text.Editable;
import android.text.Spannable;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.history.OperationType;
import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.spans.AreLeadingMarginSpan;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;


public class ARE_IndentLeft extends ARE_ABS_FreeStyle   {

	public ARE_IndentLeft(AREditText editText) {
		super(editText.getContext());
		setEditText(editText);
	}


	@Override
	public void setListenerForImageView(ImageView imageView) {

	}

	public void setIndentLeft() {
		EditText editText = getEditText();
		int currentLine = Util.getCurrentCursorLine(editText);
		int start = Util.getThisLineStart(editText, currentLine);
		int end = Util.getThisLineEnd(editText, currentLine);

		Editable editable = editText.getText();
		AreLeadingMarginSpan[] existingLMSpans = editable.getSpans(start, end, AreLeadingMarginSpan.class);
		int currentLevel = 0;
		int newLevel = 0;

		if (null != existingLMSpans && existingLMSpans.length == 1) {
			AreLeadingMarginSpan currentLeadingMarginSpan = existingLMSpans[0];
			int originalEnd = editable.getSpanEnd(currentLeadingMarginSpan);
			editable.removeSpan(currentLeadingMarginSpan);
			currentLevel = currentLeadingMarginSpan.getLevel();
			newLevel = currentLeadingMarginSpan.decreaseLevel();
			if (newLevel > 0) {
				editable.setSpan(currentLeadingMarginSpan, start, originalEnd, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
			}

			// 记录操作到撤销重做管理器
			if (mEditText != null) {
				AREditText arEditText = mEditText;
				arEditText.getRichTextUndoRedoManager().recordOperation(
					start,
					end,
					OperationType.INDENT_LEFT,
					AreLeadingMarginSpan.class,
					newLevel,
					false
				);
			}
		}
		else {
			// No leading margin span found
			// Do nothing
		}
	}

	@Override
	public void applyStyle(Editable editable, int start, int end, Boolean isRecordToHistory) {
		// TODO Auto-generated method stub

	}

	@Override
	public Boolean needApplyStyle() {
		return false;
	}

	@Override
	public ImageView getImageView() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void setChecked(boolean isChecked) {
		// TODO Auto-generated method stub

	}

	@Override
	public void setisValid(boolean isValid) {

	}

	@Override
	public boolean getIsValid() {
		return false;
	}

	@Override
	public void updateCheckStatus(boolean checked) {

	}
}
