package com.tcl.ai.note.handwritingtext.utils

import android.graphics.Paint
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.utils.toPx
import kotlin.math.ceil

object TextTools {
    fun getDefaultTextSize(): IntArray {
        // 创建一个Paint对象
        val paint = Paint()
        paint.textSize = 12.dp.toPx
        // 要测量的文本
        val text = "输入文字 "
        // 测量文本宽度
        val textWidth = paint.measureText(text)
        // 测量文本高度
        val fontMetrics = paint.fontMetricsInt
        val textHeight = fontMetrics.bottom - fontMetrics.top
        return intArrayOf(ceil(textWidth.toDouble()).toInt(), textHeight)
    }
}