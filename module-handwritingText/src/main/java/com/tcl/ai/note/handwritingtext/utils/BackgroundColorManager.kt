package com.tcl.ai.note.handwritingtext.utils

import androidx.compose.ui.graphics.Color
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.utils.toArgbLong

/**
 * 背景颜色版本管理
 * 包含两个版本：V1旧版和V2新版，和二者直接的映射关系
 */
/**
 * 背景颜色版本管理
 */
object BackgroundColorManager {

    // V1 旧版颜色 (6个颜色)
    val bgColorsV1 = arrayListOf(
        Color(Skin.oldDefColor),    // 默认颜色
        Color(0xffF3F9FF),       // 浅蓝色
        Color(0xffECF7EC),       // 浅绿色
        Color(0xffFDFBF1),       // 浅黄色
        Color(0xffFFF1F1),       // 浅红色
        Color(0xffF7EFFF)        // 浅紫色
    )

    // V2 新版颜色 (7个颜色)
    val bgColorsV2 = arrayListOf(
        Color(Skin.defColor),    // 默认颜色
        Color(0xffF3C8AA),       // 橙色系
        Color(0xffF2E4A2),       // 黄色系
        Color(0xffCAE7E1),       // 绿色系
        Color(0xffCDE0F4),       // 蓝色系
        Color(0xffE3DBF6),       // 紫色系
        Color(0xffF6D5D7)        // 粉色系
    )

    // V1到V2的颜色映射表
    private val colorMappingV1ToV2 = mapOf(
        0xffF3F9FF to 0xffCDE0F4,  // 浅蓝色 -> 蓝色系
        0xffECF7EC to 0xffCAE7E1,  // 浅绿色 -> 绿色系
        0xffFDFBF1 to 0xffF2E4A2,  // 浅黄色 -> 黄色系
        0xffFFF1F1 to 0xffF6D5D7,  // 浅红色 -> 粉色系
        0xffF7EFFF to 0xffE3DBF6   // 浅紫色 -> 紫色系
    )

    /**
     * 将V1颜色映射到V2颜色
     * @param v1Color V1版本的颜色
     * @return 对应的V2版本颜色，如果找不到则返回原颜色
     */
    fun mapV1ToV2Color(v1Color: Color): Color {
        // 如果v1版默认颜色，直接返回新版的默认颜色
        if (v1Color.value == Color(Skin.oldDefColor).value) {
            return Color(Skin.defColor)
        }

        // 查找映射表
        val mappedColorValue = colorMappingV1ToV2[v1Color.toArgbLong()]
        return if (mappedColorValue != null) {
            Color(mappedColorValue)
        } else {
            v1Color // 如果找不到映射，返回原颜色
        }
    }

    /**
     * 将V1颜色索引映射到V2颜色
     * @param v1Index V1版本的颜色索引
     * @return 对应的V2版本颜色，如果索引无效则返回默认颜色
     */
    fun mapV1IndexToV2Color(v1Index: Int): Color {
        return if (v1Index >= 0 && v1Index < bgColorsV1.size) {
            mapV1ToV2Color(bgColorsV1[v1Index])
        } else {
            Color(Skin.defColor) // 返回默认颜色
        }
    }

    /**
     * 批量将V1颜色列表映射到V2颜色列表
     * @param v1Colors V1版本的颜色列表
     * @return 映射后的V2版本颜色列表
     */
    fun mapV1ListToV2(v1Colors: List<Color>): List<Color> {
        return v1Colors.map { mapV1ToV2Color(it) }
    }

    /**
     * 获取当前使用的颜色版本（V2为最新版本）
     */
    fun getCurrentColors(): List<Color> = bgColorsV2

    /**
     * 检查是否为V1颜色
     * @param color 要检查的颜色
     * @return 如果是V1颜色返回true
     */
    fun isV1Color(color: Color): Boolean {
        return bgColorsV1.any { it.value == color.value }
    }

}