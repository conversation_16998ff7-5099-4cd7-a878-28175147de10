package com.tcl.ai.note.handwritingtext.richtext.inner;


import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils;

public class Constants {

	public static final int CHECKED_COLOR = 0XffFF4081; // android.R.color.holo_green_light;

	public static final int UNCHECKED_COLOR = 0X00000000; // android.R.color.transparent;

	public static final int COLOR_QUOTE = 0Xffcccccc;

	public static final String ZERO_WIDTH_SPACE_STR = "\u200B";

	public static final int ZERO_WIDTH_SPACE_INT = 8203;

	public static final String ZERO_WIDTH_SPACE_STR_ESCAPE = "&#8203;";

	public static final char CHAR_NEW_LINE = '\n';

	public static final int KEY_DEL = 67;

	public static final int COLOR_BACKGROUND_DEFAULT = 0XFFFFCC80;

	public static final int DEFAULT_FONT_SIZE = 18;

	public static final String EMOJI = "emoji";

	public static final int PARAGRAPH_LEADING_MARGIN = DisplayUtils.dp2px(10);
	public static final float PARAGRAPH_LEADING_RATIO = 0.045f;
}
