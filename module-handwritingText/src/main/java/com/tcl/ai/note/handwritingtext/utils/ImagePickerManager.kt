package com.tcl.ai.note.handwritingtext.utils

import android.content.ContentValues
import android.content.Context
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.result.ActivityResultLauncher
import com.tcl.ai.note.utils.Logger

/**
 * 图片选择管理类
 */
class ImagePickerManager(
    private val context: Context,
//    private val pickImagesLauncher: ActivityResultLauncher<String>
) {
    private var capturedPhotoUri: Uri? = null
    var takePhotoLauncher: ActivityResultLauncher<Uri>? = null
    private var photoCallback: ((Uri) -> Unit)? = null

    fun setPhotoCallback(callback: (Uri) -> Unit) {
        photoCallback = callback
    }

    /**
     * 启动选图
     */
    fun pickImages() {
//        pickImagesLauncher.launch("image/*")
    }

    /**
     * 启动拍照
     */
    fun takePhoto() {
        try {
            capturedPhotoUri = createPhotoUri()
            capturedPhotoUri?.let {
                takePhotoLauncher?.launch(it)
            }
        } catch (e: Exception) {
            Logger.d("ImagePickerManager", "take photo error=$e")
        }
    }

    fun handleTakePhotoResult() {
        capturedPhotoUri?.let {
            photoCallback?.invoke(it)
        }
    }

    private fun createPhotoUri(): Uri? {
        val contentValues = ContentValues().apply {
            put(MediaStore.Images.Media.DISPLAY_NAME, "photo_${System.currentTimeMillis()}.jpg")
            put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
        }
        Logger.d("ImagePickerManager", "createPhotoUri: ${contentValues.toString()}")
        return context.contentResolver.insert(
            MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
            contentValues
        )
    }
}