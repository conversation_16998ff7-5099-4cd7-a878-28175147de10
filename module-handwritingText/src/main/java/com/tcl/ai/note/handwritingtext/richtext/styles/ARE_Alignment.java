package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.text.Editable;
import android.text.Layout.Alignment;
import android.text.Spannable;
import android.text.Spanned;
import android.text.style.AlignmentSpan;
import android.text.style.AlignmentSpan.Standard;
import android.view.View;
import android.view.View.OnClickListener;
import android.view.ViewGroup;
import android.widget.ImageView;


import com.tcl.ai.note.handwritingtext.richtext.inner.Constants;
import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

import java.util.ArrayList;
import java.util.List;


public class ARE_Alignment extends ARE_ABS_FreeStyle {

	// 所有对齐按钮的实例
	private static final List<ARE_Alignment> ALL_ALIGNMENTS = new ArrayList<>(3);

	private ImageView mAlignmentImageView;

	private Alignment mAlignment;

	private AREditText mEditText;

	private boolean mChecked = false;
	private boolean mValid = false; // 是否允许点击


	public ARE_Alignment() {
		super(null);
	}


	@Override
	public void setEditText(AREditText editText) {
		this.mEditText = editText;
	}

	@Override
	public void updateCheckStatus(boolean checked) {
		this.mChecked = checked;
	}

	@Override
	public void setListenerForImageView(ImageView imageView) {
		imageView.setOnClickListener(new OnClickListener() {
			@Override
			public void onClick(View v) {
			}
		});
	}

	public void setAlignment(Alignment alignment){
		if (!mValid) return;
		mAlignment = alignment;
		synchronized (ALL_ALIGNMENTS) {
			ALL_ALIGNMENTS.add(this);
		}
		if (null != mEditText) {
			int currentLine = Util.getCurrentCursorLine(mEditText);
			int start = Util.getThisLineStart(mEditText, currentLine);
			int end = Util.getThisLineEnd(mEditText, currentLine);

			Editable editable = mEditText.getEditableText();

			// 去掉当前行所有 AlignmentSpan
			Standard[] alignmentSpans = editable.getSpans(start, end, Standard.class);
			if (alignmentSpans != null) {
				for (Standard span : alignmentSpans) {
					editable.removeSpan(span);
				}
			}

			// 插入 AlignmentSpan
			AlignmentSpan alignSpan = new Standard(mAlignment);
			if (start == end) {
				editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
				end = Util.getThisLineEnd(mEditText, currentLine);
			}
			editable.setSpan(alignSpan, start, end, Spannable.SPAN_INCLUSIVE_INCLUSIVE);

			markLineAsAlignmentSpan(mAlignment, mEditText);
			updateAllAlignmentCheckStatus(mEditText);
		}
	}

	private AREditText findFocusedAREditText(View root) {
		if (root instanceof AREditText && root.hasFocus()) {
			return (AREditText) root;
		}
		if (root instanceof ViewGroup vg) {
			for (int i = 0; i < vg.getChildCount(); i++) {
				AREditText found = findFocusedAREditText(vg.getChildAt(i));
				if (found != null) return found;
			}
		}
		return null;
	}

	@Override
	public void applyStyle(Editable editable, int start, int end, Boolean isRecordToHistory) {
		AlignmentSpan[] alignmentSpans = editable.getSpans(start, end, AlignmentSpan.class);
		if (null == alignmentSpans || alignmentSpans.length == 0) {
			return;
		}

		Alignment alignment = alignmentSpans[0].getAlignment();
		if (mAlignment != alignment) {
			return;
		}

		if (end > start) {
			//
			// User inputs
			//
			// To handle the \n case
			char c = editable.charAt(end - 1);
			if (c == Constants.CHAR_NEW_LINE) {
				int alignmentSpansSize = alignmentSpans.length;
				int previousAlignmentSpanIndex = alignmentSpansSize - 1;
				AlignmentSpan previousAlignmentSpan = alignmentSpans[previousAlignmentSpanIndex];
				int lastAlignmentSpanStartPos = editable.getSpanStart(previousAlignmentSpan);
				if (end > lastAlignmentSpanStartPos) {
					editable.removeSpan(previousAlignmentSpan);
					editable.setSpan(previousAlignmentSpan, lastAlignmentSpanStartPos, end - 1, Spanned.SPAN_INCLUSIVE_INCLUSIVE);
				}
			} // #End of user types \n
		}
		else {
			//
			// User deletes
			int spanStart = editable.getSpanStart(alignmentSpans[0]);
			int spanEnd = editable.getSpanEnd(alignmentSpans[0]);

			if (spanStart >= spanEnd) {
				//
				// User deletes the last char of the span
				// So we think he wants to remove the span
				editable.removeSpan(alignmentSpans[0]);

				//
				// To delete the previous span's \n
				// So the focus will go to the end of previous span
				if (spanStart > 0) {
					editable.delete(spanStart - 1, spanEnd);
				}
			}
		}
	}

	@Override
	public Boolean needApplyStyle() {
		return false;
	}

	/**
	 * 光标/输入变化时调用，根据当前行的对齐方式刷新所有按钮状态
	 */
	public static void updateAllAlignmentCheckStatus(AREditText editText) {
		if (editText == null) return;

		int curLine = Util.getCurrentCursorLine(editText);
		int start = Util.getThisLineStart(editText, curLine);
		int end = Util.getThisLineEnd(editText, curLine);
		Editable editable = editText.getEditableText();

		Alignment currentAlign = Alignment.ALIGN_NORMAL; // 默认
		Standard[] alignmentSpans = editable.getSpans(start, end, Standard.class);
		if (alignmentSpans != null && alignmentSpans.length > 0) {
			// 取最后一个span（最新）
			currentAlign = alignmentSpans[alignmentSpans.length - 1].getAlignment();
		}

		synchronized (ALL_ALIGNMENTS) {
			for (ARE_Alignment alignmentStyle : ALL_ALIGNMENTS) {
				boolean isSelected = (currentAlign == alignmentStyle.mAlignment);
				alignmentStyle.updateCheckStatus(isSelected);
			}
		}
	}

	private void markLineAsAlignmentSpan(Alignment alignment, AREditText editText) {
		int currentLine = Util.getCurrentCursorLine(editText);
		int start = Util.getThisLineStart(editText, currentLine);
		int end = Util.getThisLineEnd(editText, currentLine);
		Editable editable = editText.getText();
		editable.insert(start, Constants.ZERO_WIDTH_SPACE_STR);
		start = Util.getThisLineStart(editText, currentLine);
		end = Util.getThisLineEnd(editText, currentLine);

		if (end < 1) {
			return;
		}

		if (editable.charAt(end - 1) == Constants.CHAR_NEW_LINE) {
			end--;
		}

		AlignmentSpan alignmentSpan = new Standard(alignment);
		editable.setSpan(alignmentSpan, start, end,	Spannable.SPAN_INCLUSIVE_INCLUSIVE);
	}

	@Override
	public ImageView getImageView() {
		return this.mAlignmentImageView;
	}

	@Override
	public void setChecked(boolean isChecked) {
		mChecked = isChecked;
		mAlignmentImageView.setSelected(isChecked);
	}

	@Override
	public boolean getIsChecked() {
		return mChecked;
	}

	@Override
	public void setisValid(boolean isValid) {
		mValid = isValid;
	}

	@Override
	public boolean getIsValid() {
		return mValid;
	}
}
