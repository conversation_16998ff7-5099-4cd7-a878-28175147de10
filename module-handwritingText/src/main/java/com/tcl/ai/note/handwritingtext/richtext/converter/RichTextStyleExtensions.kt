package com.tcl.ai.note.handwritingtext.richtext.converter

import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.richtext.data.StyleRange
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText
import kotlinx.serialization.json.Json

/**
 * 扩展函数，获取所有样式的总数
 */
fun RichTextStyleEntity.getTotalStyleCount(): Int {
    return fontColor.size + fontSize.size + align.size + bold.size + italic.size + underline.size + list.size
}

/**
 * 扩展函数，检查是否为空文档
 */
fun RichTextStyleEntity.isEmpty(): Boolean {
    return getTotalStyleCount() == 0
}
/**
 * JSON 配置
 */
private val json = Json {
    ignoreUnknownKeys = true
    prettyPrint = true
    encodeDefaults = true
}

/**
 * 扩展 RichTextKTUtils 对象，添加 Note-V2-Rick 格式相关方法
 */
fun parseNoteV2RichText(jsonString: String): RichTextStyleEntity? {
    return try {
        json.decodeFromString<RichTextStyleEntity>(jsonString)
    } catch (e: Exception) {
        e.printStackTrace()
        null
    }
}

fun toNoteV2RichTextJson(noteData: RichTextStyleEntity): String {
    return try {
        json.encodeToString(noteData)
    } catch (e: Exception) {
        e.printStackTrace()
        "{}"
    }
}

/**
 * AREditText 扩展函数
 */
fun AREditText.applyRichTextStyleEntity(noteData: RichTextStyleEntity, text: String) {
    RichTextStyleEntityToSpanConverter.applyToEditText(noteData, this, text)
}

fun AREditText.toRichTextStyleEntity(version: String = "1.0"): RichTextStyleEntity {
    return RichTextStyleSpanToEntityConverter.extractFromEditText(this, version)
}


/**
 * 合并两个 RichTextStyleEntity 对象
 */
fun RichTextStyleEntity.merge(other: RichTextStyleEntity): RichTextStyleEntity {
    return RichTextStyleEntity(
        version = maxOf(this.version, other.version),
        fontColor = this.fontColor + other.fontColor,
        fontSize = this.fontSize + other.fontSize,
        align = this.align + other.align,
        bold = this.bold + other.bold,
        italic = this.italic + other.italic,
        underline = this.underline + other.underline,
        list = this.list + other.list
    )
}

/**
 * 偏移所有样式位置 首页会用到 如果去掉前面的换行符 样式要做偏移
 */
fun RichTextStyleEntity.offset(offset: Int): RichTextStyleEntity {
    return RichTextStyleEntity(
        version = this.version,
        fontColor = this.fontColor.map { it.copy(start = it.start + offset, end = it.end + offset) },
        fontSize = this.fontSize.map { it.copy(start = it.start + offset, end = it.end + offset) },
        align = this.align.map { it.copy(start = it.start + offset, end = it.end + offset) },
        bold = this.bold.map { it.copy(start = it.start + offset, end = it.end + offset) },
        italic = this.italic.map { it.copy(start = it.start + offset, end = it.end + offset) },
        underline = this.underline.map { it.copy(start = it.start + offset, end = it.end + offset) },
        list = this.list.map { it.copy(start = it.start + offset, end = it.end + offset) }
    )
}

/**
 * 过滤指定范围内的样式
 */
fun RichTextStyleEntity.filterRange(startPos: Int, endPos: Int): RichTextStyleEntity {
    fun StyleRange.isInRange(): Boolean {
        return this.start >= startPos && this.end <= endPos
    }
    
    return RichTextStyleEntity(
        version = this.version,
        fontColor = this.fontColor.filter { it.isInRange() },
        fontSize = this.fontSize.filter { it.isInRange() },
        align = this.align.filter { it.isInRange() },
        bold = this.bold.filter { it.isInRange() },
        italic = this.italic.filter { it.isInRange() },
        underline = this.underline.filter { it.isInRange() },
        list = this.list.filter { it.isInRange() }
    )
}

/**
 * 调整指定范围内的样式位置（相对于范围起始位置）
 */
fun RichTextStyleEntity.adjustToRange(startPos: Int, endPos: Int): RichTextStyleEntity {
    fun StyleRange.adjustToRange(): StyleRange? {
        return when {
            this.end <= startPos || this.start >= endPos -> null // 完全在范围外
            this.start < startPos && this.end > endPos -> {
                // 跨越整个范围
                this.copy(start = 0, end = endPos - startPos)
            }
            this.start < startPos -> {
                // 开始在范围前，结束在范围内
                this.copy(start = 0, end = this.end - startPos)
            }
            this.end > endPos -> {
                // 开始在范围内，结束在范围后
                this.copy(start = this.start - startPos, end = endPos - startPos)
            }
            else -> {
                // 完全在范围内
                this.copy(start = this.start - startPos, end = this.end - startPos)
            }
        }
    }
    
    return RichTextStyleEntity(
        version = this.version,
        fontColor = this.fontColor.mapNotNull { it.adjustToRange() },
        fontSize = this.fontSize.mapNotNull { it.adjustToRange() },
        align = this.align.mapNotNull { it.adjustToRange() },
        bold = this.bold.mapNotNull { it.adjustToRange() },
        italic = this.italic.mapNotNull { it.adjustToRange() },
        underline = this.underline.mapNotNull { it.adjustToRange() },
        list = this.list.mapNotNull { it.adjustToRange() }
    )
}

/**
 * 移除指定范围内的样式
 */
fun RichTextStyleEntity.removeRange(startPos: Int, endPos: Int): RichTextStyleEntity {
    val rangeLength = endPos - startPos
    
    fun StyleRange.adjustAfterRemove(): StyleRange? {
        return when {
            this.end <= startPos -> this // 在删除范围之前，不受影响
            this.start >= endPos -> {
                // 在删除范围之后，需要向前移动
                this.copy(start = this.start - rangeLength, end = this.end - rangeLength)
            }
            this.start < startPos && this.end > endPos -> {
                // 跨越删除范围，需要缩短
                this.copy(end = this.end - rangeLength)
            }
            this.start < startPos && this.end <= endPos -> {
                // 开始在删除范围前，结束在删除范围内
                this.copy(end = startPos)
            }
            this.start >= startPos && this.end > endPos -> {
                // 开始在删除范围内，结束在删除范围后
                this.copy(start = startPos, end = this.end - rangeLength)
            }
            else -> null // 完全在删除范围内，移除此样式
        }
    }
    
    return RichTextStyleEntity(
        version = this.version,
        fontColor = this.fontColor.mapNotNull { it.adjustAfterRemove() },
        fontSize = this.fontSize.mapNotNull { it.adjustAfterRemove() },
        align = this.align.mapNotNull { it.adjustAfterRemove() },
        bold = this.bold.mapNotNull { it.adjustAfterRemove() },
        italic = this.italic.mapNotNull { it.adjustAfterRemove() },
        underline = this.underline.mapNotNull { it.adjustAfterRemove() },
        list = this.list.mapNotNull { it.adjustAfterRemove() }
    )
}

/**
 * 在指定位置插入文本后调整样式位置
 */
fun RichTextStyleEntity.insertText(position: Int, length: Int): RichTextStyleEntity {
    fun StyleRange.adjustAfterInsert(): StyleRange {
        return when {
            this.end <= position -> this // 在插入位置之前，不受影响
            this.start >= position -> {
                // 在插入位置之后，需要向后移动
                this.copy(start = this.start + length, end = this.end + length)
            }
            else -> {
                // 跨越插入位置，只需要调整结束位置
                this.copy(end = this.end + length)
            }
        }
    }
    
    return RichTextStyleEntity(
        version = this.version,
        fontColor = this.fontColor.map { it.adjustAfterInsert() },
        fontSize = this.fontSize.map { it.adjustAfterInsert() },
        align = this.align.map { it.adjustAfterInsert() },
        bold = this.bold.map { it.adjustAfterInsert() },
        italic = this.italic.map { it.adjustAfterInsert() },
        underline = this.underline.map { it.adjustAfterInsert() },
        list = this.list.map { it.adjustAfterInsert() }
    )
}

/**
 * 清理无效的样式范围（start >= end 或者位置为负数）
 */
fun RichTextStyleEntity.cleanup(): RichTextStyleEntity {
    fun StyleRange.isValid(): Boolean {
        return this.start >= 0 && this.end > this.start
    }
    
    return RichTextStyleEntity(
        version = this.version,
        fontColor = this.fontColor.filter { it.isValid() },
        fontSize = this.fontSize.filter { it.isValid() },
        align = this.align.filter { it.isValid() },
        bold = this.bold.filter { it.isValid() },
        italic = this.italic.filter { it.isValid() },
        underline = this.underline.filter { it.isValid() },
        list = this.list.filter { it.isValid() }
    )
}

/**
 * 获取指定位置的所有样式
 */
fun RichTextStyleEntity.getStylesAtPosition(position: Int): Map<String, List<StyleRange>> {
    fun List<StyleRange>.getAtPosition(): List<StyleRange> {
        return this.filter { position >= it.start && position < it.end }
    }
    
    return mapOf(
        "color" to fontColor.getAtPosition(),
        "fontSize" to fontSize.getAtPosition(),
        "align" to align.getAtPosition(),
        "bold" to bold.getAtPosition(),
        "italic" to italic.getAtPosition(),
        "underline" to underline.getAtPosition(),
        "li" to list.getAtPosition()
    )
}

/**
 * 检查指定范围是否有某种样式
 */
fun RichTextStyleEntity.hasStyleInRange(startPos: Int, endPos: Int, styleType: String): Boolean {
    val styles = when (styleType.lowercase()) {
        "color" -> fontColor
        "fontsize" -> fontSize
        "align" -> align
        "bold" -> bold
        "italic" -> italic
        "underline" -> underline
        "li" -> list
        else -> emptyList()
    }
    
    return styles.any { style ->
        !(style.end <= startPos || style.start >= endPos)
    }
}

/**
 * 获取文档的样式统计信息
 */
fun RichTextStyleEntity.getStyleStatistics(): Map<String, Int> {
    return mapOf(
        "color" to fontColor.size,
        "fontSize" to fontSize.size,
        "align" to align.size,
        "bold" to bold.size,
        "italic" to italic.size,
        "underline" to underline.size,
        "li" to list.size,
        "total" to getTotalStyleCount()
    )
}

/**
 * 转换为调试字符串
 */
fun RichTextStyleEntity.toDebugString(): String {
    val sb = StringBuilder()
    sb.appendLine("RichTextStyleEntity (version: $version)")
    sb.appendLine("Color styles: ${fontColor.size}")
    fontColor.forEach { sb.appendLine("  $it") }
    sb.appendLine("FontSize styles: ${fontSize.size}")
    fontSize.forEach { sb.appendLine("  $it") }
    sb.appendLine("Align styles: ${align.size}")
    align.forEach { sb.appendLine("  $it") }
    sb.appendLine("Bold styles: ${bold.size}")
    bold.forEach { sb.appendLine("  $it") }
    sb.appendLine("Italic styles: ${italic.size}")
    italic.forEach { sb.appendLine("  $it") }
    sb.appendLine("Underline styles: ${underline.size}")
    underline.forEach { sb.appendLine("  $it") }
    sb.appendLine("List styles: ${list.size}")
    list.forEach { sb.appendLine("  $it") }
    sb.appendLine("Total styles: ${getTotalStyleCount()}")
    return sb.toString()
}