package com.tcl.ai.note.handwritingtext.richtext.data

import kotlinx.serialization.Serializable

/**
 * RichTextStyleEntity 格式的富文本数据结构
 */
@Serializable
data class RichTextStyleEntity(
    val version: String = "1.0",
    val fontColor: List<StyleRange> = emptyList(),
    val fontSize: List<StyleRange> = emptyList(),
    val align: List<StyleRange> = emptyList(),
    val bold: List<StyleRange> = emptyList(),
    val italic: List<StyleRange> = emptyList(),
    val underline: List<StyleRange> = emptyList(),
    val strikethrough: List<StyleRange> = emptyList(),// 删除线
    val backgroundColor: List<StyleRange> = emptyList(),
    val indent: List<StyleRange> = emptyList(),// 缩进
    val list: List<StyleRange> = emptyList()
)

/**
 * 富文本的文字样式
 */
@Serializable
data class StyleRange(
    val start: Int,
    val end: Int,
    // values 取值有 fontColor, fontSize, align, backgroundColor,
    val value: String = "",//ProtoBuf 不支持 null, kotlinx.serialization.SerializationException: 'null' is not supported for optional properties in ProtoBuf
    val checked:Boolean=false,
    val number: Int = 1,
)

//data class LiStyleRange(  ):StyleRange()