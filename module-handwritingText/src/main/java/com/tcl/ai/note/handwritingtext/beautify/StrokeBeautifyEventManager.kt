package com.tcl.ai.note.handwritingtext.beautify

import com.sunia.penengine.sdk.data.ListCurve
import com.sunia.singlepage.sdk.InkFunc
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * 笔迹美化事件管理器
 * 用于传递笔迹美化的事件
 */
object StrokeBeautifyEventManager {

    private val _strokeBeautifyEvents = MutableSharedFlow<StrokeBeautifyEvent>()
    val strokeBeautifyEvents = _strokeBeautifyEvents.asSharedFlow()

    suspend fun sendStrokeBeautifyEvent(event: StrokeBeautifyEvent) {
        _strokeBeautifyEvents.emit(event)
    }


    suspend fun sendStrokeBeautifyEvent(curveList: ListCurve?, inkFunc: InkFunc) {
        sendStrokeBeautifyEvent(
            StrokeBeautifyEvent(
                type = BeautifyEventType.BEAUTIFY,
                downTime = 0L,
                inkFunc = inkFunc,
                curveList = curveList
            )
        )
    }
}

enum class BeautifyEventType {
    ACTION_DOWN, ACTION_UP, BEAUTIFY
}

data class StrokeBeautifyEvent(
    val type: BeautifyEventType,
    val downTime : Long,
    val inkFunc: InkFunc?,
    val curveList: ListCurve?,
)
