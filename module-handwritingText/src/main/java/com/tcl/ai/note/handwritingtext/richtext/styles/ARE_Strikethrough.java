package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.text.style.StrikethroughSpan;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.EditText;
import android.widget.ImageView;

import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

/**
 * 删除线
 */
public class ARE_Strikethrough extends ARE_ABS_Style<StrikethroughSpan> {

	private ImageView mStrikethroughImageView;

	private boolean mStrikethroughChecked;
	private boolean mStrikethroughValid = false;



	public ARE_Strikethrough() {
		super(StrikethroughSpan.class);
	}

	@Override
	public void setisValid(boolean isValid) {
		mStrikethroughValid = isValid;
	}

	@Override
	public boolean getIsValid() {
		return mStrikethroughValid;
	}

	/**
	 *
	 * @param editText
	 */
	public void setEditText(AREditText editText) {
		this.mEditText = editText;
	}

	@Override
	public EditText getEditText() {
		return this.mEditText;
	}

	@Override
	public void setListenerForImageView(final ImageView imageView) {
		imageView.setOnClickListener(new OnClickListener() {
			@Override
			public void onClick(View v) {
			}
		});
	}

	public void setStrikethrough() {
		if (!mStrikethroughValid)
			return;
		mStrikethroughChecked = !mStrikethroughChecked;
		updateCheckStatus(mStrikethroughChecked);
		if (null != mEditText) {
			int start = mEditText.getSelectionStart();
			int end = mEditText.getSelectionEnd();
			mIsRecordToHistory = start != end;
			applyStyle(mEditText.getEditableText(),
					start,
					end, mIsRecordToHistory);
		}
	}

	@Override
	public void updateCheckStatus(boolean checked) {
		setChecked(checked);
	}
	@Override
	public ImageView getImageView() {
		return this.mStrikethroughImageView;
	}

	@Override
	public void setChecked(boolean isChecked) {
		this.mStrikethroughChecked = isChecked;
	}

	@Override
	public boolean getIsChecked() {
		return this.mStrikethroughChecked;
	}

	@Override
	public StrikethroughSpan newSpan() {
		return new StrikethroughSpan();
	}
}
