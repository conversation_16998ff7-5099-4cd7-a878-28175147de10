package com.tcl.ai.note.handwritingtext.utils

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.ui.categorydialog.DialogCategory
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger


fun isExistCategory(items:List<NoteCategory>, categoryName:String?, colorIndex: Int, isAddCategoryMode: Boolean):Boolean{
    return items.any { item ->
        item.name.equals(categoryName, ignoreCase = true) &&
                (isAddCategoryMode || item.colorIndex == colorIndex) // 区分 new / rename category
    }
}

/**
 * 1、新增分类：只判断是否名字重复
 * 2、重命名：判断列表中重复的和当前正在修改的是否为同一个分类，如果是，则判断名字和颜色是否重复，如果不是同一个分类，则只判断名字是否重复（规避不能只修改颜色的问题）
 * 3、重命名时如果输入的名称与原始名称相同，则不认为是重复
 */
fun isExistCategory(items: List<DialogCategory>, categoryName: String?, categoryId: Long, colorIndex: Int, isAddCategoryMode: Boolean, originalName: String? = null): Boolean {
    // 如果类别名为空，直接返回false
    if (categoryName.isNullOrEmpty()) return false

    Logger.i("CategoryUtils", "categoryName:${categoryName}, categoryId:$categoryId, colorIndex:$colorIndex, originalName:$originalName")

    // 重命名模式下，如果输入的名称与原始名称相同，则不认为是重复
    if (!isAddCategoryMode && !originalName.isNullOrEmpty() && categoryName.equals(originalName, ignoreCase = true)) {
        return false
    }

    // 简化逻辑，使用条件表达式
    return items.any { item ->
        item.name.equals(categoryName, ignoreCase = true) &&
                (isAddCategoryMode || item.categoryId != categoryId || item.colorIndex == colorIndex)
    }
}

/**
 * 存入数据库
 */
fun determineIcon(category: NoteCategory): Int {
    // 这里是确定图标的逻辑，可以根据分类的某些属性或预置逻辑设置图标。
    val colorIndex = category.colorIndex
    Logger.i("CategoryUtils","determineIcon, colorIndex:$colorIndex")
    return when (colorIndex) {
        CategoryColors.NONE_COLOR -> R.drawable.ic_category_uncategorised // 未分类
        CategoryColors.YELLOW_COLOR -> R.drawable.ic_category_yellow
        CategoryColors.ORANGE_COLOR -> R.drawable.ic_category_orange
        CategoryColors.PINK_COLOR -> R.drawable.ic_category_pink
        CategoryColors.PURPLE_COLOR -> R.drawable.ic_category_purple
        CategoryColors.BLUE_COLOR -> R.drawable.ic_category_blue
        CategoryColors.GREEN_COLOR -> R.drawable.ic_category_green
        else -> R.drawable.ic_all_notes
    }
}


// 重新设置显示名称
fun determineName(category: NoteCategory):String{
    return when {
        category.isRename -> category.name
        else -> when (category.categoryId) {
            1L -> GlobalContext.Companion.instance.getString(R.string.database_preset_category_none)
            2L -> GlobalContext.Companion.instance.getString(R.string.database_preset_category_education)
            3L -> GlobalContext.Companion.instance.getString(R.string.database_preset_category_work)
            4L -> GlobalContext.Companion.instance.getString(R.string.database_preset_category_travel)
            5L -> GlobalContext.Companion.instance.getString(R.string.database_preset_shopping_list)
            else -> category.name
        }
    }
}