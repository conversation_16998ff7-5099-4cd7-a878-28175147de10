package com.tcl.ai.note.handwritingtext.sunia.convert

import android.view.MotionEvent
import com.sunia.penengine.sdk.operate.touch.KspMotionEvent
import com.sunia.penengine.sdk.operate.touch.TouchPoint

internal object SuniaMotionEventUtils {
    private const val INVALID_VALUE: Float = -9999.0f
    private const val DEFAULT_PRESSURE: Float = INVALID_VALUE
    private const val DEFAULT_TIL: Float = INVALID_VALUE

    fun obtain(event: MotionEvent?): KspMotionEvent? {
        if (event == null) {
            return null
        }
        val count = event.pointerCount
        val kspMotionEvent = KspMotionEvent()
        kspMotionEvent.action = event.actionMasked
        kspMotionEvent.actionIndex = event.actionIndex
        kspMotionEvent.flags = event.flags
        val validIds: MutableList<Int> = ArrayList(count)
        for (i in 0 until count) {
            validIds.add(event.getPointerId(i))
        }
        kspMotionEvent.touchMotionEvents = getTouchMotion(event, validIds)
        return kspMotionEvent
    }

    private fun getTouchMotion(
        event: MotionEvent,
        validIds: List<Int>?
    ): Array<KspMotionEvent.TouchMotionEvent?>? {
        val count = event.pointerCount
        val validCount = validIds?.size ?: count
        if (validCount == 0) {
            return null
        }
        val touchMotionEvents = arrayOfNulls<KspMotionEvent.TouchMotionEvent>(validCount)
        var index = 0
        for (i in 0 until count) {
            val pointerId = event.getPointerId(i)
            if (validIds!!.contains(pointerId)) {
                val touchMotionEvent = getTouchMotionEvent(event, i)
                touchMotionEvents[index] = touchMotionEvent
                index++
            }
        }
        return touchMotionEvents
    }

    private fun getTouchMotionEvent(event: MotionEvent, index: Int): KspMotionEvent.TouchMotionEvent {
        val touchMotionEvent = KspMotionEvent.TouchMotionEvent()
        touchMotionEvent.toolType = event.getToolType(index)
        touchMotionEvent.pointerId = event.getPointerId(index)
        touchMotionEvent.touchPoints = getTouchPointsByMotionEvent(event, index, 0f)
        return touchMotionEvent
    }

    private fun getTouchPointsByMotionEvent(
        event: MotionEvent,
        index: Int,
        offsetOrientation: Float
    ): Array<TouchPoint?> {
        val touchPoints: Array<TouchPoint?>
        val x = event.getX(index)
        val y = event.getY(index)
        val pressure =
            if (event.getToolType(index) == MotionEvent.TOOL_TYPE_FINGER) DEFAULT_PRESSURE else event.pressure
        val eventTime = event.eventTime
        val orientation = event.orientation + offsetOrientation
        val tilt =
            if (event.getToolType(index) == MotionEvent.TOOL_TYPE_FINGER) DEFAULT_TIL else event.getAxisValue(
                MotionEvent.AXIS_TILT
            )
        val touchPoint = TouchPoint(x, y, pressure, eventTime, orientation, tilt)
        if (event.historySize != 0) {
            touchPoints = arrayOfNulls(event.historySize + 1)
            for (i in 0 until touchPoints.size - 1) {
                val hx = event.getHistoricalX(index, i)
                val hy = event.getHistoricalY(index, i)
                val hp =
                    if (event.getToolType(index) == MotionEvent.TOOL_TYPE_FINGER) DEFAULT_PRESSURE else event.getHistoricalPressure(
                        i
                    )
                val orient = event.getHistoricalOrientation(index, i) + offsetOrientation
                val til =
                    if (event.getToolType(index) == MotionEvent.TOOL_TYPE_FINGER) DEFAULT_TIL else event.getHistoricalAxisValue(
                        MotionEvent.AXIS_TILT,
                        index,
                        i
                    )
                val time = event.getHistoricalEventTime(i)
                val hTouchPoint = TouchPoint(hx, hy, hp, time, orient, til)

                touchPoints[i] = hTouchPoint
            }
            touchPoints[touchPoints.size - 1] = touchPoint
        } else {
            touchPoints = arrayOfNulls(1)
            touchPoints[0] = touchPoint
        }
        return touchPoints
    }
}