package com.tcl.ai.note.handwritingtext.sunia.estimate;

import android.view.MotionEvent;

import com.sunia.penengine.sdk.operate.touch.KspMotionEvent;
import com.sunia.penengine.sdk.operate.touch.TouchPoint;

import java.util.ArrayList;
import java.util.List;

public class MotionEventTransformHandler {
    public static final float INVALID_VALUE = -9999.0f;
    public static final float defaultPressure = INVALID_VALUE;
    public static final float defaultTil = INVALID_VALUE;

    public static KspMotionEvent obtain(MotionEvent event) {
        if (event == null) {
            return null;
        }
        int count = event.getPointerCount();
        KspMotionEvent kspMotionEvent = new KspMotionEvent();
        kspMotionEvent.setAction(event.getActionMasked());
        kspMotionEvent.setActionIndex(event.getActionIndex());
        kspMotionEvent.setFlags(event.getFlags());
        List<Integer> validIds = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            validIds.add(event.getPointerId(i));
        }
        kspMotionEvent.setTouchMotionEvents(getTouchMotion(event, validIds));
        return kspMotionEvent;
    }

    private static KspMotionEvent.TouchMotionEvent[] getTouchMotion(MotionEvent event, List<Integer> validIds) {
        int count = event.getPointerCount();
        int validCount = validIds != null ? validIds.size() : count;
        if (validCount == 0) {
            return null;
        }
        KspMotionEvent.TouchMotionEvent[] touchMotionEvents = new KspMotionEvent.TouchMotionEvent[validCount];
        int index = 0;
        for (int i = 0; i < count; i++) {
            int pointerId = event.getPointerId(i);
            if (validIds.contains(pointerId)) {
                KspMotionEvent.TouchMotionEvent touchMotionEvent = getTouchMotionEvent(event, i);
                touchMotionEvents[index] = touchMotionEvent;
                index++;
            }
        }
        return touchMotionEvents;
    }

    private static KspMotionEvent.TouchMotionEvent getTouchMotionEvent(MotionEvent event, int index) {
        KspMotionEvent.TouchMotionEvent touchMotionEvent = new KspMotionEvent.TouchMotionEvent();
        touchMotionEvent.setToolType(event.getToolType(index));
        touchMotionEvent.setPointerId(event.getPointerId(index));
        touchMotionEvent.setTouchPoints(getTouchPointsByMotionEvent(event, index, 0));
        return touchMotionEvent;
    }

    public static TouchPoint[] getTouchPointsByMotionEvent(MotionEvent event, int index, float offsetOrientation) {
        TouchPoint[] touchPoints;
        float x = event.getX(index);
        float y = event.getY(index);
        float pressure = event.getToolType(index) == MotionEvent.TOOL_TYPE_FINGER ? defaultPressure : event.getPressure();
//        long eventTime = event.getEventTime();
        long eventTime = System.currentTimeMillis();
        float orientation = event.getOrientation() + offsetOrientation;
        float tilt = event.getToolType(index) == MotionEvent.TOOL_TYPE_FINGER ? defaultTil : event.getAxisValue(MotionEvent.AXIS_TILT);
        TouchPoint touchPoint = new TouchPoint(x, y, pressure, eventTime, orientation, tilt);
        if (event.getHistorySize() != 0) {
            touchPoints = new TouchPoint[event.getHistorySize() + 1];
            for (int i = 0; i < touchPoints.length - 1; i++) {
                float hx = event.getHistoricalX(index, i);
                float hy = event.getHistoricalY(index, i);
                float hp = event.getToolType(index) == MotionEvent.TOOL_TYPE_FINGER ? defaultPressure : event.getHistoricalPressure(i);
                float orient = event.getHistoricalOrientation(index, i) + offsetOrientation;
                float til = event.getToolType(index) == MotionEvent.TOOL_TYPE_FINGER ? defaultTil : event.getHistoricalAxisValue(MotionEvent.AXIS_TILT, index, i);
                long time = event.getHistoricalEventTime(i) - event.getEventTime() + eventTime;
                TouchPoint hTouchPoint = new TouchPoint(hx, hy, hp, time, orient, til);

                touchPoints[i] = hTouchPoint;
            }
            touchPoints[touchPoints.length - 1] = touchPoint;
        } else {
            touchPoints = new TouchPoint[1];
            touchPoints[0] = touchPoint;
        }
        return touchPoints;
    }
}
