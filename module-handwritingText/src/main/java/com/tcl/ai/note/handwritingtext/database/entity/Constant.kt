package com.tcl.ai.note.handwritingtext.database.entity

object DBConst {
    const val DB_NAME = "note.db"
    const val TABLE_NAME_NOTES = "notes"
    const val TABLE_NAME_CATEGORIES = "categories"
    const val TABLE_NAME_CONTENTS = "contents"
    const val TABLE_NAME_NOTE_CONTENT = "note_content"
    const val TABLE_NAME_HANDWRITING = "handwriting"
    const val TABLE_NAME_DRAW = "draws"
}

object CategoryName {
    const val ALL_NOTES = "All notes"
    const val TRAVEL = "Travel"
    const val LEARNING = "Learning"
    const val WORK = "Work"
    const val UN_CATEGORISED = "Uncategorised"
}

enum class ContentType {
    TEXT, // 文字
    IMAGE, // 图片
    TODO, // 待办事项
    AUDIO, // 音频
}