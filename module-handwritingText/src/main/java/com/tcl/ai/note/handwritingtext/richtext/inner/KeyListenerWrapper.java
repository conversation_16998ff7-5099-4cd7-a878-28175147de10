package com.tcl.ai.note.handwritingtext.richtext.inner;

import android.text.Editable;
import android.text.method.KeyListener;
import android.view.KeyEvent;
import android.view.View;

public class KeyListenerWrapper implements KeyListener {

    private final KeyListener mBase;

    public KeyListenerWrapper(KeyListener mBase) {
        this.mBase = mBase;
    }

    @Override
    public int getInputType() {
        return mBase.getInputType();
    }

    @Override
    public boolean onKeyDown(View view, Editable text, int keyCode, KeyEvent event) {
        return mBase.onKeyDown(view, text, keyCode, event);
    }

    @Override
    public boolean onKeyUp(View view, Editable text, int keyCode, KeyEvent event) {
        return mBase.onKeyUp(view, text, keyCode, event);
    }

    @Override
    public boolean onKeyOther(View view, Editable text, KeyEvent event) {
        return mBase.onKeyOther(view, text, event);
    }

    @Override
    public void clearMetaKeyState(View view, Editable content, int states) {
        mBase.clearMetaKeyState(view, content, states);
    }
}
