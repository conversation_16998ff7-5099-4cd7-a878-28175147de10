package com.tcl.ai.note.handwritingtext.repo

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.NoteDatabase
import com.tcl.ai.note.handwritingtext.database.entity.NoteCategory
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.handwritingtext.utils.determineIcon
import com.tcl.ai.note.handwritingtext.utils.determineName
import com.tcl.ai.note.utils.CategoryColors
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.withContext

/**
 * 分类数据变化类型
 */
enum class CategoryChangeType {
    CATEGORY_LIST_CHANGED,  // 分类列表发生变化（增删改分类）
    NOTE_COUNT_CHANGED,     // 笔记数量发生变化
    BOTH_CHANGED           // 两者都发生变化
}

/**
 * 分类数据结果
 */
data class CategoryDataResult(
    val categories: List<NoteCategory>,
    val totalNoteCount: Int,
    val changeType: CategoryChangeType
)

/**
 * 首页数据仓库 包含笔记和分类的数据仓库
 * 只引入必要的接口
 */
object HomeRepository {
    private const val TAG = "HomeRepository"
    private val noteDao = NoteDatabase.getInstance(GlobalContext.instance).noteDao()
    private val categoryDao = NoteDatabase.getInstance(GlobalContext.instance).categoryDao()

    // 缓存上一次的数据，用于比较变化类型
    private var lastCategoriesData: List<NoteCategory>? = null
    private var lastNoteCount: Int? = null

    // 获取所有Note数据
    fun getAllNoteWithCategory(): Flow<List<NoteListItem>> {
        Logger.d(TAG, "getAllNoteWithCategory - Starting database query")
        return noteDao.getAllNotesWithCategory()
            .onStart { Logger.d(TAG, "getAllNoteWithCategory -Database query flow started: ${System.currentTimeMillis()}") }
            .onEach { notes ->
                Logger.d(TAG, "getAllNoteWithCategory -Database query completed: ${System.currentTimeMillis()}, found ${notes.size} records")
            }
    }

    /**
     * 获取所有分类名称，同时监听分类和笔记总数量的变化
     * 返回 Pair<List<NoteCategory>, Int>
     */
    fun getAllCategoriesList(): Flow<Pair<List<NoteCategory>, Int>> {
        return combine(
            categoryDao.getAllCategoriesFlow(),
            noteDao.getNoteCountFlow() // 监听笔记表的变化
        ) { categories, allNoteCount ->
            // 当分类表或笔记表发生变化时，都会重新计算
            categories.map { category ->
                // 计算每个分类中的Note数量
                category.noteCounts = calculateNoteCounts(category.categoryId)
                // 确定分类icon
                category.icon = determineIcon(category)
                // 确定显示名称
                category.name = determineName(category)
                category
            } to allNoteCount
        }
    }

    /**
     * 获取所有分类名称，同时监听分类和笔记总数量的变化，并支持判断变化类型
     * 返回 CategoryDataResult，包含变化类型信息
     */
    fun getAllCategoriesListWithChangeType(): Flow<CategoryDataResult> {
        return combine(
            categoryDao.getAllCategoriesFlow(),
            noteDao.getNoteCountFlow() // 监听笔记表的变化
        ) { categories, allNoteCount ->
            Logger.d(TAG, "getAllCategoriesListWithChangeType - categories size: ${categories.size}, noteCount: $allNoteCount")

            // 处理分类数据
            val processedCategories = categories.map { category ->
                // 计算每个分类中的Note数量
                category.noteCounts = calculateNoteCounts(category.categoryId)
                // 确定分类icon
                category.icon = determineIcon(category)
                // 确定显示名称
                category.name = determineName(category)
                category
            }

            // 判断变化类型
            val changeType = determineChangeType(processedCategories, allNoteCount)
            Logger.d(TAG, "getAllCategoriesListWithChangeType - changeType: $changeType")

            // 更新缓存
            lastCategoriesData = processedCategories.map { it.copy() }
            lastNoteCount = allNoteCount

            CategoryDataResult(
                categories = processedCategories,
                totalNoteCount = allNoteCount,
                changeType = changeType
            )
        }
    }
    /**
     * 判断数据变化类型
     */
    private fun determineChangeType(
        currentCategories: List<NoteCategory>,
        currentNoteCount: Int
    ): CategoryChangeType {
        val lastCategories = lastCategoriesData
        val lastCount = lastNoteCount

        // 首次加载
        if (lastCategories == null || lastCount == null) {
            Logger.d(TAG, "determineChangeType - First load, returning BOTH_CHANGED")
            return CategoryChangeType.BOTH_CHANGED
        }

        // 检查分类列表是否发生变化
        val categoryListChanged = isCategoryListChanged(lastCategories, currentCategories)

        // 检查笔记数量是否发生变化
        val noteCountChanged = lastCount != currentNoteCount

        Logger.d(TAG, "determineChangeType - categoryListChanged: $categoryListChanged, noteCountChanged: $noteCountChanged")

        return when {
            categoryListChanged && noteCountChanged -> CategoryChangeType.BOTH_CHANGED
            categoryListChanged -> CategoryChangeType.CATEGORY_LIST_CHANGED
            noteCountChanged -> CategoryChangeType.NOTE_COUNT_CHANGED
            else -> CategoryChangeType.NOTE_COUNT_CHANGED // 默认返回笔记数量变化，因为可能是分类内笔记数量变化
        }
    }

    /**
     * 检查分类列表是否发生变化
     */
    private fun isCategoryListChanged(
        oldCategories: List<NoteCategory>,
        newCategories: List<NoteCategory>
    ): Boolean {
        // 数量不同
        if (oldCategories.size != newCategories.size) {
            Logger.d(TAG, "isCategoryListChanged - Size changed: ${oldCategories.size} -> ${newCategories.size}")
            return true
        }

        // 比较每个分类的关键属性
        for (i in oldCategories.indices) {
            val oldCategory = oldCategories[i]
            val newCategory = newCategories[i]

            if (oldCategory.categoryId != newCategory.categoryId ||
                oldCategory.name != newCategory.name ||
                oldCategory.colorIndex != newCategory.colorIndex ||
                oldCategory.isRename != newCategory.isRename ||
                oldCategory.modifyTime != newCategory.modifyTime) {
                Logger.d(TAG, "isCategoryListChanged - Category changed: ${oldCategory.categoryId}")
                return true
            }
        }

        return false
    }

    // 计算每个分类中的Note数量
    suspend fun calculateNoteCounts(categoryId: Long): Int {
        // 这里是计算 noteCounts 的逻辑，例如从笔记表中统计笔记数量。
        return noteDao.getNotesCountByCategoryId(categoryId)
    }

    /**
     * 新增category
     */
    suspend fun addCategory(category: NoteCategory): Long = withContext(Dispatchers.IO) {
        categoryDao.insert(category)
    }
    /**
     * 删除某个分类下的Note数据
     */
    suspend fun deleteNotesByCategoryId(categoryId: Long):Int = withContext(Dispatchers.IO) {
        Logger.d(TAG, "deleteNotesByCategoryId, categoryId: $categoryId")
        noteDao.deleteNotesByCategoryId(categoryId)
    }

    /**
     * 删除某个分类下的Note数据
     */
    suspend fun updateCategoryId(categoryId: Long,newCategoryId:Long): Int =
        withContext(Dispatchers.IO) {
            Logger.d(
                TAG,
                "updateCategoryId, categoryId: $categoryId, newCategoryId: $newCategoryId"
            )
            noteDao.updateCategoryId(categoryId, newCategoryId)
        }
    /**
     * 删除指定的Notes
     */
    suspend fun deleteNotes(noteIds: List<Long>):Int = withContext(Dispatchers.IO) {
        Logger.d(TAG, "deleteNotes: $noteIds")
        noteDao.deleteNotes(noteIds)
//        noteDao.logicDeleteNotes(noteIds)
    }

    /**
     * 物理删除（清理回收站用）
     */
    suspend fun deleteNotesForce(noteIds: List<Long>):Int = withContext(Dispatchers.IO) {
        Logger.d(TAG, "deleteNotes: $noteIds")
        noteDao.deleteNotes(noteIds)
    }
    /**
     * 删除一个Category
     */
    suspend fun deleteCategory(category: NoteCategory):Int = withContext(Dispatchers.IO) {
        categoryDao.delete(category)
    }
    /**
     * 更Category
     */
    suspend fun updateCategory(category: NoteCategory): Long = withContext(Dispatchers.IO) {
        categoryDao.update(category)
        category.categoryId
    }

    /**
     * 获取一条Category
     */
    suspend fun getCategory(categoryId: Long): NoteCategory? = withContext(Dispatchers.IO) {
        val category = categoryDao.getCategoryById(categoryId)?.apply {
            icon = determineIcon(this)
            name = determineName(this)
        }
        return@withContext category
    }
    /**
     * 更新多条指定Note的 categoryId
     */
    suspend fun updateNotesCategoryId(noteIds: List<Long>, newCategoryId: Long): Int = withContext(Dispatchers.IO){
        Logger.d(TAG, "updateNotesCategoryId, noteIds: $noteIds, newCategoryId: $newCategoryId")
        noteDao.updateNotesCategoryId(noteIds,newCategoryId)
    }
}