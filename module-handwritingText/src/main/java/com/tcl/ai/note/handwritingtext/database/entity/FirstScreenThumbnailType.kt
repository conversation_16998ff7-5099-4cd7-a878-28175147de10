package com.tcl.ai.note.handwritingtext.database.entity

/**
 * 首屏缩略图类型
 * 对应数据库中 note 表中字段 handwritingThumbnail
 */
enum class FirstScreenThumbnailType(val dbValue: String) {
    // 图片
    IMAGE("IMAGE"),
    // 纯手写
    HAND_WRITING("HAND_WRITING"),
    // 图片和手写混合
    IMAGE_HAND_WRITING("IMAGE_HAND_WRITING");

    // 直接返回枚举中存储的dbValue
    fun toDBString(): String = dbValue

    companion object {
        private val valueMap = entries.associateBy { it.dbValue }

        fun fromDBString(str: String?): FirstScreenThumbnailType? =
            str?.trim()?.uppercase()?.let { valueMap[it] }
    }
}