package com.tcl.ai.note.handwritingtext.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.tcl.ai.note.handwritingtext.database.entity.Content
import com.tcl.ai.note.handwritingtext.database.entity.DBConst

@Dao
interface ContentDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(content: Content): Long

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(contents: List<Content>):List<Long>

    @Query("SELECT * FROM ${DBConst.TABLE_NAME_CONTENTS} WHERE noteId = :noteId ORDER BY `order` ASC")
    fun getContentsByNoteId(noteId: Long): List<Content>

    @Delete
    fun delete(content: Content)

    @Update
    suspend fun update(content: Content):Int

    @Query("DELETE FROM ${DBConst.TABLE_NAME_CONTENTS} WHERE noteId = :noteId")
    fun deleteByNoteId(noteId: Long):Int
}