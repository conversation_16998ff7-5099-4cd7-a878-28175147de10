package com.tcl.ai.note.handwritingtext.bean

import android.graphics.Paint
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.StrokeJoin
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.utils.judge
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 具体到笔DrawMode.PEN的细分，不同笔对应的 cap,join 有差异
 * 外部根据 DoodlePen 设置blendMode (马克笔对应BlendMode.Multiply)
 * @param strokeCap
 * @param strokeJoin
 */
@Serializable
sealed class DoodlePen(
    val strokeCap: Paint.Cap,
    val strokeJoin: Paint.Join,
    var color: Long = 0xFF000000
) {



    @Serializable
    @SerialName("FountainPen")
    data object FountainPen : DoodlePen(
        strokeCap = Paint.Cap.ROUND,
        strokeJoin = Paint.Join.ROUND
    ) // 钢笔

    @Serializable
    @SerialName("Markpen")
    data object Markpen : DoodlePen(
        strokeCap = Paint.Cap.SQUARE,
        strokeJoin = Paint.Join.BEVEL
    ) //马克笔

    @Serializable
    @SerialName("Ballpen")
    data object Ballpen : DoodlePen(
        strokeCap = Paint.Cap.ROUND,
        strokeJoin = Paint.Join.ROUND

    ) //圆珠笔

    companion object{

        fun toDoodPen(strName:String): DoodlePen = when(strName){
            "Markpen" ->Markpen
            "Ballpen" -> Ballpen
            else  ->  FountainPen
        }
    }

}

/**
 *  当底部菜单选择画笔时（BrushMenu.PEN）取出对应图片
 */
fun DoodlePen.resId(sel: Boolean): Int = when (this) {
    DoodlePen.FountainPen -> DoodlePen.FountainPen.resId(sel)
    DoodlePen.Ballpen -> DoodlePen.Ballpen.resId(sel)
    DoodlePen.Markpen -> DoodlePen.Markpen.resId(sel)
}
fun DoodlePen.tabletResId(sel: Boolean): Int = when (this) {
    DoodlePen.FountainPen -> DoodlePen.FountainPen.tabletResId(sel)
    DoodlePen.Ballpen -> DoodlePen.Ballpen.tabletResId(sel)
    DoodlePen.Markpen -> DoodlePen.Markpen.tabletResId(sel)
}
fun DoodlePen.toStrName():String = when(this){
    DoodlePen.FountainPen -> "FountainPen"
    DoodlePen.Ballpen -> "Ballpen"
    DoodlePen.Markpen -> "Markpen"
}

fun DoodlePen.progressToDp(progress:Float): Dp = when(this){
    DoodlePen.FountainPen -> (progress*5).dp
    else  -> (progress*10).dp
}

fun DoodlePen.FountainPen.resId(sel: Boolean) = if (sel) {
    R.drawable.ic_pen_sel
} else {
    R.drawable.ic_pen_nor
}

fun DoodlePen.FountainPen.tabletResId(sel: Boolean) = sel.judge(R.drawable.ic_tablet_menu_pen_weak,R.drawable.ic_tablet_menu_pen_nor)


fun DoodlePen.Markpen.tabletResId(sel: Boolean) = sel.judge(R.drawable.ic_tablet_menu_markpen_weak,R.drawable.ic_tablet_menu_markpen_nor)

fun DoodlePen.Ballpen.tabletResId(sel: Boolean) = sel.judge(R.drawable.ic_tablet_menu_ballpen_weak,R.drawable.ic_tablet_menu_ballpen_nor)

fun DoodlePen.Markpen.resId(sel: Boolean) = if (sel) {
    R.drawable.ic_markpen_sel
} else {
    R.drawable.ic_markpen_nor
}

fun DoodlePen.Ballpen.resId(sel: Boolean) = if (sel) {
    R.drawable.ic_ballpen_sel
} else {
    R.drawable.ic_ballpen_nor
}

fun Paint.Cap.toStrokeCap(): StrokeCap {
    return when (this) {
        Paint.Cap.BUTT -> StrokeCap.Butt
        Paint.Cap.ROUND -> StrokeCap.Round
        Paint.Cap.SQUARE -> StrokeCap.Square
        else -> StrokeCap.Round
    }
}

fun Paint.Join.toStrokeJoin(): StrokeJoin {
    return when (this) {
        Paint.Join.BEVEL -> StrokeJoin.Bevel
        Paint.Join.ROUND -> StrokeJoin.Round
        Paint.Join.MITER -> StrokeJoin.Miter
        else -> StrokeJoin.Round
    }
}

