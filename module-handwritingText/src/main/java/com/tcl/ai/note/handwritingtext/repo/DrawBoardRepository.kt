package com.tcl.ai.note.handwritingtext.repo

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.bean.DrawStroke
import com.tcl.ai.note.handwritingtext.database.NoteDatabase
import com.tcl.ai.note.handwritingtext.database.entity.Draw
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object DrawBoardRepository {
    private const val TAG = "DrawBoardRepository"
    private val drawDao = NoteDatabase.getInstance(GlobalContext.instance).drawDao()

    // 监听数据变化
    fun getFlowByNoteId(noteId: Long) = drawDao.getFlowByNoteId(noteId)

    suspend fun insertOrReplaceDrawBlock(draw: Draw) =
        drawDao.insertOrReplace(draw)

    suspend fun updateDrawBlock(draw: Draw) =
        drawDao.update(draw)

    suspend fun getDrawByNoteIdBlock(noteId: Long) =
        drawDao.getByNoteId(noteId)

    suspend fun updateStrokes(draw: Draw) =
        drawDao.updateStrokes(draw.id, draw.strokes, draw.modifyTime)

    suspend fun modifyStrokeBatchAsync(noteId: Long, drawStrokeList: List<DrawStroke>) =
        coroutineScope {
            val drawStrokeMap = drawStrokeList.associateBy { it.id }
            // 通过collectLatest保持获取最新的数据，避免修改数据时错乱
            drawDao.getFlowByNoteId(noteId).filterNotNull().collectLatest { draw ->
                val newStrokeList = draw.strokes.toMutableList()
                newStrokeList.forEachIndexed { index, stroke ->
                    val newDrawStroke = drawStrokeMap[stroke.id]
                    if (newDrawStroke != null) {
                        // 替换对应id的笔画
                        newStrokeList[index] = newDrawStroke
                    }
                }
                drawDao.updateStrokes(draw.id, newStrokeList, System.currentTimeMillis())
                Logger.d(TAG, "modifyStrokeBatchAsync end")
                // 保存后销毁协程
                cancel()
            }
        }

    suspend fun update(draw: Draw) =
        drawDao.update(draw)
}