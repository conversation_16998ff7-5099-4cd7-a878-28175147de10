package com.tcl.ai.note.handwritingtext.richtext.views;

import android.text.Layout;
import android.text.Spannable;
import android.text.method.ArrowKeyMovementMethod;
import android.text.method.MovementMethod;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.widget.EditText;
import android.widget.TextView;

import com.tcl.ai.note.handwritingtext.richtext.spans.AbstractClickSpan;
import com.tcl.ai.note.utils.Logger;

/**
 * author: xuyuan
 * created on:19-7-29 pm 20:11
 * description:
 */
public class EditMovementMethod extends ArrowKeyMovementMethod {

    private static final String TAG = "EditMovementMethod";

    private static final int MOVE_DISTANCE = 5;
    private EditText mEditText;
    private int mDownX;
    private int mDownY;
    private long mKeyDownTime = 0;
    private static EditMovementMethod sInstance;

    private boolean isMoved(MotionEvent event) {
        int x = (int) event.getX();
        int y = (int) event.getY();
        int add = (x - mDownX) * (x - mDownX) + (y - mDownY) * (y - mDownY);
        int distance = (int) Math.sqrt(add);
        return distance > MOVE_DISTANCE;
    }

    public void hideSoftInput(TextView textView) {
        if (textView.getShowSoftInputOnFocus()) {
            textView.setShowSoftInputOnFocus(false);
        }
    }

    public void showSoftInput(TextView textView) {
        if (!textView.getShowSoftInputOnFocus()) {
            textView.setShowSoftInputOnFocus(true);
        }
        textView.setCursorVisible(true);
        textView.requestFocus();
    }

    @Override
    public boolean onTouchEvent(TextView widget, Spannable buffer, MotionEvent event) {
        int action = event.getActionMasked();

//        Logger.d("onTouchEvent action=" + action +" x/y=" + event.getX() + "/" + event.getY());
        if (action == MotionEvent.ACTION_DOWN) {
            mDownX = (int) event.getX();
            mDownY = (int) event.getY();
            mKeyDownTime = System.currentTimeMillis();
//            if(mActivity instanceof ShowActivity){
//                ((ShowActivity)mActivity).hideSoftInput();
//            }
            hideSoftInput(widget);
        }
        if (action == MotionEvent.ACTION_UP ||
                action == MotionEvent.ACTION_DOWN) {
            int x = (int) event.getX();
            int y = (int) event.getY();

            x -= widget.getTotalPaddingLeft();
            y -= widget.getTotalPaddingTop();

            x += widget.getScrollX();
            y += widget.getScrollY();

            Layout layout = widget.getLayout();
            int line = layout.getLineForVertical(y);
            int off = layout.getOffsetForHorizontal(line, x);
            int lineBottom = layout.getLineBottom(line);

            AbstractClickSpan[] link = buffer.getSpans(off, off, AbstractClickSpan.class);

            if (action == MotionEvent.ACTION_UP) {
                if (link.length != 0) {
//                    Logger.d("xxx onTouch line=" + line + " off=" + off + " lineBottom=" + lineBottom);
                    boolean isMoved = isMoved(event);
                    boolean isClickValid = link[0].isClickValid(widget, event, lineBottom);
//                    Logger.d("xxx onTouch isMoved=" + isMoved + " isClickValid=" + isClickValid);
                    if (!isMoved && isClickValid) {
                        long keyUpTime = System.currentTimeMillis();
                        if (!isLongTouch(mKeyDownTime, keyUpTime)) {
                            link[0].onClick(widget);
                        }
                        return true;
                    }
                }
                mKeyDownTime = 0;
//                if(mActivity instanceof ShowActivity){
//                    ((ShowActivity)mActivity).enterEditMode();
//                }
                showSoftInput(widget);
            }
        }

        return super.onTouchEvent(widget, buffer, event);
    }

    private boolean isLongTouch(long downTime, long upTime) {
        if (upTime-downTime > 200){
            return true;
        }
        return false;
    }

    @Override
    public boolean onKeyUp(TextView widget, Spannable text, int keyCode, KeyEvent event) {
        try {
            return super.onKeyUp(widget, text, keyCode, event);
        }catch (IllegalArgumentException ignored) {
            Logger.e(TAG, ignored.getMessage());}
        return false;
    }

    @Override
    public boolean canSelectArbitrarily() {
        return true;
    }

    //hold a single object , use for every AREditText
    public static MovementMethod getInstance() {
        if (sInstance == null) {
            sInstance = new EditMovementMethod();
        }

        return sInstance;
    }
}
