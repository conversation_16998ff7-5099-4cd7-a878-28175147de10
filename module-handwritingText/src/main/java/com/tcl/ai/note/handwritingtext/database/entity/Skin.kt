package com.tcl.ai.note.handwritingtext.database.entity

import com.tcl.ai.note.handwritingtext.R
import com.tcl.ai.note.handwritingtext.richtext.viewholder.RichTextViewHolder.Companion.LINE_HEIGHT_SP
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge

data class Skin (
    var noteId: Long =0,
    var bgMode: BgMode = BgMode.none,
    var color: Long =defColor
){
    companion object{
        const val  defColor:Long = 0xffFFFFFF
        const val oldDefColor:Long = 0xFFF5F6F7
         fun  lineHeight(): Int = isTablet.judge(32,28)


    }
}


enum class BgMode {
    none, row, grid
}

fun BgMode.resId(): Int = when (this) {
    BgMode.none -> R.drawable.ic_anxiliary_line_none
    BgMode.row -> R.drawable.ic_anxiliary_line_line
    else -> R.drawable.ic_anxiliary_line_grid
}
fun BgMode.contentDescription():Int =when(this){
    BgMode.none -> com.tcl.ai.note.base.R.string.edit_bottom_menu_skin_def
    BgMode.grid -> com.tcl.ai.note.base.R.string.edit_bottom_menu_skin_grid
    BgMode.row -> com.tcl.ai.note.base.R.string.edit_bottom_menu_skin_book
}