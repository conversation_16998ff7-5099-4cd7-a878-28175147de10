package com.tcl.ai.note.handwritingtext.sunia.estimate;

import android.content.Context;
import android.os.Build;
import android.util.DisplayMetrics;

import android.view.MotionEvent;
import com.asa.paintview.core.EstimatedGenerator;
import com.sunia.penengine.sdk.operate.touch.EstimateParams;
import com.sunia.penengine.sdk.operate.touch.KspMotionEvent;
import com.sunia.penengine.sdk.operate.touch.TouchPoint;

import java.util.ArrayList;
import java.util.List;

/**
 * Predict Handler
 * 预测处理类
 */
public class PredictHandler {
    private Context context;
    private float deviceDpi;
    private EstimatedGenerator estimatedGenerator;
    private boolean enable;
    private float dpi;
    private int  estimatedTime = 36;

    public PredictHandler(Context context) {
        this.context = context;
    }

    public void enableEstimate(boolean enable, EstimateParams estimateParams) {
        this.dpi = getDeviceDpi();
        setEstimatedParam(enable, estimateParams);
    }

    public boolean isEnableEstimate() {
        return enable;
    }

    public void handlePredictKspMotionEvent(KspMotionEvent kspMotionEvent) {
        if (!isEnableEstimate() || kspMotionEvent == null) {
            return;
        }
        if (kspMotionEvent.getPointerCount() == 1) {
            KspMotionEvent.TouchMotionEvent[] touchMotionEvents = kspMotionEvent.getTouchMotionEvents();
            for (KspMotionEvent.TouchMotionEvent touchMotionEvent : touchMotionEvents) {
                TouchPoint[] touchPoints = touchMotionEvent.getTouchPoints();
                touchMotionEvent.setOffset(touchPoints.length);
                if (kspMotionEvent.getAction() == MotionEvent.ACTION_DOWN) {
                    TouchPoint[] points = touchDown(touchPoints);
                    if (points != null) {
                        touchMotionEvent.setTouchPoints(points);
                    }
                } else if (kspMotionEvent.getAction() == MotionEvent.ACTION_MOVE) {
                    TouchPoint[] points = touchMove(touchPoints);
                    if (points != null) {
                        touchMotionEvent.setTouchPoints(points);
                    }
                }
            }
        }
    }

    private TouchPoint[] touchDown(TouchPoint[] points){
        TouchPoint[] predictPoints = onTouchDownEvent(points);
        if (predictPoints != null && predictPoints.length != 0) {
            TouchPoint[] mergePoints = new TouchPoint[points.length + predictPoints.length];
            System.arraycopy(points,0,mergePoints,0,points.length);
            System.arraycopy(predictPoints,0,mergePoints,points.length,predictPoints.length);
            return mergePoints;
        }
        return null;
    }

    private TouchPoint[] touchMove(TouchPoint[] points){
        TouchPoint[] predictPoints = onTouchMoveEvent(points);
        if (predictPoints != null && predictPoints.length != 0) {
            TouchPoint[] mergePoints = new TouchPoint[points.length + predictPoints.length];
            System.arraycopy(points,0,mergePoints,0,points.length);
            System.arraycopy(predictPoints,0,mergePoints,points.length,predictPoints.length);
            return mergePoints;
        }
        /*else {
            Log.e(PredictHandler.class.getSimpleName(), "predictPoints: false ");
        }*/
        return null;
    }

    private void setEstimatedGenerator() {
        if (estimatedGenerator == null) {
            estimatedGenerator = new EstimatedGenerator();
//            estimatedGenerator.enableLog(true);
//            estimatedGenerator.setEstimatedLog(true,  context.getExternalCacheDir().getAbsolutePath(), 3);
        }
    }

    private float getDeviceDpi() {
        DisplayMetrics metrics = new DisplayMetrics();
        metrics.setTo(context.getResources().getDisplayMetrics());
        deviceDpi = Math.min(metrics.xdpi, metrics.ydpi);
        return deviceDpi;
    }

    private void setEstimatedParam(boolean enable, EstimateParams estimateParams) {
        this.enable = enable;
        estimatedTime = estimateParams.getTime();
        setEstimatedGenerator();
        //TODO TCL设备需要特定的配置
        // CurvatureThreshold=0.5;//(默认0.3)
        // LittleCheckThreshold=16; // (默认20)
        // AngleThreshold=0.35;//(默认0.15)
        if (Build.BRAND.equalsIgnoreCase("TCL") || Build.BRAND.equalsIgnoreCase("LENOVO")) {
            estimatedGenerator.setEstimatedKeyValuesConfig("CurvatureThreshold=0.5;LittleCheckThreshold=16;AngleThreshold=0.35;");
        } else {
            estimatedGenerator.setEstimatedKeyValuesConfig("CurvatureThreshold=0.5;LittleCheckThreshold=16;");
        }
        estimatedGenerator.setEstimatedDpi(dpi, 20);
        estimatedGenerator.setEstimatedHistoryPointCount(estimateParams.getHistoryCount());
        estimatedGenerator.setEstimatedMiniTraceLength(estimateParams.getHistoryMiniTrace());
        estimatedGenerator.setEstimateFactor(1.0);
        estimatedGenerator.setEstimateMaxLength(3.0);
    }

    public void enableLittleEstimate(boolean enable){
        if(estimatedGenerator!=null){
            estimatedGenerator.setLittleEstimate(enable);
        }
    }

    public void enableLog(boolean enable, String path, int level) {
        if (estimatedGenerator != null) {
            estimatedGenerator.enableLog(enable);
            estimatedGenerator.setEstimatedLog(enable, path, level);
        }
    }

    private TouchPoint lastPoint = new TouchPoint(0, 0);
    private TouchPoint[] onTouchDownEvent(TouchPoint[] touchPoints) {
        if (!enable) {
            return null;
        }
        if (touchPoints != null && touchPoints.length != 0) {
            for (int i = 0; i < touchPoints.length; i++) {
                TouchPoint touchPoint = touchPoints[i];
                estimatedGenerator.onTouchDownEvent(touchPoint.x, touchPoint.y, touchPoint.p, touchPoint.tilt, touchPoint.orientation, touchPoint.time);
                lastPoint = touchPoint;
//                Log.e("PredictHandler", " PredictHandler-onTouchDown x:"+touchPoint.x+ " y:"+touchPoint.y);
            }
        }
        return getEstimatedPoints();
    }

    private TouchPoint[] onTouchMoveEvent(TouchPoint[] touchPoints) {
        if (!enable) {
            return null;
        }
        if (touchPoints != null && touchPoints.length != 0) {
            for (int i = 0; i < touchPoints.length; i++) {
                TouchPoint touchPoint = touchPoints[i];
               /* if (checkPoint(touchPoint)) {
                    continue;
                }*/
                boolean isHistorical = i != touchPoints.length - 1;
                estimatedGenerator.onTouchMoveEvent(touchPoint.x, touchPoint.y, touchPoint.p, touchPoint.tilt, touchPoint.orientation, touchPoint.time, isHistorical, 20);
                lastPoint = touchPoint;
//                Log.d("PredictHandler", " PredictHandler-onTouchMove i: "+ i + " x:"+touchPoint.x+ " y:"+touchPoint.y);
            }
        }
        return getEstimatedPoints();
    }

    private boolean checkPoint(TouchPoint touchPoint) {
        if (Math.abs(touchPoint.x - lastPoint.x) < 0.9f && Math.abs(touchPoint.y - lastPoint.y) < 0.9f) {
            return true;
        }
        return false;
    }

    private TouchPoint[] onTouchUpEvent(TouchPoint[] touchPoints) {
        if (!enable) {
            return null;
        }
        if (touchPoints != null && touchPoints.length != 0) {
            for (int i = 0; i < touchPoints.length; i++) {
                TouchPoint touchPoint = touchPoints[i];
                boolean isHistorical = i != touchPoints.length - 1;
                estimatedGenerator.onTouchUpEvent(touchPoint.x, touchPoint.y, touchPoint.p, touchPoint.tilt, touchPoint.time, isHistorical ? 1 : 0);
            }
        }
        return getEstimatedPoints();
    }

    private TouchPoint[] getEstimatedPoints() {
        estimatedGenerator.setEstimatedTime(estimatedTime);
        com.asa.paintview.core.TouchPoint estimatedPoints = estimatedGenerator.getEstimatedPoints();
        ArrayList<com.asa.paintview.core.TouchPoint> touchPoints = new ArrayList<>();
        if (estimatedPoints != null) {
            touchPoints.add(estimatedPoints);
        }
        List<TouchPoint> points = new ArrayList<>();
        for (int i = 0; i < touchPoints.size(); i++) {
            com.asa.paintview.core.TouchPoint touchPoint = touchPoints.get(i);
            if (touchPoint != null) {
                TouchPoint point = new TouchPoint(touchPoint.x, touchPoint.y);
                point.setTime(touchPoint.downTime);
                point.setTilt(touchPoint.tilt);
                point.setP(touchPoint.pressure);
                point.setOrientation(touchPoint.orientation);
                points.add(point);
//                Log.w("PredictHandler", " PredictHandler-estimatedPoint  "+ " x:"+point.x+ " y:"+point.y);
            }
        }
        TouchPoint[] temp = new TouchPoint[points.size()];
        points.toArray(temp);
        return temp;
    }

    public void release() {
        if (estimatedGenerator != null) {
            estimatedGenerator = null;
        }
    }
}
