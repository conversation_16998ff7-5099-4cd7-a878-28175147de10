package com.tcl.ai.note.handwritingtext.vm

import androidx.lifecycle.ViewModel
// import androidx.lifecycle.viewModelScope
// import com.tcl.ai.note.GlobalContext
// import com.tcl.ai.note.handwritingtext.bean.DrawMode
// import com.tcl.ai.note.handwritingtext.bean.DrawPoint
// import com.tcl.ai.note.handwritingtext.bean.DrawStroke
// import com.tcl.ai.note.handwritingtext.repo.DrawBoardRepository
// import com.tcl.ai.note.utils.Logger
// import com.tcl.ai.note.utils.launchIO
// import com.tcl.strokebeaut.Handwriting
// import com.tcl.strokebeaut.HandwritingBeautifier
// import com.tcl.strokebeaut.Mode
// import com.tcl.strokebeaut.Point
// import kotlinx.coroutines.Deferred
// import kotlinx.coroutines.Job
// import kotlinx.coroutines.async
// import kotlinx.coroutines.coroutineScope
// import kotlinx.coroutines.delay
// import kotlinx.coroutines.flow.*
// import kotlinx.coroutines.launch
// import java.util.*
// import kotlin.collections.ArrayList
// import com.tcl.strokebeaut.Stroke as TclStroke

class DrawingBeautifyViewModel : ViewModel() {
    // private val drawBoardRepo = DrawBoardRepository
    //
    // // 笔画美化后的数据流
    // // key: 原始笔画
    // // value：美化后笔画
    // private val _beautifiedStrokesStateFlow = MutableStateFlow(LinkedHashMap<Long, DrawStroke>())
    // val beautifiedStrokesStateFlow: StateFlow<Map<Long, DrawStroke>> = _beautifiedStrokesStateFlow.asStateFlow()
    //
    // init {
    //     initHandwritingBeautifier()
    // }
    //
    // suspend fun pushPendingDrawStrokes(noteId: Long, drawStrokeList: List<DrawStroke>, needCacheDataBlock: suspend () -> Boolean = { true } ) = coroutineScope {
    //     Logger.d(TAG, "updateNoteIdAndLoadStrokes, noteId: $noteId")
    //     val startTime = System.currentTimeMillis()
    //     val pendingStrokeQueue = LinkedList<DrawStroke>()
    //     drawStrokeList.forEach {
    //         if (it.style.drawMode == DrawMode.BEAUTIFICATION && it.points.size > 1 && !beautifiedStrokesStateFlow.value.containsKey(it.id)) {
    //             pendingStrokeQueue.add(it)
    //         }
    //     }
    //     Logger.d(TAG, "updateNoteIdAndLoadStrokes-loadPendingStrokeQueue size: ${pendingStrokeQueue.size}, time: ${System.currentTimeMillis() - startTime}")
    //
    //     if (pendingStrokeQueue.isNotEmpty()) {
    //         Logger.d(TAG, "updateNoteIdAndLoadStrokes-generateBeautifiedStrokes ready! time: $pendingStrokeQueue")
    //         // 获取美化字迹
    //         val beautifiedStrokes = runBeautifiedStrokes(noteId, LinkedList(pendingStrokeQueue))
    //         Logger.d(TAG, "updateNoteIdAndLoadStrokes-generateBeautifiedStrokes success! time: ${System.currentTimeMillis() - startTime}")
    //         val beautifiedStrokeMap = LinkedHashMap(_beautifiedStrokesStateFlow.value)
    //         beautifiedStrokes.forEach { beautifiedStrokeMap[it.id] = it }
    //
    //         // 这里会根据needCacheData函数耗时
    //         if (needCacheDataBlock.invoke() == true) {
    //             // 插入数据库
    //             launch {
    //                 drawBoardRepo.modifyStrokeBatchAsync(noteId, beautifiedStrokes)
    //             }
    //             // 缓存到内存
    //             _beautifiedStrokesStateFlow.value = beautifiedStrokeMap
    //             Logger.d(TAG, "updateNoteIdAndLoadStrokes-beautifiedStrokeMap saveCache: ${beautifiedStrokeMap.keys}")
    //         }
    //         return@coroutineScope beautifiedStrokeMap
    //     }
    //     return@coroutineScope beautifiedStrokesStateFlow.value
    // }
    //
    // private suspend fun runBeautifiedStrokes(noteId: Long, pendingStrokeQueue: LinkedList<DrawStroke>): List<DrawStroke> {
    //     if (pendingStrokeQueue.isEmpty()) {
    //         return emptyList()
    //     }
    //     // 等待字迹美化库初始化完成, 非空就是初始化完成
    //     val beautifier = handwritingBeautifierStateFlow.filterNotNull().first()
    //
    //     // 拉出队列的数组，先保存size。
    //     // 因为可能在数据出队，同时有新的数据进队列改变了size
    //     val queueSize = pendingStrokeQueue.size
    //     val readyStrokes = ArrayList<DrawStroke>(queueSize)
    //     repeat(queueSize) {
    //         pendingStrokeQueue.poll()?.let { readyStrokes.add(it) }
    //     }
    //     // 获取美化后的字迹
    //     val beautifiedStrokes = generateBeautifiedStrokes(beautifier, readyStrokes)
    //     Logger.d(TAG, "pendingStrokeCounter, beautifiedStrokes: $beautifiedStrokes")
    //     return beautifiedStrokes
    // }
    //
    // /**
    //  * 实际发起字迹美化请求
    //  */
    // private fun generateBeautifiedStrokes(beautifier: HandwritingBeautifier, rawStrokes: ArrayList<DrawStroke>): List<DrawStroke> {
    //     try {
    //         val tclStrokes = rawStrokes.toTclStrokes()
    //         val handwriting = Handwriting(tclStrokes)
    //         Logger.d(TAG, "generateBeautifiedStrokes, readyStrokes: ${handwriting.strokes().map { it.points() }}")
    //         // 获取字迹美化的结果
    //         val strokes = beautifier.beautify(handwriting).beautified.strokes()
    //         Logger.d(TAG, "generateBeautifiedStrokes, beautifiedStrokes: ${strokes.map { it.points() }}")
    //
    //         // 转化成DrawStrokes
    //         val beautifiedStrokes = ArrayList<DrawStroke>(rawStrokes.size)
    //         rawStrokes.forEachIndexed { index, drawStroke ->
    //             val tclPoints = strokes[index].points()
    //             val newDrawPoints = ArrayList<DrawPoint>(tclPoints.size)
    //             tclPoints.forEach {
    //                 newDrawPoints.add(DrawPoint(it.x.toFloat(), it.y.toFloat()))
    //             }
    //             // 已经美化了，还原成常规笔画
    //             beautifiedStrokes.add(drawStroke.copy(points = newDrawPoints, style = drawStroke.style.copy(drawMode = DrawMode.PEN)))
    //         }
    //         return beautifiedStrokes
    //     } catch (e: Exception) {
    //         Logger.w(TAG, "generateBeautifiedStrokes, error: ${e.stackTraceToString()}")
    //     }
    //     return listOf()
    // }
    //
    // private fun ArrayList<DrawStroke>.toTclStrokes(): ArrayList<TclStroke> {
    //     Logger.d(TAG, "toTclStrokes, size: $size")
    //     val tclStrokes = ArrayList<TclStroke>(size)
    //     // 转换
    //     forEachIndexed { index, drawStroke ->
    //         val tclPoints = ArrayList<Point>(drawStroke.points.size)
    //         drawStroke.points.forEach {
    //             tclPoints.add(Point(it.x.toDouble(), it.y.toDouble()))
    //         }
    //         // 添加到数组
    //         tclStrokes.add(TclStroke(tclPoints))
    //     }
    //     return tclStrokes
    // }
    //
    // companion object {
    //     private const val TAG = "DrawingBeautifyViewModel"
    //
    //     private val handwritingBeautifierStateFlow = MutableStateFlow<HandwritingBeautifier?>(null)
    //     fun initHandwritingBeautifier() {
    //         if (handwritingBeautifierStateFlow.value != null) {
    //             return
    //         }
    //         GlobalContext.applicationScope.launchIO {
    //             // 初始化失败，无限重试
    //             while (handwritingBeautifierStateFlow.value == null) {
    //                 val startMillis = System.currentTimeMillis()
    //                 runCatching {
    //                     handwritingBeautifierStateFlow.value = HandwritingBeautifier.Builder(GlobalContext.instance)
    //                         .enableAutoAlign()
    //                         .setMode(Mode.LOCAL)
    //                         .setMorphSteps(10)
    //                         .build()
    //                     Logger.i(TAG, "init, success. time: ${System.currentTimeMillis() - startMillis}")
    //                 }.onFailure {
    //                     // 初始化失败，强制重载配置文件
    //                     HandwritingBeautifier.Builder(GlobalContext.instance).build().reload(true)
    //                     Logger.w(TAG, "init, error: ${it.message}")
    //                 }
    //                 // 延时5秒，避免频繁初始化
    //                 delay(5000)
    //             }
    //         }
    //     }
    // }
}