package com.tcl.ai.note.handwritingtext.track

import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.handwritingtext.bean.StrokeStyle
import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.state.RichTextState
import com.tcl.ai.note.handwritingtext.vm.draw.SuniaDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.text.RichTextViewModel2
import com.tcl.ai.note.handwritingtext.vm.TextAndDrawViewModel
import com.tcl.ai.note.handwritingtext.vm.pen.PenToolbarViewModel
import com.tcl.ai.note.handwritingtext.state.EditMode
import com.tcl.ai.note.handwritingtext.bean.MenuBar
import com.tcl.ai.note.handwritingtext.bean.BrushMenu
import com.tcl.ai.note.handwritingtext.bean.DoodlePen
import com.tcl.ai.note.handwritingtext.bean.PenStyle
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import com.tcl.ai.note.track.AbsAnalyticsSubModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.combine
import com.sunia.penengine.sdk.operate.touch.PenType
import androidx.compose.ui.text.input.TextFieldValue

/**
 * 分析手写界面使用情况
 *
 * 用于上报埋点
 */
object AnalyticsHandWritingTextModel: AbsAnalyticsSubModel() {
    private const val TAG = "AnalysisEditScreen"

    // 富文本数据状态
    private var _richTextStateFlow: MutableStateFlow<RichTextState?> = MutableStateFlow(null)
    val richTextStateFlow = _richTextStateFlow.asStateFlow()

    // 笔类型
    private var _strokeStyleStateFlow: MutableStateFlow<StrokeStyle?> = MutableStateFlow(null)
    val strokeStyleStateFlow = _strokeStyleStateFlow.asStateFlow()

    // 二期版本原始编辑模式状态（用于编辑时长统计）
    private var _editModeStateFlow: MutableStateFlow<EditMode?> = MutableStateFlow(null)
    val editModeStateFlow = _editModeStateFlow.asStateFlow()

    // 二期版本笔刷选择状态（用于笔刷使用时长统计）
    private var _penSelectionStateFlow: MutableStateFlow<PenStyle?> = MutableStateFlow(null)
    val penSelectionStateFlow = _penSelectionStateFlow.asStateFlow()

    // 获取皮肤id
    fun getSkinName(): BgMode {
        // 优先从二期版本的RichTextState中获取皮肤信息
        val richTextState = _richTextStateFlow.value
        if (richTextState != null) {
            return richTextState.bgMode
        }
        // 兼容一期版本，从SkinViewModel获取
        return BgMode.none
    }

    /**
     * 获取RichTextViewModel2的内容，用于二期版本
     */
    internal fun loadRichTextViewModel2(
        richTextViewModel: RichTextViewModel2,
        textAndDrawViewModel: TextAndDrawViewModel
    ) {
        // 监听ViewModel的状态变化，结合编辑模式
        combine(
            richTextViewModel.uiState,
            textAndDrawViewModel.editModeState
        ) { uiState, editMode ->
            //Logger.d(TAG, "uiState变化: noteId=${uiState.noteId}, title='${uiState.title}', content.length=${uiState.content.length}, images=${uiState.images.size}, audios=${uiState.audios.size}, bgMode=${uiState.bgMode}, bgColor=${uiState.bgColor}, editMode=$editMode")
            
            val contents = mutableListOf<EditorContent>().apply {
                addAll(uiState.images)
                addAll(uiState.audios)
                // 如果有富文本内容，添加文本块
                if (uiState.content.isNotEmpty()) {
                    add(EditorContent.TextBlock(
                        text = TextFieldValue(uiState.content),
                        paragraphStyle = ParagraphStyle.NONE
                    ))
                    //Logger.d(TAG, "添加文本块：content.length=${uiState.content.length}")
                } else {
                    //Logger.d(TAG, "富文本内容为空，不添加文本块")
                }
                //Logger.d(TAG, "内容列表构建完成：images=${uiState.images.size}, audios=${uiState.audios.size}, 总内容块=${size}")
            }

            // 将新的UI状态转换为旧的RichTextState格式，保持兼容性
            val richTextState = RichTextState(
                note = Note(
                    noteId = uiState.noteId ?: 0L,
                    title = uiState.title,
                    categoryId = uiState.categoryId,
                    createTime = System.currentTimeMillis(),
                    modifyTime = System.currentTimeMillis()
                ),
                title = uiState.title,
                contents = contents,
                editMode = editMode == EditMode.TEXT,
                bottomMenuType = when (editMode) {
                    EditMode.DRAW -> MenuBar.BRUSH
                    EditMode.TEXT -> MenuBar.KEYBOARD
                    else -> MenuBar.KEYBOARD
                },
                brushMenuType = if (editMode == EditMode.DRAW) BrushMenu.PEN else "",
                bgMode = uiState.bgMode,
                bgColor = uiState.bgColor
            )
            _richTextStateFlow.value = richTextState
            _editModeStateFlow.value = editMode
            //Logger.d(TAG, "loadRichTextViewModel2 - richTextState updated")
        }.collectWithScope(richTextViewModel.viewModelScope) { }
            .invokeOnCompletion {
                _richTextStateFlow.value = null
                _editModeStateFlow.value = null
                //Logger.d(TAG, "loadRichTextViewModel2--invokeOnCompletion: $it")
            }
    }

    /**
     * 转移至【菜单栏】了，查看 loadPenToolbarViewModel
     * 获取SuniaDrawViewModel的笔画切换，用于二期版本
     */
    internal fun loadSuniaDrawViewModel(suniaDrawViewModel: SuniaDrawViewModel) {
        // 监听笔属性状态变化
        suniaDrawViewModel.penPropState.collectWithScope(suniaDrawViewModel.viewModelScope) { penProp ->
            // 将Sunia的PenType转换为StrokeStyle
            val strokeStyle = when (penProp.penType) {
                PenType.PEN_FOUNTAIN.value -> StrokeStyle(
                    width = penProp.penSize,
                    color = penProp.penColor.toLong(),
                    doodlePen = DoodlePen.FountainPen
                )
                PenType.PEN_BALLPOINT.value -> StrokeStyle(
                    width = penProp.penSize,
                    color = penProp.penColor.toLong(),
                    doodlePen = DoodlePen.Ballpen
                )
                PenType.PEN_MARK.value -> StrokeStyle(
                    width = penProp.penSize,
                    color = penProp.penColor.toLong(),
                    doodlePen = DoodlePen.Markpen
                )
                else -> null
            }
            strokeStyle?.let {
                _strokeStyleStateFlow.value = it
                //Logger.d(TAG, "loadSuniaDrawViewModel - strokeStyle: $it")
            }
        }.invokeOnCompletion {
            _strokeStyleStateFlow.value = null
            //Logger.d(TAG, "loadSuniaDrawViewModel--invokeOnCompletion: $it")
        }
    }

    /**
     * 获取PenToolbarViewModel的笔刷选择，用于二期版本笔刷使用时长统计
     */
    internal fun loadPenToolbarViewModel(penToolbarViewModel: PenToolbarViewModel) {
        // 监听笔刷选择状态变化
        penToolbarViewModel.penSelectedIndexFlow
            .collectWithScope(penToolbarViewModel.viewModelScope) { selectedIndex ->
                val selectedPen = penToolbarViewModel.penStyles.getOrNull(selectedIndex)
                _penSelectionStateFlow.value = selectedPen
                //Logger.d(TAG, "loadPenToolbarViewModel - penSelection: index=$selectedIndex, pen=$selectedPen")
            }.invokeOnCompletion {
                _penSelectionStateFlow.value = null
                //Logger.d(TAG, "loadPenToolbarViewModel--invokeOnCompletion: $it")
            }
    }
}