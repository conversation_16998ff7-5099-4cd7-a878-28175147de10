package com.tcl.ai.note.handwritingtext.richtext.styles;

import android.graphics.drawable.GradientDrawable;
import android.text.Editable;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.content.Context;

import com.tcl.ai.note.handwritingtext.richtext.inner.Util;
import com.tcl.ai.note.handwritingtext.richtext.spans.AreForegroundColorSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.windows.FontColorChangeListener;
import com.tcl.ai.note.handwritingtext.richtext.utils.RichTextKTUtilsKt;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

public class ARE_FontColor extends ARE_ABS_Dynamic_Style<AreForegroundColorSpan> implements FontColorChangeListener {

	// 字体颜色按钮视图
	private ImageView mFontColorImageView;
	// 字体颜色样式的有效性标志
	private boolean mFontColorValid = false;
	// 字体颜色按钮选中状态
	private boolean mFontColorChecked;
	// 当前选中颜色
	private int mColor = -1;

	// 字体颜色按钮选中状态
	// 上下文
	private Context mContext;
	// 色盘弹窗
//	private ColorPickerWindow colorPickerWindow;


	public ARE_FontColor() {
		super(AreForegroundColorSpan.class);
	}
	/**
	 * 构造方法
	 * @param fontColorImage 用作显示及操作字体颜色的ImageView
	 */
	public ARE_FontColor(ImageView fontColorImage) {
		super(AreForegroundColorSpan.class);
		this.mContext = fontColorImage.getContext();
		this.mFontColorImageView = fontColorImage;
		setListenerForImageView(this.mFontColorImageView);
	}

	/**
	 * 设置关联的编辑框
	 * @param editText 富文本编辑框
	 */
	public void setEditText(AREditText editText) { this.mEditText = editText; }

	/**
	 * 为字体颜色按钮设置点击监听
	 * 单击后弹出色盘
	 * @param imageView 字体颜色ImageView按钮
	 */
	@Override
	public void setListenerForImageView(final ImageView imageView) {
		imageView.setOnClickListener(new View.OnClickListener() {
			@Override
			public void onClick(View v) {
//				showColorPicker();
			}
		});
	}



	/**
	 * 设置字体颜色
	 * @param color
	 */
	public void setFontColor(int color){
		mFontColorChecked = true;
		mColor = color;
		if (null != mEditText) {
			Editable editable = mEditText.getEditableText();
			int start = mEditText.getSelectionStart();
			int end = mEditText.getSelectionEnd();

			// 选区非空才记录
			boolean record = end > start;

			if (end >= start) {
				applyNewStyle(editable, start, end, mColor);

				// 记录到撤销/重做栈
				if (record) {
					RichTextKTUtilsKt.recordApplyStyleToUndoRedo(start, end, true, mEditText, AreForegroundColorSpan.class);
				}
			}
		}
	}




	/**
	 * 显示颜色选择器弹出窗口
	 */
//	private void showColorPicker() {
//		if (colorPickerWindow == null) {
//			// 创建带回调的色盘窗口
//			colorPickerWindow = new ColorPickerWindow(mContext, new ColorPickerListener() {
//				@Override
//				public void onPickColor(int color) {
//					mColor = color;
//					setChecked(true); // 更新按钮状态
//					updateButtonBackground(color);
//					// 选区非空时才应用字体颜色
//					if (null != mEditText) {
//						Editable editable = mEditText.getEditableText();
//						int start = mEditText.getSelectionStart();
//						int end = mEditText.getSelectionEnd();
//
//						if (end > start) {
//							applyNewStyle(editable, start, end, mColor);
//						}
//					}
//					// 关闭弹窗
//					if (colorPickerWindow != null && colorPickerWindow.isShowing()) {
//						colorPickerWindow.dismiss();
//					}
//				}
//			});
//		}
//		// 可设置色板当前色（可选）
//		colorPickerWindow.setColor(mColor);
//		colorPickerWindow.showAsDropDown(mFontColorImageView);
//	}


	/**
	 * 工具方法：更新字体颜色按钮的背景色为指定颜色（显示当前所选颜色）
	 * @param color 要更新的背景色
	 */
	private void updateButtonBackground(int color) {
		GradientDrawable drawable = new GradientDrawable();
		drawable.setColor(color);
		drawable.setShape(GradientDrawable.OVAL);
		drawable.setSize(80, 80); // 按钮背景圆形区域
		mFontColorImageView.setBackground(drawable);
	}

	/**
	 * 刷新并更新颜色（在onSelectionChanged时调用）
	 * @param editText 关联的编辑框
	 * @param position 当前光标位置
	 * @param defaultColor 没有span时用的颜色，比如黑色
	 */
	public void updateColorAtCursor(AREditText editText, int position, int defaultColor) {
		if (editText == null) return;
		Editable editable = editText.getEditableText();
		// 使用position到position+1的范围来查找span，确保能找到覆盖该位置的span
		AreForegroundColorSpan[] spans = editable.getSpans(position, position + 1, AreForegroundColorSpan.class);
		int newColor;
		if (spans.length > 0) {
			// 检查span是否真正覆盖当前位置
			for (AreForegroundColorSpan span : spans) {
				int spanStart = editable.getSpanStart(span);
				int spanEnd = editable.getSpanEnd(span);
				if (position >= spanStart && position < spanEnd) {
					newColor = span.getForegroundColor();
					setChecked(true);
					this.mColor = newColor;
					updateButtonBackground(newColor);
					return;
				}
			}
		}
		// 没有找到颜色span，使用默认颜色
		newColor = defaultColor;
		setChecked(false);
		this.mColor = newColor;
		updateButtonBackground(newColor);
	}

	/**
	 * 如果span区块内的样式颜色不一致，则用新选颜色替换，并打印log
	 */
	@Override
	protected void changeSpanInsideStyle(Editable editable, int start, int end, AreForegroundColorSpan existingSpan) {
		int currentColor = existingSpan.getForegroundColor();
		if (currentColor != mColor) {
			applyNewStyle(editable, start, end, mColor);
			logAllFontColorSpans(editable);
		}
	}

	/**
	 * 打印所有现有字体颜色span区间（开发调试用）
	 * @param editable 要统计的文本内容
	 */
	private void logAllFontColorSpans(Editable editable) {
		ForegroundColorSpan[] listItemSpans = editable.getSpans(0,
				editable.length(), ForegroundColorSpan.class);
		for (ForegroundColorSpan span : listItemSpans) {
			int ss = editable.getSpanStart(span);
			int se = editable.getSpanEnd(span);
			Util.log("List All: " + " :: start == " + ss + ", end == " + se);
		}
	}

	/**
	 * 工厂: 创建一个指定颜色的span对象
	 * @return 新建的AreForegroundColorSpan
	 */
	@Override
	public AreForegroundColorSpan newSpan() { return new AreForegroundColorSpan(this.mColor); }

	/**
	 * 获取字体颜色按钮ImageView
	 * @return 当前ImageView
	 */
	@Override
	public ImageView getImageView() { return this.mFontColorImageView; }

	/**
	 * 设置字体颜色按钮是否处于选中态
	 */
	@Override
	public void setChecked(boolean isChecked) { this.mFontColorChecked = isChecked; }

	/**
	 * 获取字体颜色按钮当前选中态
	 */
	@Override
	public boolean getIsChecked() { return this.mFontColorChecked;  }

	/**
	 * 设置功能样式的有效性
	 */
	@Override
	public void setisValid(boolean isValid) { mFontColorValid = isValid; }


	@Override
	public boolean getIsValid() { return mFontColorValid; }


	@Override
	public EditText getEditText() { return this.mEditText; }


	@Override
	public void updateCheckStatus(boolean checked) {
		this.mFontColorChecked = checked;
	}

	@Override
	protected AreForegroundColorSpan newSpan(int color) { return new AreForegroundColorSpan(color); }

	/**
	 * 钩子：当功能切换时刷新。同步按钮色块和内部颜色
	 * @param lastSpanColor 上一个span颜色
	 */
	@Override
	protected void featureChangedHook(int lastSpanColor) {
		mColor = lastSpanColor;
		updateButtonBackground(mColor);
	}

	@Override
	public void onFontColorChanged(int color) {
		setFontColor(color);
	}
}
