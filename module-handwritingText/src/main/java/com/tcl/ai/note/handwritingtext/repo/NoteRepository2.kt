package com.tcl.ai.note.handwritingtext.repo

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.database.NoteDatabase
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.repo.HandWritingThumbnailRepo.getBitmapLastModifyMillis
import com.tcl.ai.note.utils.Logger

object NoteRepository2 {
    private const val TAG = "NoteRepository2"
    private val noteDao = NoteDatabase.getInstance(GlobalContext.instance).noteDao()

    suspend fun get(noteId: Long?): Note? = noteId?.let { id ->
        noteDao.getNote(id)
    }

    suspend fun insert(note: Note) =
        noteDao.insert(note)

    suspend fun update(note: Note) =
        noteDao.update(note)

    suspend fun deleteOneNote(noteId: Long): Int {
        return noteDao.deleteOneNote(noteId)
    }

    suspend fun logicDelete(noteId: Long): Int {
        return noteDao.logicDeleteNote(noteId)
    }

    suspend fun hideNote(noteId: Long): Int {
        return noteDao.hideNote(noteId)
    }

    suspend fun updateThumbnail(
        noteId: Long,
        bitmapPath: String,
        modifyTime: Long = System.currentTimeMillis()
    ) {
        Logger.v(TAG, "updateThumbnail, noteId: $noteId, bitmapPath: $bitmapPath modifyTime: $modifyTime")
        noteDao.updateThumbnail(noteId, bitmapPath, modifyTime)
    }
}