package com.tcl.ai.note.handwritingtext.richtext.utils;

public class Invariants {

    // audio record shortest time(ms)
    public static final int SHORTEST_AUDIO_RECORD_TIME = 2000;

    public static final String ALARM_NOTIFY_ACTION = "com.tct.smart.notes.alarmnotice";

    public static final String APP_PACKAGE_NAME = "com.tct.smart.notes";

    public static final String NEW_NOTE_ACTION = "com.tct.note.intent.action.createNewNote";

    public static final int MAX_TEXT_SIZE_FOR_SHOW = 100;

    public static final String IS_LINEAR_PERMISSTION = "isLinear";
    public static final String IS_NOTE_LINEAR_PERMISSTION = "isNoteLinear";
    public static final String MAIN_CATEGORY_ID = "cid";
    public static final String IS_FIRST_START = "isFirstStart";
    public static final String DEFAULT_CNAME_LANG_FLAG = "langCNaChangeFlag";

}
