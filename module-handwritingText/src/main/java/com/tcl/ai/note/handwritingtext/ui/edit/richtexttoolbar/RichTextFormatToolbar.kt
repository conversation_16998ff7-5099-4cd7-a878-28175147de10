package com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar

import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.hilt.navigation.compose.hiltViewModel
import com.tcl.ai.note.handwritingtext.ui.edit.richtexttoolbar.renderer.RichTextToolBarRenderer
import com.tcl.ai.note.handwritingtext.vm.text.RichTextToolBarViewModel
import com.tcl.ai.note.widget.HorizontalLine
import com.tcl.ai.note.widget.VerticalLine

/**
 * 富文本工具栏（暂只适配平板）
 * 可通过RichTextToolBarConfig配置工具栏内容
 * 
 * 工具栏支持水平滚动，在手机上可滚动查看所有工具项
 * 滚动功能在RichTextToolBarRenderer中实现
 */
@SuppressLint("DesignSystem")
@Composable
fun RichTextFormatToolbar(
    setPopupComposable: ((@Composable (areaHeight:Int) -> Unit)?) -> Unit = {},
    modifier: Modifier = Modifier.fillMaxWidth(),
    viewModel: RichTextToolBarViewModel = hiltViewModel()
) {
    val toolBarState by viewModel.toolBarState.collectAsState()
    val toolBarConfig by viewModel.toolBarConfig.collectAsState()
    toolBarState.setPopupComposable =setPopupComposable
    Box {
        RichTextToolBarRenderer(
            config = toolBarConfig,
            state = toolBarState,
            modifier = modifier
        )
        HorizontalLine(modifier = Modifier.align(Alignment.BottomCenter))
    }
}
