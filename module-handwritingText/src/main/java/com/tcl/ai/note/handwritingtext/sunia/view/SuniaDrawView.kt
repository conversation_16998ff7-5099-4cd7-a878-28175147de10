package com.tcl.ai.note.handwritingtext.sunia.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.Bitmap.Config
import android.graphics.PointF
import android.graphics.Color
import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.text.StaticLayout
import android.text.TextPaint
import android.util.Log
import android.view.MotionEvent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.util.fastForEach
import com.sunia.penengine.sdk.data.CurveDataArray
import com.sunia.penengine.sdk.data.ICurve
import com.sunia.penengine.sdk.data.ListCurve
import com.sunia.penengine.sdk.data.ListData
import com.sunia.penengine.sdk.data.RecoDatasInfoArray
import com.sunia.penengine.sdk.data.SimpleTextData
import com.sunia.penengine.sdk.operate.canvas.DarkModeMenu
import com.sunia.penengine.sdk.operate.canvas.MixedMode
import com.sunia.penengine.sdk.operate.canvas.ScaleInfo
import com.sunia.penengine.sdk.operate.edit.SelectRectF
import com.sunia.penengine.sdk.operate.edit.DataType
import com.sunia.penengine.sdk.operate.edit.SelectLimit
import com.sunia.penengine.sdk.operate.ruler.RulerType
import com.sunia.penengine.sdk.operate.touch.DeleteProp
import com.sunia.penengine.sdk.operate.touch.EstimateParams
import com.sunia.penengine.sdk.operate.touch.IShapeRecognizeListener
import com.sunia.penengine.sdk.operate.touch.InteractiveMode
import com.sunia.penengine.sdk.operate.touch.KspMotionEvent
import com.sunia.penengine.sdk.operate.touch.PenProp
import com.sunia.penengine.sdk.operate.touch.PenType
import com.sunia.penengine.sdk.operate.touch.ShapeRecognizeParams
import com.sunia.penengine.sdk.operate.touch.TouchEffectType
import com.sunia.singlepage.sdk.InkFunc
import com.sunia.singlepage.sdk.listener.IDataListener
import com.sunia.singlepage.sdk.listener.IInkWriteListener
import com.sunia.singlepage.sdk.param.BitmapParams
import com.sunia.singlepage.sdk.param.LayoutMode
import com.sunia.singlepage.sdk.param.PointType
import com.sunia.viewlib.view.EditDrawView
import com.sunia.viewlib.model.SelectViewModel.ICON_ROTATE_OFFSET_RIGHT
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.bean.TemplateText
import com.tcl.ai.note.handwritingtext.beautify.BeautifyEventType
import com.tcl.ai.note.handwritingtext.beautify.StrokeBeautifyEvent
import com.tcl.ai.note.handwritingtext.beautify.StrokeBeautifyEventManager
import com.tcl.ai.note.handwritingtext.repo.HandWritingThumbnailRepo
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils
import com.tcl.ai.note.handwritingtext.state.StrokeState
import com.tcl.ai.note.handwritingtext.sunia.convert.SuniaMotionEventUtils
import com.tcl.ai.note.handwritingtext.sunia.estimate.PredictHandler
import com.tcl.ai.note.handwritingtext.ui.utils.scale.MatrixInfo
import com.tcl.ai.note.handwritingtext.utils.ShapeRecognizeUtil
import com.tcl.ai.note.handwritingtext.vm.draw.LassoRotateEvent
import com.tcl.ai.note.sunia.authorize.SuniaVerifyHandler
import com.tcl.ai.note.handwritingtext.utils.FileUtils
import com.tcl.ai.note.handwritingtext.utils.FileUtils.getJournalContentEntPath
import com.tcl.ai.note.handwritingtext.utils.TextTools
import com.tcl.ai.note.handwritingtext.utils.isEmptyExceptDeleteOrNull
import com.tcl.ai.note.sunia.view.AbsSuniaDrawInkView
import com.tcl.ai.note.template.bean.ElementType
import com.tcl.ai.note.template.bean.JournalContentInfo
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.delegate
import com.tcl.ai.note.utils.dp2px
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.launchIO
import com.tcl.ai.note.utils.runIO
import com.tcl.ai.note.utils.screenSizeMax
import com.tcl.ai.note.utils.screenSizeMin
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.cancel
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import java.util.concurrent.Executors
import kotlin.coroutines.resume
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.cos
import java.io.File
import kotlin.math.max
import kotlin.math.sin
import com.sunia.penengine.sdk.multipage.IMultiPage
import com.sunia.penengine.sdk.multipage.ISinglePage
import com.sunia.penengine.sdk.multipage.MultiPageEnt
import com.sunia.penengine.sdk.operate.edit.AddDataSelectParam
import com.sunia.penengine.sdk.operate.edit.AddDataType
import com.sunia.singlepage.sdk.InkSDK
import com.tcl.ai.note.handwritingtext.gesture.GestureRecognizeManager
import com.tcl.ai.note.handwritingtext.ui.image.InsertImage
import com.tcl.ai.note.sunia.EngineManager
import com.tcl.ai.note.utils.FeatureUtils
import com.tcl.ai.note.utils.isLiteVersion
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.flow.take
import org.json.JSONArray
import org.json.JSONObject
import java.util.Locale
import kotlin.coroutines.EmptyCoroutineContext

@SuppressLint("ViewConstructor")
class SuniaDrawView(
    context: Context,
    // 默认垂直无限画布
    val layoutMode: LayoutMode = LayoutMode.INFINITE_VERTICAL,
) : AbsSuniaDrawInkView(context, layoutMode) {
    private val singleDispatcher = Executors.newSingleThreadExecutor {
        Thread(it, "$TAG-SingleDispatcher").apply { priority = Thread.MAX_PRIORITY }
    }.asCoroutineDispatcher()
    private var scope = CoroutineScope(SupervisorJob() + singleDispatcher)

    // 等待手绘引擎加载，手绘引擎，但不一定加载完成
    private val mEngineState = MutableStateFlow<InkFunc?>(null)
    private val engineUnstable by mEngineState.delegate()
    suspend fun engine(): InkFunc {
        // 手绘引擎对象， 用于手绘引擎没有初始化完成时，阻塞协程
        return if (engineUnstable != null) engineUnstable!! else mEngineState.filterNotNull().first()
    }

    // 笔迹预测
    private var predictHandler: PredictHandler = PredictHandler(context.applicationContext)

    // 处理手绘状态
    //private var isCanvasDrawing = false //放在父类中

    /**
     * 是否允许手指绘制
     */
    private val mEnableFingerDrawingState = MutableStateFlow(true)
    var enableFingerDrawing by mEnableFingerDrawingState.delegate()
    // 是否在框选状态
    var isEditSelect = false

    private val onCanvasHandledCallbacks = mutableListOf<(isSuccess: Boolean) -> Unit>()

    private val shapeRecognizeParams by lazy {
        ShapeRecognizeParams(500, 30f, InteractiveMode.INTERACTIVE_MODE_LAST_STAY)
    }

    private val listeners = mutableSetOf<Listener>()

    private val _textRectFListState = MutableStateFlow(listOf<RectF>())
    val textRectFListState = _textRectFListState.asStateFlow()

    private var onShowEditMenuListener: OnShowEditMenuListener? = null

    // 套索菜单顶部偏移量， 弹窗高度+默认偏移量
    private val TOP_OFFSET =  DisplayUtils.dp2px(44 + 12)
    // 套索菜单左边偏移量
    private val LEFT_OFFSET =  DisplayUtils.dp2px(94) / 2

    // 套索框选区域
    private var lassoSelectRectF: SelectRectF? = null

    private var isBrushMode = true


    /**
     * 是否启用笔迹美化
     */
    private val enableStrokeBeautifyState = MutableStateFlow(false)
    var enableStrokeBeautify by enableStrokeBeautifyState.delegate()

    /**
     * 引擎初始化完成
     */
    override fun onInitEngineFinish(inkFunc: InkFunc) {
        // 关闭安格斯缩放滚动
        inkFunc.closeScrollAndScaleHandle(true)
        // 数据回调
        inkFunc.setInkWriteListener(inkWriteListener)
        // 关闭马克笔正片叠底效果
        inkFunc.setMarkerPenMode(false, false)
        // 激活马克笔透明度
        inkFunc.enableMarkLowAlpha(true)
        // 点限制100w
        inkFunc.setSavePointsNumLimit(true, 1000000)
        // 笔记预测
        enablePrediction(inkFunc,true)
        // 手写模式下可编辑图片
        inkFunc.enableWritingPointSelect(true)
        inkFunc.selectEditFunc.setSelectLimit(SelectLimit(0, 10000, 0, 10000))
        // 旋转角度校准
        inkFunc.selectEditFunc.enableSelectRotateRevise(true, 2.5f, 15)
        // 图片移动安全边界
        inkFunc.selectEditFunc.enableEditMoveLimit(true, Rect(0, 0, 60, 60))
        inkFunc.setBitmapMaxNum(50)
        // 配置结束，再load ent文件
        mEngineState.value = inkFunc
        setEditDrawViewListener()
        // 初始化一笔成型/数学公式计算配置
        // 平板/精简版支持： 涂抹擦除
        // 手机版支持： 一笔成型/涂抹擦除
        if (isTablet || isLiteVersion) {
            initTabletShapeRecognize()
        } else {
            initShapeRecognize()
        }
        initPenFontainPressure()
        //平板将钢笔平滑设置为0，即取消平滑
        if(isTablet){
            setPenFountainPressure(0f)
            setPenBallpointPressure(0f)
            setPenPencilPressure(0f)
        }
        listeners.forEach { it.onInitEngineFinish() }
        Logger.d(TAG, "onInitEngineFinish")
    }

    /**
     * 打开笔迹预测
     *
     * @param enable 开关
     * @param predictionLength 预测长度，推荐：36
     */
    private fun enablePrediction(inkFunc: InkFunc, enable: Boolean, predictionLength: Int = 36) {
        val params: EstimateParams = EstimateParams()
        params.setHistoryMiniTrace(200) // 最小历史路径长度，长度不足不预测
        params.setHistoryCount(5) // 最小历史点个数，个数不足时不预测
        params.setTime(predictionLength) // 预测时间长度，推荐：36
        inkFunc.enableEstimate(enable, params)
        predictHandler.enableEstimate(enable, params)
        predictHandler.enableLittleEstimate(false)
    }

    private fun setEditDrawViewListener() {
        editDrawView?.setListener(object : EditDrawView.EditDrawListener {
            override fun showSelectMenu(selectType: Int, selectRectF: SelectRectF?) {
                Logger.d(
                    TAG,
                    "showSelectMenu type $selectType"
                )
                isEditSelect = true
                onShowEditMenuListener?.onEditSelect(isEditSelect)
                lassoSelectRectF = selectRectF
                selectRectF?.let {
                    calculateTopPointAfterCenterRotation(
                        RectF(it.left, it.top, it.right, it.bottom), it.angle
                    ).let { points ->

                        val angle = abs(selectRectF.angle)

                        val offset = if (angle > 160 && angle < 200) {
                            ICON_ROTATE_OFFSET_RIGHT
                        } else {
                            0
                        }

                        val minPointF = points.minByOrNull { it.y } ?: PointF()
                        val point = if (minPointF.y < 110 + offset) {
                            points.maxByOrNull { it.y } ?: PointF()
                        } else {
                            minPointF
                        }

                        val newLeft = getPopupX(it.centerX())
                        //如果旋转后的旋转按钮坐标比矩形顶部大，取矩形顶部加上标题栏高度，否则取旋转按钮坐标加上标题栏高度
                        var newTop = point.y
                        if (minPointF.y < 110  + offset) {
                            newTop += DisplayUtils.dp2px( 12)
                            if (it.angle > -20 && it.angle < 20) {
                                newTop += ICON_ROTATE_OFFSET_RIGHT + DisplayUtils.dp2px( 12)
                            }
                        } else {
                            newTop = newTop - TOP_OFFSET - if (angle > 160 && angle < 200) ICON_ROTATE_OFFSET_RIGHT + DisplayUtils.dp2px( 12) else 0
                        }
                        onShowEditMenuListener?.onShowImageEditMenu(
                            newLeft - LEFT_OFFSET,
                            newTop
                        )
                    }
                }
                onShowEditMenuListener?.onLassoRotateAngle(null)
            }

            override fun showTableEditMenu(
                selectCellRectF: RectF?,
                tableRectF: RectF?,
                rowCount: Int,
                columnCount: Int
            ) {

            }

            override fun showPasteMenu(type: Int, x: Float, y: Float) {

            }

            override fun reset() {
                onShowEditMenuListener?.onHintEditMenu()
                onShowEditMenuListener?.onLassoRotateAngle(null)
            }

        })
    }

    private fun getPopupX(center: Float): Float {
        // 阿拉伯语言是从右往左的
        return if (Locale.getDefault().language.equals("ar", true)) {
            DisplayUtils.getScreenWidth() - center
        } else {
            center
        }
    }

    /**
     * 计算矩形绕中心旋转后的最高点坐标
     *
     * @param rect 原始矩形
     * @param rotationDegrees 旋转角度（度，顺时针为正）
     * @return 旋转后的最高点坐标（y值最小的点）
     */
    private fun calculateTopPointAfterCenterRotation(rect: RectF, rotationDegrees: Float): List<PointF> {
        // 将角度转换为弧度
        val radians = rotationDegrees * (PI.toFloat() / 180f)
        val cosTheta = cos(radians)
        val sinTheta = sin(radians)
        // 计算中心点
        val centerX = rect.centerX()
        val centerY = rect.centerY()
        // 矩形四个顶点
        val topLeft = PointF(rect.left, rect.top)
        val topRight = PointF(rect.right, rect.top)
        val bottomRight = PointF(rect.right, rect.bottom)
        val bottomLeft = PointF(rect.left, rect.bottom)
        // 旋转四个顶点
        val rotatedTopLeft = rotatePoint(topLeft, centerX, centerY, cosTheta, sinTheta)
        val rotatedTopRight = rotatePoint(topRight, centerX, centerY, cosTheta, sinTheta)
        val rotatedBottomRight = rotatePoint(bottomRight, centerX, centerY, cosTheta, sinTheta)
        val rotatedBottomLeft = rotatePoint(bottomLeft, centerX, centerY, cosTheta, sinTheta)
        // 找到旋转后y值最小的点
        return listOf(rotatedTopLeft, rotatedTopRight, rotatedBottomRight, rotatedBottomLeft)
    }

    private fun rotatePoint(
        point: PointF,
        cx: Float,
        cy: Float,
        cos: Float,
        sin: Float
    ): PointF {
        // 移动到以中心点为原点的坐标系
        val offsetX = point.x - cx
        val offsetY = point.y - cy
        // 应用旋转矩阵
        val rotatedX = offsetX * cos - offsetY * sin
        val rotatedY = offsetX * sin + offsetY * cos
        // 移回原始坐标系
        return PointF(rotatedX + cx, rotatedY + cy)
    }

    fun setEditMenuListener(listener: OnShowEditMenuListener) {
        onShowEditMenuListener = listener
    }

    fun addListener(listener: Listener) {
        listeners.add(listener)
    }

    fun removeListener(listener: Listener) {
        listeners.remove(listener)
    }

    private val inkWriteListener = object : IInkWriteListener {
        override fun onStepChanged(stepType: Int, stepId: Int, stepName: String?, canUndo: Boolean, canRedo: Boolean) {
            Logger.d(TAG, "SuniaDrawView onStepChanged, stepType: $stepType, stepId: ${stepId}, stepName: $stepName, canUndo : ${canUndo}, canRedo : $canRedo")
            scope.launch(singleDispatcher) {
                val drawStrokeCount = engine().dataSet.curveList.size
                val drawStrokeRect = engine().contentRange
                listeners.forEach { it.onStepChanged(stepType, stepId, stepName, canUndo, canRedo, drawStrokeCount, drawStrokeRect) }
                // 最后一笔的位置
                engine().dataSet?.curveList?.lastOrNull()?.drawRect?.let { drawRect ->
                    val scaleFactor = screenSizeMin.toFloat() / measuredWidth.toFloat()
                    drawRect.left = drawRect.left * scaleFactor
                    drawRect.top = drawRect.top * scaleFactor
                    drawRect.right = drawRect.right * scaleFactor
                    drawRect.bottom = drawRect.bottom * scaleFactor
                    listeners.forEach { it.onLastDrawDataRect(drawRect) }
                }
            }
        }

        override fun onStepFinished() {
            Logger.d(TAG, "SuniaDrawView onStepFinished")
        }

        override fun onDataChanged(p0: Int) {
            Logger.d(TAG, "SuniaDrawView onDataChanged, p0 Int: $p0")
        }

        override fun onDataUpdate(state: Int, p1: CurveDataArray?, p2: Boolean) {
            Logger.d(TAG, "SuniaDrawView onDataUpdate, state Int: $state, p1 CurveDataArray: ${p1}, p2 Boolean: $p2")

        }

        /**
         * 数据更新回调
         * Data update onCurveWrite
         * @param state 更新状态. 0:添加、1:删除。Updated status： 0:added 、1:deleted
         * @param curveList 更新的数据 Line of variation
         * @param isFinish 更新是否完成
         * */
        override fun onCurveWrite(state: Int, curveList: ListCurve?, isFinish: Boolean) {
            Logger.v(TAG,"onCurveWrite, enableStrokeOptimize:$enableStrokeBeautify, state:$state, curveList:$curveList, isFinish:$isFinish")
            //只有切换至美化笔, 且是新增笔迹，才走对应逻辑
            if (enableStrokeBeautify && state == StrokeState.ADD.type){
                scope.launch(Dispatchers.Main.immediate) {
                    StrokeBeautifyEventManager.sendStrokeBeautifyEvent(
                        StrokeBeautifyEvent(
                            type = BeautifyEventType.BEAUTIFY,
                            curveList = curveList,
                            downTime = 0L,
                            inkFunc = engine()
                        )
                    )
                }
            }
        }

        /**
         * @brief   识别数据在数据中心更新后回调
         * @param   curveIds: 美字笔迹Id
         * @param   type 美字替换错误原因 0：线被删除，1：线被修改
         */
        override fun onBeautyAnimatioinError(curveIds: IntArray?, type: Int) {
            Logger.e(TAG,"onBeautyAnimationError, curveIds:$curveIds, type:$type")
        }

        override fun onRecoDataUpdate(p0: Int, p1: RecoDatasInfoArray?) {
            Logger.d(TAG, "SuniaDrawView onError, p0 Int: $p0, p1 RecoDatasInfoArray: $p1")
        }

        override fun onMaxLimit(p0: Int) {
            Logger.e(TAG, "onMaxLimit: $p0")
            when(p0) {
                IInkWriteListener.MAX_LIMIT_POINT -> {
                    ToastUtils.makeWithCancel(GlobalContext.instance.getString(com.tcl.ai.note.base.R.string.handwriting_limit_message))
                }
                IInkWriteListener.MAX_LIMIT_BITMAP -> {
                    ToastUtils.makeWithCancel(GlobalContext.instance.getString(com.tcl.ai.note.base.R.string.max_image_limit))
                }
                IInkWriteListener.MAX_LIMIT_BITMAP_MEMORY_SIZE -> {
//                    ToastUtils.makeWithCancel(GlobalContext.instance.getString(com.tcl.ai.note.base.R.string.handwriting_limit_message))
                }
            }

        }

        override fun obtainKspMotionEvent(event: MotionEvent?): KspMotionEvent? {
            Logger.d(TAG, "obtainKspMotionEvent: $event")
            var kspMotionEvent = SuniaMotionEventUtils.obtain(event)
            predictHandler.handlePredictKspMotionEvent(kspMotionEvent)
            return kspMotionEvent
        }

        override fun onError(p0: Int) {
            Logger.d(TAG, "SuniaDrawView onError, p0 Int: $p0")
        }
    }

    private var isBatchMode = false
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        if (w == 0 || h == 0) {
            return
        }
        if (visibleWidth != w) {
            scope.launch(Dispatchers.Main.immediate) {
                // 横竖屏切换优化，view的size变化前要设置为true
                // setBatchMode会内存泄漏
                engine().setBatchMode(true)
                isBatchMode = true
            }
        }
        super.onSizeChanged(w, h, oldw, oldh)
    }

    override fun onSetVisibleSizeFinish() {
        super.onSetVisibleSizeFinish()
        scope.launch(Dispatchers.Main.immediate) {
            listeners.forEach { it.onSetVisibleSizeFinish(visibleWidth, visibleHeight) }
            if (isBatchMode) {
                isBatchMode = false
                engine().setBatchMode(false)
            }
        }
    }

    /**
     * 手绘图缩放位移结束就会回调
     */
    override fun onCanvasHandleFinish() {
        super.onCanvasHandleFinish()
        val tmpList = onCanvasHandledCallbacks.toList()
        tmpList.fastForEach { it.invoke(true) }
        onCanvasHandledCallbacks.removeAll(tmpList)
        listeners.forEach { it.onCanvasHandleFinish() }
    }

    /**
     * 修改大小回调，调用前父级已经完成判断，
     * 不需要额外判断w和h是否合法
     */
    override fun onVisibleSizeChanged(width: Int, height: Int) {
        val oldw = visibleWidth
        val oldh = visibleHeight
        super.onVisibleSizeChanged(width, height)
        listeners.forEach { it.onSizeChangeStart(width, height, oldw, oldh) }
    }

    suspend fun changePenProp(penProp: PenProp) = scope.launch {
        val time = System.currentTimeMillis()
        engine().setPenProp(PenProp(penProp))
        isBrushMode = true
        Logger.d(TAG, "changePenProp, use time: ${System.currentTimeMillis() - time}, penProp:$penProp")
    }

    /**
     *
     * @param deleteProp 橡皮擦属性
     * @param needScale 是否跟随缩放变化
     */
    suspend fun changeErase(deleteProp: DeleteProp, needScale: Boolean = false) = scope.launch(Dispatchers.Main.immediate)   {//=
        if (!needScale) {
            val scale = engine().scaleInfo.scale
            deleteProp.eraserRadius = deleteProp.eraserRadius / scale
        }
        isBrushMode = false
        val time = System.currentTimeMillis()
        Logger.v(TAG, "changeErase: $deleteProp, needScale: $needScale, scale: ${engine().scaleInfo.scale}")
        engine().setDeleteProp(deleteProp)
        Logger.v(TAG, "changeErase, use time: ${System.currentTimeMillis() - time}")
    }

    suspend fun enableRulerMode(enable: Boolean)  = scope.launch {
        engine().enableRulerMode(enable, RulerType.LOOP_RULER)
    }

    suspend fun getAllDrawStroke(): List<ICurve>? = scope.async {
        engine().dataSet.curveList
    }.await()

    fun undo() = scope.launch {
        if (engine().canUndo()) {
            engine().undo()
        }
    }

    fun redo() = scope.launch {
        if (engine().canRedo()) {
            engine().redo()
        }
    }

    /**
     * 获取是否可以撤销
     */
    suspend fun canUndo(): Boolean  {
        return engine().canUndo()
    }

    /**
     * 获取是否可以重做
     */
    suspend fun canRedo(): Boolean {
        return engine().canRedo()
    }

    /**
     * 获取手绘可视大小
     */
    suspend fun getDrawStrokeVisibleSize(): RectF {
        val contentRange = engine().contentRange
        return contentRange
    }

    fun clearStep() = scope.launch {
        engine().clearStep()
    }

    fun clear() = scope.launch {
        //旅行日记需要清除画布所有内容的接口
        Logger.d(TAG, "clear: Clear all content on the canvas")
        engine().clear()
    }

    /**
     * 仅清除所有笔画
     */
    fun clearAllDrawStroke() = scope.launch {
        engine().clear(arrayOf(DataType.LINE))
    }

    /**
     * 强制修改安格斯画布的缩放位移
     *
     * 绝对缩放
     */
//    private val absScaleInfo = ScaleInfo()
//    fun changeScale(matrixInfo: MatrixInfo) = scope.launch(Dispatchers.Main.immediate) {
//        val scaleFactor = screenSizeMin.toFloat() / measuredWidth.toFloat()
//        absScaleInfo.scale = matrixInfo.scale * scaleFactor
//        absScaleInfo.offsetX = matrixInfo.offsetX
//        absScaleInfo.offsetY = matrixInfo.offsetY
//        absScaleInfo.scaleCenterX = matrixInfo.scaleCenterX
//        absScaleInfo.scaleCenterY = matrixInfo.scaleCenterY
//        engine().updateScaleInfo(absScaleInfo)
//    }

    /**
     * @param matrixInfo 是绝对缩放参数
     *
     * [changeScaleRelative]和[changeScaleRelativeEnd]必须搭配使用
     */
    private val relativeScaleInfo = ScaleInfo()
    private var lastRelativeScaleInfo = ScaleInfo()
    private var lastWidth = width
    private var lastHeight = height
    fun changeScaleRelative(matrixInfo: MatrixInfo) = scope.launch(Dispatchers.Main.immediate) {
        if (lastWidth != width) {
            // 屏幕旋转，本轮所有doScale无效，交给changeScaleRelativeEnd处理
            Logger.d(TAG, "changeScaleRelative: The screen is rotated. Abort doScale!")
            return@launch
        }
        // 绝对缩放转换成相对缩放
        val scaleFactor = screenSizeMin.toFloat() / measuredWidth.toFloat()
        relativeScaleInfo.scale = matrixInfo.scale * scaleFactor / lastRelativeScaleInfo.scale
        relativeScaleInfo.offsetX = matrixInfo.offsetX - lastRelativeScaleInfo.offsetX
        relativeScaleInfo.offsetY = matrixInfo.offsetY - lastRelativeScaleInfo.offsetY
        relativeScaleInfo.scaleCenterX = lastRelativeScaleInfo.offsetX
        relativeScaleInfo.scaleCenterY = lastRelativeScaleInfo.offsetY
        engine().doScale(relativeScaleInfo)
        Logger.d(TAG, "changeScaleRelative: $matrixInfo, relativeScaleInfo.scale: ${relativeScaleInfo.scale}, lastRelativeScaleInfo.scale: ${lastRelativeScaleInfo.scale}")
    }

    /**
     *
     * @param matrixInfo 是绝对缩放参数
     *
     * [changeScaleRelative]和[changeScaleRelativeEnd]必须搭配使用
     */
    fun changeScaleRelativeEnd(matrixInfo: MatrixInfo) = scope.launch(Dispatchers.Main.immediate) {
        val scaleFactor = screenSizeMin.toFloat() / measuredWidth.toFloat()
        if (lastWidth != width) {
            // 屏幕旋转，重置并用绝对缩放纠正
            lastRelativeScaleInfo.scale = matrixInfo.scale * scaleFactor
            lastRelativeScaleInfo.offsetX = matrixInfo.offsetX
            lastRelativeScaleInfo.offsetY = matrixInfo.offsetY
            engine().resetScale()
            engine().updateScaleInfo(lastRelativeScaleInfo)
            Logger.d(TAG, "changeScaleRelativeEnd: The screen is rotated. fixed scaleInfo! lastWidth: $lastWidth, lastHeight: $lastHeight, width: $width, height: $height")
            lastWidth = width
            lastHeight = height
            return@launch
        }

        // 绝对缩放转换成相对缩放
        // 安格斯连续调用两次doScrollScaleEnd会画布消失
        // 所以在调用doScrollScaleEnd方法确保调用一次doScale，让doScale和doScaleEnd始终成一组
        relativeScaleInfo.scale = matrixInfo.scale * scaleFactor / lastRelativeScaleInfo.scale
        relativeScaleInfo.offsetX = matrixInfo.offsetX - lastRelativeScaleInfo.offsetX
        relativeScaleInfo.offsetY = matrixInfo.offsetY - lastRelativeScaleInfo.offsetY
        relativeScaleInfo.scaleCenterX = lastRelativeScaleInfo.offsetX
        relativeScaleInfo.scaleCenterY = lastRelativeScaleInfo.offsetY
        engine().doScale(relativeScaleInfo)

        // 本轮缩放结束，记录下来。用于下次缩放使用
        lastRelativeScaleInfo.scale = matrixInfo.scale * scaleFactor
        lastRelativeScaleInfo.offsetX = matrixInfo.offsetX
        lastRelativeScaleInfo.offsetY = matrixInfo.offsetY
        engine().doScrollScaleEnd()
        Logger.d(TAG, "changeScaleRelativeEnd: $matrixInfo, relativeScaleInfo.scale: ${relativeScaleInfo.scale}, lastRelativeScaleInfo.scale: ${lastRelativeScaleInfo.scale}")
    }

    /**
     * 是否允许绘制笔画
     */
    fun changeEditable(isEdit: Boolean) {
        Logger.d(TAG, "changeEditMode: $isEdit")
        isCanvasDrawing = isEdit
    }

    /**
     * 改为暗黑模式
     */
    fun changeDarkMode(isDark: Boolean) = scope.launch {
        Logger.d(TAG, "changeDarkMode: $isDark")
        engine().setDarkMode(
            if (isDark) DarkModeMenu.MODE_HSL
            else DarkModeMenu.NONE
        )
        engine().forceRedraw()
    }

    /**
     * path路径
     * rectF 图片宽高
     * isSelect插入后是否选中
     * angle旋转角
     */
    fun insertBitmap(path: String, width: Int, height: Int, isSelect: Boolean = false, angle: Float = 0f) = scope.launch {
        // 根据画布的可视区域，计算插入图片的位置
        val showRect = InsertImage.getInsertImageRect(width, height, engine().canvasVisibleRectF)
        Logger.d("insertBitmap", "showRect=$showRect, canvasVisibleRectF=${engine().canvasVisibleRectF}, $isSelect")
        val addData: MutableList<AddDataSelectParam> = mutableListOf()
        addData.add(AddDataSelectParam(AddDataType.AddBitmap, isSelect))
        engine().setAddDataSelect(addData)
        engine().addBitmap(path, showRect, isSelect)
    }

    /**
     * 获取笔画数量
     */
    suspend fun getCurveCount(): Int {
        try {
            val curveList = scope.async { engine().dataSet.curveList }.await()
            if (curveList != null) return curveList.size
            return 0
        } catch (e: Exception) {
            Logger.e(TAG, "getCurveCount: $e")
        }
        return 0
    }

    suspend fun getImageCount(): Int {
        try {
            val bitmapList = scope.async { engine().dataSet.bitmapList }.await()
            if (bitmapList != null) return bitmapList.size
            return 0
        } catch (e: Exception) {
            Logger.e(TAG, "getImageCount: $e")
        }
        return 0
    }

    fun setLassoEnable(enable: Boolean) = scope.launch {
        Logger.d(TAG, "setLassoEnable=$enable")
        engine().enableSelectMode(enable)
        engine().setPreViewMode(false)
    }

    fun deleteSelect() = scope.launch {
        engine().selectEditFunc.onDelete()
    }

    suspend fun selectIsReady(): Boolean {
        return engine().selectEditFunc?.isSelectedReady == true
    }

    suspend fun clickPointByType(point: PointF, type: PointType): Boolean {
        finishEditSelect()
        val isContainerImage =  engine().isInPointByType(point, type)
        Logger.d(TAG, "clickPointByType: $point, type: $type, isContainerImage: $isContainerImage")
        if (isContainerImage) {
            engine().selectEditFunc.pointSelect(point)
        } else {
        }
        return isContainerImage
    }


    fun finishEditSelect() = scope.launch {
        finishSelect()
    }


    fun setShapeRecognizeEnable(enable: Boolean) = scope.launch {
        engine().enableShapeRecognize(enable, shapeRecognizeParams)
    }

    /**
     * 初始化手写笔压力
     */
    fun initPenFontainPressure() = scope.launch {
        engine().setWriteEffect(PenType.PEN_FOUNTAIN, TouchEffectType.PRESSURE,isTablet.judge(80f,50f))
    }

    /**
     * TouchEffectType增加枚举SMOOTH，用于调整平滑强度。接口是按笔设置，一种笔要设置一次，可以直接在引擎初始化的时候，直接初始化设置这个值
     * void setWriteEffect(PenType penType, TouchEffectType effectType, float value);
     * PEN_FOUNTAIN 钢笔
     */
    private fun setPenFountainPressure(smoothValue :Float = 0f) = scope.launch {
        Logger.d(TAG, "setPenFountainPressure smoothValue : $smoothValue")
        engine().setWriteEffect(PenType.PEN_FOUNTAIN, TouchEffectType.SMOOTH,smoothValue)
    }
    /**
     * PEN_FOUNTAIN 圆珠笔
     */
    private fun setPenBallpointPressure(smoothValue :Float = 0f) = scope.launch {
        Logger.d(TAG, "setPenBallpointPressure smoothValue : $smoothValue")
        engine().setWriteEffect(PenType.PEN_BALLPOINT, TouchEffectType.SMOOTH,smoothValue)
    }
    /**
     * PEN_FOUNTAIN 铅笔
     */
    private fun setPenPencilPressure(smoothValue :Float = 0f) = scope.launch {
        Logger.d(TAG, "setPenPencilPressure smoothValue : $smoothValue")
        engine().setWriteEffect(PenType.PEN_PENCIL, TouchEffectType.SMOOTH,smoothValue)
    }
    fun initShapeRecognize() = scope.launch {
        initGestureRecognize()
        engine().enableShapeRecognize(true, shapeRecognizeParams)
        val shapeUtils = ShapeRecognizeUtil.getInstance(
            GlobalContext.appContext,
            SuniaVerifyHandler.suniaVerifyStateFlow.value
        )
        Logger.d(TAG, "initShapeRecognize")
        // 可以传assets路径
        shapeUtils.setShapeRecognizeParam(true)
        engine().setShapeRecognizeListener(object : IShapeRecognizeListener {
            override fun syncRecognize(
                p0: DoubleArray?,
                p1: DoubleArray?,
                p2: DoubleArray?
            ): String {
                val shape = shapeUtils.recognize(p0, p1, p2)
                if (shapeUtils.isShape(shape)) return shape
                return if (FeatureUtils.isEnableGestureEraser()) {
                    GestureRecognizeManager.getGesture(p0, p1, p2)
                } else {
                    shape
                }
            }
        })
    }

    fun initTabletShapeRecognize() = scope.launch {
        initGestureRecognize()
        engine().enableShapeRecognize(true, shapeRecognizeParams)

        Logger.d(TAG, "initTabletShapeRecognize")
        // 可以传assets路径
        engine().setShapeRecognizeListener(object : IShapeRecognizeListener {
            override fun syncRecognize(
                p0: DoubleArray?,
                p1: DoubleArray?,
                p2: DoubleArray?
            ): String {
                return if (FeatureUtils.isEnableGestureEraser()) {
                    GestureRecognizeManager.getGesture(p0, p1, p2)
                } else {
                    ""
                }
            }
        })
    }

    suspend fun initGestureRecognize() {
        val currentEngine = EngineManager.getEngine()
        Logger.d("GestureRecognizeManager", "initGestureRecognize, engin=$currentEngine")
        if (currentEngine != null) {
            GestureRecognizeManager.init(currentEngine)
        } else {
            EngineManager.engineState.filterNotNull()
                .take(1)
                .collect { engine ->
                    Logger.d("GestureRecognizeManager", "initGestureRecognize, engin init success=$engine")
                    GestureRecognizeManager.init(engine)
                }
        }
    }

    /**
     * @param entPath 单页ent路径, 历史遗留问题, 现在变成多页
     * 同步加载
     */
    suspend fun loadData(entPath: String) = coroutineScope {
        Log.i(TAG, "loadData entPath: $entPath")
        var isLoaded = false
        val entPathDir = File(entPath).parent!!
        val multiEntPath = entPathDir + File.separator + InkSDK.MULTI_PAGE_ENT_NAME
        if (File(entPath).exists()) {
            // 先单页转多页
            singleEntToMultiEnt(entPath)
            val iMultiPage = MultiPageEnt.loadMultiPageEnt(multiEntPath, entPathDir + File.separator)
            isLoaded = true
            val count = iMultiPage.pageCount
            Logger.i(TAG, "loadData, pageCount: $count")
            if (count > 0) {
                Logger.i(TAG, "loadData, inkPath: ${iMultiPage.getPage(0).inkPath}")
                isLoaded = innerLoadEnt(
                    engine(),
                    iMultiPage.getPage(0).inkPath,
                    iMultiPage.pageExtInfo
                )
            }
            iMultiPage.release()
        }
        scope.launch {
            val drawStrokeCount = engineUnstable?.dataSet?.curveList?.size ?: 0
            val drawStrokeRect = engineUnstable?.contentRange ?: RectF()
            Logger.d(TAG, "onDataLoaded: $drawStrokeCount, drawStrokeRect: $drawStrokeRect")
            listeners.forEach { it.onDataLoaded(drawStrokeCount, drawStrokeRect, isLoaded) }
        }
        return@coroutineScope isLoaded
    }

    /**
     * 单页ent转多页ent, 一期note升级二期note使用
     */
    private suspend fun singleEntToMultiEnt(singleEntPath: String) = runCatching {
        val entPathDir = File(singleEntPath).parent
        if (entPathDir.isNullOrEmpty()) {
            Logger.w(TAG, "singleEntToMultiEnt: entPathDir is null")
            return@runCatching
        }
        // 多页ent路径
        val multiEntPath = entPathDir + File.separator + InkSDK.MULTI_PAGE_ENT_NAME
        val multiEntFile = File(multiEntPath)
        multiEntFile.parentFile?.mkdirs()
        // 这个文件用来标记是否转换完成
        val multiEntConvertPath = "$multiEntPath.convert"
        val multiEntConvertFlagFile = File(multiEntConvertPath)
        // 转换中途,进程被删了，重新开始转换。
        if (multiEntConvertFlagFile.exists()) {
            multiEntFile.delete()
            multiEntConvertFlagFile.delete()
        }
        if (!multiEntFile.exists()) {
            Logger.i(TAG, "singleEntToMultiEnt start. singleEntPath: $singleEntPath")
            val start = System.currentTimeMillis()
            // 标记是否转换完成
            multiEntConvertFlagFile.createNewFile()
            // 单页转多页
            innerSaveMultiEnt(engine(), singleEntPath)
            listeners.forEach { it.onVersionUpgrade(2) }
            // 转换完成, 删除文件
            multiEntConvertFlagFile.delete()
            Logger.i(TAG, "singleEntToMultiEnt cost: ${System.currentTimeMillis() - start}")
        }
    }.onFailure {
        Logger.e(TAG, "singleEntToMultiEnt error: ${it.stackTraceToString()}")
    }

    private suspend fun innerLoadEnt(engine: InkFunc, entFilePath: String, extInfo: String?) = coroutineScope {
        suspendCancellableCoroutine { cont ->
            Logger.d(TAG, "innerLoadEnt, path: $entFilePath")
            val start = System.currentTimeMillis()
            engine.loadEntFile(entFilePath, object : IDataListener {
                override fun onDataLoaded(p0: Boolean) {
                    runCatching {
                        Log.w(TAG, "innerLoadEnt extInfo: $extInfo}")
                        val obj = JSONObject(extInfo ?: "{}")
                        val infoStr = obj.optString("single_page_info")
                        val info = JSONObject(infoStr)
                        val array = info.optJSONArray("edit_bitmap_paths")
                        if (array != null && array.length() > 0) {
                            val tempPaths = mutableListOf<String>()
                            for (i in 0 until array.length()) {
                                tempPaths.add(array.optString(i))
                            }
//                            addBitmaps(tempPaths)
                        }
                    }.onFailure {
                        Log.w(TAG, "innerLoadEnt extInfo parse fail, reason: ${it.stackTraceToString()}")
                    }
                    Logger.i(TAG, "innerLoadEnt result: $p0, take: ${System.currentTimeMillis() - start} ms")
                    cont.resume(p0)
                }

                override fun onDataSaved(p0: Boolean) {

                }
            })
        }
    }

    /**
     * 同步保存
     */
    suspend fun saveData(singleEntPath: String) = coroutineScope {
        suspendCancellableCoroutine { cont ->
            Logger.d(TAG, "saveData, start to save: $singleEntPath")
            launch {
                val engine = engine()
                val start = System.currentTimeMillis()
                engine.saveEntFile(singleEntPath, object : IDataListener {
                    override fun onDataLoaded(p0: Boolean) {}
                    override fun onDataSaved(p0: Boolean) {
                        Logger.d(TAG, "saveData, save result: $p0, take: ${System.currentTimeMillis() - start} ms, path: $singleEntPath")
                        innerSaveMultiEnt(engine, singleEntPath)
                        cont.resume(p0)
                    }
                })
            }
        }
    }

    private fun innerSaveMultiEnt(engine: InkFunc, singleEntPath: String) = runCatching {
        val entPathDir = File(singleEntPath).parent
        if (entPathDir.isNullOrEmpty()) {
            Logger.w(TAG, "innerSaveMultiEnt: entPathDir is null")
            return@runCatching
        }
        val multiEntPath = entPathDir + File.separator + InkSDK.MULTI_PAGE_ENT_NAME
        val multiPage: IMultiPage = MultiPageEnt.createMultiPage()
        val singlePage: ISinglePage = multiPage.insertPage(0)

        singlePage.bgColor = engine.engineConfigs.backgroundColor
        singlePage.bgTexture = engine.engineConfigs.gridStyle.value.toShort()
        singlePage.bgTextureColor = engine.engineConfigs.gridLineColor
        singlePage.bgTextureLineWidth = engine.engineConfigs.gridLineSize
        singlePage.inkPath = singleEntPath

        val jsonObject = JSONObject()
        val info = JSONObject()
        info.put("layout_mode", layoutModeValue)
        if (engine.engineConfigs.viewPortRectF != null && !engine.engineConfigs.viewPortRectF.isEmpty) {
            val viewPortArray = JSONArray()
            viewPortArray.put(engine.engineConfigs.viewPortRectF.left.toString())
            viewPortArray.put(engine.engineConfigs.viewPortRectF.top.toString())
            viewPortArray.put(engine.engineConfigs.viewPortRectF.right.toString())
            viewPortArray.put(engine.engineConfigs.viewPortRectF.bottom.toString())
            info.put("view_port", viewPortArray)
        }
//        if (editBitmapPaths.size > 0) {
//            val array = JSONArray()
//            for (index in editBitmapPaths.indices) {
//                array.put(editBitmapPaths[index])
//            }
//            info.put("edit_bitmap_paths", array)
//        }
        info.put("grid_point_size", engine.engineConfigs.pointSize)
        info.put("grid_point_spacing", engine.engineConfigs.pointSpacing)
        info.put("grid_line_spacing", engine.engineConfigs.lineSpacing)

        // 记录上一次的画布位置
        info.put("last_canvas_scale", engine.scaleInfo.scale)
        info.put("last_canvas_offset_x", engine.scaleInfo.offsetX)
        info.put("last_canvas_offset_y", engine.scaleInfo.offsetY)

        jsonObject.put("single_page_info", info)

        multiPage.pageExtInfo = jsonObject.toString()
        val success = MultiPageEnt.saveMultiPageEnt(multiPage, multiEntPath)
        multiPage.release()
        Log.v(TAG, "innerSaveMultiEnt, saveEnt: $success, $jsonObject")
    }.onFailure {
        Logger.e(TAG, "innerSaveMultiEnt error: ${it.stackTraceToString()}")
    }

    /**
     * 保存日间、夜间缩略图，线性，不能并发
     * 当不存在笔画和图片时会删除现存缩略图
     * @param noteId 笔记ID
     * @return ThumbnailSaveResult 保存结果，包含是否有图片和笔画的信息
     */
    suspend fun saveThumbnailBlockWithDark(noteId: Long): List<Deferred<Unit>> = withContext(EmptyCoroutineContext) {
//        Logger.v(TAG, "save thumbnail: $noteId")
        val startTime = System.currentTimeMillis()
        // dataSet以及其内部方法都需要加锁或者单线程处理
        // 如果既没有笔画也没有图片，删除缩略图并返回
        val engine = engine()
        Logger.v(TAG, "saveThumbnailBlockWithDark start")
        val isEmptyDelete = async { engine.isContentEmpty }.await()
        if (isEmptyDelete) {
            HandWritingThumbnailRepo.deleteHandwritingThumbnail(noteId)
            val take = System.currentTimeMillis() - startTime
            Logger.v(TAG, "saveThumbnailBlockWithDark, curve is empty or delete, delete thumbnail and return, take: $take ms")
            return@withContext emptyList()
        }
        finishSelect()
        Logger.v(TAG, "saveThumbnailBlockWithDark readyTime: ${System.currentTimeMillis() - startTime}")

        val saveBatchDeferred = listOf(
            GlobalContext.applicationScope.async(Dispatchers.IO) {
                HandWritingThumbnailRepo.saveBitmap(noteId, false)
            },
            GlobalContext.applicationScope.async(Dispatchers.IO) {
                HandWritingThumbnailRepo.saveBitmap(noteId, true)
            }
        )
        Logger.v(TAG, "saveThumbnailBlockWithDark createJobTime: ${System.currentTimeMillis() - startTime}")
        // 返回保存结果
        return@withContext saveBatchDeferred
    }

//    private suspend fun saveThumbnailBlock(noteId: Long, isDark: Boolean = false) = withContext(Dispatchers.IO) {
//        Logger.v(TAG, "save thumbnail: $noteId")
//        val width = (screenSizeMin * THUMBNAIL_SAVE_SCALE).roundToInt()
//        val height = (screenSizeMax * THUMBNAIL_SAVE_SCALE).roundToInt()
//        val contentRange = engine().contentRange
//        val bitmap = Bitmap.createBitmap(
//            width,
//            height,
//            Config.ARGB_8888
//        )
//        val contentRect = RectF(
//            0f,
//            // contentRange.top,
//            0f,
//            visibleWidth.toFloat(),
//            // 底部越界不会有影响，根据宽度缩放的
//            // contentRange.top + screenSizeMax * 2,
//            screenSizeMax * 2f,
//        )
//        val targetRect = RectF(
//            0f,
//            0f,
//            width.toFloat(),
//            height.toFloat(),
//        )
//        finishSelect()
//        if (isDark) {
//            engine().setDarkMode(DarkModeMenu.MODE_HSL)
//        } else {
//            engine().setDarkMode(DarkModeMenu.NONE)
//        }
//        engine().startRendToBitmap()
//        engine().rendToBitmap(
//            // 目标bitmap
//            bitmap,
//            // 画布区域
//            contentRect,
//            // bitmap的目标区域
//            targetRect,
//            // 混合模式，覆盖
//            MixedMode.Displace,
//            // 是否包含背景
//            false,
//        )
//        engine().endRendToBitmap()
//        HandWritingThumbnailRepo.saveBitmap(noteId, bitmap, isDark)
//        Logger.v(TAG, "save thumbnail success: $noteId")
//    }

    // sunia原始内容范围，会根据画板可视宽度变更
    suspend fun getContentRange() = engine().contentRange ?: RectF()

    // 内容区域会根据屏幕宽度缩放，所以需要另外处理一遍
    suspend fun getScaledContentRange(): RectF {
        val contentRange = engine().contentRange ?: return RectF()
        val scale = screenSizeMin.toFloat() / visibleWidth.toFloat()
        return RectF(
            contentRange.left * scale,
            contentRange.top * scale,
            contentRange.right * scale,
            contentRange.bottom * scale,
        )
    }

    /**
     * dataSet以及其内部方法都需要加锁或者单线程处理
     */
    suspend fun isContentEmptyExceptDelete() =
        scope.async { engine().dataSet.isEmptyExceptDeleteOrNull() }.await()

    suspend fun toBitmap(bitmap: Bitmap) = runIO {
        val contentRange = engine().contentRange
        val contentRect = RectF(
            0f,
            0f,
            visibleWidth.toFloat(),
            // 底部越界不会有影响，根据宽度缩放的
            contentRange.bottom,
        )
        val targetRect = RectF(
            0f,
            0f,
            bitmap.width.toFloat(),
            bitmap.height.toFloat(),
        )
        finishSelect()
        engine().startRendToBitmap()
        engine().rendToBitmap(
            bitmap,
            contentRect,
            targetRect,
            MixedMode.Overlying,
            false,
        )
        engine().endRendToBitmap()
        bitmap
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (event == null) return super.onTouchEvent(event)

        if (!isCanvasDrawing && event.pointerCount == 1) {
            return false
        }

        // 不允许手指绘制笔画
        if (!enableFingerDrawing && !isEditSelect && event.getToolType(0) == MotionEvent.TOOL_TYPE_FINGER) {
            return false
        }

        handleTouchEvent(event)

        event?.let { motionEvent ->
            when (motionEvent.actionMasked) {
                MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                    // 只在单指触摸时传递位置
                    if (motionEvent.pointerCount == 1) {
                        val offset = androidx.compose.ui.geometry.Offset(
                            motionEvent.x,
                            motionEvent.y
                        )
                        // 框选模式下，不传递位置，否则在图片处于编辑模式时，切换到橡皮擦后，没有触摸屏幕，也会出现橡皮擦
                        if (!isEditSelect) {
                            listeners.forEach { it.onTouchPositionUpdate(offset) }
                        }
                    }
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    // 触摸结束时清除位置
                    listeners.forEach { it.onTouchPositionUpdate(null) }
                }
            }
        }

        return super.onTouchEvent(event)
    }

    override fun release() {
        super.release()
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        scope.cancel()
        scope = CoroutineScope(SupervisorJob() + singleDispatcher)
        Logger.v(TAG, "onAttachedToWindow")
    }

    override fun onRotate(angle: Float, revisedAngle: Float) {
        Logger.d(TAG, "onRotate: $angle, revisedAngle: $revisedAngle")
        lassoSelectRectF?.let {
            onShowEditMenuListener?.onLassoRotateAngle(
                LassoRotateEvent(revisedAngle.toInt().toString(),
                    getPopupX(it.centerX()) - DisplayUtils.dp2px(32),
                    it.centerY() - DisplayUtils.dp2px(20))
            )
        }

    }

    override fun getVisibleRectF(): RectF? {
        return mEngineState.value?.canvasVisibleRectF
    }

    override fun onDetachedFromWindow() {
        listeners.clear()
        onShowEditMenuListener = null
        super.onDetachedFromWindow()
        scope.cancel()
        Logger.v(TAG, "onDetachedFromWindow")
    }

    private fun handleTouchEvent(event: MotionEvent){
        // 选中美化笔，且只有书写模式时，才把数据传递过去（避免擦除和书写相互切换时 产生脏数据）
        if (enableStrokeBeautify && isBrushMode) {
            when (event.actionMasked) {
                MotionEvent.ACTION_DOWN, MotionEvent.ACTION_UP,MotionEvent.ACTION_CANCEL -> {
                    val eventType = if (event.actionMasked == MotionEvent.ACTION_DOWN)
                        BeautifyEventType.ACTION_DOWN
                    else
                        BeautifyEventType.ACTION_UP

                    scope.launch(Dispatchers.Main.immediate) {
                        StrokeBeautifyEventManager.sendStrokeBeautifyEvent(
                            StrokeBeautifyEvent(
                                type = eventType,
                                downTime = event.downTime,
                                inkFunc = null,
                                curveList = null
                            )
                        )
                    }
                }
            }
        }
    }

    fun handleContentInfo(journalContentInfo: JournalContentInfo?, onComplete: () -> Unit) {
        //runOnUiThread {
            if (engineUnstable != null || mEngineState.value != null) {
                scope.launch {
                    try {
//                        delay(300)
                        engine().clear()
                        engine().clearStep()
                    } catch (e: Exception) {
                        Logger.e(TAG, "handleContentInfo error: ${e.message}")
                        e.printStackTrace()
                    }
                    journalContentInfo?.let {
                        if (it.templateData != null) {
                            handleTemplate(journalContentInfo) {
                                onComplete()
                            }
                        } else if (it.entFile != null) {
                            loadEntFile(it.entFile.orEmpty())
                        }
                    }
                }
            }
        //}
    }

    private suspend fun handleTemplate(
        journalContentInfo: JournalContentInfo,
        onDataSaved: ((isSuccess: Boolean) -> Unit)?
    ) {
        Logger.v(TAG, "handleTemplate data: $journalContentInfo")
        var rectF: RectF
        val top = when (journalContentInfo.templateData!!.layoutType) {
            1 -> 0f
            2 -> journalContentInfo.statusBarHeight
            else -> journalContentInfo.statusBarHeight + journalContentInfo.topBarHeight
        }

        val outDir = File(GlobalContext.instance.filesDir, "template/image")
        val templateTextList = mutableListOf<TemplateText>()
        journalContentInfo.templateData?.elements?.forEach { element ->
            rectF = getContentRectF(element.size, element.position, top)
            if (element.type == ElementType.TEXT) {
                //插入文本
                templateTextList.add(TemplateText("", element.textColor, rectF))
            } else {
                val picturePath = if (element.type == ElementType.IMAGE) {
                    element.content
                } else {
                    outDir.absolutePath + "/" + element.content //需要集成者自己获取图片真实的访问路径
                }
                // 插入图片 BitmapParams 可以添加tag参数
                val bitmapParameter = BitmapParams(picturePath, rectF, false, element.angle)
                engine().addBitmap(bitmapParameter)
            }
        }
        addText(templateTextList)
        pasteAnInvisibleText()
        finishSelect()
        saveJournalContent(
            journalId = journalContentInfo.journalId,
            pageIndex = journalContentInfo.pageIndex,
            onDataSaved = onDataSaved
        )
    }

    private suspend fun finishSelect() {
        // sunia sdk 会出现空指针异常
        try {
            if (engine().selectEditFunc != null) {
                engine().selectEditFunc.finishSelect()
            }
        } catch (e: Exception) {
            Logger.d(TAG, "call engine().selectEditFunc.finishSelect() error: $e")
        }
    }

    /**
     * 获取自适应字体大小
     */
    private fun getTextSize(rectF: RectF, text: String): Float {
        var finalTextSize = 12f
        val paint = TextPaint()
        paint.textSize = finalTextSize.dp2px.toFloat()
        val rectWidth = rectF.width()
        val rectHeight = rectF.height()

        var layout = StaticLayout.Builder.obtain(text, 0, text.length, paint, rectWidth.toInt())
            .setLineSpacing(0f, 1.0f)
            .setIncludePad(true).build()
        while (layout.height > rectHeight) {
            finalTextSize -= 1f
            paint.textSize = finalTextSize.dp2px.toFloat()
            layout = StaticLayout.Builder.obtain(text, 0, text.length, paint, rectWidth.toInt())
                .setLineSpacing(0f, 1.0f)
                .setIncludePad(true).build()
        }
        Logger.d(
            TAG,
            "layout.height:${layout.height} rectHeight:$rectHeight finalTextSize:$finalTextSize"
        )
        return finalTextSize
    }

    private suspend fun loadEntFile(entFilePath: String) {
        Logger.v(
            TAG,
            "loadEntFile entFilePath: $entFilePath, isFileExist: ${File(entFilePath).exists()}"
        )
        engine().loadEntFile(entFilePath, null)
        engine().enableSelectMode(false)
    }

    private fun getContentRectF(
        size: List<Float>,
        position: List<Float>,
        topPadding: Float
    ): RectF {
        val widthRatio = GlobalContext.screenWidth / 360f
        val heightRatio = GlobalContext.screenHeight / 780f
        return RectF(
            position[0] * widthRatio,
            (position[1] + topPadding) * heightRatio,
            (position[0] + size[0]) * widthRatio,
            (position[1] + size[1] + topPadding) * heightRatio
        )
    }

    /**
     * 插入文本
     */
    private suspend fun addText(list: List<TemplateText>) {
        try {
            val textPaint = TextPaint().apply { textSize = 12f.dp2px.toFloat() }
            var layout: StaticLayout
            val textRectFList = mutableListOf<RectF>()

            var ratio = visibleWidth * 1.0f / 1000
            ratio = if (ratio == 0f) 1f else ratio
            list.forEach { data ->
                //插入文本
                Logger.d(TAG, "addText: ${data.text}, ratio: $ratio, visibleWidth: $visibleWidth")
                val rectF = data.contentRectF
                val rectF2 = RectF(data.contentRectF.left / ratio, data.contentRectF.top, data.contentRectF.right / ratio, data.contentRectF.bottom)
                layout = StaticLayout.Builder
                    .obtain(data.text, 0, data.text.length, textPaint, rectF2.width().toInt())
                    .setLineSpacing(0f, 1.0f)
                    .setIncludePad(true)
                    .setUseLineSpacingFromFallbacks(true)
                    .build()
                for (i in layout.lineCount - 1 downTo 0) {
                    Logger.d(TAG, "lint:$i LineBottom:${layout.getLineBottom(i)} rect height:${rectF2.height()}")
                    if (layout.getLineBottom(i) <= rectF2.height()) {
                        rectF2.bottom = rectF.top + layout.getLineBottom(i) + 3
                        break
                    }
                }
                Logger.d(TAG,"final rect height:${rectF2.height()}")
                textRectFList.add(rectF)
                _textRectFListState.update { textRectFList.toList() }
                onPasteText(
                    listOf(
                        SimpleTextData().apply {
                            text = data.text
                            textColor = Color.parseColor(data.textColor ?: "#80000000")
                            textSize = 12f.dp2px.toFloat()
                            contentRectF = rectF2
                        }
                    ),
                    rectF
                )
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun pasteAnInvisibleText() {
        val rectF = RectF(0f, 0f, 0f, 0f)
        onPasteText(
            listOf(
                SimpleTextData().apply {
                    text = ""
                    textColor = Color.parseColor("#00000000")
                    textSize = 0f
                    contentRectF = rectF
                }
            ),
            rectF
        )
    }
    private suspend fun onPasteText(textData: List<SimpleTextData>, rectF: RectF) {
        try {
            val listData = ListData()
            listData.textData = textData
            engine().selectEditFunc.onPaste(
                listData,
                rectF.left + rectF.width() / 2f,
                rectF.top + rectF.height() / 2f
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private suspend fun saveCanvasToBitmapFile(journalId: Long, pageIndex: Int) = withContext(Dispatchers.IO) {
        Logger.d(TAG, "saveCanvasToBitmapFile journalId:$journalId pageIndex:$pageIndex")
        val contentRect = RectF(
            0f,
            0f,
            visibleWidth.toFloat(),
            screenSizeMax.toFloat(),
        )

        with(engine()) {
            val bitmap = Bitmap.createBitmap(
                contentRect.width().toInt(),
                contentRect.height().toInt(),
                Config.ARGB_8888
            )
            startRendToBitmap()
            rendToBitmap(bitmap, contentRect, contentRect, MixedMode.Overlying, true)
            endRendToBitmap()
            FileUtils.saveJournalContentBitmapToFilesDir(
                bitmap = bitmap,
                journalId = journalId,
                pageIndex = pageIndex
            )
            listeners.forEach { it.onSaveCompleted(pageIndex) }
        }
    }

    private fun saveCanvasToEntFile(
        journalId: Long,
        pageIndex: Int,
        onDataSaved: ((isSuccess: Boolean) -> Unit)?
    ) = scope.launchIO {
        Logger.d(TAG, "saveCanvasToEntFile journalId:$journalId pageIndex:$pageIndex")
        //调用保存接口
        val path = getJournalContentEntPath(journalId = journalId, pageIndex = pageIndex)
        engine().saveEntFile(path, object : IDataListener {
            override fun onDataLoaded(p0: Boolean) {
                Logger.d(TAG, "onDataLoaded zzz: $p0, path: $path")
            }
            override fun onDataSaved(p0: Boolean) {
                Logger.d(TAG, "save result zzz: $p0, path: $path")
                scope.launch {
                    saveCanvasToBitmapFile(journalId, pageIndex)
                }
                onDataSaved?.invoke(p0)
            }
        })
    }

    fun saveJournalContent(
        journalId: Long,
        pageIndex: Int,
        onDataSaved: ((isSuccess: Boolean) -> Unit)?
    ) {
        saveCanvasToEntFile(journalId, pageIndex, onDataSaved)
    }

    fun clearText() = scope.launch {
        if (mEngineState.value != null) {
            engine().clear(arrayOf(DataType.TEXT))
        }
    }

    fun replaceText(list: List<String>, journalContentInfo: JournalContentInfo?) = scope.launch {
        if (mEngineState.value != null && list.isNotEmpty() && journalContentInfo != null) {
            val top = when (journalContentInfo.templateData?.layoutType) {
                1 -> 0f
                2 -> journalContentInfo.statusBarHeight
                else -> journalContentInfo.statusBarHeight + journalContentInfo.topBarHeight
            }

            engine().clear(arrayOf(DataType.TEXT))
            val templateTextList = mutableListOf<TemplateText>()
            journalContentInfo.templateData?.elements?.filter { it.type == ElementType.TEXT }
                ?.forEachIndexed { index, element ->
                    templateTextList.add(
                        TemplateText(
                            list.getOrNull(index).orEmpty(),
                            element.textColor,
                            getContentRectF(element.size, element.position, top)
                        )
                    )
                }
            addText(templateTextList)
            pasteAnInvisibleText()
            saveJournalContent(
                journalId = journalContentInfo.journalId,
                pageIndex = journalContentInfo.pageIndex
            ) {}
        }
    }

    override fun rendToScreen(rectF: RectF, state: Int) {
        super.rendToScreen(rectF, state)
        listeners.forEach { it.rendToScreen(rectF, state) }
    }

    suspend fun isContentEmpty(): Boolean {
        return engine().isContentEmpty
    }

    fun addText(text: String) = scope.launch(Dispatchers.Main) {
        finishSelect()
        setSpannedEditing(true)
        val array = TextTools.getDefaultTextSize()
        val ratio = width / 1000f
        array[0] = (array[0] * ratio).toInt()
        array[1] = (array[1] * ratio).toInt()
        val textLocation = getTextLocation(array[0], array[1])
        engine().addText(text, textLocation)
        engine().enableSelectMode(true)
        engine().setPreViewMode(false)
    }

    private suspend fun getTextLocation(width1: Int, height1: Int): RectF {
        var width = width1
        var height = height1
        val scale = engine().scaleInfo.scale
        width = (width * scale).toInt()
        height = (height * scale).toInt()
        val realRect =
            RectF(0f, 0f, this.width.toFloat(), this.height.toFloat())
        val centerX = realRect.centerX()
        val centerY = realRect.centerY()

        var left: Float
        var top: Float
        val visibleWidth = realRect.width()
        val visibleHeight = realRect.height()

        if (visibleWidth >= width) {
            left = centerX - width / 2f
        } else {
            left = realRect.left
            width = realRect.width().toInt()
        }
        left = max(realRect.left, left)
        if (visibleHeight >= height) {
            top = centerY - height / 2f
        } else {
            top = realRect.top
            height = realRect.height().toInt()
        }
        top = max(realRect.top, top)
        val locationRectF = RectF(left, top, left + width, top + height)
        return locationRectF
    }

    override fun onSelectFinished() {
        isEditSelect = false
        onShowEditMenuListener?.onEditSelect(isEditSelect)
    }

    companion object {
        private const val TAG = "SuniaDrawView"
        private const val THUMBNAIL_SAVE_SCALE = 0.3f
    }

    interface OnShowEditMenuListener {
        fun onShowImageEditMenu(x: Float, y: Float)
        fun onHintEditMenu()
        fun onLassoRotateAngle(rotate: LassoRotateEvent?)
        fun onEditSelect(isSelect: Boolean)
    }

    interface Listener {
        @Deprecated("缩放功能已经自己实现，不再用安格斯的")
        fun onScale(scaleInfo: ScaleInfo)
        fun onDataLoaded(drawStrokeCount: Int, drawStrokeRect: RectF, isSuccess: Boolean)
        fun onVersionUpgrade(versionCode: Int)
        fun onStepChanged(stepType: Int, stepId: Int, stepName: String?, canUndo: Boolean, canRedo: Boolean, drawStrokeCount: Int, drawStrokeRect: RectF)
        fun onSetVisibleSizeFinish(width: Int, height: Int)
        fun onCanvasHandleFinish()
        fun onLastDrawDataRect(lastDrawRect: RectF)
        fun onTouchPositionUpdate(position: Offset?)
        fun onSizeChangeStart(w: Int, h: Int, oldw: Int, oldh: Int)
        fun onInitEngineFinish()
        fun rendToScreen(rectF: RectF, state: Int)
        fun onSaveCompleted(pageIndex: Int)
    }
}