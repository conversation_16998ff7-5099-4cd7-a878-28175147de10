package com.tcl.ai.note.handwritingtext.vm

import android.os.SystemClock
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.sunia.penengine.sdk.data.ListCurve
import com.sunia.penengine.sdk.operate.edit.BeautyInfo
import com.sunia.penengine.sdk.operate.edit.RecoStrokeDatas
import com.sunia.singlepage.sdk.InkFunc
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.beautify.BeautifyEventType
import com.tcl.ai.note.handwritingtext.beautify.StrokeBeautifyEventManager
import com.tcl.ai.note.handwritingtext.data.TclStroke
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import com.tcl.strokebeaut.Handwriting
import com.tcl.strokebeaut.HandwritingBeautifier
import com.tcl.strokebeaut.Mode
import com.tcl.strokebeaut.Point
import com.tcl.strokebeaut.Stroke
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference
import java.util.LinkedList
import kotlin.collections.indices
import kotlin.let
import kotlin.onFailure
import kotlin.ranges.until
import kotlin.runCatching
import kotlin.text.format
import kotlin.text.split
import kotlin.text.toFloat

/**
 *  author : junze.liu
 *  date : 2025-07-29 14:34
 *  description : 笔迹美化
 *  笔迹美化的业务逻辑
 *  对齐一期的笔迹美化逻辑：抬手后才将数据传递给鸿鹄
 *  1、如果用户一直在写，书写时间超过5s，则强制把这5s内写的轨迹一起美化
 *  2、如果抬手超过500ms，则触发美化
 *  3、笔迹美化适配按键擦除，美化触发前，擦除轨迹，则剩余轨迹不再美化（产品接受该现象），
 *    擦除完，立马写，需确保传递给SDK的数据是完整的（没有脏数据，把擦除轨迹误传过去会导致异常）
 *  4、笔迹美化适配画布缩放（画布缩放会缩放时也会有事件过来，但由于没有真正新增轨迹，所以不会触发BEAUTIFY，导致没有触发美化，故需要在UP时主动触发一下美化任务，
 *    如果是正常书写，UP时发出的延时任务在后面的逻辑就会被取消掉了，不会影响到正常的业务逻辑）
 *
 * 增加需求：用户持续不抬笔书写 若用户持续不抬笔书写、 并连续书写长线笔迹超过15秒，则用户抬笔后不触发美化
 * （可根据event中的downTime来确定 down 和up 是否属于同一批次的事件）
 *
 */
class StrokeBeautifyViewModel : ViewModel() {

    private data class WritingState(
        var eventDownTime: Long = 0L,// 每次按下的时间节点，用于判断是否属于同一批次事件
        var lastStrokeEndTime: Long = 0, //上一笔抬手时 时间节点
        var continuousStartTime: Long = 0, //持续书写时每次按下的时间节点
        var isContinuous: Boolean = false, //标注是否是持续书写 上一笔up 和下一笔down时，时间间隔超过500ms，则为false（不是持续书写）
        var isForceBeautifyTriggered: Boolean = false, //标记触发了强制美化
        var isBeautifyTimeout: Boolean = false //标记美化超时，取消美化
    )

    private val writingState = WritingState()

    private val strokeLock = Any()

    private val pendingStrokes = LinkedList<TclStroke>() // 待美化笔迹队列
    private var beautifyJob: Job? = null

    // 美化状态
    private var isBeautifying = false

    private var inkFuncWeakRef: WeakReference<InkFunc>? = null


    init {
        initHandwritingBeautifier()
        observeStrokeBeautifyEvents()
    }


    //上一次抬手后距离下一次抬手时间间隔超过600ms，则触发美化，持续书写超过5s 则强制触发美化
    private fun observeStrokeBeautifyEvents() {
        viewModelScope.launch {
            StrokeBeautifyEventManager.strokeBeautifyEvents.collect { event ->
                when (event.type) {
                    BeautifyEventType.ACTION_DOWN -> {
                        //可根据event中的downTime来确定 是否属于同一批次的事件，来决定后续事件是否上报SDK
                        onActionDown(SystemClock.uptimeMillis(), event.downTime)
                    }

                    BeautifyEventType.ACTION_UP -> {
                        onActionUp(event.downTime)
                    }

                    BeautifyEventType.BEAUTIFY -> {
                        val curveList = event.curveList
                        val currentInkFunc = event.inkFunc
                        // 仅当inkFunc发生变化时更新弱引用
                        if (inkFuncWeakRef?.get() != currentInkFunc) {
                            inkFuncWeakRef = WeakReference(currentInkFunc)
                        }
                        Logger.d(TAG, "observeStrokeBeautifyEvents, add stroke")
                        doStrokeBeautify(curveList)
                    }
                }
            }
        }
    }

    /**
     * 当用户开始新笔迹时调用（按下事件）
     * @param startTime 新笔迹的开始时间
     */
    private fun onActionDown(startTime: Long, downTime: Long) {
        Logger.d(TAG, "observeStrokeBeautifyEvents, ACTION_DOWN")
        writingState.eventDownTime = downTime
        beautifyJob?.cancel()
        beautifyJob = null
        Logger.v(
            TAG,
            "onActionDown, forceBeautifyTriggered:${writingState.isForceBeautifyTriggered}, time difference:${startTime - writingState.lastStrokeEndTime}"
        )
        if (startTime - writingState.lastStrokeEndTime > IDLE_THRESHOLD_MS || writingState.isForceBeautifyTriggered) {
            // 间隔超过600ms，开始新的连续书写周期
            writingState.isContinuous = true
            writingState.continuousStartTime = startTime
        } else {
            Logger.v(
                TAG,
                "onActionDown, Continuous writing (the previous up is less than 500ms from the next down time)"
            )
        }
        writingState.isForceBeautifyTriggered = false
    }

    /**
     * 抬手时处理的业务逻辑
     */
    private fun onActionUp(downTime: Long) {
        val isSameBatchEvent = downTime == writingState.eventDownTime
        Logger.d(TAG, "onActionUp, ACTION_UP, same batch of events: $isSameBatchEvent")
        val time = SystemClock.uptimeMillis()
        writingState.lastStrokeEndTime = time
        //用户持续不抬笔书写 若用户持续不抬笔书写、 并连续书写长线笔迹超过15秒，则用户抬笔后不触发美化
        if (isSameBatchEvent && time - writingState.eventDownTime > CONTINUOUS_WRITING_TIMEOUT_MS) {
            writingState.isBeautifyTimeout = true
            writingState.continuousStartTime = time
        } else {
            //500ms延迟任务（如果期间没有新笔迹则触发美化）,这里逻辑是为了兼容缩放，缩放时也会有事件过来，
            // 但由于没有真正新增轨迹，所以不会触发BEAUTIFY，导致没有触发美化，这里主动触发一下，
            // 如果是正常书写，这里发出的延时任务在后面的逻辑就会被取消掉了
            scheduleBeautifyJob()
        }
    }


    private fun doStrokeBeautify(curveList: ListCurve?) {
        Logger.d(TAG, "doStrokeBeautify curveList: $curveList")
        if (writingState.isBeautifyTimeout) {
            writingState.isBeautifyTimeout = false
            Logger.w(
                TAG,
                "doStrokeBeautify, always writing for more than 15 seconds, timeout, beautification cancel"
            )
            return
        }

        curveList?.let { list ->
            Logger.d(TAG, "doStrokeBeautify list: ${list.size()}")
            val strokes: MutableList<Stroke> = mutableListOf()
            for (index in 0 until list.size()) {
                val curve = list.get(index)
                val points: MutableList<Point> = mutableListOf()
                for (p in curve.savePoints) {
                    points.add(Point(p.x.toDouble(), p.y.toDouble(),
                        "${p.sizeFactor},${p.pressure},${p.tilt},${p.orientation},${p.speed},${p.time};"))
                }
                strokes.add(Stroke(points))
                val endTime = SystemClock.uptimeMillis()
                addStroke(TclStroke(curve.dataId, Stroke(points), endTime))
            }
        }
    }

    /**
     * 当用户完成一笔时调用（抬手后回调）
     * @param stroke 完成的笔迹数据
     */
    private fun addStroke(stroke: TclStroke) {
        Logger.v(TAG, "addStroke id:${stroke.strokeId}")
        pendingStrokes.add(stroke)
        Logger.v(TAG, "addStroke pendingStrokes.size:${pendingStrokes.size}")
        // 是否开始新的连续书写
        if (!writingState.isContinuous) {
            writingState.isContinuous = true
        }

        //强制美化条件（持续书写5秒后抬手）
        checkForcedBeautification(stroke)

        //500ms延迟任务（如果期间没有新笔迹则触发美化）
        scheduleBeautifyJob()
    }


    /**
     * 检查是否满足强制美化条件
     */
    private fun checkForcedBeautification(currentStroke: TclStroke) {
        // 计算持续书写时间（从第一笔开始到当前笔结束）
        val continuousDuration = currentStroke.endTime - writingState.continuousStartTime
        Logger.v(
            TAG,
            "checkForcedBeautification, continuousDuration:$continuousDuration, isContinuous:${writingState.isContinuous}"
        )

        if (writingState.isContinuous && continuousDuration >= CONTINUOUS_WRITING_THRESHOLD_MS) {
            // 强制触发美化
            Logger.i(
                TAG,
                "checkForcedBeautification, continuous writing for more than 5 seconds, forced beautification"
            )
            processPendingStrokes()

            writingState.isContinuous = false
            writingState.isForceBeautifyTriggered = true
        }
    }


    // 统一调度美化任务
    private fun scheduleBeautifyJob() {
        Logger.d(TAG, "scheduleBeautifyJob, Cancel last beautification task, delay 500ms restart the beautification task")
        beautifyJob?.cancel()
        beautifyJob = viewModelScope.launch {
            // 确保延迟期间不会丢失最新状态
            val currentPendingSize: Int
            synchronized(strokeLock) {
                currentPendingSize = pendingStrokes.size
            }

            // 只有有待处理笔迹时才执行延迟
            if (currentPendingSize > 0) {
                delay(IDLE_THRESHOLD_MS)

                synchronized(strokeLock) {
                    // 再次检查状态，防止在延迟期间状态变化
                    if (pendingStrokes.isNotEmpty() && !writingState.isBeautifyTimeout) {
                        Logger.d(
                            TAG,
                            "500ms time is up, perform beautification tasks, Triggering beautification for ${pendingStrokes.size} strokes"
                        )
                        processPendingStrokes()
                    }
                }
            }
        }
    }


    private fun processPendingStrokes() {
        synchronized(strokeLock) {
            if (writingState.isBeautifyTimeout) {
                Logger.w(TAG, "processPendingStrokes, Beautify skipped due to timeout")
                pendingStrokes.clear()
                writingState.isBeautifyTimeout = false
                return
            }

            if (pendingStrokes.isEmpty()) return

            try {
                val strokesToProcess = pendingStrokes.toList()
                pendingStrokes.clear()

                Logger.d(TAG, "processPendingStrokes ${strokesToProcess.size} strokes")

                val result = triggerStrokeBeautify(strokesToProcess)
                Logger.i(TAG, "processPendingStrokes,send beautify results to SDK, result: $result")
                inkFuncWeakRef?.get()?.iRecoOptFunc?.setCurveBeautyInfo(result)

            } catch (e: Exception) {
                Logger.e(TAG, "processPendingStrokes, Beautify processing exception:${e}")
            }
        }
    }


    /**
     * 实际处理笔迹美化逻辑
     */
    private fun triggerStrokeBeautify(strokes: List<TclStroke>): MutableList<BeautyInfo> {
        Logger.v(TAG, "triggerStrokeBeautify, -------logic of handwriting beautification-------start")
        val time = System.currentTimeMillis()
        val strokeList: MutableList<TclStroke> = mutableListOf()
        strokeList.addAll(strokes)
        val list: MutableList<Stroke> = mutableListOf()
        for (stroke in strokeList) {
            list.add(stroke.stroke)
        }
        Logger.v(TAG, "triggerStrokeBeautify stroke size ${list.size}")

        val handwriting = Handwriting(list)

        Logger.v(
            TAG,
            "triggerStrokeBeautify, handwritingBeautifierStateFlow:${handwritingBeautifierStateFlow.value}"
        )

        val beautifier = handwritingBeautifierStateFlow.value

        val beautyInfos: MutableList<BeautyInfo> = mutableListOf()

        var beautifiedHandwriting = beautifier?.beautify(handwriting)
        if (beautifiedHandwriting != null) {
            serializeBeautyInfo(beautifiedHandwriting.beautified, beautyInfos)
        } else {
            Logger.w(TAG, "Beautified handwriting is null")
        }
        if (beautyInfos.size == strokeList.size) {
            for (i in beautyInfos.indices) {
                beautyInfos[i].recoStroke.recoId = strokeList[i].strokeId
            }
        } else {
            Logger.w(TAG, "triggerStrokeBeautify size not the same")
        }
        Logger.v(TAG, "triggerStrokeBeautify beautyInfos:${beautyInfos.size}")
        Logger.v(
            TAG,
            "triggerStrokeBeautify, -------logic of handwriting beautification-------end, use time:${System.currentTimeMillis() - time}"
        )
        return beautyInfos
    }


    private fun serialize(handwriting: Handwriting, indent: String = ""): String {
        var serialized = "${indent}{"
        for (stroke in handwriting.strokes()) {
            serialized += "\n${serialize(stroke, "$indent  ")}"
        }
        serialized += "\n${indent}}"
        return serialized
    }

    private fun serialize(stroke: Stroke, indent: String = ""): String {
        val points = stroke.points()
        var serialized = "${indent}{"
        for (i in points.indices) {
            if (i % 6 == 0) {
                serialized += "\n${indent}  "
            }
            serialized += serialize(points[i])
            serialized += if (i == points.size - 1) "" else ", "
        }
        serialized += "\n${indent}}"
        return serialized
    }

    private fun serialize(point: Point): String {
        return "{ ${"%.3f".format(point.x)}, ${"%.3f".format(point.y)} }"
    }

    private fun serializeBeautyInfo(
        handwriting: Handwriting?,
        beautyInfos: MutableList<BeautyInfo>
    ) {
        if (handwriting == null) {
            Logger.e(TAG, "serializeBeautyInfo, handwriting is null")
            return
        }
        for (stroke in handwriting.strokes()) {
            val info = BeautyInfo()
            val points = stroke.points()
            Logger.d(TAG, "serializeBeautyInfo points.size:${points.size}")
            info.pointCount = points.size
            info.vecPressure = FloatArray(points.size)
            info.vecTilt = FloatArray(points.size)
            info.vecOrientation = FloatArray(points.size)
            info.vecSpeed = FloatArray(points.size)
            info.vecTime = LongArray(points.size)
            val datas = RecoStrokeDatas()
            datas.recoId = 0
            datas.xArr = FloatArray(points.size)
            datas.yArr = FloatArray(points.size)
            datas.wArr = FloatArray(points.size)
            for (i in points.indices) {
                datas.xArr[i] = points[i].x.toFloat()
                datas.yArr[i]= points[i].y.toFloat()
                val metadata = points[i].metadata
                var size = 0.5f
                metadata.let {item->
                    val subItem = item.split(";")
                    if (subItem.isNotEmpty()) {
                        val subs = subItem[0].split(",")
                        if (subs.size >= 6) {
                            size = subs[0].toFloat()
                            info.vecPressure[i] = subs[1].toFloat()
                            info.vecTilt[i] = subs[2].toFloat()
                            info.vecOrientation[i] = subs[3].toFloat()
                            info.vecSpeed[i] = subs[4].toFloat()
                            info.vecTime[i] = subs[5].toLong()
                        }
                    }
                }
                datas.wArr[i] = size
                info.vecPressure[i] = 1f
            }
            info.recoStroke = datas
            beautyInfos.add(info)
        }
    }

    fun setBeautifyParam(smooth: Double, align: Double, threshold: Double) {
        Logger.d(TAG, "setBeautifyParam, smooth:$smooth, align:$align, threshold:$threshold")
        smoothValue = smooth
        alignValue = align
        thresholdValue = threshold
        handwritingBeautifierStateFlow.value = null
        initHandwritingBeautifier()
    }

    fun getBeautifyParam(): Triple<Double, Double, Double> {
        return Triple(smoothValue, alignValue, thresholdValue)
    }

    override fun onCleared() {
        super.onCleared()
        pendingStrokes.clear()
        writingState.isContinuous = false
        writingState.isForceBeautifyTriggered = false
        isBeautifying = false
        inkFuncWeakRef = null
    }


    companion object {

        private val TAG = "StrokeBeautifyViewModel"

        private val IDLE_THRESHOLD_MS = 500L

        //持续书写 超过5s，强制触发美化
        private val CONTINUOUS_WRITING_THRESHOLD_MS = 5000L

        //用户持续不抬笔书写 若用户持续不抬笔书写、 并连续书写长线笔迹超过15秒，则用户抬笔后不触发美化
        private val CONTINUOUS_WRITING_TIMEOUT_MS = 15000L

        //产品确定后的默认美化参数 0.3  1.0  0.8
        var smoothValue: Double = 0.3
        var alignValue: Double = 1.0
        var thresholdValue: Double = 0.8


        private val handwritingBeautifierStateFlow = MutableStateFlow<HandwritingBeautifier?>(null)
        fun initHandwritingBeautifier() {
            Logger.i(
                TAG,
                "initHandwritingBeautifier, Initialization Beautification Engin, smoothValue:$smoothValue, alignValue:$alignValue, thresholdValue:$thresholdValue"
            )
            if (handwritingBeautifierStateFlow.value != null) {
                return
            }
            GlobalContext.applicationScope.launchIO {
                // 初始化失败，无限重试
                while (handwritingBeautifierStateFlow.value == null) {
                    val startMillis = System.currentTimeMillis()
                    runCatching {
                        handwritingBeautifierStateFlow.value = HandwritingBeautifier.Builder(
                            GlobalContext.instance
                        )
                            .setSmoothStrength(smoothValue)//平滑强度：较高的值会更平滑，但可能会丢失细节，范围：0.1~1.0，默认0.5
                            .setAlignStrength(alignValue) // 对齐强度：较高的值允许更多的垂直移动，范围：0.1~1.0，默认0.5
                            .setThresholdStrength(thresholdValue)//阈值强度：翻倍的阈值强度，更高的值会检索更多的样本，但可能会丢失原始形状，范围：0.1~1.0，默认0.5
                            .setMorphSteps(10)
                            .setMode(Mode.LOCAL)
                            .build()
                        Logger.i(
                            TAG,
                            "init, success. time: ${System.currentTimeMillis() - startMillis}"
                        )
                    }.onFailure {
                        // 初始化失败，强制重载配置文件
                        HandwritingBeautifier.Builder(GlobalContext.instance).build().reload(true)
                        Logger.w(TAG, "init, error: ${it.message}")
                    }
                    // 延时5秒，避免频繁初始化
                    delay(5000)
                }
            }
        }
    }


}