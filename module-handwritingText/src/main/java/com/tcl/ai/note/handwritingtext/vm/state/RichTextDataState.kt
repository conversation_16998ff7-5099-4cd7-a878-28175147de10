package com.tcl.ai.note.handwritingtext.vm.state

import com.tcl.ai.note.handwritingtext.database.entity.BgMode
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent
import com.tcl.ai.note.handwritingtext.database.entity.Skin
import com.tcl.ai.note.handwritingtext.richtext.data.RichTextStyleEntity
import com.tcl.ai.note.handwritingtext.utils.BackgroundColorManager
import com.tcl.ai.note.utils.toArgbLong
import com.tcl.ai.note.utils.toComposeColor

data class RichTextDataState(
    val noteId: Long? = null,
    val title: String = "", // 标题内容
    val content: String = "", // 富文本纯文本内容
    val richTextStyleEntity: RichTextStyleEntity = RichTextStyleEntity(), // 富文本样式结构
    val images: List<EditorContent.ImageBlock> = emptyList(), // 图片列表
    var audios: List<EditorContent.AudioBlock> = emptyList(),   // 音频列表
    var bgMode: BgMode = BgMode.none,
    var bgColor: Long = Skin.defColor,
    var categoryId: Long = 1, // 未分类
    val isNewNote: Boolean = false, // 标识是否为新建笔记
    val isSaved:Boolean = false, // 是否执行过保存操作
    var isShow: Boolean = false, // 是否显示在首页
    var isTopPopupShowing: Boolean = false // 笔记详情页是否显示顶部菜单栏弹窗
){
    // 背景颜色 包含一期到二期的适配，更准确
    val displayBgColor: Long
        get() = BackgroundColorManager.mapV1ToV2Color(bgColor.toComposeColor()).toArgbLong()
}