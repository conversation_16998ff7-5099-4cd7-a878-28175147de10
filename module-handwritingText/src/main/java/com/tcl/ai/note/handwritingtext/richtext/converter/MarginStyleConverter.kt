package com.tcl.ai.note.handwritingtext.richtext.converter

import android.graphics.Paint
import android.text.Editable
import android.text.Spannable
import android.text.Spanned
import android.widget.EditText
import com.tcl.ai.note.handwritingtext.richtext.spans.AreLeadingMarginSpan
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils
import com.tcl.ai.note.handwritingtext.richtext.utils.DisplayUtils.sp2px
import com.tcl.ai.note.utils.Logger
import java.util.regex.Pattern
import kotlin.math.ceil

class MarginLeft(internal val mLevel: Int)

/**
 * 左右缩进样式转换器
 */
object MarginStyleConverter {

    private const val DefaultFontSize = 12 //sp
    //参考文本，用于计算两个汉字的宽度
    private const val REFERENCE_TEXT: String = "汉字"

    private var sMarginLeftPattern: Pattern? = null

    fun matchMarginLeftStyle(text: Editable, style: String) {
        val m = getMarginLeftPattern().matcher(style)
        if (m.find()) {
            val marginValue = m.group(1)
            var level = 0
            if (marginValue != null) {
                level = parseMarginLeftLevel(marginValue)
            }
            if (level > 0) {
                start(text, MarginLeft(level))
            }
        }
    }

    fun start(text: Editable, mark: Any?) {
        val len = text.length
        text.setSpan(mark, len, len, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
    }

    /**
     * 处理缩进
     * @param text
     */
     fun endMarginLeftStyle(text: Editable) {
        val marginLeft = getLast(text, MarginLeft::class.java)
        if (marginLeft != null) {
            val leadingMarginSpan = AreLeadingMarginSpan()
            leadingMarginSpan.level = marginLeft.mLevel
            Logger.d("MarginLeft: level=${marginLeft.mLevel}")
            setSpanFromMark(text, marginLeft, leadingMarginSpan)
        }
    }

    private fun <T> getLast(text: Spanned, kind: Class<T>?): T? {
        /*
         * This knows that the last returned object from getSpans()
         * will be the most recently added.
         */
        val objs = text.getSpans(0, text.length, kind)

        return if (objs.size == 0) {
            null
        } else {
            objs[objs.size - 1]
        }
    }

    private fun setSpanFromMark(text: Spannable, mark: Any?, vararg spans: Any?) {
        val where = text.getSpanStart(mark)
        text.removeSpan(mark)
        val len = text.length
        if (where != len) {
            for (span in spans) {
                text.setSpan(span, where, len, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }
    }

    private fun getMarginLeftPattern(): Pattern {
        if (sMarginLeftPattern == null) {
            sMarginLeftPattern =
                Pattern.compile("(?:\\s+|\\A)margin-left\\s*:\\s*(\\S*)\\b")
        }
        return sMarginLeftPattern!!
    }

    /**
     * 解析margin-left值并转换为缩进级别
     *
     * @param marginValue like "2em", "4em", etc.
     * @return 缩进级别
     */
    private fun parseMarginLeftLevel(marginValue: String): Int {
        try {
            if (marginValue.endsWith("em")) {
                val valueStr = marginValue.substring(0, marginValue.length - 2)
                val emValue = valueStr.toFloat()
                // 每2em对应一个缩进级别
                return (emValue / 2).toInt()
            } else if (marginValue.endsWith("px")) {
                val valueStr = marginValue.substring(0, marginValue.length - 2)
                val pxValue = valueStr.toInt()
                // 假设每40px对应一个缩进级别（大约两个汉字的宽度）
                return pxValue / 40
            }
        } catch (e: NumberFormatException) {
            // 解析失败，返回0
        }
        return 0
    }

    /**
     * 计算两个汉字的宽度,只考虑默认字体大小
     *
     * @return 两个汉字的像素宽度
     */
     fun calculateChineseCharacterWidth(): Int {
        val paint = Paint()
        // 设置默认文本大小，可以根据需要调整
        paint.textSize = sp2px(DefaultFontSize.toFloat()) //
        paint.isAntiAlias = true

        // 测量两个汉字的宽度
        val textWidth = paint.measureText(REFERENCE_TEXT)

        return ceil(textWidth.toDouble()).toInt()
    }
    /**
     * 计算一个屏幕宽度最多可以容纳多少个缩进级别
     * 只考虑当前行的文本宽度
     */
    fun calculateMaxLevelPerLine(editText: EditText): Int {
        val screenWidth = DisplayUtils.getScreenWidth()
        // 减去左右的padding
        val padding = editText.paddingLeft + editText.paddingRight
        val availableWidth = screenWidth - padding

        // 获取当前光标所在行
        val layout = editText.layout
        val selectionStart = editText.selectionStart
        if (layout == null || selectionStart < 0) {
            // 如果没有布局或光标位置无效，返回默认值
            return 1
        }

        // 获取当前行号
        val currentLine = layout.getLineForOffset(selectionStart)

        // 获取当前行的起始和结束位置
        val lineStart = layout.getLineStart(currentLine)
        val lineEnd = layout.getLineEnd(currentLine)

        // 获取当前行的文本
        val currentLineText = editText.text.subSequence(lineStart, lineEnd).toString()

        // 测量当前行文本宽度
        val paint = editText.paint
        val currentLineTextWidth = paint.measureText(currentLineText)

        // 计算剩余可用宽度
        val leftWidth = availableWidth - currentLineTextWidth.toInt()

        // 计算每个缩进级别需要的宽度（两个汉字的宽度）
        val characterWidth = calculateChineseCharacterWidth()

        // 计算最多可以容纳的缩进级别
        val maxLevel = leftWidth / characterWidth

        // 返回结果，确保至少有1级缩进
        return maxLevel.coerceAtLeast(1)
    }
}