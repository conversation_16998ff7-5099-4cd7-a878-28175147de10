package com.tcl.ai.note.handwritingtext.sunia.demo

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Rect
import android.graphics.RectF
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.compose.ui.unit.IntSize
import com.sunia.penengine.sdk.data.CurveDataArray
import com.sunia.penengine.sdk.data.ListCurve
import com.sunia.penengine.sdk.data.RecoDatasInfoArray
import com.sunia.penengine.sdk.operate.canvas.IScreen
import com.sunia.penengine.sdk.operate.canvas.ScreenInfo
import com.sunia.penengine.sdk.operate.edit.LayerMode
import com.sunia.penengine.sdk.operate.touch.KspMotionEvent
import com.sunia.penengine.sdk.operate.touch.OperatorMode
import com.sunia.penengine.sdk.operate.touch.PenProp
import com.sunia.penengine.sdk.operate.touch.PenType
import com.sunia.singlepage.sdk.InitInfo
import com.sunia.singlepage.sdk.InkFactory
import com.sunia.singlepage.sdk.InkFunc
import com.sunia.singlepage.sdk.InkSDK
import com.sunia.singlepage.sdk.listener.ICanvasChangedListener
import com.sunia.singlepage.sdk.listener.IEngineVerifyListener
import com.sunia.singlepage.sdk.listener.IInkWriteListener
import com.sunia.singlepage.sdk.param.LayoutMode
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.handwritingtext.sunia.convert.SuniaMotionEventUtils
import com.tcl.ai.note.sunia.authorize.SuniaVerifyHandler
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.runBlocking
import kotlin.math.ceil
import kotlin.math.floor
import kotlin.math.roundToInt

@Deprecated("SuniaDrawView代替")
class SuniaSingleDemo(
    context: Context
) : View(context) {

    private var inkFunc: InkFunc? = null
    private val coroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private val inkScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var screenSize = IntSize(0, 0)
    private var bitmap: Bitmap? = null
    private var screen: IScreen? = null

    private val canvasChangeListener = object : ICanvasChangedListener {
        override fun onCanvasDataChanged(p0: RectF?) {
            inkScope.launchIO {
                val rectF = RectF(p0)
                val srcRect = Rect(
                    floor(rectF.left).roundToInt(),
                    floor(rectF.top).roundToInt(),
                    ceil(rectF.right).roundToInt(),
                    ceil(rectF.bottom).roundToInt(),
                )
                val screenInfo = ScreenInfo()
                screenInfo.screen = screen
                screenInfo.srcRect = srcRect
                screenInfo.dstRect = srcRect
                Logger.v(TAG, "onCanvasDataChanged")
                inkFunc?.rendToScreen(rectF, screenInfo)
                postInvalidate()
            }
        }

        override fun onCanvasStateChanged(p0: Int) {
        }

        override fun onSetVisibleSizeFinish() {
            Logger.v(TAG, "onSetVisibleSizeFinish")
        }

        override fun onCanvasHandleFinish() {
        }
    }

    private val inkWriteListener = object : IInkWriteListener {
        override fun onStepChanged(p0: Int, p1: Int, p2: String?, p3: Boolean, p4: Boolean) {
        }

        override fun onStepFinished() {
        }

        override fun onDataChanged(p0: Int) {
        }

        override fun onDataUpdate(p0: Int, p1: CurveDataArray?, p2: Boolean) {
        }

        override fun onCurveWrite(p0: Int, p1: ListCurve?, p2: Boolean) {
        }

        override fun onBeautyAnimatioinError(p0: IntArray?, p1: Int) {
        }

        override fun onRecoDataUpdate(p0: Int, p1: RecoDatasInfoArray?) {
        }

        override fun onMaxLimit(p0: Int) {
        }

        override fun obtainKspMotionEvent(p0: MotionEvent?): KspMotionEvent? {
            Logger.v(TAG, "obtainKspMotionEvent; $p0")
            return SuniaMotionEventUtils.obtain(p0)
        }

        override fun onError(p0: Int) {

        }
    }

    init {
        coroutineScope.launchIO {
            InkSDK.init(
                GlobalContext.instance,
                SuniaVerifyHandler.suniaVerifyStateFlow.filterNotNull().first()
            )
            InkSDK.logLevel = 3
            inkFunc = InkFactory.newInkFunc()
            inkFunc!!.apply {
                init(
                    InitInfo(LayoutMode.INFINITE_VERTICAL, LayerMode.layerModel1, false, 0)
                ) { p0, p1 ->
                    Logger.v(TAG, "inkFunc init: $p0, $p1")
                }
                setInkSize()
                setCanvasStateListener {

                }
                setCanvasChangedListener(canvasChangeListener)
                setInkWriteListener(inkWriteListener)
                setPenProp(
                    PenProp(
                        PenType.PEN_BALLPOINT.value,
                        Color.BLACK,
                        3f,
                        100,
                    )
                )
            }
        }
    }

    private fun setInkSize() {
        Logger.v(TAG, "setInkSize")
        inkFunc?.apply {
            setVisibleSize(screenSize.width, screenSize.height)
            screen?.destroyScreen()
            bitmap?.let {
                if (it.width != screenSize.width || it.height != screenSize.height) {
                    it.recycle()
                    val tmpBitmap = Bitmap.createBitmap(
                        screenSize.width,
                        screenSize.height,
                        Bitmap.Config.ARGB_8888
                    )
                    bitmap = tmpBitmap.copy(Bitmap.Config.HARDWARE, false)
                    tmpBitmap.recycle()
                }
            }
            bitmap?.let {
                createScreen(it)?.let { screens ->
                    screen = screens[0]
                    Logger.v(TAG, "createScreen: $screen")
                    Logger.v(TAG, "createScreen bitmap: $bitmap")
                }
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        inkFunc?.let {
            return it.onTouchEvent(event)
        }
        return super.onTouchEvent(event)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        Logger.v(TAG, "onSizeChanged, $w, $h")
        screenSize = IntSize(w, h)
        setInkSize()
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        bitmap?.let {
            canvas.drawBitmap(it, 0f, 0f, null)
        }
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        inkFunc?.destroy()
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
    }

    companion object {
        private const val TAG = "SuniaSingleDemo"
    }
}