package com.tcl.ai.note.handwritingtext.bean

import android.graphics.Paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import com.tcl.ai.note.handwritingtext.ui.edit.meunbar.popup.EraserMode
import kotlinx.serialization.Serializable

@Serializable
data class StrokeStyle(
    val width: Float = 5f,
    val color: Long = 0xFF000000,
    val drawMode: DrawMode = DrawMode.PEN,
    val doodlePen: DoodlePen = DoodlePen.FountainPen,
    val eraserMode: EraserMode = EraserMode.AREA, // 橡皮擦模式: 点擦除或线条擦除
) {
    fun toComposePaint() = androidx.compose.ui.graphics.Paint().apply {
        color = Color(<EMAIL>)
        Stroke(
            width = width,
            cap = doodlePen.strokeCap.toStrokeCap(),
            join = doodlePen.strokeJoin.toStrokeJoin(),
        )
    }

    fun toPaint() = Paint().apply {
        val intColor = <EMAIL>()
        val musk = 0xff
        val a = (intColor ushr 24) and musk
        val r = (intColor ushr 16) and musk
        val g = (intColor ushr 8) and musk
        val b = intColor and musk
        color = android.graphics.Color.argb(a, r, g, b)
        strokeWidth = width
        style = Paint.Style.STROKE
        strokeCap = doodlePen.strokeCap
        strokeJoin = doodlePen.strokeJoin
    }
}