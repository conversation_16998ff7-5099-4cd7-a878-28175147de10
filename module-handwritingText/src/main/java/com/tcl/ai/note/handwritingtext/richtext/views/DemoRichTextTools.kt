package com.tcl.ai.note.handwritingtext.richtext.views

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.compose.runtime.getValue
import com.tcl.ai.note.handwritingtext.vm.event.RichTextOperateEvent
import com.tcl.ai.note.handwritingtext.vm.text.RichTextToolBarViewModel

/**
 * 富文本工具栏Demo
 */
@Composable
fun DemoRichTextTools(modifier: Modifier = Modifier, onSave: () -> Unit= {},
                      richTextViewModel: RichTextToolBarViewModel = hiltViewModel()
) {
    val toolBarState by richTextViewModel.toolBarState.collectAsStateWithLifecycle()
    val scrollState = rememberScrollState()
    // 富文本所有功能按钮示例，全部用Button代替（生产环境可改成IconButton/自定义控件/ImageView等）
    Row( modifier = modifier
        .horizontalScroll(scrollState)
        .padding(8.dp)) {
        Button(onClick = {
            onSave()
        }) {
            Text("save")
        }
        Button(onClick = {
            richTextViewModel.triggerOperateActionEvent(RichTextOperateEvent.Undo)
        }, colors = ButtonDefaults.buttonColors(
            containerColor = if (toolBarState.isCanUndo) Color.Yellow else Color.Gray,
            contentColor = if (toolBarState.isCanUndo) Color.Black else Color.White
        )) {Text("undo") }
        Button(onClick = {
            richTextViewModel.triggerOperateActionEvent(RichTextOperateEvent.Redo)
        }, colors = ButtonDefaults.buttonColors(
            containerColor = if (toolBarState.isCanRedo) Color.Yellow else Color.Gray,
            contentColor = if (toolBarState.isCanRedo) Color.Black else Color.White
        )) {Text("redo") }

//        Button(
//            onClick = { RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.TodoToggled(!toolBarState.isTodoActive)) },
//            colors = ButtonDefaults.buttonColors(
//                containerColor = if (toolBarState.isTodoActive) Color.Yellow else Color.Gray,
//                contentColor = if (toolBarState.isTodoActive) Color.Black else Color.White
//            )
//        ) {
//            Text("待办事项")
//        }
//        Button(
//            onClick = { RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.NumberedListToggled(!toolBarState.isNumberListActive)) },
//            colors = ButtonDefaults.buttonColors(
//                containerColor = if (toolBarState.isNumberListActive) Color.Yellow else Color.Gray,
//                contentColor = if (toolBarState.isNumberListActive) Color.Black else Color.White
//            )
//        ) {
//            Text("有序列表")
//        }
//        Button(
//            onClick = { RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.BulletedListToggled(!toolBarState.isBulletListActive)) },
//            colors = ButtonDefaults.buttonColors(
//                containerColor = if (toolBarState.isBulletListActive) Color.Yellow else Color.Gray,
//                contentColor = if (toolBarState.isBulletListActive) Color.Black else Color.White
//            )
//        ) {
//            Text("无序列表")
//        }
//        Button(
//            onClick = { RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.BoldToggled(!toolBarState.isBoldActive)) },
//            colors = ButtonDefaults.buttonColors(
//                containerColor = if (toolBarState.isBoldActive) Color.Yellow else Color.Gray,
//                contentColor = if (toolBarState.isBoldActive) Color.Black else Color.White
//            )
//        ) {
//            Text("粗体")
//        }
//        Button(
//            onClick = { RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.ItalicToggled(!toolBarState.isItalicActive)) },
//            colors = ButtonDefaults.buttonColors(
//                containerColor = if (toolBarState.isItalicActive) Color.Yellow else Color.Gray,
//                contentColor = if (toolBarState.isItalicActive) Color.Black else Color.White
//            )
//        ) {
//            Text("斜体")
//        }
//        Button(
//            onClick = { RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.UnderlineToggled(!toolBarState.isUnderlineActive)) },
//            colors = ButtonDefaults.buttonColors(
//                containerColor = if (toolBarState.isUnderlineActive) Color.Yellow else Color.Gray,
//                contentColor = if (toolBarState.isUnderlineActive) Color.Black else Color.White
//            )
//        ) {
//            Text("下划线")
//        }
//        Button(
//            onClick = { RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.StrikethroughToggled(!toolBarState.isStrikethroughActive)) },
//            colors = ButtonDefaults.buttonColors(
//                containerColor = if (toolBarState.isStrikethroughActive) Color.Yellow else Color.Gray,
//                contentColor = if (toolBarState.isStrikethroughActive) Color.Black else Color.White
//            )
//        ) {
//            Text("删除线")
//        }
//        Button(
//            onClick = { RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.IndentLeftApplied) },
//            colors = ButtonDefaults.buttonColors(
//                containerColor = Color.Gray,
//                contentColor = Color.White
//            )
//        ) {
//            Text("左移")
//        }
//        Button(
//            onClick = { RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.IndentRightApplied) },
//            colors = ButtonDefaults.buttonColors(
//                containerColor = Color.Gray,
//                contentColor = Color.White
//            )
//        ) {
//            Text("右移")
//        }
//        Button(onClick = {
//            RichTextViewModel.triggerStyleActionEvent(RichTextStyleActionEvent.SaveNote)
//        }) {
//            Text("保存 ")
//        }
    }
}