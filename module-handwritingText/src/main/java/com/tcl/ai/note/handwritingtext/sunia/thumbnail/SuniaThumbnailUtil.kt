package com.tcl.ai.note.handwritingtext.sunia.thumbnail

import android.graphics.Point
import android.graphics.Rect
import android.graphics.RectF
import android.util.Log
import androidx.core.graphics.toRectF
import com.sunia.penengine.sdk.operate.canvas.DarkModeMenu
import com.sunia.singlepage.sdk.InkExportFunc
import com.sunia.singlepage.sdk.InkFactory
import com.sunia.singlepage.sdk.listener.IExportListener
import com.tcl.ai.note.utils.Logger
import java.io.File
import java.util.concurrent.CountDownLatch
import kotlin.math.exp

/**
 * Sunia提供的类
 *
 * Created on 2025/8/6.
 * desc:
 * <AUTHOR> Qin
 */
object SuniaThumbnailUtil {
    fun saveOfflineThumbnail(
        pathDir: String,
        fileName: String,
        visibleSize: Rect,
        exportSize: Rect,
        darkMode: DarkModeMenu = DarkModeMenu.NONE,
    ): String {
        Logger.i("SuniaThumbnailUtil", "saveOfflineThumbnail, visibleSize:$visibleSize, exportSize: $exportSize, pathDir:$pathDir, fileName: $fileName")
        val exportFunc = InkFactory.newOfflineExportFunc(pathDir)
        val countDownLatch = CountDownLatch(1)
        with(exportFunc) {
            setVisibleSize(visibleSize.width(), visibleSize.height())
            setExportType(InkExportFunc.EXPORT_CANVAS_TYPE_IMAGE)
            setExportImageSuffix(InkExportFunc.EXPORT_IMAGE_SUFFIX_PNG)
            setExportRectF(visibleSize.toRectF())
            setExportSize(exportSize.width(), exportSize.height())
            enableExceptBackground(false)
            enablePencilSizeScale(false)
            setDarkMode(darkMode)
            setExportFileDir(pathDir)
            setExportFileName(fileName)
            setExportListener(object : IExportListener {
                override fun onFinish(p0: Boolean, p1: List<String?>?) {
                    countDownLatch.countDown()
                }

                override fun onExportDataType(p0: Int): String? {
                    return null
                }
            })
            start()
        }
        countDownLatch.await()
        Log.e("SuniaThumbnailUtil", "saveOfflineThumbnail end")
        return File(pathDir, fileName).absolutePath
    }
}