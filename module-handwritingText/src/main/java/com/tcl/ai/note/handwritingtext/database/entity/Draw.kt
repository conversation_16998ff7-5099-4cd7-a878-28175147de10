package com.tcl.ai.note.handwritingtext.database.entity

import android.graphics.Bitmap
import android.graphics.Bitmap.Config
import android.graphics.Canvas
import android.graphics.Point
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.util.fastForEach
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.tcl.ai.note.handwritingtext.bean.DrawStroke
import com.tcl.ai.note.handwritingtext.database.convertor.DrawConvertor
import com.tcl.ai.note.handwritingtext.repo.DrawBoardRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext
import kotlin.math.max
import kotlin.math.min

@Entity(
    tableName = DBConst.TABLE_NAME_DRAW,
    indices = [Index(value = ["noteId"], unique = true)]
)
@TypeConverters(DrawConvertor::class)
data class Draw(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val noteId: Long = 0,
    val strokes: List<DrawStroke> = emptyList(),
    val createTime: Long = System.currentTimeMillis(),
    val modifyTime: Long = System.currentTimeMillis(),
)

/**
 * 获取手绘图绘制的总大小
 */
fun List<DrawStroke>.getHandwritingBitmapSize(): IntSize {
    val drawStrokeList = this@getHandwritingBitmapSize
    // 计算手绘的起始点和尺寸大小
    var leftTopX = drawStrokeList.getOrNull(0)?.points?.getOrNull(0)?.x?.toInt() ?: 0
    var leftTopY = drawStrokeList.getOrNull(0)?.points?.getOrNull(0)?.y?.toInt() ?: 0
    var width = 0
    var height = 0
    drawStrokeList.fastForEach {
        leftTopX = min(leftTopX, it.points.minByOrNull { it.x }?.x?.minus(it.style.width / 2 - 1)?.toInt() ?: 0)
        leftTopY = min(leftTopY, it.points.minByOrNull { it.y }?.y?.minus(it.style.width / 2 - 1)?.toInt() ?: 0)
        width = max(width, it.points.maxByOrNull { it.x }?.x?.plus(it.style.width / 2 + 1)?.toInt() ?: 0)
        height = max(height, it.points.maxByOrNull { it.y }?.y?.plus(it.style.width / 2 + 1)?.toInt() ?: 0)
    }
    // val leftTopPoint = Point(leftTopX, leftTopY)
    // val handwritingRealSize = IntSize(width - leftTopX, height - leftTopY)
    return IntSize(width, height)
}