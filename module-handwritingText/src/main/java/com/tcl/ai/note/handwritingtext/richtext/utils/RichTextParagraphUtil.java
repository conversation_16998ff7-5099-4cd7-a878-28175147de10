package com.tcl.ai.note.handwritingtext.richtext.utils;

import android.text.Editable;
import android.text.Layout;
import android.text.Spannable;

import com.tcl.ai.note.handwritingtext.richtext.converter.RichTextStyleEntityToSpanConverter;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListBulletSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.ListNumberSpan;
import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

public class RichTextParagraphUtil {
    // 清除选区内所有段落类span（待办、有序、无序）
    public static void clearParagraphSpansByLine(AREditText editText, Editable editable, int selStart, int selEnd) {
        if (editText == null || editable == null) return;
        Layout layout = editText.getLayout();
        if (layout == null) return;
        int pos = selStart;
        while (pos < selEnd) {
            int line = layout.getLineForOffset(pos);
            int lineStart = layout.getLineStart(line);
            int lineEnd = layout.getLineEnd(line);

            UpcomingListSpan[] todos = editable.getSpans(lineStart, lineEnd, UpcomingListSpan.class);
            ListBulletSpan[] bullets = editable.getSpans(lineStart, lineEnd, ListBulletSpan.class);
            ListNumberSpan[] numbers = editable.getSpans(lineStart, lineEnd, ListNumberSpan.class);
            for (Object span : todos) editable.removeSpan(span);
            for (Object span : bullets) editable.removeSpan(span);
            for (Object span : numbers) editable.removeSpan(span);
            pos = lineEnd + 1;
        }
    }

    // 将选区内每行都统一设置为指定的段落span（且只保留目标类型，不留其它类型）
    public static void setParagraphSpanByLine(
            AREditText editText,
            Editable editable,
            int selStart,
            int selEnd,
            Class<?> targetSpanClass
    ) {
        if (editText == null || editable == null) return;

        String text = editable.toString();
        int textLen = text.length();

        // 1. 找到选区涉及到的所有行的行首/行尾（即便选区在行内，只要有覆盖到就转）
        int rangeStart = selStart;
        int rangeEnd = selEnd;

        // 向前找到最靠左的行头
        while (rangeStart > 0 && text.charAt(rangeStart - 1) != '\n') {
            rangeStart--;
        }
        // 向后找到最靠右的行尾（选区末尾扩展到该行的下一个\n，或到文本末尾）
        while (rangeEnd < textLen && text.charAt(rangeEnd) != '\n') {
            rangeEnd++;
        }
        // 如果没到文本末尾，再往后加
        if (rangeEnd < textLen) rangeEnd++; //覆盖最后的\n本身

        int pos = rangeStart;
        int number = 1;
        while (pos < rangeEnd) {
            // 找到本行终点
            int lineEnd = text.indexOf('\n', pos);
            if (lineEnd == -1 || lineEnd >= rangeEnd) lineEnd = rangeEnd;
            int lineStart = pos;

            CharSequence lineText = editable.subSequence(lineStart, lineEnd);

            boolean isEmptyLine = isLogicalEmptyLine(lineText);

            // ---- 清除其它段落类span ----
            UpcomingListSpan[] todos = editable.getSpans(lineStart, lineEnd, UpcomingListSpan.class);
            ListBulletSpan[] bullets = editable.getSpans(lineStart, lineEnd, ListBulletSpan.class);
            ListNumberSpan[] numbers = editable.getSpans(lineStart, lineEnd, ListNumberSpan.class);

            // 先处理 UpcomingListSpan
            for (UpcomingListSpan todoSpan : todos) {
                // 记录 span 范围
                int spanStart = editable.getSpanStart(todoSpan);
                int spanEnd = editable.getSpanEnd(todoSpan);

                // 如果是已完成的待办事项
                if (todoSpan.isChecked()) {
                    // 移除和它重叠范围完全相同的删除线
                    android.text.style.StrikethroughSpan[] strikeSpans = editable.getSpans(spanStart, spanEnd, android.text.style.StrikethroughSpan.class);
                    for (android.text.style.StrikethroughSpan strike : strikeSpans) {
                        if (editable.getSpanStart(strike) == spanStart && editable.getSpanEnd(strike) == spanEnd) {
                            editable.removeSpan(strike);
                        }
                    }
                    // 移除和它重叠范围完全相同的前景色
                    android.text.style.ForegroundColorSpan[] colorSpans = editable.getSpans(spanStart, spanEnd, android.text.style.ForegroundColorSpan.class);
                    for (android.text.style.ForegroundColorSpan color : colorSpans) {
                        if (editable.getSpanStart(color) == spanStart && editable.getSpanEnd(color) == spanEnd) {
                            editable.removeSpan(color);
                        }
                    }
                }
                // 无论是否已完成都移除段落 span 本身
                editable.removeSpan(todoSpan);
            }
            // 移除无序列表span
            for (Object span : bullets) editable.removeSpan(span);
            // 移除有序列表span
            for (Object span : numbers) editable.removeSpan(span);

            // ---- 设置目标span（有序列表特殊处理） ----
            if (targetSpanClass == ListNumberSpan.class) {
                if (!isEmptyLine) {
                    ListNumberSpan span = new ListNumberSpan(number++);
                    editable.setSpan(span, lineStart, lineEnd, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                } else {
                    number = 1; // 空行断编号
                }
            } else if (targetSpanClass == ListBulletSpan.class) {
                if (!isEmptyLine) {
                    ListBulletSpan span = new ListBulletSpan();
                    editable.setSpan(span, lineStart, lineEnd, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                }
            } else if (targetSpanClass == UpcomingListSpan.class) {
                if (!isEmptyLine) {
                    UpcomingListSpan span = new UpcomingListSpan(
                            RichTextStyleEntityToSpanConverter.INSTANCE.getTodoImageWidth_Edit());
                    editable.setSpan(span, lineStart, lineEnd, Spannable.SPAN_INCLUSIVE_INCLUSIVE);
                }
            }

            pos = lineEnd + 1;
        }
    }

    // 判断当前行是否为空行
    private static boolean isLogicalEmptyLine(CharSequence text) {
        // 只要全是零宽、空格、制表、换行符（\n \r），就算空行
        int len = text.length();
        for (int i = 0; i < len; i++) {
            char ch = text.charAt(i);
            if (ch != '\n' && ch != '\r' && ch != ' ' && ch != '\t' && ch != 0x200B) {
                return false;
            }
        }
        return true;
    }
}


