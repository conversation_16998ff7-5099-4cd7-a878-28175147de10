package com.tcl.ai.note.handwritingtext.repo

import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R
import com.tcl.ai.note.handwritingtext.database.NoteDatabase
import com.tcl.ai.note.handwritingtext.database.entity.Note
import com.tcl.ai.note.handwritingtext.database.entity.NoteListItem
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.runIO
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart

/**
 * 临时创建，用于Note数据库创建的参考
 *
 */
@Deprecated("二期使用NoteRepository2")
object NoteRepository {
    private const val TAG = "NoteRepository"
    private val noteDao = NoteDatabase.getInstance(GlobalContext.instance).noteDao()

    /**
     * 获取所有Note(默认按照创建时间时间)
     */
    suspend fun getAllNotesPaged(page: Int, pageSize: Int): Pair<List<NoteListItem>, Boolean> {
        Logger.d(TAG, "getAllNotesPaged, page: $page, pageSize: $pageSize")
        val offset = (page - 1) * pageSize
        val items = noteDao.getAllNotesPaged(offset, pageSize)
        val hasMore = items.size == pageSize
        return Pair(items, hasMore)
    }

    /**
     * 获取所有Note(按照修改时间)
     */
    suspend fun getAllNotesByModifyTimePaged(
        page: Int,
        pageSize: Int
    ): Pair<List<NoteListItem>, Boolean> = runIO {
        Logger.d(TAG, "getAllNotesByModifyTimePaged, page: $page, pageSize: $pageSize")
        val offset = (page - 1) * pageSize
        val items = noteDao.getAllNotesByModifyTimePaged(offset, pageSize)
        val hasMore = items.size == pageSize
        return@runIO Pair(items, hasMore)
    }


    /**
     * 获取一个分类下的Note数据(默认按照创建时间)
     */
    suspend fun getNotesByCategoryIdPaged(
        categoryId: Long,
        page: Int,
        pageSize: Int
    ): Pair<List<NoteListItem>, Boolean> = runIO {
        Logger.d(
            TAG,
            "getNotesByCategoryId, categoryId: $categoryId, page: $page, pageSize: $pageSize"
        )
        val offset = (page - 1) * pageSize
        val items = noteDao.getNotesByCategoryIdPaged(categoryId, offset, pageSize)
        val hasMore = items.size == pageSize
        return@runIO Pair(items, hasMore)
    }

    /**
     * 获取一个分类下的Note数据(按照修改时间)
     */
    suspend fun getNotesByCategoryIdByModifyTimePaged(
        categoryId: Long,
        page: Int,
        pageSize: Int
    ): Pair<List<NoteListItem>, Boolean> = runIO {
        Logger.d(
            TAG,
            "getNotesByCategoryIdByModifyTime, categoryId: $categoryId, page: $page, pageSize: $pageSize"
        )
        val offset = (page - 1) * pageSize
        val items = noteDao.getNotesByCategoryIdByModifyTimePaged(categoryId, offset, pageSize)
        val hasMore = items.size == pageSize
        return@runIO Pair(items, hasMore)
    }

    /**
     * 删除某个分类下的Note数据
     */
    suspend fun deleteNotesByCategoryId(categoryId: Long): Int = runIO {
        Logger.d(TAG, "deleteNotesByCategoryId, categoryId: $categoryId")
        noteDao.deleteNotesByCategoryId(categoryId)
    }

    /**
     * 删除某个分类下的Note数据
     */
    suspend fun updateCategoryId(categoryId: Long, newCategoryId: Long): Int = runIO {
        Logger.d(TAG, "updateCategoryId, categoryId: $categoryId, newCategoryId: $newCategoryId")
        noteDao.updateCategoryId(categoryId, newCategoryId)
    }

    /**
     * 更新多条指定Note的 categoryId
     */
    suspend fun updateNotesCategoryId(noteIds: List<Long>, newCategoryId: Long): Int = runIO {
        Logger.d(TAG, "updateNotesCategoryId, noteIds: $noteIds, newCategoryId: $newCategoryId")
        noteDao.updateNotesCategoryId(noteIds, newCategoryId)
    }


    /**
     * 更新单条指定 Note 的 categoryId
     */
    suspend fun updateNoteCategoryId(noteId: Long, categoryId: Long): Int = runIO {
        Logger.d(TAG, "updateNoteCategoryId, noteId: $noteId, categoryId: $categoryId")
        noteDao.updateNoteCategoryId(noteId, categoryId)
    }


    /**
     * 搜索笔记数据
     */
    suspend fun searchNotes(text: String): List<NoteListItem> = runIO {
        Logger.d(TAG, "searchNotes, text: $text")
        val onlyImage = GlobalContext.instance.getString(R.string.image_title)
        val onlyVoice = GlobalContext.instance.getString(R.string.audio_title)
        val onlyImageVoice = GlobalContext.instance.getString(R.string.image_audio_title)
        val notes = noteDao.searchNotes(text, onlyImage, onlyVoice, onlyImageVoice)
        return@runIO notes
    }

    /**
     * 搜索笔记数据
     */
    suspend fun searchNotesByCategoryAndQuery(text: String, categoryId: Long): List<NoteListItem> =
        runIO {
            Logger.d(TAG, "searchNotesByCategoryAndQuery, text: $text, categoryId:$categoryId")
            val onlyImage = GlobalContext.instance.getString(R.string.image_title)
            val onlyVoice = GlobalContext.instance.getString(R.string.audio_title)
            val onlyImageVoice = GlobalContext.instance.getString(R.string.image_audio_title)
            val notes = noteDao.searchNotesByCategoryAndQuery(
                text,
                categoryId,
                onlyImage,
                onlyVoice,
                onlyImageVoice
            )
            return@runIO notes
        }

    /**
     * 新增note
     */
    suspend fun insertNote(note: Note): Long = runIO {
        Logger.d(TAG, "insertNote: $note")
        noteDao.insert(note)
    }

    /**
     * 更note，这里代码还有缺陷，只作业务参考，
     */
    suspend fun updateNote(note: Note): Long = runIO {
        Logger.d(TAG, "updateNote: $note")
        noteDao.update(note)
        note.noteId
    }

    /**
     * 删除一条Note
     */
    suspend fun deleteNote(noteId: Long): Int = runIO {
        Logger.d(TAG, "deleteNote: $noteId")
        HandWritingThumbnailRepo.deleteHandwritingThumbnail(noteId)
        noteDao.deleteOneNote(noteId)
    }


    /**
     * 删除指定的Notes
     */
    suspend fun deleteNotes(noteIds: List<Long>): Int = runIO {
        Logger.d(TAG, "deleteNotes: $noteIds")
        noteDao.deleteNotes(noteIds)
    }

    /**
     * 获取一条Note
     */
    suspend fun getNote(noteId: Long): Note? = runIO {
        Logger.d(TAG, "getNote: $noteId")
        noteDao.getNote(noteId)
    }

    // 新增获取笔记数量的方法
    suspend fun getNoteCount(): Int = runIO {
        Logger.d(TAG, "getNoteCount")
        return@runIO noteDao.getNoteCount()
    }

    // 获取所有Note数据
    fun getAllNoteWithCategory(): Flow<List<NoteListItem>> {
        Logger.d(TAG, "getAllNote - Starting database query")
        return noteDao.getAllNotesWithCategory()
            .onStart { Logger.d(TAG, "Database query flow started: ${System.currentTimeMillis()}") }
            .onEach { notes ->
                Logger.d(
                    TAG,
                    "Database query completed: ${System.currentTimeMillis()}, found ${notes.size} records"
                )
            }
    }

    fun getNoteCountFlow(): Flow<Int> {
        Logger.d(TAG, "getNoteCount")
        return noteDao.getNoteCountFlow()
    }
}