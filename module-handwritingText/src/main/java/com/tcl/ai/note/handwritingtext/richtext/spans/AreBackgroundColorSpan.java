package com.tcl.ai.note.handwritingtext.richtext.spans;

import android.text.style.BackgroundColorSpan;

import androidx.annotation.ColorInt;

public class AreBackgroundColorSpan extends BackgroundColorSpan implements AreDynamicSpan {
    public AreBackgroundColorSpan(@ColorInt int color) {
        super(color);
    }

    @Override
    public int getDynamicFeature() {
        return this.getBackgroundColor();
    }
}
