package com.tcl.ai.note.handwritingtext.utils

import android.graphics.Bitmap
import android.graphics.Typeface
import android.text.SpannableString
import android.text.Spanned
import android.text.style.StyleSpan
import android.text.style.UnderlineSpan
import android.util.Base64
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.util.fastForEach
import androidx.compose.ui.util.fastForEachIndexed
import androidx.compose.ui.util.fastForEachReversed
import androidx.core.text.clearSpans
import androidx.core.text.toHtml
import coil.util.Logger
import com.tcl.ai.note.handwritingtext.database.entity.EditorContent.TextBlock
import com.tcl.ai.note.handwritingtext.database.entity.ParagraphStyle
import java.io.ByteArrayOutputStream
import java.util.regex.Pattern
import kotlin.time.Duration.Companion.minutes

sealed class HtmlTextStyle {
    object NONE : HtmlTextStyle()
    data class BULLETED(val lineNum: Int) : HtmlTextStyle()
    data class NUMBERED(val lineNum: Int) : HtmlTextStyle()
}

class HtmlDocument {
    private var title = ""
    private val htmlBuilder = StringBuilder()

    fun setTitle(string: String): HtmlDocument {
        title = string
        return this
    }

    fun addText(string: String, htmlTextStyle: HtmlTextStyle = HtmlTextStyle.NONE): HtmlDocument {
        val htmlString = when (htmlTextStyle) {
            is HtmlTextStyle.NONE -> {
                "$string<br>"
            }

            is HtmlTextStyle.BULLETED -> {
                """
                <ul><li type ="disc" value="${htmlTextStyle.lineNum}">${string.replace(Regex("</?p.*?>"), "")}</li></ul>
                """.trim()
            }

            is HtmlTextStyle.NUMBERED -> {
                """
                <ol><li type ="1" value="${htmlTextStyle.lineNum}">${string.replace(Regex("</?p.*?>"), "")}</li></ol>
                """.trim()
            }
        }

        val text = """
            <div>$htmlString</div>
        """.trim()
        htmlBuilder.appendLine()
        htmlBuilder.append(text)
        return this
    }

    fun addTodo(string: String, checked: Boolean): HtmlDocument {
        val checkBox = """
            <div><input type="checkbox" ${if (checked) "checked" else ""}>${string.replace(Regex("</?p.*?>"), "")}</input><br></div>
        """.trim()
        htmlBuilder.appendLine()
        htmlBuilder.append(checkBox)
        return this
    }

    fun addImage(bitmap: Bitmap): HtmlDocument {
        // 将Bitmap转换为Base64字符串
        val byteArrayOutputStream = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.PNG, 100, byteArrayOutputStream)
        val byteArray = byteArrayOutputStream.toByteArray()
        val base64Image: String? = Base64.encodeToString(byteArray, Base64.DEFAULT)

        // 创建HTML字符串
        val htmlImageString = "<div><img src='data:image/png;base64,$base64Image'/><br></div>"
        htmlBuilder.appendLine()
        htmlBuilder.append(htmlImageString)
        return this
    }

    fun build(): String {
        return """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8"/>
                <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
                <title>$title</title>
            </head>
            <style type="text/css">
                ol,ul{
                display:block;
                list-style-type:none;
                margin:3px;
                }
            </style>
            <body>
            ${htmlBuilder.trim()}
            </body>
            </html>
        """.trim()
    }

    companion object {
        private const val TAG = "HtmlDocument"

        fun TextFieldValue.toHtmlText(): String {
            val spannableString = SpannableString(text)

            val spanStyles = annotatedString.spanStyles.distinct().sortedWith(compareBy({ it.start }, { it.end }))
            // com.tcl.ai.note.utils.Logger.d(TAG,"aaaa,text: $text, ${spanStyles}")
            spanStyles.forEachIndexed { index, it ->
                val nextSpanStyle = spanStyles.getOrNull(index + 1)
                val nextStartIndex = nextSpanStyle?.start ?: it.end
                // com.tcl.ai.note.utils.Logger.d(TAG,"span: ${it.start}-${it.end}, select:${it.start}-${nextStartIndex}, ${it.item}")

                if (it.item.fontWeight == FontWeight.Bold) {
                    // com.tcl.ai.note.utils.Logger.d(TAG,"set BOLD, select:${it.start}-${nextStartIndex}")
                    spannableString.setSpan(StyleSpan(Typeface.BOLD), it.start.coerceAtLeast(0), nextStartIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
                }
                if (it.item.fontStyle == FontStyle.Italic) {
                    // com.tcl.ai.note.utils.Logger.d(TAG,"set Italic, select:${it.start}-${nextStartIndex}")
                    spannableString.setSpan(StyleSpan(Typeface.ITALIC), it.start.coerceAtLeast(0), nextStartIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
                }
                if (it.item.textDecoration?.contains(TextDecoration.Underline) == true) {
                    // com.tcl.ai.note.utils.Logger.d(TAG,"set Underline, select:${it.start}-${nextStartIndex}")
                    spannableString.setSpan(UnderlineSpan(), it.start, nextStartIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
                }
            }

            // 移除 <p> 和 </p> 标签
            return spannableString.toHtml().trim()
        }
    }
}