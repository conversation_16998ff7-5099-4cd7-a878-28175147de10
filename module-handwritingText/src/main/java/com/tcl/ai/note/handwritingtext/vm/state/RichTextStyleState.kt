package com.tcl.ai.note.handwritingtext.vm.state

import android.text.Layout

/**
 * 富文本样式状态
 * 用于管理富文本编辑器的各种样式状态
 */
data class RichTextStyleState(
    // 文本样式
    val isBoldActive: Boolean = false,
    val isItalicActive: Boolean = false,
    val isUnderlineActive: Boolean = false,
    val isStrikethroughActive: Boolean = false,

    // 段落样式
    val alignment: Layout.Alignment = Layout.Alignment.ALIGN_NORMAL,
    val isNumberedListActive: Boolean = false,
    val isBulletedListActive: Boolean = false,

    // 待办事项
    val isTodoActive: Boolean = false,

    // 字体样式
    val fontSize: Int = 16,
    val fontColor: Int = 0xFF000000.toInt(),
    val backgroundColor: Int = 0x00000000,

    // 缩进的...
    val indentLevel: Int = 0
)