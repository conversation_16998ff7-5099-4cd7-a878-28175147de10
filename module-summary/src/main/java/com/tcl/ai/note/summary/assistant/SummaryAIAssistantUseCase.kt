package com.tcl.ai.note.summary.assistant

import com.tcl.ai.note.base.R
import com.tcl.ai.note.exception.AIChatException
import com.tcl.ai.note.handwritingtext.repo.NoteRepository
import com.tcl.ai.note.handwritingtext.richtext.utils.NoteContentUtil
import com.tcl.ai.note.net.NetworkMonitor
import com.tcl.ai.note.state.ChartStreamingMsgUtils
import com.tcl.ai.note.state.ChatEffect
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getAIApiLangCode
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 概述的UseCase
 */
class SummaryAIAssistantUseCase @Inject constructor(
    private val networkMonitor: NetworkMonitor,
    private val summaryAiAssistantApi: SummaryAIAssistantApi,
) {
    private lateinit var _isOffline: StateFlow<Boolean>
    private val isOffline: StateFlow<Boolean> get() = _isOffline

    val chatStreamingMsgSate = MutableStateFlow<Result<ChatStreamingMsg>?>(null)
    val effect = MutableStateFlow<ChatEffect?>(
        null
    )
    private var isHasClickStop=false

    private val TAG = "AIAssistantUseCase"
    private var assistCallId: Int = -1
    private var mOriginalText: String = ""
    private var accumulatedText = StringBuilder()
    fun setupNetworkMonitor(scope: CoroutineScope) {
        _isOffline = networkMonitor.isOnline.map(Boolean::not)
            .stateIn(scope = scope, started = SharingStarted.Companion.Eagerly, initialValue = false)
        scope.launch {
            _isOffline.collectLatest {
                if (it) {
                    (chatStreamingMsgSate.value as? Result.Success)?.let { state ->
                        val successData = state.data
                        if (successData.status == StreamingStatus.IN_PROGRESS){
                            effect.emit(ChatEffect.ShowToastRes(R.string.network_error))
                        }
                    }
                }
            }
        }
    }
    /**
     * 重新发送消息
     */
    suspend fun reSendMsg(preset: String) {
        sendMsgWithSate(preset = preset, content = mOriginalText)
    }

    /**
     * 停止生成文本
     */
    fun stopGenerateText() {
        isHasClickStop=true
        ChartStreamingMsgUtils.setMsgStateStop(chatStreamingMsgSate)
        summaryAiAssistantApi.stopAIAssistant(assistCallId)
    }

    /**
     * 生成文本
     */
    suspend fun generateText(
        noteId: Long,
        preset: String,
    ) {
        try {
            // 等待异步回调完成
            val callId = summaryAiAssistantApi.obtainAssistSuspend()
            Logger.i(TAG, "obtainAssist --> callId: $callId")
            assistCallId = callId

            // 回调成功后执行后续操作
            val note = NoteRepository.getNote(noteId)
            val content = NoteContentUtil.dealWithNoteContentForAI(note)
            mOriginalText = content
            Logger.d(TAG, " content :   $content")
            sendMsgWithSate(preset, content)
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to obtain assist: ${e.message}")
            if (e is AIChatException) {
                Logger.e(TAG, "Failed to obtain assist: ${e.message}")
                effect.value = ChartStreamingMsgUtils.dealWithErrorResult(e.code)
            }
        }
    }
    /*
     *   发送消息 并收集 显示状态
     */
    private suspend fun sendMsgWithSate(preset: String, content: String) {
        if (isOffline.value) {
            effect.value= ChatEffect.ShowToastRes(R.string.network_error)
            return
        }
        effect.value=null
        chatStreamingMsgSate.value = Result.Loading
        isHasClickStop=false
        accumulatedText= StringBuilder()
        summaryAiAssistantApi.sendUserMsg(assistCallId, preset, content, getAIApiLangCode())
            .onEach { delay(50) }.onCompletion { cause ->
                if (cause == null) {//异常完成，要设置停止状态
                    ChartStreamingMsgUtils.setErrorResultMsgState(chatStreamingMsgSate)
                 }
                Logger.d(TAG, "sendMsgWithSate:OnCompletion: $cause")
        }.collect {result->
            Logger.d(TAG, "sendMsgWithSate: collect  $result")
            if (result is Result.Success&&!isHasClickStop) {
                accumulatedText.append(result.data.text)
                chatStreamingMsgSate.value = result.copy(data = result.data.copy(text = accumulatedText.toString()))
            }else{
                if (result is Result.Error) {
                    val code = result.code
                    ChartStreamingMsgUtils.setErrorResultMsgState(chatStreamingMsgSate)
                    effect.value =ChartStreamingMsgUtils.dealWithErrorResult(code, result)
                }
            }
        }
    }

}