package com.tcl.ai.note.summary.assistant

import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class SummaryAIAssistantModule {

    @Provides
    @Singleton
    fun provideAIAssistantApi(): SummaryAIAssistantApi {
        return SummaryAIAssistantApi()
    }

}