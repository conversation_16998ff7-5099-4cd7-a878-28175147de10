package com.tcl.ai.note.summary.track

import androidx.lifecycle.viewModelScope
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.state.StreamingStatus
import com.tcl.ai.note.summary.track.AnalyticsAiSummaryModel.collectWithScope
import com.tcl.ai.note.summary.vm.SummaryViewModel
import com.tcl.ai.note.track.AbsAnalyticsSubModel
import com.tcl.ai.note.track.TclAnalytics
import kotlinx.coroutines.flow.filterNotNull


/**
 * 分析AI概要使用情况
 *
 * 用于上报埋点
 */
object AnalyticsAiSummaryModel : AbsAnalyticsSubModel() {
    fun loadHelpWritingViewModel(summaryViewModel: SummaryViewModel) {
        reportAiSummaryCompletionState(summaryViewModel)
    }

    /**
     * 上报AI概要获取获取状态
     */
    private fun reportAiSummaryCompletionState(summaryViewModel: SummaryViewModel) {
        summaryViewModel.aiLoadingStatus.collectWithScope(summaryViewModel.viewModelScope) { result ->
            if (result is Result.Success && result.data.status == StreamingStatus.COMPLETED) {
                // AI概要数据获取成功
                TclAnalytics.reportAiSummaryCompletionState("0")
            }
        }
        summaryViewModel.effect.filterNotNull().collectWithScope(summaryViewModel.viewModelScope) {
            // AI概要数据获取失败
            TclAnalytics.reportAiSummaryCompletionState("1")
        }
    }
}