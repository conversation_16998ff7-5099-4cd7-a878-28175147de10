package com.tcl.ai.note.state

import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.ProducerScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

//敏感词返回码
const val ERR_TRANSLATE_SENSITIVE_WORDS = 430
//使用量限制
const val ERR_USAGE_LIMITED = 431

// 数据解析相关的错误
const val ERR_PARSE_DATA  = 301
//绑定失败
const val ERR_BIND   = 101

val testFullText = "I am a test AI model, I can help you with anything you need.".repeat(30)
val testSuccessResult=Result.Success(
    ChatStreamingMsg(
        status = StreamingStatus.COMPLETED,
        testFullText,
        "threadId",
        "userMessageId"
    )
)



object ChartStreamingMsgUtils {
    /**
     * 停止生成文字,如果已经成功生成文字，则修改状态为停止生成文字
     */
    fun setMsgStateStop(
        chatStreamingMsgSate: MutableStateFlow<Result<ChatStreamingMsg>?>,
    ) {
        val tmpMsgState = chatStreamingMsgSate.value
        if (tmpMsgState is Result.Success) {
            chatStreamingMsgSate.value = (
                    Result.Success(
                        ChatStreamingMsg(
                            status = StreamingStatus.STOPPED,
                            text = tmpMsgState.data.text,
                            threadId = tmpMsgState.data.threadId
                        )
                    )
            )
        } else if (tmpMsgState is Result.Loading) {
            chatStreamingMsgSate.value = null
        }
    }

    /**
     * 处理AI错误结果
     */
     fun dealWithErrorResult(code: Int?, error: Result.Error?=null): ChatEffect {
        return when (code) {
            103, 104, ERR_PARSE_DATA -> {
                Logger.e("dealWithErrorResult", "Result.Error: ${error?.exception} code==: $code")
                //Error(exception=java.lang.Exception: SDK error, code=103) //assistCallId 传-1 会有这个错误
                ChatEffect.ShowToastRes(R.string.ai_generate_failed)
            }

            ERR_TRANSLATE_SENSITIVE_WORDS -> {
                ChatEffect.ShowToastRes(R.string.message_generate_failed_since_sensitive_words)
            }
            ERR_BIND -> {
                ChatEffect.ShowToastRes(R.string.binding_failed)
            }
            ERR_USAGE_LIMITED -> {
                ChatEffect.ErrUsageLimited()
            }
            else -> {
                ChatEffect.ShowToastRes(R.string.network_response_failed)
            }
        }
    }

    /**
     * 碰到已经生成了文字的情况 异常了 只需要设置完成即可，保留生成的文本
     */
     fun setErrorResultMsgState(state: MutableStateFlow<Result<ChatStreamingMsg>?>) {
         val tmpState = state.value
        if (tmpState is Result.Success) {
            state.value =
                tmpState.copy(
                    data = tmpState.data.copy(status = StreamingStatus.COMPLETED, errorCode = tmpState.data.errorCode)
                )
        } else {
            state.value = null
        }
    }
}

suspend fun ProducerScope<Result<ChatStreamingMsg>>.sendErrorResult(
    errorCode: Int
) {
    when (errorCode) {
        ERR_USAGE_LIMITED -> {
            send(Result.Error(Exception("SDK error"), errorCode))
        }
        ERR_TRANSLATE_SENSITIVE_WORDS -> {
            send(Result.Error(Exception("SDK error"), errorCode))
        }
        ERR_PARSE_DATA-> {
            send(Result.Error(Exception("SDK error"), errorCode))
        }
    }
}