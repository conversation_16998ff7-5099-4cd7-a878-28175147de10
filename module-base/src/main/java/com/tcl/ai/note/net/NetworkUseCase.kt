package com.tcl.ai.note.net

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import javax.inject.Inject

/**
 * 监听网络状态的 UseCase
 */
class NetworkUseCase @Inject constructor(
    private val networkMonitor: NetworkMonitor
) {
    fun isOffline(scope: CoroutineScope): StateFlow<Boolean> {
        return networkMonitor.isOnline.map(Boolean::not)
            .stateIn(scope = scope, started = SharingStarted.Eagerly, initialValue = false)
    }
}