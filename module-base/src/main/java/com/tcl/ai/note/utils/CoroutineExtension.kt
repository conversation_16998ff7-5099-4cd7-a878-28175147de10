package com.tcl.ai.note.utils

import kotlinx.coroutines.CancellableContinuation
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.withTimeoutOrNull
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

fun CoroutineScope.launchMain(
    block: suspend CoroutineScope.() -> Unit
) = this.launch(Dispatchers.Main) {
    block.invoke(this)
}

fun CoroutineScope.launchMain(
    delay: Long? = null,
    block: suspend CoroutineScope.() -> Unit
) = this.launch(Dispatchers.Main) {
    delay?.let {
        delay(delay)
    }
    block.invoke(this)
}

fun CoroutineScope.launchIO(
    block: suspend CoroutineScope.() -> Unit
) = this.launch(Dispatchers.IO) {
    block.invoke(this)
}

fun CoroutineScope.launchIO(
    delay: Long? = null,
    block: suspend CoroutineScope.() -> Unit
) = this.launch(Dispatchers.IO) {
    delay?.let {
        delay(delay)
    }
    block.invoke(this)
}

suspend inline fun <T> suspendCoroutineWithTimeout(
    timeout: Long,
    crossinline block: (CancellableContinuation<T>) -> Unit
) = withTimeout(timeout) {
    suspendCancellableCoroutine(block = block)
}

suspend inline fun <T> suspendCoroutineWithTimeoutOrNull(
    timeout: Long,
    crossinline block: (CancellableContinuation<T>) -> Unit
) = withTimeoutOrNull(timeout) {
    suspendCancellableCoroutine(block = block)
}

fun <T> CancellableContinuation<T>.safeResume(value: T) {
    if (isActive) {
        resume(value)
    } else {
        Logger.v("safeResume", "ignore this resume because it has already resumed")
    }
}

fun <T> CancellableContinuation<T>.safeResumeWithException(exception: Throwable) {
    if (isActive) {
        resumeWithException(exception)
    } else {
        Logger.v("safeResumeWithException", "ignore this resume because it has already resumed")
    }
}

suspend fun <T> runMain(block: suspend () -> T) =
    withContext(Dispatchers.Main) {
        block.invoke()
    }

suspend fun <T> runIO(block: suspend () -> T) =
    withContext(Dispatchers.IO) {
        block.invoke()
    }

suspend fun <T> runDefault(block: suspend () -> T) =
    withContext(Dispatchers.Default) {
        block.invoke()
    }
