package com.tcl.ai.note.exception

// 通常在一个独立的文件中定义这些异常类
sealed class AppException(message: String? = null, cause: Throwable? = null) :
    Exception(message, cause) {

    class NetworkException(message: String? = null, cause: Throwable? = null) :
        AppException(message, cause)

    class DatabaseException(message: String? = null, cause: Throwable? = null) :
        AppException(message, cause)

    class InvalidArgException(message: String? = null, cause: Throwable? = null) :
        AppException(message, cause)

    class UnknownException(message: String? = null, cause: Throwable? = null) :
        AppException(message, cause)

    class TranslateException(message: String, cause: Throwable) :
        AppException(message, cause)
}