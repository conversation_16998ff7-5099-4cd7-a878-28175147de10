package com.tcl.ai.note.track

import bi.com.tcl.bi.DataReport
import bi.com.tcl.bi.common.BaseConfig
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.AppActivityManager
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.judge
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


object TclAnalytics {
    private const val TAG = "TclAnalytics"

    private fun reportAnalytics(params: HashMap<String, String> ) {
        Logger.d(TAG, "reportAnalytics params: $params")
        DataReport.custReport(params)
    }

    fun init() {
        try {
            DataReport.setContext(GlobalContext.instance)
                .setArea(BaseConfig.AREA_OVERSEA)
                .setTXMobile(true)
                .init()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 上报启动时间
     *
     * 上报时机：应用冷启动时
     */
    fun reportLaunchTime(launchTime: String) {
        val params = hashMapOf(
            AnalyticsConstant.LaunchTime.EVENT_ID,
            AnalyticsConstant.LaunchTime.LAUNCH_TIME to launchTime,
        )
        reportAnalytics(params)
    }

    /**
     * 应用使用时长 单位：秒
     * 上报时机：每次退出应用时上报
     */
    fun reportLaunchDuration(launchDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.LaunchDuration.EVENT_ID,
            AnalyticsConstant.LaunchDuration.LAUNCH_DURATION to launchDuration,
        )
        reportAnalytics(params)
    }

    /**
     * 每周统计一下note使用情况
     *
     * 上报时机：每周
     */
    fun reportNoteUsageWeekly(count: String, sketchCount: String, avgLength: String, avgImgCount: String, avgAudioCount: String) {
        val params = hashMapOf(
            AnalyticsConstant.NoteList.EVENT_ID,
            AnalyticsConstant.NoteList.COUNT to count,
            AnalyticsConstant.NoteList.SKETCH_COUNT to sketchCount,
            AnalyticsConstant.NoteList.AVG_LENGTH to avgLength,
            AnalyticsConstant.NoteList.AVG_IMG_COUNT to avgImgCount,
            AnalyticsConstant.NoteList.AVG_AUDIO_COUNT to avgAudioCount,
        )
        reportAnalytics(params)
    }

    /**
     * 笔记内容统计
     *
     * 上报时机：关闭编辑页时，如返回上一级、点击保存、自动保存时上报
     */
    fun reportNoteContentWhenSaving(count: String, bgStyle: String, imgCount: String, audioCount: String, audioTotalLength: String) {
        val params = hashMapOf(
            AnalyticsConstant.NoteContent.EVENT_ID,
            AnalyticsConstant.NoteContent.CONTENT_LENGTH to count,
            AnalyticsConstant.NoteContent.BG_STYLE to bgStyle,
            AnalyticsConstant.NoteContent.IMG_COUNT to imgCount,
            AnalyticsConstant.NoteContent.AUDIO_COUNT to audioCount,
            AnalyticsConstant.NoteContent.AUDIO_TOTAL_LENGTH to audioTotalLength,
        )
        reportAnalytics(params)
    }

    /**
     * 手写笔记
     * FountainPen笔统计
     *
     * 上报时机：关闭编辑页时，如返回上一级、点击保存、自动保存时上报
     */
    fun reportPenUsageWhenSaving(penAction:String, penDuration:String) {
        val params = hashMapOf(
            AnalyticsConstant.NoteHandWritingContent.EVENT_ID,
            AnalyticsConstant.NoteHandWritingContent.PEN_ACTIONS to penAction,
            AnalyticsConstant.NoteHandWritingContent.PEN_DURATION to penDuration,
        )
        reportAnalytics(params)
    }

    /**
     * BallPen统计
     *
     * 上报时机：关闭编辑页时，如返回上一级、点击保存、自动保存时上报
     */
    fun reportBallPenUsageWhenSaving(ballPenAction:String, ballPenDuration:String) {
        val params = hashMapOf(
            AnalyticsConstant.NoteHandWritingContent.EVENT_ID,
            AnalyticsConstant.NoteHandWritingContent.BALL_PEN_ACTIONS to ballPenAction,
            AnalyticsConstant.NoteHandWritingContent.BALL_PEN_DURATION to ballPenDuration,
        )
        reportAnalytics(params)
    }

    /**
     * MarkPen统计
     *
     * 上报时机：关闭编辑页时，如返回上一级、点击保存、自动保存时上报
     */
    fun reportMarkPenUsageWhenSaving(markPenAction:String, markPenDuration:String) {
        val params = hashMapOf(
            AnalyticsConstant.NoteHandWritingContent.EVENT_ID,
            AnalyticsConstant.NoteHandWritingContent.MART_PEN_ACTIONS to markPenAction,
            AnalyticsConstant.NoteHandWritingContent.MARK_PEN_DURATION to markPenDuration,
        )
        reportAnalytics(params)
    }

    /**
     * 编辑时长统计
     *
     * 上报时机：关闭编辑页时，如返回上一级、点击保存、自动保存时上报
     */
    fun reportEditDuration(editDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.NoteEdit.EVENT_ID,
            AnalyticsConstant.NoteEdit.EDIT_DURATION to editDuration,
        )
        reportAnalytics(params)
    }

    /**
     * @param transcribeId 记录转写录音条
     * @param transcribeLength 转写录音条时长
     * @param transcribeActionState 成功发送转写请求：0
     *                              无法转写-时长过长时：1
     *                              无法转写-时长过短时：2
     *                              无法转写-额度不足可续费时：3
     *                              无法转写-额度不足不可续费时：4
     *                              无网：5
     *                              其他：6
     *
     * 上报时机：点击AI录音转写时
     */
    fun reportRecordToTextUsage(transcribeId: String, transcribeLength: String, transcribeActionState: String) {
        val params = hashMapOf(
            AnalyticsConstant.RecordToText.EVENT_ID,
            AnalyticsConstant.RecordToText.TRANSCRIPE_ID to transcribeId,
            AnalyticsConstant.RecordToText.TRANSCRIPE_LENGTH to transcribeLength,
            AnalyticsConstant.RecordToText.TRANSCRIPE_ACTION_STATE to transcribeActionState,
        )
        reportAnalytics(params)
    }

    /**
     * @param transcribeAccountState 点击前往购买时：1
     *                               点击取消：0
     *
     * AI额度不足弹窗点击选项时
     */
    fun reportRecordToTextPurchaseIntention(transcribeAccountState: String) {
        val params = hashMapOf(
            AnalyticsConstant.RecordToText.EVENT_ID,
            AnalyticsConstant.RecordToText.TRANSCRIPE_ACCOUNT_STATE to transcribeAccountState,
        )
        reportAnalytics(params)
    }

    /**
     * @param transcribeState 转写成功：0
     *                        转写失败：1
     *                        其他：2
     * @param transcribeDuration 从点击转写至成功生成时长
     *
     * 录音转写展示数据完毕时
     */
    fun reportRecordToTextCompletionState(transcribeState: String, transcribeDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.RecordToText.EVENT_ID,
            AnalyticsConstant.RecordToText.TRANSCRIPE_STATE to transcribeState,
            AnalyticsConstant.RecordToText.TRANSCRIPE_DURATION to transcribeDuration,
        )
        reportAnalytics(params)
    }

    /**
     * @param transcribePause true or false
     * @param transcribePauseDuration 开始转写至点击停止的时长
     *
     * 录音转写展示数据完毕时
     */
    fun reportRecordToTextPause(transcribePause: String, transcribePauseDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.RecordToText.EVENT_ID,
            AnalyticsConstant.RecordToText.TRANSCRIPE_PAUSE to transcribePause,
            AnalyticsConstant.RecordToText.TRANSCRIPE_PAUSE_DURATION to transcribePauseDuration,
        )
        reportAnalytics(params)
    }

    /**
     * @param transcribeMax true or false
     *
     * 上报时机：拖拽最大化时
     */
    fun reportRecordToTextMax(transcribeMax: String) {
        val params = hashMapOf(
            AnalyticsConstant.RecordToText.EVENT_ID,
            AnalyticsConstant.RecordToText.TRANSCRIPE_MAX to transcribeMax,
        )
        reportAnalytics(params)
    }

    /**
     * @param transcribeCopy true or false
     *
     * 上报时机：点击复制时
     */
    fun reportRecordToTextCopy(transcribeCopy: String) {
        val params = hashMapOf(
            AnalyticsConstant.RecordToText.EVENT_ID,
            AnalyticsConstant.RecordToText.TRANSCRIPE_COPY to transcribeCopy,
        )
        reportAnalytics(params)
    }

    /**
     * @param transcripeReset true or false
     *
     * 上报时机：点击重试时
     */
    fun reportRecordToTextReset(transcripeReset: String) {
        val params = hashMapOf(
            AnalyticsConstant.RecordToText.EVENT_ID,
            AnalyticsConstant.RecordToText.TRANSCRIPE_RESET to transcripeReset,
        )
        reportAnalytics(params)
    }

    fun reportOcrUsage(ocrCount: String, ocrLang: String) {
        val params = hashMapOf(
            AnalyticsConstant.HandWritingToText.EVENT_ID,
            AnalyticsConstant.HandWritingToText.OCR_COUNT to ocrCount,
            AnalyticsConstant.HandWritingToText.OCR_LANG to ocrLang,
        )
        reportAnalytics(params)
    }

    fun reportOcrCompletionState(ocrState: String, ocrDuration: String, ocrLength: String) {
        val params = hashMapOf(
            AnalyticsConstant.HandWritingToText.EVENT_ID,
            AnalyticsConstant.HandWritingToText.OCR_DURATION to ocrDuration,
            AnalyticsConstant.HandWritingToText.OCR_LENGTH to ocrLength,
            AnalyticsConstant.HandWritingToText.OCR_STATE to ocrState,
        )
        reportAnalytics(params)
    }

    fun reportOcrStop(ocrStop: String, ocrStopDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.HandWritingToText.EVENT_ID,
            AnalyticsConstant.HandWritingToText.OCR_STOP to ocrStop,
            AnalyticsConstant.HandWritingToText.OCR_STOP_DURATION to ocrStopDuration,
        )
        reportAnalytics(params)
    }

    fun reportOcrReset(ocrReset: String) {
        val params = hashMapOf(
            AnalyticsConstant.HandWritingToText.EVENT_ID,
            AnalyticsConstant.HandWritingToText.OCR_RESET to ocrReset,
        )
        reportAnalytics(params)
    }

    fun reportOcrReplace(ocrReplace: String) {
        val params = hashMapOf(
            AnalyticsConstant.HandWritingToText.EVENT_ID,
            AnalyticsConstant.HandWritingToText.OCR_REPLACE to ocrReplace,
        )
        reportAnalytics(params)
    }

    fun reportOcrCopy(ocrCopy: String) {
        val params = hashMapOf(
            AnalyticsConstant.HandWritingToText.EVENT_ID,
            AnalyticsConstant.HandWritingToText.OCR_COPY to ocrCopy,
        )
        reportAnalytics(params)
    }

    fun reportOcrMax(ocrMax: String) {
        val params = hashMapOf(
            AnalyticsConstant.HandWritingToText.EVENT_ID,
            AnalyticsConstant.HandWritingToText.OCR_MAX to ocrMax,
        )
        reportAnalytics(params)
    }

    fun reportAiSummaryLoginState(summaryStartState: Boolean) {
        val loginState = if(summaryStartState) "1" else "0"
        val params = hashMapOf(
            AnalyticsConstant.AiSummary.EVENT_ID,
            AnalyticsConstant.AiSummary.SUMMARY_START_STATE to loginState,
        )
        reportAnalytics(params)
    }

    fun reportAiSummaryCompletionState(summaryReturnState: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiSummary.EVENT_ID,
            AnalyticsConstant.AiSummary.SUMMARY_RETURN_STATE to summaryReturnState,
        )
        reportAnalytics(params)
    }

    fun reportAiSummaryStop(summaryStop: String, summaryStopDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiSummary.EVENT_ID,
            AnalyticsConstant.AiSummary.SUMMARY_STOP to summaryStop,
            AnalyticsConstant.AiSummary.SUMMARY_STOP_DURATION to summaryStopDuration,
        )
        reportAnalytics(params)
    }

    fun reportAiSummaryCopy(summaryCopy: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiSummary.EVENT_ID,
            AnalyticsConstant.AiSummary.SUMMARY_COPY to summaryCopy,
        )
        reportAnalytics(params)
    }

    fun reportAiSummaryReset(summaryReset: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiSummary.EVENT_ID,
            AnalyticsConstant.AiSummary.SUMMARY_RESET to summaryReset,
        )
        reportAnalytics(params)
    }

    fun reportAiSummaryMax(summaryMax: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiSummary.EVENT_ID,
            AnalyticsConstant.AiSummary.SUMMARY_MAX to summaryMax,
        )
        reportAnalytics(params)
    }

    fun reportAiRewriteLoginState(rewriteStartState: Boolean) {
        val loginState = if(rewriteStartState) "1" else "0"
        val params = hashMapOf(
            AnalyticsConstant.AiRewrite.EVENT_ID,
            AnalyticsConstant.AiRewrite.REWRITE_START_STATE to loginState,
        )
        reportAnalytics(params)
    }

    fun reportAiRewriteTopic(rewriteTopic: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiRewrite.EVENT_ID,
            AnalyticsConstant.AiRewrite.REWRITE_TOPIC to rewriteTopic,
        )
        reportAnalytics(params)
    }

    fun reportAiRewriteCompletionState(rewriteReturnState: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiRewrite.EVENT_ID,
            AnalyticsConstant.AiRewrite.REWRITE_RETURN_STATE to rewriteReturnState,
        )
        reportAnalytics(params)
    }

    fun reportAiRewriteStop(rewriteStop: String, rewriteStopDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiRewrite.EVENT_ID,
            AnalyticsConstant.AiRewrite.REWRITE_STOP to rewriteStop,
            AnalyticsConstant.AiRewrite.REWRITE_STOP_DURATION to rewriteStopDuration,
        )
        reportAnalytics(params)
    }

    fun reportAiRewriteCopy(rewriteCopy: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiRewrite.EVENT_ID,
            AnalyticsConstant.AiRewrite.REWRITE_COPY to rewriteCopy,
        )
        reportAnalytics(params)
    }

    fun reportAiRewriteReset(rewriteReset: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiRewrite.EVENT_ID,
            AnalyticsConstant.AiRewrite.REWRITE_RESET to rewriteReset,
        )
        reportAnalytics(params)
    }

    fun reportAiRewriteMax(rewriteMax: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiRewrite.EVENT_ID,
            AnalyticsConstant.AiRewrite.REWRITE_MAX to rewriteMax,
        )
        reportAnalytics(params)
    }

    /**
     * @param writeStartState 未登录：0
     *                        已登录：1
     *                        其他：2
     *
     * 上报时机：点击AI帮写时
     */
    fun reportAiWriteLoginState(writeStartState: Boolean) {
        val loginState = if(writeStartState) "1" else "0"
        val params = hashMapOf(
            AnalyticsConstant.AiWrite.EVENT_ID,
            AnalyticsConstant.AiWrite.WRITE_START_STATE to loginState,
        )
        reportAnalytics(params)
    }

    /**
     * @param writeTopic 指令的值或ID
     *
     * 上报时机：点击发送时
     *
     */
    fun reportAiWriteTopic(writeTopic: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiWrite.EVENT_ID,
            AnalyticsConstant.AiWrite.WRITE_TOPIC to writeTopic,
        )
        reportAnalytics(params)
    }

    /**
     *  @param writeReturnState 成功：0
     *                          失败：1
     *                          其他：2
     *
     * 上报时机：生成内容展示完毕时
     */
    fun reportAiWriteCompletionState(writeReturnState: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiWrite.EVENT_ID,
            AnalyticsConstant.AiWrite.WRITE_RETURN_STATE to writeReturnState,
        )
        reportAnalytics(params)
    }

    /**
     * 上报时机：点击停止时
     */
    fun reportAiWriteStop(writeStop: String, writeStopDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiWrite.EVENT_ID,
            AnalyticsConstant.AiWrite.WRITE_STOP to writeStop,
            AnalyticsConstant.AiWrite.WRITE_STOP_DURATION to writeStopDuration,
        )
        reportAnalytics(params)
    }

    /**
     * 上报时机：点击复制时
     */
    fun reportAiWriteCopy(writeCopy: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiWrite.EVENT_ID,
            AnalyticsConstant.AiWrite.WRITE_COPY to writeCopy,
        )
        reportAnalytics(params)
    }

    /**
     * 上报时机：点击重试时
     */
    fun reportAiWriteReset(writeReset: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiWrite.EVENT_ID,
            AnalyticsConstant.AiWrite.WRITE_RESET to writeReset,
        )
        reportAnalytics(params)
    }

    /**
     * 上报时机：点击插入时
     */
    fun reportAiWriteInset(writeReset: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiWrite.EVENT_ID,
            AnalyticsConstant.AiWrite.WRITE_INSERT to writeReset,
        )
        reportAnalytics(params)
    }

    /**
     * 上报时机：拖拽最大化时
     */
    fun reportAiWriteMax(writeMax: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiWrite.EVENT_ID,
            AnalyticsConstant.AiWrite.WRITE_MAX to writeMax,
        )
        reportAnalytics(params)
    }

    /**
     * 统计二次修改Topic指令情况
     *
     * 上报时机：点击二次修改Topic
     */
    fun reportAiWriteResentTopic(resentTopic: String) {
        val params = hashMapOf(
            AnalyticsConstant.AiWrite.EVENT_ID,
            AnalyticsConstant.AiWrite.WRITE_RESENT_TOPIC to resentTopic,
        )
        reportAnalytics(params)
    }

    /**
     * 统计旅行日记用户单次使用情况及使用时长
     * */
    fun reportJournalUsageTime(appStartTime: Long) {
        fun formatTime(timeMillis: Long): String {
            val sdf = SimpleDateFormat("HH", Locale.getDefault())
            val hourTime: String = sdf.format(Date(timeMillis))
           return hourTime
        }
        var journalUsageTime = AppActivityManager.journalUsageTime
        if (AppActivityManager.journalUsageStartTime > 0) {
            journalUsageTime += (System.currentTimeMillis() - AppActivityManager.journalUsageStartTime)
        }
        reportJournalUsageTime(
            useTravel = (journalUsageTime/ 1000).toString(), // 旅行日记使用
            useApp = ((System.currentTimeMillis() - appStartTime) / 1000).toString(), // 应用使用
            startPeriod = formatTime(appStartTime) // 使用开始时间戳
        )
    }
    private fun reportJournalUsageTime(useTravel: String, useApp: String, startPeriod: String) {
        val params = hashMapOf(
            AnalyticsConstant.JournalUsageTime.EVENT_ID,
            AnalyticsConstant.JournalUsageTime.USE_TRAVEL to useTravel,
            AnalyticsConstant.JournalUsageTime.USE_APP to useApp,
            AnalyticsConstant.JournalUsageTime.START_PERIOD to startPeriod
        )
        reportAnalytics(params)
    }

    /**
     * 旅行日记新建/编辑数据
     * */
    fun reportJournalCreation(title: String, cover: String, type: String, opType: String, savedSuccess: String, lastEditedItem: Int = 0, useDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.JournalCreation.EVENT_ID,
            AnalyticsConstant.JournalCreation.TITLE_LENGTH to title.length.toString(),
            AnalyticsConstant.JournalCreation.COVER_PREFERENCE to cover,
            AnalyticsConstant.JournalCreation.TYPE_PREFERENCE to type.length.toString(),
            AnalyticsConstant.JournalCreation.OPERATION_TYPE to opType,
            AnalyticsConstant.JournalCreation.IS_SAVED_SUCCESSFULLY to savedSuccess,
            AnalyticsConstant.JournalCreation.LAST_EDITED_ITEM to lastEditedItem.toString(),
            AnalyticsConstant.JournalCreation.USE_DURATION to useDuration

        )
        reportAnalytics(params)
    }

    /**
     * 回忆集单页创建数据
     * */
    fun reportJournalContent(createType: String, recommendOn: Boolean, template:String, isAIUsed: String, aiRewriteCount: String, photoCount: String, wordCount: String, pageStayDuration: String) {
        val params = hashMapOf(
            AnalyticsConstant.JournalContentCreation.EVENT_ID,
            AnalyticsConstant.JournalContentCreation.IS_INSPIRATION_RECOMMENDATION_ON to recommendOn.judge("0", "1"),
            AnalyticsConstant.JournalContentCreation.CREATION_TYPE to createType,
            AnalyticsConstant.JournalContentCreation.TEMPLATE_INDEX to template,
            AnalyticsConstant.JournalContentCreation.IS_AI_USED to isAIUsed,
            AnalyticsConstant.JournalContentCreation.AI_REWRITE_COUNT to aiRewriteCount,
            AnalyticsConstant.JournalContentCreation.PHOTO_ADD_COUNT to photoCount,
            AnalyticsConstant.JournalContentCreation.WORD_COUNT to wordCount,
            AnalyticsConstant.JournalContentCreation.PAGE_STAY_DURATION to pageStayDuration,
        )
        reportAnalytics(params)
    }

    /**
     * AI帮写使用数据
     * */
    fun reportJournalAiWrite(createType: String, style: String, companion: String, wordCount: String, specialEventFilled: String,
                             specialEventStringCount: String, operationDuration: String, generationDuration: String, isAdopted: Boolean, isRewrite: Boolean) {
        val params = hashMapOf(
            AnalyticsConstant.JournalAiWrite.EVENT_ID,
            AnalyticsConstant.JournalAiWrite.CREATION_TYPE to createType,
            AnalyticsConstant.JournalAiWrite.WRITING_PREFERENCE_STYLE to style,
            AnalyticsConstant.JournalAiWrite.WRITING_PREFERENCE_COMPANION to companion,
            AnalyticsConstant.JournalAiWrite.WRITING_PREFERENCE_WORD_COUNT to wordCount,
            AnalyticsConstant.JournalAiWrite.IS_SPECIAL_EVENT_FILLED to specialEventFilled,
            AnalyticsConstant.JournalAiWrite.SPECIAL_EVENT_STRING_COUNT to specialEventStringCount,
            AnalyticsConstant.JournalAiWrite.WRITING_OPERATION_DURATION to operationDuration,
            AnalyticsConstant.JournalAiWrite.WRITING_GENERATION_DURATION to generationDuration,
            AnalyticsConstant.JournalAiWrite.IS_ADOPTED to isAdopted.judge("0", "1"),
            AnalyticsConstant.JournalAiWrite.IS_REWRITE_THIS_TIME to isRewrite.judge("0", "1"),
        )
        reportAnalytics(params)
    }

    /**
     * 旅行日记整体使用数据
     * */
    fun reportJournalUsage(journalCount: Int, createCount: String, deleteCount: String, viewMode: String, recommendOn: Boolean) {
        val params = hashMapOf(
            AnalyticsConstant.JournalUsage.EVENT_ID,
            AnalyticsConstant.JournalUsage.DIARY_COUNT to journalCount.toString(),
            AnalyticsConstant.JournalUsage.TODAY_CREATED_COUNT to createCount,
            AnalyticsConstant.JournalUsage.TODAY_DELETED_COUNT to deleteCount,
            AnalyticsConstant.JournalUsage.EXHIBITION_MODE to viewMode,
            AnalyticsConstant.JournalUsage.IS_INSPIRATION_RECOMMENDATION_ON to recommendOn.judge("0", "1"), //待修改
        )
        reportAnalytics(params)
    }
}

