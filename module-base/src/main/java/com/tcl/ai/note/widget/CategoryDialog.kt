package com.tcl.ai.note.widget


import android.annotation.SuppressLint
import android.util.Log
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.tcl.ai.note.base.BuildConfig
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.CursorColor
import com.tcl.ai.note.utils.getCategoryColor
import com.tcl.ai.note.utils.getCategoryColorArray
import com.tcl.ai.note.utils.getCategoryIconResId
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes

/**
 * 添加笔记分类对话框
 *
 * @param isAddCategoryMode 是否添加模式（有添加/修改两种模式，默认添加模式）
 * @param categoryName 分类名称
 * @param onCategoryNameChange 分类名称变更回调
 * @param selectedColorIndex 选中的颜色索引
 * @param onColorIndexSelected 颜色索引选择回调
 * @param isEnabled 确认按钮是否可用
 * @param isRepeated 分类名称是否重复
 * @param isOnChanged 值是否已更改
 * @param onDismiss 关闭对话框回调
 * @param onConfirm 确认回调
 */
@SuppressLint("UnusedBoxWithConstraintsScope", "DesignSystem")
@Composable
fun CategoryDialog(
    isAddCategoryMode: Boolean,
    categoryName: String,
    onCategoryNameChange: (String) -> Unit,
    selectedColorIndex: Int,
    onColorIndexSelected: (Int) -> Unit,
    isEnabled: Boolean,
    isRepeated: Boolean,
    isOnChanged: Boolean,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    // 使用选定的颜色索引获取颜色和图标
    val selectedColor = if (selectedColorIndex > 0) {
        colorResource(getCategoryColor(selectedColorIndex - 1))
    } else {
        colorResource(R.color.category_color_YELLOW)
    }

    val selectedIcon = if (selectedColorIndex > 0) {
        getCategoryIconResId(selectedColorIndex)
    } else {
        R.drawable.ic_category_yellow
    }

    var showColorPicker by remember { mutableStateOf(false) }
    val context = LocalContext.current
    val density = LocalDensity.current

    // IME，动画spring参数
    val imeInsets = WindowInsets.ime.getBottom(LocalDensity.current)
    val offsetY by animateDpAsState(
        targetValue = with(LocalDensity.current) { -imeInsets.toDp() },
        animationSpec = spring(
            dampingRatio = Spring.DampingRatioLowBouncy,
            stiffness = Spring.StiffnessMedium
        ),
        label = "dialog_on_home_keyboard_offset"
    )


    Dialog(
        onDismissRequest = onDismiss,
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false
        )
    ) {
        BoxWithConstraints(
            modifier = Modifier
                .fillMaxSize()
                .padding(start = 8.dp, end = 8.dp,top = 10.dp,
                    bottom = (imeInsets>0).judge(
                        BuildConfig.IS_PHONE.judge(70,40), 70).dp
                )
                .offset(y = with(LocalDensity.current) { offsetY })
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .widthIn(min = 344.dp,max = 380.dp)
                    .heightIn(187.dp,230.dp)
                    .clip(RoundedCornerShape(16.dp))
                    .background(colorResource(R.color.bg_dialog))
                    .padding(start = 24.dp, end = 24.dp, bottom = 5.dp, top = 24.dp)
            ) {
                Column {
                    Text(
                        text = if (isAddCategoryMode)
                            stringResource(R.string.dialog_category_name_title)
                        else
                            stringResource(R.string.dialog_category_rename_title),
                        fontSize =  with(density) { 20.dp.toSp() },
                        fontWeight = FontWeight.Medium,
                        color = colorResource(R.color.text_title)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Row(
                            modifier = Modifier
                                .semantics {
                                    contentDescription =context.getString(
                                        showColorPicker.judge(
                                            R.string.dialog_category_close_colour_swatches,
                                            R.string.dialog_category_expand_colour_swatches
                                        )
                                    )
                                    role = Role.Button
                                }
                                .clickable(
                                    interactionSource = remember { MutableInteractionSource() },
                                    indication = ripple(),
                                    onClick = { showColorPicker = !showColorPicker }
                                ),
                            verticalAlignment = Alignment.CenterVertically

                        ) {
                            Image(
                                painter = painterResource(id = selectedIcon),
                                contentDescription = null,
                                modifier = Modifier
                                    .padding(1.dp)
                                    .width(32.dp)
                                    .height(32.dp)
                            )
                            val icon = if(showColorPicker){
                                R.drawable.ic_arrow_drop_up
                            } else {
                                R.drawable.ic_arrow_drop_down
                            }

                            IconButton(
                                modifier = Modifier.wrapContentSize(),
                                onClick = { showColorPicker = !showColorPicker }
                            ){
                                Icon(
                                    painter = painterResource(id = icon),
                                    contentDescription =   showColorPicker.judge(
                                        R.string.dialog_category_close_colour_swatches,
                                        R.string.dialog_category_expand_colour_swatches
                                    ).stringRes(),
                                    modifier = Modifier.align(alignment = Alignment.CenterVertically)
                                )
                            }

                            Spacer(modifier = Modifier.width(8.dp))
                        }
                        Column(modifier = Modifier.padding(end = 6.dp)) {
                            BasicTextField(
                                value = categoryName,
                                onValueChange = { newText ->
                                    // 限制最大输入长度为200个字符
                                    if (newText.length <= 200) {
                                        onCategoryNameChange(newText)
                                    }
                                },
                                maxLines = 1,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .background(Color.Transparent),
                                decorationBox = { innerTextField ->
                                    Column {
                                        Box(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(bottom = 12.dp) // 为下划线留出空间
                                        ) {
                                            if (categoryName.isEmpty()) {
                                                Text(
                                                    text = stringResource(R.string.category_name),
                                                    color = colorResource(R.color.text_category_placeholder),
                                                    fontSize =  with(density) { 16.dp.toSp() },
                                                    lineHeight =  with(density) { 26.dp.toSp() },
                                                    overflow = TextOverflow.Ellipsis,
                                                    maxLines = 1
                                                )
                                            }
                                            innerTextField()
                                        }
                                        // 绘制下划线
                                        Divider(
                                            color = if (isRepeated && isOnChanged) Color.Red else colorResource(R.color.text_field_border),
                                            thickness = 1.dp
                                        )
                                    }
                                },
                                cursorBrush = SolidColor(CursorColor),
                                textStyle = LocalTextStyle.current.copy(
                                    color = colorResource(R.color.text_title),
                                    fontSize =  with(density) { 16.dp.toSp() },
                                    lineHeight =  with(density) { 26.dp.toSp() },
                                )
                            )
                            if (isRepeated && isOnChanged) {
                                Text(
                                    text = stringResource(R.string.category_name_exists),
                                    color = Color.Red
                                )
                            }
                        }
                    }

                    if(showColorPicker){
                        ColorPicker(
                            selectedColor = selectedColor,
                            onColorSelectedIndex = onColorIndexSelected,
                        )
                    } else {
                        Spacer(modifier = Modifier.height(36.dp))
                    }

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(36.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        TextButton(
                            onClick = onDismiss,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(
                                text = stringResource(R.string.category_cancel),
                                color = colorResource(R.color.btn_new_category_cancel),
                                fontSize =  with(density) { 14.dp.toSp() }
                            )
                        }
                        Box(
                            Modifier
                                .width(1.dp)
                                .height(24.dp)
                                .background(colorResource(R.color.btn_new_category_separator))
                        )
                        TextButton(
                            enabled = isEnabled,
                            onClick = onConfirm,
                            modifier = Modifier.weight(1f)
                        ) {
                            Text(
                                text = stringResource(
                                    if (isAddCategoryMode) R.string.category_add else R.string.confirm
                                ),
                                color = if(isEnabled)
                                    colorResource(R.color.btn_new_category_cancel)
                                else
                                    colorResource(R.color.btn_new_category_create),
                                fontSize =  with(density) { 14.dp.toSp() }
                            )
                        }
                    }
                }
            }
        }


    }
}

/**
 * 颜色选择器
 */
@Composable
internal fun ColorPicker(
    selectedColor: Color,
    onColorSelectedIndex: (Int) -> Unit,
//    onColorSelected: (Color) -> Unit = {}
) {
    //获取颜色列表
    val colors = getCategoryColorArray()
    Row(
        modifier = Modifier
            .padding(top = 15.dp, bottom = 15.dp)
            .horizontalScroll(rememberScrollState())
    ) {
        colors.forEachIndexed { index, color ->
            val curColor = colorResource(color)

            val contentDescription = when(index){
                0 -> R.string.dialog_category_colour_yellow.stringRes()
                1 ->R.string.dialog_category_colour_orange.stringRes()
                2 ->R.string.dialog_category_colour_pink.stringRes()
                3 ->R.string.dialog_category_colour_purple.stringRes()
                4 ->R.string.dialog_category_colour_azure.stringRes()
                5 ->R.string.dialog_category_colour_bluish.stringRes()
                6 ->R.string.dialog_category_colour_green.stringRes()
                7 ->R.string.dialog_category_colour_gray.stringRes()
                else ->""
            }
            HoverProofIconButton(
                onClick = {
                    onColorSelectedIndex(index + 1)
                },
                modifier = Modifier
                    .background(
                        color = colorResource(color),
                        shape = CircleShape
                    )
                    .size(24.dp)
                    .semantics {
                        this.contentDescription = contentDescription
                        this.role = Role.Button
                    },
            ) {
                //被选中的颜色加一个选中图标
                if(selectedColor == colorResource(color)){
                    Image(
                        painter = painterResource(id = R.drawable.ic_color_checked),
                        contentDescription = null,
                        modifier = Modifier.padding(4.dp)
                    )
                }
            }
            if(index < colors.size-1){
                Spacer(modifier = Modifier.width(14.dp))
            }
        }
    }
}


