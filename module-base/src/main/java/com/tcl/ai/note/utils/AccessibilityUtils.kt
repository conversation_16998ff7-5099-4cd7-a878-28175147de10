package com.tcl.ai.note.utils

import android.accessibilityservice.AccessibilityServiceInfo
import android.view.accessibility.AccessibilityManager
import com.tcl.ai.note.GlobalContext

object AccessibilityUtils {
    private val am: AccessibilityManager? =
        GlobalContext.instance.getSystemService(AccessibilityManager::class.java)

    // 利用触摸判断是否开启talkback
    val isExploreByTouchEnabled
        get() = am?.isEnabled == true && am.isTouchExplorationEnabled
}