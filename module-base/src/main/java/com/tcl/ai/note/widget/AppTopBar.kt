@file:Suppress("UNUSED_EXPRESSION")

package com.tcl.ai.note.widget

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.size
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.TclTheme.dimens

@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppTopBar(
    title: String,
    onBackClick: (() -> Unit)? = null,
    bgColor: Color? = null,
    barActions: @Composable RowScope.() -> Unit = {},
    ) {
    var lastClickTime by remember { mutableStateOf(0L) }
    val debouncePeriod = 600L  // 设置防抖的时间间隔，例如600毫秒
    TopAppBar(
        actions = barActions,
        title = {
            Text(
                title,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                fontSize = 20.sp,
                fontWeight = FontWeight.Medium
            )
        },
        navigationIcon = {
            onBackClick?.let {
                IconButton(onClick = {
                    // 增加点击防抖，避免快速点击多次出现多次响应的问题
                    val currentClickTime = System.currentTimeMillis()
                    if (currentClickTime - lastClickTime >= debouncePeriod) {
                        lastClickTime = currentClickTime
                        // 点击逻辑
                        onBackClick.invoke()
                    }
                }) {
                    Image(
                        painter = painterResource(id = R.drawable.arrow_back_24),
                        contentDescription = stringResource(id = R.string.edit_top_menu_back_icon),
                        modifier = Modifier.size(dimens.iconSize)
                    )
                }
            }
        },
        colors = if (bgColor == null) TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Transparent
        ) else TopAppBarDefaults.topAppBarColors(
            containerColor = bgColor
        )
    )
}


@Preview
@Composable
private fun AppTopBarPreview() {
    AppTopBar(title = "Title", {})
}