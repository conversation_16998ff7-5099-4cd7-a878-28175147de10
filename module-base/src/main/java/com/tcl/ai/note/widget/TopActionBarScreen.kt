package com.tcl.ai.note.dashboard.ui

import android.annotation.SuppressLint
import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.theme.NoteTclTheme
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.theme.getGlobalDimens
import com.tcl.ai.note.utils.adaptUIModifier
import com.tcl.ai.note.utils.adaptUIValue
import com.tcl.ai.note.utils.invisibleSemantics
import com.tcl.ai.note.widget.HorizontalLine
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.AutoScrollText

/**
 * 显示模式下首页头部功能区
 */
@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DisplayModeTopAppBar(
    currentCategoryName: String,
    viewType:String,
    backgroundColor: Color = TclTheme.colorScheme.tctTopAppBarBgColor,
    onSearchClicked: () -> Unit,
    onMoreActionsClicked: () -> Unit,
    onSortClicked: () -> Unit,
    onRenameCategory: () -> Unit,
    onDeleteCategory: () -> Unit,
    onTabClick: (Int) -> Unit = {},
    pageIndex: Int = 0,
    onTitleClicked: () -> Unit // 点击标题的回调，默认无操作
) {
    var isDropdownMenuExpanded by remember { mutableStateOf(false) }
    //笔记分类默认分类为“全部便签”
    val initSelectedCategory = stringResource(R.string.database_preset_category_all)
    // 当前分类
    val dimens = getGlobalDimens()
    val context = LocalContext.current
    Column {
        TopAppBar(
            expandedHeight =  dimens.navigationBarHeight,
            title = {
                Text(
                    text = stringResource(id = R.string.app_name),
                    fontSize = adaptUIValue(TclTheme.textSizes.titleMedium, TclTheme.textSizes.titleLarge),
                    fontWeight = FontWeight.Medium,
                    color = colorResource(R.color.text_title),
                    modifier = Modifier.padding(start = 0.dp).clickable{
                        onTitleClicked.invoke()
                    }
                )
                /*NoteTclTheme {
                    TabView(pageIndex = pageIndex) {
                        onTabClick(it)
                    }
                }*/
            },
            actions = {
                HoverProofIconButton(
                    onClick = onSearchClicked,
                    modifier = Modifier.adaptUIModifier(Modifier, Modifier.size(dimens.iconSize))
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_search),
                        contentDescription = stringResource(R.string.search_icon),
                    )
                }

                Spacer(modifier = Modifier.width(adaptUIValue(0, 16).dp))
                if (pageIndex == 0) {
                    HoverProofIconButton(
                        onClick = onMoreActionsClicked,
                        modifier = Modifier.adaptUIModifier(Modifier, Modifier.size(dimens.iconSize))
                    ) {
                        var id = R.drawable.ic_staggered_grid_show
                        if (viewType == DataStoreParam.VIEW_TYPE_GRID){
                            id = R.drawable.ic_list_show
                        }
                        Icon(
                            painter = painterResource(id = id),
                            contentDescription = if (viewType == DataStoreParam.VIEW_TYPE_GRID)stringResource(R.string.view_type_list) else stringResource(R.string.view_type_staggered_grid),
                        )
                    }

                    Spacer(modifier = Modifier.width(adaptUIValue(0, 16).dp))
                }
                HoverProofIconButton(
                    onClick = {isDropdownMenuExpanded = !isDropdownMenuExpanded},
                    modifier = Modifier.adaptUIModifier(Modifier, Modifier.size(dimens.iconSize))
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_sort),
                        contentDescription = stringResource(R.string.more_actions),
                    )
                }
                Spacer(modifier = Modifier.width(adaptUIValue(0, 8).dp))
                DropdownMenu(
                    expanded = isDropdownMenuExpanded,
                    onDismissRequest = { isDropdownMenuExpanded = false },
                    modifier = Modifier
                        .invisibleSemantics()
                        .clip(RoundedCornerShape(8.dp))
                        .background(
                            color = colorResource(R.color.bg_dialog),
                            shape = RoundedCornerShape(8.dp)
                        ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    // 排序
                    DropdownMenuItem(
                        text = { Text(stringResource(R.string.sort)) },
                        onClick = {
                            onSortClicked()
                            isDropdownMenuExpanded = false
                        }
                    )
                    // 全部分类和未分类的不能重命名和删除分类
                    if(currentCategoryName.isNotEmpty() && currentCategoryName != stringResource(R.string.database_preset_category_none)){
                        // 重命名分类
                        DropdownMenuItem(
                            text = {Text(stringResource(R.string.rename_category))},
                            onClick = {
                                onRenameCategory()
                                isDropdownMenuExpanded = false
                            },
                        )

                        // 删除分类
                        DropdownMenuItem(
                            text = {Text(stringResource(R.string.delete_category))},
                            onClick = {
                                onDeleteCategory()
                                isDropdownMenuExpanded = false
                            },
                        )
                    }
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor
            )
        )

        HorizontalLine()
    }
}


/**
 * 长按编辑模式下首页头部功能区
 */
@SuppressLint("DesignSystem")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditModeTopAppBar(
    backgroundColor: Color = TclTheme.colorScheme.tctGlobalBgColor,
    isSelectedAllMode: Boolean,
    selectedItemCount: Set<Int>,
    onCancel: () -> Unit,
    onSelectedAll: () -> Unit,
    onDeleteSelected: () -> Unit,
    onMoveTo: () -> Unit,
    onTabClick: (Int) -> Unit = {},
    pageIndex: Int = 0,
) {
    var isDropdownMenuExpanded by remember { mutableStateOf(false) }
    Column {
        var title = stringResource(R.string.select_items)
        if(selectedItemCount.isNotEmpty()){
            title = selectedItemCount.size.toString()
        }
        TopAppBar(
            expandedHeight = getGlobalDimens().navigationBarHeight,
            title = {
                AutoScrollText(
                    text = title,
                    fontSize = TclTheme.textSizes.titleMedium,
                    color = colorResource(R.color.text_title)
                )
            },
            navigationIcon = {
                // 取消已选中的回到显示模式
                IconButton(onClick = onCancel) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_cancel_selected),
                        contentDescription = stringResource(id = R.string.cancel),
                    )
                }
            },
            actions = {
                // 全选
                IconButton(
                    onClick = onSelectedAll,
                ) {
                    Icon(
                        painter = painterResource(id = isSelectedAllMode.judge(R.drawable.ic_selected_all_checked,R.drawable.ic_selected_all)),
                        contentDescription = stringResource(id = R.string.item_top_check_all),
                    )
                }
                // 删除
                IconButton(
                    enabled = selectedItemCount.isNotEmpty(),
                    onClick = onDeleteSelected,
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_delete_selected),
                        contentDescription = stringResource(id = R.string.list_top_delete),
                    )
                }
                // 一下功能第一期暂时无需做，二期的功能，暂时隐藏
                // 移至某一个分类
                IconButton(
                    onClick = { isDropdownMenuExpanded = !isDropdownMenuExpanded }
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.ic_sort),
                        contentDescription = "Search icon",
                    )
                }
                DropdownMenu(
                    expanded = isDropdownMenuExpanded,
                    onDismissRequest = { isDropdownMenuExpanded = false },
                    modifier = Modifier
                        .clip(RoundedCornerShape(8.dp))
                        .background(
                            color = colorResource(R.color.bg_dialog),
                            shape = RoundedCornerShape(8.dp)
                        ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    DropdownMenuItem(
                        enabled = selectedItemCount.isNotEmpty(),
                        text = {
                            if(selectedItemCount.isEmpty()){
                                Text(
                                    text = stringResource(R.string.move_to),
                                    color = colorResource(R.color.text_summary),
                                )
                            }else{
                                Text(stringResource(R.string.move_to))
                            }
                        },
                        onClick = {
                            onMoveTo()
                            isDropdownMenuExpanded = false
                        },
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = backgroundColor
            )
        )
        Divider(
            color = colorResource(R.color.bg_outline_5),
            thickness = 1.dp,
            modifier = Modifier.fillMaxWidth()
        )
    }

}