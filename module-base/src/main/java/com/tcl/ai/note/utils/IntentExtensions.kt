package com.tcl.ai.note.utils

import android.content.Intent
import android.os.Bundle
import java.io.Serializable

fun Intent.putExtra(vararg pairs: Pair<String, Any>): Intent {
    pairs.forEach {
        when (it.second) {
            is Int -> this.putExtra(it.first, it.second as Int)
            is Boolean -> this.putExtra(it.first, it.second as Boolean)
            is String -> this.putExtra(it.first, it.second as String)
            is Long -> this.putExtra(it.first, it.second as Long)
            is Float -> this.putExtra(it.first, it.second as Float)
            is Serializable -> this.putExtra(it.first, it.second as Serializable)
        }
    }
    return this
}

fun Bundle.put(vararg pairs: Pair<String, Any>): Bundle {
    pairs.forEach {
        when (it.second) {
            is Int -> this.putInt(it.first, it.second as Int)
            is Boolean -> this.putBoolean(it.first, it.second as Boolean)
            is String -> this.putString(it.first, it.second as String)
            is Long -> this.putLong(it.first, it.second as Long)
            is Float -> this.putFloat(it.first, it.second as Float)
            is Serializable -> this.putSerializable(it.first, it.second as Serializable)
        }
    }
    return this
}