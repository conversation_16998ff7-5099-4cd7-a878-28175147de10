package com.tcl.ai.note.widget.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionResult
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition

import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.isTabletLandscape


@Composable
fun GenerateInProgressView(
    isOffline: Boolean,
    isRowLayout: Boolean = isTabletLandscape,
) {
    val content = if (isOffline) {
        stringResource(id = R.string.network_error)
    } else {
        stringResource(id = R.string.downloading_tip)
    }
    GenerateInProgressView(isOffline, isRowLayout, content)
}

/**
 * 重载一个需要可以传入文字的 GenerateInProgressView
 */
@Composable
fun GenerateInProgressView(
    isOffline: Boolean,
    isRowLayout: Boolean = false,
    content: String = ""
) {
    val animationSpec =
        rememberLottieCompositionResult()
    val progress by animateLottieCompositionAsState(
        composition = animationSpec.value,
        isPlaying = true,
        iterations = LottieConstants.IterateForever
    )
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        if (isRowLayout) {
            // 平板横屏模式下使用左右结构
            Row(
                modifier = Modifier
                    .wrapContentSize()
                    .align(Alignment.Center),
                verticalAlignment = Alignment.CenterVertically
            ) {
                LottieAnimation(
                    composition = animationSpec.value,
                    progress = progress,
                    modifier = Modifier
                        .size(26.dp),
                    alignment = Alignment.Center,
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = content,
                    textAlign = TextAlign.Start,  // 左右结构中文本左对齐更自然
                    modifier = Modifier
                        .wrapContentWidth(),
                    fontSize = 13.sp  //
                )
            }
        } else {
            // 手机或平板竖屏模式下使用上下结构
            Column(
                modifier = Modifier
                    .wrapContentSize()
                    .align(Alignment.Center),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                LottieAnimation(
                    composition = animationSpec.value,
                    progress = progress,
                    modifier = Modifier
                        .size(64.dp),
                    alignment = Alignment.Center,
                )
                Spacer(modifier = Modifier.height(12.dp))
                Text(
                    text = content,
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentWidth(Alignment.CenterHorizontally)
                        .padding(horizontal = 20.dp),
                    fontSize = 14.sp
                )
            }
        }
    }
}

@Composable
private fun rememberLottieCompositionResult(): LottieCompositionResult {
    val animationSpec =
        rememberLottieComposition(LottieCompositionSpec.RawRes(R.raw.record_generate_in_progress))
    return animationSpec
}

@Composable
@Preview(backgroundColor = 0xFFFFFFFF, showBackground = true)
fun GenerateInProgressViewPreview() {
    GenerateInProgressView(true, isRowLayout = true)
}