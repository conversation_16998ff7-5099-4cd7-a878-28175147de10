package com.tcl.ai.note.widget

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.drawable.VectorDrawable
import androidx.annotation.DrawableRes
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.ShaderBrush
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import com.tcl.ai.note.base.R


@Composable
fun Modifier.verticalScrollbar(
    state: ScrollState,
    alwaysShowScrollBar: Boolean = false,
    scrollbarWidth: Dp = 3.dp,
    color: Color = colorResource(id = R.color.tct_vertical_scrollbar_color),
    offsetX: Float = 0f,
    offsetY: Float = 0f,
    radius: Dp = 10.dp,
    scrollbarHeightOffsetY: Float = 0f
): Modifier {
    val scrollInProgress = state.isScrollInProgress
    val alpha by animateFloatAsState(
        targetValue = if (scrollInProgress || alwaysShowScrollBar) 1f else 0f,
        animationSpec = tween(
            400,
            delayMillis = if (scrollInProgress || alwaysShowScrollBar) 0 else 700
        ),
        label = ""
    )

    return this then Modifier.drawWithContent {
        drawContent()


        val viewHeight = state.viewportSize.toFloat()
        val contentHeight = state.maxValue + viewHeight
        //当组件高度太小的时候就无法绘制滚动条，此时会抛出异常
        try {
            // 计算滚动条高度，但确保 viewHeight / contentHeight 不会导致无效范围
            val scrollbarHeightRatio = (viewHeight / contentHeight).coerceAtLeast(0f)
            val calculatedHeight = viewHeight * scrollbarHeightRatio

            // 确保范围的起始值小于结束值
            val minHeight = 10.dp.toPx()

            // 使用 minOf 和 maxOf 确保范围有效
            val scrollbarHeight = calculatedHeight.coerceIn(
                minOf(minHeight, viewHeight)..maxOf(
                    minHeight,
                    viewHeight
                )
            )

            val variableZone = viewHeight - scrollbarHeight - scrollbarHeightOffsetY
            val scrollbarYoffset = (state.value.toFloat() / state.maxValue) * variableZone
            drawRoundRect(
                cornerRadius = CornerRadius(radius.toPx(), radius.toPx()),
                color = color,
                topLeft = Offset(
                    this.size.width - scrollbarWidth.toPx() - offsetX,
                    scrollbarYoffset + offsetY
                ),
                size = Size(scrollbarWidth.toPx(), scrollbarHeight),
                alpha = alpha
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }
}

@Composable
fun Modifier.verticalScrollbar(
    state: LazyListState,
    alwaysShowScrollBar: Boolean = false,
    scrollbarWidth: Dp = 3.dp,
    color: Color = colorResource(id = R.color.tct_vertical_scrollbar_color),
    offsetX: Float = 0f,
    offsetY: Float = 0f,
    radius: Dp = 10.dp,
    scrollbarHeightOffsetY: Float = 0f
): Modifier {
    val scrollInProgress = state.isScrollInProgress
    val alpha by animateFloatAsState(
        targetValue = if (scrollInProgress || alwaysShowScrollBar) 1f else 0f,
        animationSpec = tween(
            400,
            delayMillis = if (scrollInProgress || alwaysShowScrollBar) 0 else 700
        ),
        label = ""
    )

    return this then Modifier.drawWithContent {
        drawContent()

        val layoutInfo = state.layoutInfo
        if (layoutInfo.totalItemsCount == 0) return@drawWithContent

        try {
            val viewHeight = this.size.height
            val firstVisibleItemIndex = layoutInfo.visibleItemsInfo.firstOrNull()?.index ?: 0
            val lastVisibleItemIndex = layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0

            // 计算内容总高度的估算值
            val averageItemHeight = if (layoutInfo.visibleItemsInfo.isNotEmpty()) {
                layoutInfo.visibleItemsInfo.sumOf { it.size } / layoutInfo.visibleItemsInfo.size.toFloat()
            } else {
                50f // 默认项目高度
            }
            val estimatedContentHeight = layoutInfo.totalItemsCount * averageItemHeight

            // 计算当前滚动位置
            val firstVisibleItemOffset = layoutInfo.visibleItemsInfo.firstOrNull()?.offset ?: 0
            val scrollOffset = firstVisibleItemIndex * averageItemHeight - firstVisibleItemOffset

            // 计算滚动条高度比例
            val scrollbarHeightRatio = (viewHeight / estimatedContentHeight).coerceIn(0f, 1f)
            val minHeight = 10.dp.toPx()
            val scrollbarHeight = (viewHeight * scrollbarHeightRatio).coerceAtLeast(minHeight)

            // 计算滚动条位置
            val scrollProgress = if (estimatedContentHeight > viewHeight) {
                (scrollOffset / (estimatedContentHeight - viewHeight)).coerceIn(0f, 1f)
            } else {
                0f
            }

            val variableZone = viewHeight - scrollbarHeight - scrollbarHeightOffsetY
            val scrollbarYOffset = scrollProgress * variableZone

            drawRoundRect(
                cornerRadius = CornerRadius(radius.toPx(), radius.toPx()),
                color = color,
                topLeft = Offset(
                    this.size.width - scrollbarWidth.toPx() - offsetX,
                    scrollbarYOffset + offsetY
                ),
                size = Size(scrollbarWidth.toPx(), scrollbarHeight),
                alpha = alpha
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

@Composable
fun Modifier.drawBehindWithDrawableRes(
    context: Context,
    @DrawableRes imgRes: Int,
    offset: Offset
): Modifier {
    (ContextCompat.getDrawable(context, imgRes) as? VectorDrawable)?.let { imageId ->
        val bitmap = Bitmap.createBitmap(
            imageId.intrinsicWidth,
            imageId.intrinsicHeight,
            Bitmap.Config.ARGB_8888
        )
        val canvas = Canvas(bitmap)
        imageId.setBounds(0, 0, canvas.width, canvas.height)
        imageId.draw(canvas)
        return this.drawBehind {
            drawImage(
                topLeft = offset,
                image = bitmap.asImageBitmap()
            )
        }
    }
    return Modifier
}

@Composable
fun Modifier.drawFadeOutRegionHorizontally(
    containColor: Color,
    regionColor: Color,
    fadeSize: Dp
): Modifier {

    return this.drawWithCache {
        onDrawWithContent {
            drawContent()
            drawIntoCanvas {
                val rightBrush = Brush.horizontalGradient(
                    colors = listOf(containColor, regionColor),
                    startX = this.size.width - fadeSize.toPx(),
                    endX = this.size.width
                )
                val rightPaint = Paint().apply {
                    asFrameworkPaint().shader = (rightBrush as? ShaderBrush)?.createShader(
                        Size(width = fadeSize.toPx(), height = <EMAIL>)
                    )
                }
                it.drawRect(
                    left = this.size.width - fadeSize.toPx(),
                    top = 0f,
                    right = this.size.width,
                    bottom = this.size.height,
                    paint = rightPaint
                )

                val leftBrush = Brush.horizontalGradient(
                    colors = listOf(regionColor, containColor),
                    startX = 0f,
                    endX = fadeSize.toPx()
                )
                val leftPaint = Paint().apply {
                    asFrameworkPaint().shader = (leftBrush as? ShaderBrush)?.createShader(
                        Size(width = fadeSize.toPx(), height = <EMAIL>)
                    )
                }
                it.drawRect(
                    left = 0f,
                    top = 0f,
                    right = fadeSize.toPx(),
                    bottom = this.size.height,
                    paint = leftPaint
                )
            }
        }
    }

}