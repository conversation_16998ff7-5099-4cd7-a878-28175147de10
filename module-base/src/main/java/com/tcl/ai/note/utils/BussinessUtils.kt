package com.tcl.ai.note.utils

import com.tcl.ai.note.base.R


fun getCategoryColorArray(): IntArray {
    var colorArray: IntArray? = null
    colorArray = intArrayOf(
        R.color.category_color_PINK,
        R.color.category_color_PURPLE,
        R.color.category_color_BLUE,
        R.color.category_color_GREEN,
        R.color.category_color_YELLOW,
        R.color.category_color_ORANGE
    )
    return colorArray
}

fun getCategoryColor(colorIndex: Int): Int {
    val colorArray: IntArray = getCategoryColorArray()
    if (colorIndex < 0 || colorIndex >= colorArray.size) {
        return R.color.category_color_PINK
    }
    return colorArray[colorIndex]
}

/**
 * @param colorIndex CategoryColors
 * 根据colorIndex获取分类图标
 *
 */
fun getCategoryIconResId(colorIndex: Int): Int {
    if (colorIndex < 0 || colorIndex > CategoryColors.ALL_COLOR) {
        return R.drawable.ic_category_pink
    }
    return when (colorIndex) {
        CategoryColors.NONE_COLOR -> R.drawable.ic_category_uncategorised // 未分类
        CategoryColors.YELLOW_COLOR -> R.drawable.ic_category_yellow
        CategoryColors.ORANGE_COLOR -> R.drawable.ic_category_orange
        CategoryColors.PINK_COLOR -> R.drawable.ic_category_pink
        CategoryColors.PURPLE_COLOR -> R.drawable.ic_category_purple
        CategoryColors.BLUE_COLOR -> R.drawable.ic_category_blue
        CategoryColors.GREEN_COLOR -> R.drawable.ic_category_green
        else -> R.drawable.ic_all_notes
    }
}

/**
 * 根据保存的colorIndex获取分类图标的颜色
 */
fun getCategoryIconColor(colorIndex: Int): Int {
    return (when (colorIndex) {
        CategoryColors.NONE_COLOR -> R.color.category_color_GRAY // 未分类
        CategoryColors.YELLOW_COLOR -> R.color.category_color_YELLOW
        CategoryColors.ORANGE_COLOR -> R.color.category_color_ORANGE
        CategoryColors.PINK_COLOR -> R.color.category_color_PINK
        CategoryColors.PURPLE_COLOR -> R.color.category_color_PURPLE
        CategoryColors.BLUE_COLOR -> R.color.category_color_BLUE
        CategoryColors.GREEN_COLOR -> R.color.category_color_GREEN
        else -> R.color.category_color_GRAY
    })
}



// 注意这是硬编码, 返回按定义顺序排列的颜色列表（1~8）
// 使用 lazy 委托，确保只计算一次并缓存结果
val allCategoryColorIndices by lazy {
    listOf(
        CategoryColors.PINK_COLOR,
        CategoryColors.PURPLE_COLOR,
        CategoryColors.BLUE_COLOR,
        CategoryColors.GREEN_COLOR,
        CategoryColors.YELLOW_COLOR,
        CategoryColors.ORANGE_COLOR,
    )
}



// 获取使用频率最小的颜色（优先按定义顺序）
fun <T> findLeastUsedColor(items: List<T>, getColorIndex: (T) -> Int): Int? {
    // 1. 获取有序颜色列表
    val allColors = allCategoryColorIndices
    if (allColors.isEmpty()) return null

    // 2. 使用 LinkedHashMap 统计频率（保证顺序）
    val colorFrequency = allColors.associateWithTo(LinkedHashMap()) { color ->
        items.count { getColorIndex(it) == color } // 计算每个颜色的使用次数
    }

    // 3. 找到最小频率值
    val minFrequency = colorFrequency.minByOrNull { it.value }?.value

    // 4. 按颜色定义顺序返回第一个最小频率的颜色
    val result = allColors.firstOrNull { color ->
        colorFrequency[color] == minFrequency
    }
    return result // 打日志方便
}
