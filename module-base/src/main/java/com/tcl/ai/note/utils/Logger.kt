package com.tcl.ai.note.utils

import android.util.Log
import com.tcl.ai.note.GlobalContext

object Logger {
    private const val TAG = "Note"
    private const val MIN_LEVEL = Log.VERBOSE
    private const val SP_KEY_LOG_ENABLED = "sp_key_log_enabled"

    @JvmStatic
    fun d(tag: String, message: String) {
        if (Log.DEBUG >= MIN_LEVEL) {
            val length = message.length
            if (length <= 4000) {
                Log.i("${TAG}_${tag}", message)
                return
            }
            var i = 0
            while (i < length) {
                val splitContent =
                    if (i + 4000 < length) message.substring(i, i + 4000) else message.substring(
                        i,
                        length
                    )
                Log.i("${TAG}_${tag}", splitContent)
                i += 4000
            }
        }
    }

    /**
     * 写死tag的d方法
     */
    @JvmStatic
    fun d(message: String) {
        if (Log.DEBUG >= MIN_LEVEL) {
            Log.d("${TAG}_", message)
        }
    }

    @JvmStatic
    fun e(tag: String, message: String) {
        if (Log.ERROR >= MIN_LEVEL) {
            Log.e("${TAG}_${tag}", message)
        }
    }

    @JvmStatic
    fun w(tag: String, message: String) {
        if (Log.WARN >= MIN_LEVEL) {
            Log.w("${TAG}_${tag}", message)
        }
    }

    @JvmStatic
    fun v(tag: String, message: String) {
        if (Log.VERBOSE >= MIN_LEVEL) {
            Log.v("${TAG}_${tag}", message)
        }
    }

    @JvmStatic
    fun i(tag: String, message: String) {
        if (Log.INFO >= MIN_LEVEL) {
            Log.i("${TAG}_${tag}", message)
        }
    }

    @JvmStatic
    fun i(message: String) {
        if (Log.INFO >= MIN_LEVEL) {
            Log.i("${TAG}_", message)
        }
    }

    /**
     * 打印调用栈
     */
    fun printStackTrace(tag: String, message: String = "") {
        if (Log.DEBUG >= MIN_LEVEL) {
            runCatching {
                throw Exception(message)
            }.onFailure {
                Logger.w(tag, it.stackTraceToString())
            }
        }
    }

    private val mIsLogEnabled by lazy {
        SPUtils.getBoolean(
            context = GlobalContext.instance,
            key = SP_KEY_LOG_ENABLED,
            defValue = true
        )
    }

    var isLogEnabled: Boolean
        get() = mIsLogEnabled
        set(value) {
            SPUtils.setSync(
                context = GlobalContext.instance,
                key = SP_KEY_LOG_ENABLED,
                value = value,
            )
        }
}