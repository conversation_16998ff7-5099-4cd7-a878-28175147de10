package com.tcl.ai.note.utils

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow

/**
 * Toast管理器 - 可以在Composable中使用，支持连续点击时只显示最新内容且不叠加
 */
object ComposableToast {
    private var currentToast: Toast? = null
    private var isShowing = false
    private val handler = Handler(Looper.getMainLooper())
    private val toastFlow = MutableStateFlow<ToastData?>(null)

    /**
     * 显示Toast
     *
     * @param context 上下文
     * @param message 消息内容
     * @param duration Toast显示时长 Toast.LENGTH_SHORT 或 Toast.LENGTH_LONG
     */
    fun show(context: Context, message: String, duration: Int = Toast.LENGTH_SHORT) {
        // 取消当前显示的Toast
        currentToast?.cancel()
        isShowing = false
        
        // 创建新的Toast
        currentToast = Toast.makeText(context, message, duration)
        currentToast?.show()
        
        // 更新状态流，以便Composable组件能感知变化
        toastFlow.value = ToastData(message, duration)
        
        // 记录显示状态
        isShowing = true
        
        // 设置显示结束的回调
        val displayTime = if (duration == Toast.LENGTH_SHORT) 2000L else 3500L
        handler.removeCallbacksAndMessages(null)
        handler.postDelayed({
            isShowing = false
            toastFlow.value = null
        }, displayTime)
    }
    
    /**
     * 获取Toast状态流
     */
    fun getToastStateFlow(): StateFlow<ToastData?> = toastFlow
    
    /**
     * 判断是否正在显示
     */
    fun isShowing(): Boolean = isShowing
}

/**
 * Toast数据类
 */
data class ToastData(
    val message: String,
    val duration: Int
)

/**
 * 在Composable中使用的Toast
 *
 * @param message 消息内容
 * @param duration Toast显示时长 Toast.LENGTH_SHORT 或 Toast.LENGTH_LONG
 */
@Composable
fun ComposeToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
    val context = LocalContext.current
    LaunchedEffect(message, duration) {
        ComposableToast.show(context, message, duration)
    }
}

/**
 * Toast状态管理Hook
 */
@Composable
fun rememberToastState(): MutableState<String?> {
    val context = LocalContext.current
    val toastState = remember { mutableStateOf<String?>(null) }
    
    LaunchedEffect(toastState.value) {
        toastState.value?.let {
            ComposableToast.show(context, it)
            toastState.value = null
        }
    }
    
    return toastState
} 