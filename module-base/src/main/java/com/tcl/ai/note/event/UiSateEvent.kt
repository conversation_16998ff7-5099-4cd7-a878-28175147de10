package com.tcl.ai.note.event

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow

object UiSateEvent {
    private val _uiStateChangeEvent = MutableSharedFlow<Int>(extraBufferCapacity = 1)
    val uiStateChangeEvent = _uiStateChangeEvent.asSharedFlow()

    /**
     * 发送UiState变化回调事件
     */
    suspend fun sendUiStateChangeCallBackEvent(size: Int) {
        _uiStateChangeEvent.emit(size)
    }
}