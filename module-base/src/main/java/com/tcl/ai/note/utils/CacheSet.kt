package com.tcl.ai.note.utils

open class CacheMap<K, V>(val maxSize: Int) : LinkedHashMap<K, V>(maxSize, 0.75f, false) {
    override fun removeEldestEntry(eldest: MutableMap.MutableEntry<K, V>?): Boolean {
        return size > maxSize
    }
}

class CacheSet<T>(maxSize: Int) : CacheMap<T, Unit>(maxSize) {
    fun add(key: T) {
        this[key] = Unit
    }

    fun contains(key: T) =
        this.containsKey(key)
}