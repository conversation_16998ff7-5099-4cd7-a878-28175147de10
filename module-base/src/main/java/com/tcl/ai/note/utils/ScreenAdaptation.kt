package com.tcl.ai.note.utils

import androidx.compose.ui.Modifier


// 屏幕适配参数值方法，适配平板横屏、平板竖屏与手机竖屏
fun <T> adaptUIValue(
    phonePortraitValue: T, tabletAndLandScapeValue: T, tabletAndPortraitValue: T? = null
): T {
    return if (isTablet) {
        return (if (isLandScape) {
            tabletAndLandScapeValue
        } else {
            tabletAndPortraitValue ?: tabletAndLandScapeValue
        })
    } else phonePortraitValue   // phone
}


// 屏幕适配 Modifier 方法，适配平板横屏、平板竖屏与手机竖屏
fun Modifier.adaptUIModifier(
    phonePortraitModifier: Modifier,
    tabletAndLandScapeModifier: Modifier,
    tabletAndPortraitModifier: Modifier? = null
): Modifier {
    return if (isTablet) {
        if (isLandScape) {
            this.then(tabletAndLandScapeModifier)
        } else {
            this.then(tabletAndPortraitModifier ?: tabletAndLandScapeModifier)
        }
    } else phonePortraitModifier
}
