package com.tcl.ai.note.widget.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.state.ChatEffect
import com.tcl.ai.note.state.ChatStreamingMsg
import com.tcl.ai.note.state.Result
import com.tcl.ai.note.theme.AI_CONTENT_PADDING_HORIZONTAL
import com.tcl.ai.note.theme.AI_CONTENT_PADDING_VERTICAL
import com.tcl.ai.note.theme.CornerShapeAIBottomDialogContent
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.ToastUtils
import com.tcl.ai.note.utils.rememberIsInMultiWindowMode
import com.tcl.ai.note.utils.startTCLAIChargeActivity
import com.tcl.ai.note.widget.ExpandPanel
import com.tcl.ai.note.widget.UsageLimitDialog
import com.tcl.ai.note.widget.verticalScrollbar

@Composable
fun AIShowContentText(
    state: Result.Success<ChatStreamingMsg>,
) {
    val density = LocalDensity.current
    val scrollState = rememberScrollState()
    LaunchedEffect(key1 = state) {
        scrollState.animateScrollTo(scrollState.maxValue)
    }
    val isScrollEnd = remember {
        derivedStateOf {
            scrollState.value==scrollState.maxValue
        }
    }
    val text = state.data.text
    BottomFadeBox(modifier = Modifier.fillMaxWidth(),isDrawGradient = !isScrollEnd.value){
        Text(
            text = text,
            fontSize = 16.sp,
            modifier = Modifier
                .fillMaxWidth()
                .semantics {
                    contentDescription= text
                }
                .verticalScrollbar(
                    state = scrollState,
                    scrollbarHeightOffsetY = with(density) { (10.dp).toPx() })
                .verticalScroll(scrollState)
        )
    }
}

/**
 * AI 通用的结果面板Column布局
 * 来统一调整内外边距 顶部没有padding
 */
@Composable
fun AICommonResultPanelColumn(modifier: Modifier = Modifier,bottom:Dp = 16.dp, content: @Composable ColumnScope.() -> Unit) {
    //外边距
    val isInMultiWindow= rememberIsInMultiWindowMode()
    val outMargin =if (isInMultiWindow) 10.dp else 16.dp
    val innerMargin =if (isInMultiWindow) 10.dp else AI_CONTENT_PADDING_VERTICAL

    Column(
        modifier = modifier
            .padding(start = outMargin, bottom = bottom, end = outMargin)
//            .height(295.dp)
            .clip(CornerShapeAIBottomDialogContent)
            .background(color = TclTheme.colorScheme.tctResultBgColor)
            .padding(vertical = innerMargin, horizontal = AI_CONTENT_PADDING_HORIZONTAL).padding() //内边距
    ) {
        content()
    }
}

/**
 * 封装通用的Toast和 用户限制 Dialog
 */
@Composable
 fun ShowToastAndDialog(effect: ChatEffect?, onLearnMoreClick: () -> Unit) {
    val context = LocalContext.current
    var showLimitDialog by remember {
        mutableStateOf(false)
    }
    LaunchedEffect(effect) {
        Logger.d(tag = "ShowToastAndDialog", message ="effect:$effect")
        if (effect != null && effect is ChatEffect.ShowToastRes) {
            ToastUtils.makeWithCancel(effect.resourceId)
        }
        if (effect is ChatEffect.ErrUsageLimited) {
            showLimitDialog = true
        }
    }
    if (showLimitDialog) {
            UsageLimitDialog(
                onDismissRequest = {
                    showLimitDialog = false
                },
                onLearnMoreClick = {
                    showLimitDialog = false
                    onLearnMoreClick()
                    context.startTCLAIChargeActivity()
                }
            )

    }
}

/**
 * 标题上的关闭按钮,四个弹窗复用
 */
@Composable
fun AIHeaderCloseImage(onCloseClick: () -> Unit) {
    Image(
        painter = painterResource(id = R.drawable.close),
        contentDescription = stringResource(R.string.close),
        modifier = Modifier.clickable(
            interactionSource = null,
            indication = ripple(bounded = false)
        ) {
            onCloseClick()
        }
    )
}
/**
 * 顶部展开的 拓展文本内容
 */
@Composable
 fun ExpandedTextContent(
    textExpanded: Boolean,
    changeTextExpanded: () -> Unit,
    paddingTop: Float,
    originalText: String
) {
    Logger.d(tag = "ReWriteContent", message ="textExpanded:$textExpanded paddingTop:$paddingTop")
    Box(modifier = Modifier.fillMaxSize()) {
        val focusManager = LocalFocusManager.current
        if (textExpanded) {
            Spacer(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable(
                        interactionSource = null,
                        indication = null
                    ) {
                        focusManager.clearFocus()
                        changeTextExpanded()
                    })
        }

        Column(modifier = Modifier.padding(top = if (paddingTop > 0) (paddingTop + 6).dp else 0.dp)) {
            ExpandPanel(
                textExpanded = textExpanded,
                originalText = originalText,
                onShowIconClick = {
                    changeTextExpanded()
                },
            )
        }

    }
}

@Preview
@Composable
private fun ExpandedTextContentPreview() {
    ExpandedTextContent(true,{}, 0f, "Android 相关任何问题都可以提，且可以让他提供具体实现方案。 新的独立问题建议点击右下方全新对话后在问")
}
