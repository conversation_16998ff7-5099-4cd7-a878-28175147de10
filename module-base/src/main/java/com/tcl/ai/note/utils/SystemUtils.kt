package com.tcl.ai.note.utils

import android.annotation.SuppressLint
import android.util.Log

private const val TAG = "SystemUtils"

fun setSystemProperty(key: String, value: String) {
    try {
        @SuppressLint("PrivateApi")
        val c = Class.forName("android.os.SystemProperties")
        val set = c.getMethod("set", String::class.java, String::class.java)
        set.invoke(c, key, value)
    } catch (e: Exception) {
        Log.e(TAG, "setProperty = $e")
    }
}

fun getSystemProperty(key: String, def: String): String {
    return try {
        val systemPropertiesClass = Class.forName("android.os.SystemProperties")
        val getMethod = systemPropertiesClass.getMethod("get", String::class.java)
        getMethod.invoke(null, key) as String
    } catch (e: java.lang.Exception) {
        e.printStackTrace()
        def
    }
}