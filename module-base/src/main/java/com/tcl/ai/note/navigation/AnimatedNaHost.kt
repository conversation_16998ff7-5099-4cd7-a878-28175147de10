package com.tcl.ai.note.navigation

import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost

/**
 * 带转场动画的NavHost
 * 全部Compose页面都会带上转场动画
 * 针对夜间模式优化，减少闪屏现象
 */
@Composable
fun AnimatedNaHost(
    navController: NavHostController,
    startDestination: String,
    durationMillis: Int = 350,
    builder: NavGraphBuilder.() -> Unit
) {
    val isDarkTheme = isSystemInDarkTheme()

    // 夜间模式去掉fade效果，减少闪屏
    val fadeAnimationDuration = if (isDarkTheme) 0 else 150
    val fadeDelay = if (isDarkTheme) 0 else 150
    val initialAlpha = if (isDarkTheme) 1f else 0.3f
    val targetAlpha = if (isDarkTheme) 1f else 0.3f

    NavHost(
        navController, startDestination,
        enterTransition = {
            // 模仿 Activity 默认转场：水平滑动 + 淡入，使用系统动画曲线和时长
            slideInHorizontally(
                animationSpec = tween(durationMillis, easing = FastOutSlowInEasing),
                initialOffsetX = { fullWidth -> fullWidth }
            ) + fadeIn(
                animationSpec = tween(fadeAnimationDuration, delayMillis = fadeDelay, easing = FastOutSlowInEasing),
                initialAlpha = initialAlpha
            )
        }, exitTransition = {
            // 退出时：水平滑动 + 淡出
            slideOutHorizontally(
                animationSpec = tween(durationMillis, easing = FastOutSlowInEasing),
                targetOffsetX = { fullWidth -> -fullWidth }
            ) + fadeOut(
                animationSpec = tween(fadeAnimationDuration, easing = FastOutSlowInEasing),
                targetAlpha = targetAlpha
            )
        },
        popEnterTransition = {
            // 返回时进入：从左侧滑入 + 淡入
            slideInHorizontally(
                animationSpec = tween(durationMillis, easing = FastOutSlowInEasing),
                initialOffsetX = { fullWidth -> -fullWidth }
            ) + fadeIn(
                animationSpec = tween(fadeAnimationDuration, delayMillis = fadeDelay, easing = FastOutSlowInEasing),
                initialAlpha = initialAlpha
            )
        },
        popExitTransition = {
            // 返回时退出：向右滑出 + 淡出
            slideOutHorizontally(
                animationSpec = tween(durationMillis, easing = FastOutSlowInEasing),
                targetOffsetX = { fullWidth -> fullWidth }
            ) + fadeOut(
                animationSpec = tween(fadeAnimationDuration, easing = FastOutSlowInEasing),
                targetAlpha = targetAlpha
            )
        }
    ) {
        builder()
    }
}
