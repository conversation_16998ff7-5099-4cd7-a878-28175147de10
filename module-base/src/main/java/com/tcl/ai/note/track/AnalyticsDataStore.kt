package com.tcl.ai.note.track

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.utils.clear
import com.tcl.ai.note.utils.getData
import com.tcl.ai.note.utils.putData
import kotlinx.coroutines.flow.first

object AnalyticsDataStore {

    // 创建DataStore
    private val Context.analyticsDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "Analytics"
    )

    // DataStore变量
    private val dataStore: DataStore<Preferences> by lazy { GlobalContext.instance.analyticsDataStore }

    suspend fun <T> putData(key: String, value: T) {
        dataStore.putData(key, value)
    }

    suspend fun <T> getData(key: String, value: T): T {
        return dataStore.getData(key, value)
    }

    suspend fun getStringData(key: String, default: String): String {
        val preferences = dataStore.data.first()
        return preferences[stringPreferencesKey(key)] ?: default
    }

    suspend fun putStringData(key: String, value: String) {
        dataStore.edit { preferences ->
            preferences[stringPreferencesKey(key)] = value
        }
    }

    suspend fun getEnumData(key: String, default: Enum<*>): String {
        val preferences = dataStore.data.first()
        return preferences[stringPreferencesKey(key)] ?: default.name
    }

    suspend fun putEnumData(key: String, value: Enum<*>) {
        dataStore.edit { preferences ->
            preferences[stringPreferencesKey(key)] = value.name
        }
    }

    suspend fun clear() {
        dataStore.clear()
    }
}