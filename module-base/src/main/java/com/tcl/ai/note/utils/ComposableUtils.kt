package com.tcl.ai.note.utils

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ButtonElevation

import androidx.compose.material3.adaptive.currentWindowAdaptiveInfo
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf

import androidx.compose.runtime.remember

import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.ViewModel
import androidx.window.core.layout.WindowWidthSizeClass
import com.tcl.ai.note.GlobalContext
import kotlin.math.max


@Composable
fun isHorizonDraggable(): Boolean {
    val windowSizeClass = currentWindowAdaptiveInfo().windowSizeClass
    Logger.d("isHorizonDraggable", "isHorizonDraggable : $windowSizeClass")
    return with(windowSizeClass.windowWidthSizeClass) {
        this == WindowWidthSizeClass.MEDIUM || this == WindowWidthSizeClass.EXPANDED
    }
}

@Composable
inline fun <reified VM : ViewModel> hiltViewModelApp() =
    hiltViewModel<VM>(GlobalContext.instance)

@Composable
fun DebouncedButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    debounceTime: Long = 300L,
    enabled: Boolean = true,
    shape: Shape = ButtonDefaults.shape,
    colors: ButtonColors = ButtonDefaults.buttonColors(),
    elevation: ButtonElevation? = ButtonDefaults.buttonElevation(),
    border: BorderStroke? = null,
    contentPadding: PaddingValues = ButtonDefaults.ContentPadding,
    content: @Composable () -> Unit
) {
    var lastClickTime by remember { mutableLongStateOf(0L) }

    Button(
        onClick = {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastClickTime >= debounceTime) {
                onClick()
                lastClickTime = currentTime
            }
        },
        modifier = modifier,
        enabled = enabled,
        shape = shape,
        border = border,
        elevation = elevation,
        colors = colors,
        contentPadding = contentPadding
    ) {
        content()
    }
}

val LocalScaffoldPadding = compositionLocalOf { PaddingValues(0.dp) }

@Composable
fun OrientationChangeHandler(action: (orientation: Int) -> Unit) {
    val configuration = LocalConfiguration.current
    var lastOrientation by remember {
        mutableIntStateOf(configuration.orientation)
    }

    LaunchedEffect(configuration) {
        if (configuration.orientation != lastOrientation) {
            lastOrientation = configuration.orientation
            action.invoke(lastOrientation)
        }
    }
}

@Composable
fun rememberImeHeightPxWithoutNav(): Int {
    val imeInsets = WindowInsets.ime
    val navInsets = WindowInsets.navigationBars
    val density = LocalDensity.current
    return remember {
        derivedStateOf {
            val heightPx = imeInsets.getBottom(density)
            val navHeight = navInsets.getBottom(density)
            max(heightPx-navHeight,0)
        }
    }.value
}
