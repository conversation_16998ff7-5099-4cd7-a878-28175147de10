package com.tcl.ai.note.utils

import android.app.Application
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import com.tcl.ai.note.GlobalContext
import kotlinx.coroutines.flow.first

/**
 * Created by tao.ning on 2024/11/6 16:38
 * Describe：AppDataStore.kt
 */
object AppDataStore {

    private const val APP_DATA_KEY_ADD_NOTE_ID ="app_data_key_add_note_id"


    // 创建DataStore
    private val Application.appDataStore: DataStore<Preferences> by preferencesDataStore(
        name = "App"
    )

    // DataStore变量
    private val dataStore: DataStore<Preferences> by lazy { GlobalContext.instance.appDataStore }

    suspend fun <T> putData(key: String, value: T) {
        dataStore.putData(key, value)
    }

    suspend fun <T> getData(key: String, value: T): T {
        return dataStore.getData(key, value)
    }

    suspend fun getStringData(key: String, default: String): String {
        val preferences = dataStore.data.first()
        return preferences[stringPreferencesKey(key)] ?: default
    }

    suspend fun putStringData(key: String, value: String) {
        dataStore.edit { preferences ->
            preferences[stringPreferencesKey(key)] = value
        }
    }

    suspend fun getInt(key: String, default: Int) =
        getData(key, default)

    suspend fun putInt(key: String, value: Int) =
        putData(key, value)

    suspend fun getLong(key: String, default: Long) =
        getData(key, default)

    suspend fun putLong(key: String, value: Long) =
        putData(key, value)

    suspend fun getBoolean(key: String, default: Boolean) =
        getData(key, default)

    suspend fun putBoolean(key:String, value: Boolean) =
        putData(key, value)

    suspend fun getEnumData(key: String, default: Enum<*>): String {
        val preferences = dataStore.data.first()
        return preferences[stringPreferencesKey(key)] ?: default.name
    }

    suspend fun putEnumData(key: String, value: Enum<*>) {
        dataStore.edit { preferences ->
            preferences[stringPreferencesKey(key)] = value.name
        }
    }
    suspend fun putAddNoteId(value:Long){
        putLong(APP_DATA_KEY_ADD_NOTE_ID,value)
    }
    suspend fun getAddNoteId():Long {
        return getLong(APP_DATA_KEY_ADD_NOTE_ID,0)
    }

    suspend fun clear() {
        dataStore.clear()
    }
}