package com.tcl.ai.note.utils

import android.content.Context
import android.content.res.Configuration
import android.view.Surface
import android.view.WindowManager
import androidx.activity.compose.LocalActivity
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.semantics.invisibleToUser
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.BuildConfig
import kotlin.math.max
import kotlin.math.min

fun Modifier.invisibleSemantics() = this.semantics {
    invisibleToUser()
}

// 适配平板横屏模式
val isTabletAndLandScape
    get() = isTablet && isLandScape

/**
 * 是否为横屏
 */
val isLandScape
    get() = GlobalContext.instance.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE

val isLandScapeByScreenSize
    get() = GlobalContext.instance.getSystemService(WindowManager::class.java)?.let {
        val bounds = it.maximumWindowMetrics.bounds
        bounds.width() > bounds.height()
    } ?: false

val isLandScapeCompose: Boolean
    @Composable
    get() {
        val config = LocalConfiguration.current
        return config.orientation == Configuration.ORIENTATION_LANDSCAPE
    }

val isPortraitCompose: Boolean
    @Composable
    get() {
        val config = LocalConfiguration.current
        return config.orientation == Configuration.ORIENTATION_PORTRAIT
    }

/**
 * 是否为平板
 * 只会在第一次访问时获取一次，之后会缓存结果。
 */
val isTablet: Boolean by lazy {
    "tablet" == getSystemProperty("ro.build.characteristics", "phone")
}

/**
 * 是否处在分屏模式
 */
val isInMultiWindowMode:Boolean
    @Composable
    get() {
        val activity = LocalActivity.current
        return activity?.isInMultiWindowMode?:false
    }

/*
 * 检测设备是否为平板电脑且处于横屏模式,需要跟随Compose重组
 */
val isTabletLandscape: Boolean
    @Composable
    get() = isTablet && isLandScapeCompose


/**
 *  是否为精简版，后面添加SystemProperty
 */
val isLiteVersion: Boolean
    get() = FeatureUtils.hasSystemFeature(GlobalContext.instance.applicationContext, FeatureUtils.KEY_LITE)

/**
 * 判断快速点击
 * */
private var lastClickTime: Long = 0
fun isFastClick(): Boolean {
    val interval = (System.currentTimeMillis() - lastClickTime)
    if (interval <= 300) {
        return true
    }
    lastClickTime = System.currentTimeMillis()
    return false
}

/**
 * 判断快速点击
 * */
private var lastJournalClickTime: Long = 0
fun isJournalFastClick(): Boolean {
    val interval = (System.currentTimeMillis() - lastJournalClickTime)
    if (interval <= 800) {
        return true
    }
    lastJournalClickTime = System.currentTimeMillis()
    return false
}

fun setJournalClickTime() {
    lastJournalClickTime = System.currentTimeMillis()
}

private val disableJournalFeature by lazy {
    GlobalContext.instance.packageManager.hasSystemFeature("vendor.tct.note.disable.feature.journal")
}

val isSupportJournal: Boolean  by lazy {
    !isTablet && !disableJournalFeature && BuildConfig.moduleJournalModule
}

/**
 * 存储日记封面Rect数据
 * */
private var curCoverRect = Rect.Zero
private var createCoverRect = Rect.Zero
fun getCurCoverRect(): Rect {
    return curCoverRect
}

fun setCurCoverRect(rect: Rect) {
    curCoverRect = rect
}

fun setCreateCoverRect(rect: Rect, firstJournalId: Long? = null) {
    val contentHorizontalPadding = 12.dp.toPx
    val coverItemWidth = isDensity440.judge(96.dp, 88.dp).toPx
    val coverItemHeight = isDensity440.judge(136.dp, 124.dp).toPx
    val itemWidth = (rect.width - contentHorizontalPadding * 2) / 3
    val coverLeftPadding = if (firstJournalId == 1L) {
        itemWidth + (itemWidth - coverItemWidth) / 2
    } else {
        (itemWidth - coverItemWidth) / 2
    }
    createCoverRect = Rect(
        rect.left + contentHorizontalPadding + coverLeftPadding,
        rect.top,
        rect.left + contentHorizontalPadding + coverLeftPadding + coverItemWidth,
        rect.top + coverItemHeight
    )
}

fun getCreateCoverRect(): Rect {
    return createCoverRect
}

/**
 * 截取字符串，按字符数截取
 * @param str 字符串
 * @param maxCount 最大字符数
 * @return 截取后的字符串
 */
fun substringByCodePoints(str: String, maxCount: Int): String {
    val realCount = str.codePointCount(0, str.length)
    if (realCount <= maxCount) return str
    val index = str.offsetByCodePoints(0, maxCount)
    return str.substring(0, index)
}

/**
 * 平板竖屏
 */
val isTabletPortrait: Boolean
    @Composable
    get() = isTablet && isPortraitCompose

private val context
    get() = GlobalContext.instance

fun isLandscape(context: Context): Boolean {
    val display = context.getSystemService(WindowManager::class.java)?.defaultDisplay
    val rotation = display?.rotation
    return rotation == Surface.ROTATION_90 || rotation == Surface.ROTATION_270
}

/**
 * 竖屏
 */
fun isPortrait(context: Context): Boolean {
    val display = context.getSystemService(WindowManager::class.java)?.defaultDisplay
    val rotation = display?.rotation
    return rotation == Surface.ROTATION_0 || rotation == Surface.ROTATION_180
}

val isLTR
    get() = GlobalContext.instance.resources.configuration.layoutDirection == Configuration.SCREENLAYOUT_LAYOUTDIR_LTR

@Composable
fun rememberCurrentOffset(state: LazyListState): State<Int> {
    val position = remember {
        derivedStateOf { state.firstVisibleItemIndex }
    }
    val itemOffset = remember {
        derivedStateOf { state.firstVisibleItemScrollOffset }
    }
    val lastPosition = rememberPrevious(position.value)
    val lastItemOffset = rememberPrevious(itemOffset.value)
    val currentOffset = remember { mutableIntStateOf(0) }
    LaunchedEffect(position.value, itemOffset.value) {
        when {
            lastPosition == null || position.value == 0 ->
                currentOffset.intValue = itemOffset.value

            lastPosition == position.value ->
                currentOffset.value += (itemOffset.value - (lastItemOffset ?: 0))

            lastPosition > position.value ->
                currentOffset.value -= (lastItemOffset ?: 0)

            else ->
                currentOffset.value += itemOffset.value
        }
    }
    return currentOffset
}

@Composable
fun <T> rememberRef(): MutableState<T?> = remember {
    object : MutableState<T?> {
        override var value: T? = null

        override fun component1(): T? = value

        override fun component2(): (T?) -> Unit = { value = it }
    }
}

@Composable
fun <T> rememberPrevious(
    current: T,
    shouldUpdate: (prev: T?, curr: T) -> Boolean = { a: T?, b: T -> a != b },
): T? {
    val ref = rememberRef<T>()
    SideEffect {
        if (shouldUpdate(ref.value, current)) {
            ref.value = current
        }
    }
    return ref.value
}

/**
 * 获取状态栏高度 单位 dp
 */
@Composable
fun getStatusBarHeight(): Dp {
    val statusBarHeight = WindowInsets.statusBars.asPaddingValues().calculateTopPadding()
    return statusBarHeight
}

@Composable
fun getNavBarHeight(): Dp {
    val barHeight = WindowInsets.navigationBars.asPaddingValues().calculateBottomPadding()
    //Logger.d("barHeight", "barHeight:$barHeight")
    return barHeight
}
/**
 * 获取导航栏高度
 */
@Composable
fun getNavigationBarHeight(): Dp {
    val insets = WindowInsets.navigationBars
    val density = LocalDensity.current
    val barHeight = with(density) {
        insets.getBottom(density).toDp()
    }
    Logger.d("getNavigationBarHeight", "height:$barHeight")
    return barHeight
}

/**
 *  检测是否存在底部导航栏
 *  手势导航的高度很小，通常小于24dp，而按钮导航的高度较大（约48dp或更高）。
 *  您可以设置一个阈值来区分两种导航方式。
 */
@Composable
fun isButtonNavigation(): Boolean {
    return getNavigationBarHeight() >= 40.dp
}

val screenSizeMax by lazy {
    GlobalContext.instance.getSystemService(WindowManager::class.java)?.let {
        val bounds = it.maximumWindowMetrics.bounds
        max(bounds.height(), bounds.width())
    } ?: 1080
}

val screenSizeMin by lazy {
    GlobalContext.instance.getSystemService(WindowManager::class.java)?.let {
        val bounds = it.maximumWindowMetrics.bounds
        min(bounds.height(), bounds.width())
    } ?: 1080
}