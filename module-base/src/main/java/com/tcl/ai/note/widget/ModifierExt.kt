package com.tcl.ai.note.widget

import androidx.compose.foundation.Indication
import androidx.compose.foundation.LocalIndication
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.material3.ripple
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.focused
import androidx.compose.ui.semantics.selected
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.dp

/**
 * 测试UI的边界,能显示出这个控件的边距范围
 * @param color 边框颜色
 */
@Composable
fun Modifier.testUIBorder(color: Color = Color.Green): Modifier {
    return this.border(1.dp, color = color)
}

@Composable
fun Modifier.clickableNoRipple(onClick: () -> Unit): Modifier {
    return then(
        other = Modifier.clickable(
            onClickLabel = null,
            indication = null,
            interactionSource = remember { MutableInteractionSource() },
            onClick = onClick
        )
    )
}

/**
 * 为点击事件添加防抖功能的 Modifier 扩展（带波纹效果）
 */
@Composable
fun Modifier.debounceClickable(
    debounceTime: Long = 300L,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    indication: Indication = ripple(),
    onClick: () -> Unit
): Modifier {
    var lastClickTime by remember { mutableLongStateOf(0L) }

    return this.clickable(
        interactionSource = interactionSource,
        indication = indication
    ) {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastClickTime >= debounceTime) {
            lastClickTime = currentTime
            onClick()
        }
    }
}
/**
 * 为点击事件添加防抖功能
 */
@Composable
fun rememberThrottleClickHandler(
    intervalMillis: Long = 300L,
    onClick: () -> Unit
): () -> Unit {
    var lastClickTime by remember { mutableLongStateOf(0L) }

    return remember {
        {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastClickTime >= intervalMillis) {
                lastClickTime = currentTime
                onClick()
            }
        }
    }
}

/**
 * 无障碍 语义 可单独点击
 */
fun Modifier.accessibilityDescription(desc: String): Modifier = this.semantics {
    contentDescription = desc
    focused = true
}
fun Modifier.accessibilityDescriptionNoFocus(): Modifier = this.semantics {
    focused = false
}
fun Modifier.semanticsSelected(needTalkState: Boolean, selected: Boolean, text: String): Modifier = if (needTalkState) {
    this.semantics(mergeDescendants = true) {
        this.selected =selected
    }
} else {
    this
}

/**
 * 去掉 手写笔悬停效果
 */
@Composable
fun Modifier.clickableHover(
    key1: Any?=null,
    interactionSource: MutableInteractionSource? = remember(key1) {
        HoverPenInteractionSourceImpl()
    },
    enabled: Boolean = true,
    role: Role? = null,
    onClick: () -> Unit
): Modifier {
    return this.clickable(
        interactionSource,
        role = role,
        indication = LocalIndication.current,
        onClick = onClick,
        enabled = enabled
    )
}


fun Modifier.combinedClickable(
    onClick: () -> Unit,
    onLongClick: (() -> Unit)? = null,
): Modifier {
    return this.combinedClickable(
        onClick = onClick,
        onLongClick = onLongClick
    )
}
/**
 * 去掉 手写笔悬停效果
 */
@Composable
fun Modifier.combinedClickableHoverPen(
    key1: Any?=null,
    interactionSource: MutableInteractionSource? = remember(key1) {
        HoverPenInteractionSourceImpl()
    },
    onClick: () -> Unit,
    onLongClick: (() -> Unit)? = null,
): Modifier {
    return this.combinedClickable(
        interactionSource,
        indication = LocalIndication.current,
        onClick = onClick,
        onLongClick = onLongClick
    )
}