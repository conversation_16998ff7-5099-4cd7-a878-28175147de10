package com.tcl.ai.note.theme

import androidx.compose.material3.Typography
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.utils.nonScaledSp
import com.tcl.ai.note.utils.nonScaledSpCompose
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.judge

/**
 * AI润色和转文中向下pop的Shape
 */
val CornerShapeAIMenuDownPop = RoundedCornerShape(12.dp)

/**
 * 编辑界面的向上的Shape
 */
val CornerShapeAIBottomMenuUpPop = RoundedCornerShape(8.dp)

val CornerShapeMenuUpPop = RoundedCornerShape(8.dp)

val CornerShapeAIBottomDialogContent = RoundedCornerShape(20.dp)


// Set of Material typography styles to start with
val Typography = Typography(

    titleLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 20.sp,
        lineHeight = 18.sp,
        letterSpacing = 0.sp
    ),
    titleMedium = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.sp
    ),
    titleSmall = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Medium,
        fontSize = 12.sp,
        lineHeight = 18.sp,
        letterSpacing = 0.sp
    ),
    bodyLarge = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp
    ),
    bodyMedium = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.sp
    ),
    bodySmall = TextStyle(
        fontFamily = FontFamily.Default,
        fontWeight = FontWeight.Normal,
        fontSize = 12.sp,
        lineHeight = 18.sp,
        letterSpacing = 0.sp
    ),
)

/**
 * AI弹窗中 内容的内边距 padding
 */
val AI_CONTENT_PADDING_HORIZONTAL = if (isTablet) 22.dp else 16.dp
val AI_CONTENT_PADDING_VERTICAL = 16.dp
val AI_TITLE_TOP_PADDING = 20.dp
val AI_TITLE_BOTTOM_PADDING = 20.dp
val MENU_VERTICAL_PADDING = if (isTablet) 2.dp else 6.dp


private const val TEXT_SIZE_SP = 14
private const val LINE_HEIGHT_SP = 20
val contentTextSize
    get() = TEXT_SIZE_SP.nonScaledSp
val contentTextSizeCompose
    @Composable
    get() = TEXT_SIZE_SP.nonScaledSpCompose
val contentTextLineHeight
    get() = LINE_HEIGHT_SP.nonScaledSp
val contentTextLineHeightCompose
    @Composable
    get() = LINE_HEIGHT_SP.nonScaledSpCompose

val translationByTextSize
    @Composable
    get() = 10.nonScaledSpCompose

val TITLE_LINE_HEIGHT_SP =if (isTablet) 26  else 28

val LINE_HEIGHT = isTablet.judge(22,28)

val editorTitleTextStyle = TextStyle(
    fontSize = if (isTablet) 20.sp else 20.sp,
    lineHeight = if (isTablet) 26.sp else 28.sp,
    fontWeight = FontWeight.ExtraBold,
    platformStyle = PlatformTextStyle(
        includeFontPadding = false
    ),
    lineHeightStyle = LineHeightStyle(
        alignment = LineHeightStyle.Alignment.Bottom,
        trim = LineHeightStyle.Trim.None,
    )
)
val editorRichTextStyle = TextStyle(
    fontSize = isTablet.judge(13.sp,16.sp),
    lineHeight = isTablet.judge(22,28).sp
)
