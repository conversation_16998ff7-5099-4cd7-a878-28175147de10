package com.tcl.ai.note.widget

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay

/**
 * 自动无缝滚动文本：只要文本超出容器宽度自动滚动，跑马灯效果，
 * 每循环一次停留2秒（只滚动一半，即原文首出现在最左，首尾完全无缝）。
 * 页面加载完成后延迟1秒才开始滚动。
 *
 * @param text 展示的文本内容
 * @param modifier 组件修饰符
 * @param color 文字颜色
 * @param speedPxPerSecond 每秒滚动的像素数（即滚动速度）
 */
@Composable
fun AutoScrollText(
    text: String,
    modifier: Modifier = Modifier,
    color: Color,
    fontSize: TextUnit = 14.sp,
    speedPxPerSecond: Float = 50f
) {
    val scrollState = rememberScrollState()
    val textMeasurer = rememberTextMeasurer()

    // 1. 测量文本宽度（作为“必须滚动”与否的判断基准）
    val textWidth = remember(text, color,fontSize) {
        textMeasurer.measure(
            text = text,
            style = TextStyle(color = color, fontSize = fontSize)
        ).size.width.toFloat()
    }

    // 2. 测量容器宽度
    var visibleWidth by remember { mutableStateOf(0f) }

    // 3. 跑马灯滚动时用的内容：原文本+间隔+原文本
    val separator = "    "
    val scrollText = remember(text) { text + separator + text }

    // 4. 拼接文本的宽度
    val scrollTextWidth = remember(scrollText, color, fontSize) {
        textMeasurer.measure(
            text = scrollText,
            style = TextStyle(color = color, fontSize = fontSize)
        ).size.width.toFloat()
    }

    Box(
        modifier = Modifier.onGloballyPositioned { coordinates ->
            visibleWidth = coordinates.size.width.toFloat()
        }
    ) {
        // 只要文本内容超出容器宽度就滚动
        val shouldAutoScroll = remember(textWidth, visibleWidth) {
            textWidth > visibleWidth && visibleWidth > 0
        }

        LaunchedEffect(shouldAutoScroll, textWidth, visibleWidth, scrollTextWidth) {
            if (shouldAutoScroll) {
                delay(1000) // 页面加载后延迟1秒
                // 关键点：只滚动scrollTextWidth/2，刚好是一轮到第二次text的首部
                val distance = scrollTextWidth / 2f
                val durationMillis = (distance / speedPxPerSecond * 1000).toLong()
                while (true) {
                    val startTime = System.currentTimeMillis()
                    var stop = false
                    while (!stop) {
                        val elapsed = System.currentTimeMillis() - startTime
                        val progress = (elapsed.toFloat() / durationMillis).coerceAtMost(1f)
                        val offset = distance * progress
                        scrollState.scrollTo(offset.toInt())
                        if (progress >= 1f) {
                            stop = true
                        } else {
                            delay(16L)
                        }
                    }
                    // 到一轮终点后停2秒
                    delay(2000)
                    scrollState.scrollTo(0)
                    delay(16L)
                }
            } else {
                scrollState.scrollTo(0)
            }
        }
        Text(
            text = if (shouldAutoScroll) scrollText else text,
            color = color,
            fontSize = fontSize,
            modifier = modifier.horizontalScroll(scrollState, enabled = true),
            maxLines = 1,
            overflow = TextOverflow.Visible
        )
    }
}