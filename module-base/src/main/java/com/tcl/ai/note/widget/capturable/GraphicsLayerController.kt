package com.tcl.ai.note.widget.capturable

import androidx.compose.runtime.*
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.unit.IntSize
import com.tcl.ai.note.utils.Logger
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.*

class GraphicsLayerController() {
    internal val requestQueue = LinkedList<GraphicsLayerBitmapRequest>()
    private var invalidDraw: (() -> Unit)? = null
    internal fun setNeedInvalidDrawImpl(funcImpl: (() -> Unit)? = null) {
        invalidDraw = funcImpl
    }

    /**
     * 把composable保存成bitmap
     */
    suspend fun toImageBitmap(size: IntSize  = IntSize(0, 0), scale: Float = 1F, offset: Offset  = Offset(0F, 0F)): ImageBitmap = withContext(Dispatchers.IO) {
        val request = GraphicsLayerBitmapRequest(size, scale, offset)
        Logger.d("GraphicsLayerController", "toImageBitmap: $request")
        requestQueue.add(request)
        invalidDraw?.invoke()
        return@withContext request.result.await()
    }

    internal data class GraphicsLayerBitmapRequest(
        val size: IntSize = IntSize(0, 0),
        val scale: Float = 1F,
        val offset: Offset = Offset(0F, 0F),
        val result: CompletableDeferred<ImageBitmap> = CompletableDeferred<ImageBitmap>(),
    )
}

/**
 * 创建控制器
 */
@Composable
@Stable
fun rememberGraphicsLayerController(): GraphicsLayerController {
    val graphicsLayerController = remember { GraphicsLayerController() }
    return graphicsLayerController
}