package com.tcl.ai.note.utils

import java.lang.IndexOutOfBoundsException

fun <T> MutableList<T>.addOrExchangeWithPadding(idx: Int, element: T, defaultValue: T) {
    when {
        idx < 0 -> throw IndexOutOfBoundsException("Index $idx is negative")

        idx >= size -> {
            val paddingCount = idx - size
            addAll(List(paddingCount) { defaultValue })
            add(element)
        }

        else -> {
            if (this[idx] != element) {
                this[idx] = element
            }
        }
    }
}

fun <T> MutableList<T>.extractAndRemove(predicate: (T) -> Boolean): List<T> {
    val extracted = mutableListOf<T>()
    val iterator = this.iterator()
    while (iterator.hasNext()) {
        val element = iterator.next()
        if (predicate.invoke(element)) {
            extracted.add(element)
            iterator.remove()
        }
    }
    return extracted
}