package com.tcl.ai.note.widget.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.CompositingStrategy
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * 底部渐变的Box
 */
@Composable
fun BottomFadeBox(
    modifier: Modifier,
    isDrawGradient: Boolean = true,
    fadeHeight: Dp = 36.dp,
    content: @Composable BoxScope.() -> Unit
) {
    Box(
        modifier = modifier
            .graphicsLayer(compositingStrategy = CompositingStrategy.Offscreen)
            .drawWithContent {
                drawContent()
                if (isDrawGradient) {
                    drawRect(
                        brush = Brush.verticalGradient(
                            listOf(Color.Black, Color.White.copy(alpha = 0.4f), Color.Transparent),
                            startY = size.height - fadeHeight.toPx(),
                            endY = size.height
                        ), blendMode = BlendMode.DstIn
                    )
                }
            }) {
        content()
    }
}
@Composable
fun BottomFadeColumn(
    modifier: Modifier,
    isDrawGradient: Boolean = true,
    isButtonNavigation :Boolean = false,
    content: @Composable ColumnScope.() -> Unit
) {
    Column(
        modifier = modifier
            .graphicsLayer(compositingStrategy = CompositingStrategy.Offscreen)
            .drawWithContent {
                drawContent()
                if (isDrawGradient) {
                    drawRect(
                        brush = Brush.verticalGradient(
                            listOf(Color.Black, Color.White.copy(alpha = 0.4f), Color.Transparent),
                            startY = size.height - if (isButtonNavigation) 48.dp.toPx() else 24.dp.toPx(),
                            endY = size.height
                        ), blendMode = BlendMode.DstIn
                    )
                }
            }) {
        content()
    }
}
