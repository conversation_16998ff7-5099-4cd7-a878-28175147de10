package com.tcl.ai.note.widget

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.colorRes
import com.tcl.ai.note.utils.judge
import kotlinx.coroutines.delay

/**
 * 上三角形组件
 */
@Composable
fun UpTriangle(
    modifier: Modifier = Modifier,
    color: Color = TclTheme.colorScheme.tctStanderTextPrimary.copy(alpha = 0.7f)
) {
    Canvas(
        modifier = modifier.size(width = 4.47.dp, height = 4.dp)
    ) {
        val path = Path().apply {
            // 上三角形：顶点在上方
            moveTo(size.width / 2, 0f) // 顶点
            lineTo(0f, size.height) // 左下角
            lineTo(size.width, size.height) // 右下角
            close()
        }
        drawPath(path, color)
    }
}

/**
 * 下三角形组件
 */
@Composable
fun DownTriangle(
    modifier: Modifier = Modifier,
    color: Color = TclTheme.colorScheme.tctStanderTextPrimary.copy(alpha = 0.7f)
) {
    Canvas(
        modifier = modifier.size(width = 4.47.dp, height = 4.dp)
    ) {
        val path = Path().apply {
            // 下三角形：顶点在下方
            moveTo(0f, 0f) // 左上角
            lineTo(size.width, 0f) // 右上角
            lineTo(size.width / 2, size.height) // 底部顶点
            close()
        }
        drawPath(path, color)
    }
}

/**
 * 字体大小选择器组件
 * 显示字体大小数字和三角形指示器
 */
@Composable
fun FontSizeSelector(
    modifier: Modifier = Modifier,
    btnSize: Dp,
    fontSize: Int,
    isExpanded: Boolean = false,
    onClick: () -> Unit
) {
    var showBackground by remember { mutableStateOf(false) }

    LaunchedEffect(isExpanded) {
        if (isExpanded) {
            delay(300)
            showBackground = true
        } else {
            showBackground = false
        }
    }

    Box(
        modifier = modifier
            .size(btnSize)
            .then(
                showBackground.judge(
                    Modifier.background(
                        color = R.color.tablet_btn_checked_bg.colorRes(),
                        shape = RoundedCornerShape(btnSize / 2),
                    ),
                    Modifier
                )
            ),
        contentAlignment = Alignment.Center
    ) {
        HoverProofIconButton(
            onClick = onClick,
            modifier = Modifier.size(btnSize)
        ) {
            // 显示字体大小数字和三角形指示器
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "$fontSize",
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black.copy(alpha = 0.85f)
                )
                
                Spacer(modifier = Modifier.width(2.dp))
                
                // 三角形指示器：下三角（正常）/ 上三角（弹出）
                if (isExpanded) {
                    UpTriangle()
                } else {
                    DownTriangle()
                }
            }
        }
    }
} 