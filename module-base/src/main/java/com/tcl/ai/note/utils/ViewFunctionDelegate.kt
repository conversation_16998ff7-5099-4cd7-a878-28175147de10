package com.tcl.ai.note.utils

import android.view.View
import androidx.core.view.doOnAttach
import androidx.core.view.doOnDetach
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

open class ViewFunctionDelegate<in T, V>(default: V? = null) : ReadWriteProperty<T, V> {
    /**
     * [func]变量,用于保存Function或者Lambda表达式
     * [invokeFunc]就是返回并调用了func这个方法
     */
    private var func: V? = default

    /**
     * 调用[func]变量保存的函数
     */
    val invokeFunc: V get() = if (func == null) throw NotImplementedError() else func!!

    /**
     * 委托给by关键词，获取func函数
     * 和[invokeFunc]的作用是一模一样的
     */
    override fun getValue(thisRef: T, property: KProperty<*>): V {
        return invokeFunc
    }

    /**
     * 委托给by关键词，把函数保存到[func]变量中
     */
    override fun setValue(thisRef: T, property: KProperty<*>, value: V) {
        if (thisRef !is View) {
            throw IllegalAccessException("thisRef isn't View, but rather it's $thisRef")
        }
        thisRef.doOnAttach {
            func = value
            Logger.v("ViewFunctionDelegate", "doOnAttach: $thisRef")
        }
        thisRef.doOnDetach {
            func = null
            Logger.v("ViewFunctionDelegate", "doOnDetach: $thisRef")
        }
    }
}

/**
 * 无参
 * @param R 返回类型
 */
fun <R> delegateViewFunc(default: (() -> R)? = null) =
    object : ViewFunctionDelegate<Any?, () -> R>(default) {}

fun <R> delegateViewSuspendFunc(default: (suspend () -> R)? = null) =
    object : ViewFunctionDelegate<Any?, suspend () -> R>(default) {}


/**
 * @param A 入参类型
 *
 * @param R 返回类型
 */
fun <A, R> delegateViewFunc(default: ((A) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, (A) -> R>(default) {}

fun <A, R> delegateViewSuspendFunc(default: (suspend (A) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, suspend (A) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, R> delegateViewFunc(default: ((A, B) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, (A, B) -> R>(default) {}

fun <A, B, R> delegateViewSuspendFunc(default: (suspend (A, B) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, suspend (A, B) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, R> delegateViewFunc(default: ((A, B, C) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, (A, B, C) -> R>(default) {}

fun <A, B, C, R> delegateViewSuspendFunc(default: (suspend (A, B, C) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, suspend (A, B, C) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, R> delegateViewFunc(default: ((A, B, C, D) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, (A, B, C, D) -> R>(default) {}

fun <A, B, C, D, R> delegateViewSuspendFunc(default: (suspend (A, B, C, D) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, suspend (A, B, C, D) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 * @param E 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, E, R> delegateViewFunc(default: ((A, B, C, D, E) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, (A, B, C, D, E) -> R>(default) {}

fun <A, B, C, D, E, R> delegateViewSuspendFunc(default: (suspend (A, B, C, D, E) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, suspend (A, B, C, D, E) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 * @param E 入参类型
 * @param F 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, E, F, R> delegateViewFunc(default: ((A, B, C, D, E, F) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, (A, B, C, D, E, F) -> R>(default) {}

fun <A, B, C, D, E, F, R> delegateViewSuspendFunc(default: (suspend (A, B, C, D, E, F) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, suspend (A, B, C, D, E, F) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 * @param E 入参类型
 * @param F 入参类型
 * @param G 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, E, F, G, R> delegateViewFunc(default: ((A, B, C, D, E, F, G) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, (A, B, C, D, E, F, G) -> R>(default) {}

fun <A, B, C, D, E, F, G, R> delegateViewSuspendFunc(default: (suspend (A, B, C, D, E, F, G) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, suspend (A, B, C, D, E, F, G) -> R>(default) {}

/**
 * @param A 入参类型
 * @param B 入参类型
 * @param C 入参类型
 * @param D 入参类型
 * @param E 入参类型
 * @param F 入参类型
 * @param G 入参类型
 *
 * @param R 返回类型
 */
fun <A, B, C, D, E, F, G, H, R> delegateViewFunc(default: ((A, B, C, D, E, F, G, H) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, (A, B, C, D, E, F, G, H) -> R>(default) {}

fun <A, B, C, D, E, F, G, H, R> delegateViewSuspendFunc(default: (suspend (A, B, C, D, E, F, G, H) -> R)? = null) =
    object : ViewFunctionDelegate<Any?, suspend (A, B, C, D, E, F, G, H) -> R>(default) {}