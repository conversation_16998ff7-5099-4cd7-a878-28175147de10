package com.tcl.ai.note.utils

import android.os.Looper

/**
 * 全局异常捕获
 */
object GlobalExceptionCaughtManager {
    private const val TAG = "GlobalExceptionCaughtManager"

    init {
        val preDefaultExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
        // 设置自定义处理器
        Thread.setDefaultUncaughtExceptionHandler { thread, exception ->
            // 捕获异常
            handleException(preDefaultExceptionHandler, thread, exception)
            // 主线程要特殊处理，重启loop
            if (thread == Looper.getMainLooper().thread) {
                // 重启 Looper
                while (true) {
                    try {
                        Looper.loop()
                    } catch (e: Throwable) {
                        handleException(preDefaultExceptionHandler, Thread.currentThread(), e)
                    }
                }
            }
        }
    }

    private fun handleException(
        preDefaultExceptionHandler: Thread.UncaughtExceptionHandler?,
        thread: Thread,
        exception: Throwable
    ) {
        if (preDefaultExceptionHandler == null) {
            Logger.w(TAG, "preDefaultExceptionHandler is null")
            return
        }

        Logger.w(TAG,"""
           found Java Crash exception: 
               threadName: ${thread.name}
               type: ${exception.javaClass.name}
               exceptionMessage: ${exception.message}
        """.trimIndent())

        when {
            thread.name.contains("CanvasRenderThread", true)
                    && exception is NullPointerException
                    && exception.message?.contains("mNativeObject of", true) == true
                    && exception.message?.contains("is null. Have you called release() already?", true) == true
                -> {
                /**
                 * 情况1
                 * 处理graphics-core，初始化崩溃的问题（临时解决方案，最终需要看看google会不会解决）
                 *
                 * 异常根本原因点：[androidx.graphics.lowlatency.CanvasFrontBufferedRenderer.setParentSurfaceControlBuffer()] 的 mNativeObject==0
                 * 异常抛出点：[android.view.SurfaceControl.checkNotReleased()]
                 */
                Logger.w(TAG, "graphics-core初始化异常已捕获，情况1，${exception.stackTraceToString()}")
            }

            thread.name.contains("CanvasRenderThread", true)
                    && exception is NullPointerException
                    && exception.message?.contains("Attempt to read from field", true) == true
                    && exception.message?.contains("on a null object reference", true) == true
                -> {
                /**
                 * 情况2
                 * 处理graphics-core，初始化崩溃的问题（临时解决方案，最终需要看看google会不会解决）
                 *
                 * 异常抛出点：[android.view.SurfaceControl.merge()]
                 */
                Logger.w(TAG, "graphics-core初始化异常，情况2，已捕获，${exception.stackTraceToString()}")
            }

            thread.name.contains("CanvasRenderThread", true)
                    && exception is IllegalStateException
                    && exception.message?.contains("Attempt to draw with a HardwareBufferRenderer instance that has already been closed", true) == true
                -> {
                /**
                 * 情况3
                 * 处理graphics-core，初始化崩溃的问题（临时解决方案，最终需要看看google会不会解决）
                 *
                 * 异常抛出点：[androidx.graphics.lowlatency.SingleBufferedCanvasRenderer.create()]
                 */
                Logger.w(TAG, "graphics-core初始化异常，情况3，已捕获，${exception.stackTraceToString()}")
            }

            exception is IllegalStateException
                    && exception.message?.contains("LayoutNode should be attached to an owner", true) == true
                -> {
                /**
                 * 情况4
                 * 处理主线程异常，可能是因为主线程被重启导致的异常
                 */
                Logger.w(TAG, "主线程异常已捕获，情况4，${exception.stackTraceToString()}")
            }

            else -> {
                // 走到这里，会让程序继续crash，崩溃
                Logger.w(TAG, "Java Crash 未捕获，交给原有 ExceptionHandler 处理")
                exception.printStackTrace()
                preDefaultExceptionHandler.uncaughtException(thread, exception)
            }
        }
    }

    /**
     * 单纯用来加载类
     */
    fun init() {}
}