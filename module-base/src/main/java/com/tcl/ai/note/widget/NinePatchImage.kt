package com.tcl.ai.note.widget

import android.widget.ImageView
import androidx.annotation.DrawableRes
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView

/**
 * 显示9patch图片（.9.png）
 *
 * @param resId
 * @param modifier
 * @param scaleType     ImageView的拉伸模式，默认FIT_XY
 */
@Composable
fun NinePatchImage(
    @DrawableRes resId: Int,
    modifier: Modifier = Modifier,
    scaleType: ImageView.ScaleType = ImageView.ScaleType.FIT_XY
) {
    AndroidView(
        modifier = modifier,
        factory = { context ->
            ImageView(context).apply {
                setImageResource(resId)
                this.scaleType = scaleType

            }
        }
    )
}