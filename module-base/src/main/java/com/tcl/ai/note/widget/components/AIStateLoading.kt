package com.tcl.ai.note.widget.components

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.focused
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.utils.isTabletLandscape

@Preview(showBackground = true)
@Composable
private fun AIStateLoadingPreview() {
    AIStateLoading(isOffline = true, onStopClick = {})
}

/**
 * 重试按钮状态
 */
@Composable
fun AIStateLoadingRetry(
    onBtnClick: (Boolean) -> Unit = {},
    btnText: String = stringResource(id = R.string.text_try_again),
) {
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        BtnRow(onBtnClick = { onBtnClick(true) }) {
            LoadingText(btnText)
        }
    }
}

/**
 * 按钮样式的Row容器
 */
@Composable
fun BoxScope.BtnRow(
    modifier: Modifier = Modifier,
    onBtnClick: (Boolean) -> Unit = {},
    content: @Composable RowScope.() -> Unit
) {
    Row(
        modifier = Modifier
            .padding(bottom = 14.dp)
            .height(40.dp)
            .clip(RoundedCornerShape(24.dp))
            .background(
                colorResource(id = R.color.tct_stander_accent_primary_button_new)
            )
            .clickable(role = Role.Button) {
                onBtnClick(true)
            }
            .padding(start = 32.dp, end = 32.dp)
            .align(Alignment.BottomCenter),
        verticalAlignment = Alignment.CenterVertically) {
        content()
    }
}

@Composable
fun AIStateLoading(
    isOffline: Boolean,
    showLoadingAnim: Boolean = true,
    onStopClick: (Boolean) -> Unit = {}
) {
    val content = if (isOffline) {
        stringResource(id = R.string.network_error)
    } else {
        ""
    }
    AIStateLoading(
        isOffline = isOffline,
        tip = content,
        showLoadingAnim = showLoadingAnim,
        showStopButton = true,
        onStopClick = onStopClick
    )
}

/**
 * AI通用状态加载中
 * @param isOffline 是否离线模式
 * @param onStopClick 点击停止按钮回调
 * @param showLoadingAnim 是否显示加载动画 默认显示  IN_PROGRESS 中择不显示 只剩下停止按钮
 */
@Composable
fun AIStateLoading(
    isOffline: Boolean,
    tip: String,
    isRowLayout: Boolean = isTabletLandscape,
    btnText: String = stringResource(id = R.string.writing_stop),
    showLoadingAnim: Boolean = true,
    showStopButton: Boolean = true,
    onStopClick: (Boolean) -> Unit = {}
) {
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        if (showLoadingAnim) {
            GenerateInProgressView(isOffline, isRowLayout, tip)
        }
        if (showStopButton) {
            BtnRow(modifier = Modifier.clearAndSetSemantics {
                // 为文本添加无障碍标签，使其可以被正确播报
                contentDescription = btnText
                // 设置为可聚焦，使其可以被单独选择
                focused = true
            }, onBtnClick = { onStopClick(true) }) {
                Image(
                    painter = painterResource(id = R.drawable.writing_stop),
                    contentDescription = null,
                    modifier = Modifier.size(14.dp)
                )
                LoadingText(btnText)
            }
        }
    }
}

@Composable
private fun LoadingText(btnText: String) {
    Text(
        text = btnText,
        fontSize = 14.sp,
        color = colorResource(
            id = R.color.tct_stander_bg_basic
        ),
        modifier = Modifier
            .padding(start = 6.dp),
    )
}
