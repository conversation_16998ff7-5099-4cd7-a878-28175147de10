package com.tcl.ai.note.widget.capturable

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.drawscope.ContentDrawScope
import androidx.compose.ui.graphics.drawscope.scale
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.node.DrawModifierNode
import androidx.compose.ui.node.ModifierNodeElement
import androidx.compose.ui.node.invalidateDraw
import androidx.compose.ui.node.requireGraphicsContext
import androidx.compose.ui.platform.InspectorInfo
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.toIntSize
import androidx.compose.ui.unit.toSize
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.launchIO
import kotlin.coroutines.coroutineContext

/**
 * 添加转换成bitmap的能力，可以让compose转成bitmap
 *
 * 用例:
 *
 * ```
 *  val captureController = rememberCaptureController()
 *  val uiScope = rememberCoroutineScope()
 *
 *  // The content to be captured in to Bitmap
 *  Column(
 *      modifier = Modifier.recordGraphicsLayer(captureController),
 *  ) {
 *      // Composable content
 *  }
 *
 *  Button(onClick = {
 *      // Capture content
 *      val bitmapAsync = captureController.captureAsync()
 *      try {
 *          val bitmap = bitmapAsync.await()
 *          // Do something with `bitmap`.
 *      } catch (error: Throwable) {
 *          // Error occurred, do something.
 *      }
 *  }) { ... }
 * ```
 *
 * @param controller 传递 [GraphicsLayerController] 记录那个compose函数要记录成截图
 */
@Composable
fun Modifier.recordGraphicsLayer(controller: GraphicsLayerController): Modifier {
    return this.then(RecordGraphicsLayerModifierNodeElement(controller))
}

/**
 * Modifier implementation of RecordGraphicsLayer
 */
private data class RecordGraphicsLayerModifierNodeElement(
    private val controller: GraphicsLayerController
) : ModifierNodeElement<RecordGraphicsLayerModifierNode>() {
    override fun create(): RecordGraphicsLayerModifierNode {
        return RecordGraphicsLayerModifierNode(controller)
    }

    override fun update(node: RecordGraphicsLayerModifierNode) {
        node.updateController(controller)
    }

    override fun InspectorInfo.inspectableProperties() {
        name = "recordGraphicsLayer"
        properties["GraphicsLayerController"] = controller
    }
}

/**
 * Modifier节点
 *
 * @param controller A [GraphicsLayerController] which gives control to capture the Composable content.
 */
private class RecordGraphicsLayerModifierNode(
    private var controller: GraphicsLayerController
) : Modifier.Node(), DrawModifierNode {
    /**
     * 更新 [GraphicsLayerController]
     */
    fun updateController(newController: GraphicsLayerController) {
        controller.setNeedInvalidDrawImpl(null)
        controller = newController
        controller.setNeedInvalidDrawImpl { invalidateDraw() }
    }

    override fun onAttach() {
        super.onAttach()
        controller.setNeedInvalidDrawImpl { invalidateDraw() }
    }

    override fun onDetach() {
        super.onDetach()
        controller.setNeedInvalidDrawImpl(null)
    }

    // 获取GraphicsLayer，方便转成bitmap
    // 参考:
    // https://developer.android.com/develop/ui/compose/graphics/draw/modifiers#composable-to-bitmap
    override fun ContentDrawScope.draw() {
        // 记录当前绘制内容
        controller.requestQueue.forEach { request ->
            Logger.d("RecordGraphicsLayerModifierNode", "draw, recordSize: ${this.size}, drawScopeSize: ${this.size}")
            val currentGraphicsLayer = requireGraphicsContext().createGraphicsLayer()
            val recordSize = if (request.size.width > 1 && request.size.height > 1) request.<NAME_EMAIL>()
            runCatching {
                // recordSize的size超出GPU的Buffer后会无内容输出
                currentGraphicsLayer.record(
                    size = IntSize(
                        (recordSize.width * request.scale).toInt(),
                        (recordSize.height * request.scale).toInt(),
                    )
                ) {
                    scale(request.scale, Offset(0F, 0F)) {
                        translate(request.offset.x, request.offset.y) {
                            // 记录绘制内容
                            <EMAIL>()
                        }
                    }
                }

                coroutineScope.launchIO {
                    runCatching {
                        val bitmap = currentGraphicsLayer.toImageBitmap()
                        request.result.complete(bitmap)
                        controller.requestQueue.remove(request)
                        requireGraphicsContext().releaseGraphicsLayer(currentGraphicsLayer)
                    }.onFailure {
                        request.result.complete(ImageBitmap(1, 1))
                        controller.requestQueue.remove(request)
                        Logger.w("RecordGraphicsLayerModifierNode", "toImageBitmap, error: ${it.stackTraceToString()}")
                    }
                }
            }.onFailure {
                Logger.w("RecordGraphicsLayerModifierNode", "draw, error: ${it.stackTraceToString()}")
            }
        }

        // 绘制图像
        drawContent()
    }
}