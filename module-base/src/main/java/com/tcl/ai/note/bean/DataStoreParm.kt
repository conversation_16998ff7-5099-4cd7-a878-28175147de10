package com.tcl.ai.note.bean


object  DataStoreParam {
    // 显示模式 列表or网格
    const val KEY_VIEW_TYPE = "view_type"
    const val VIEW_TYPE_LARGE_IMAGE = "LARGE_IMAGE"
    const val VIEW_TYPE_LIST = "LIST"
    const val VIEW_TYPE_GRID = "GRID"
    const val NOTE_LIST_IS_CREATE_TIME_SORT = "notelist_is_createtime_sort"


    const val KEY_CURRENT_LANGUAGE = "current_language"
    const val DEFAULT_LANGUAGE = -1

    const val KEY_AUDIO_CURRENT_LANGUAGE = "audio_current_language"
    const val DEFAULT_AUDIO_LANGUAGE= -1

    const val KEY_AUDIO_CURRENT_TRANSCRIPTION_FILE = "audio_current_transcription_file"
    const val KEY_AUDIO_CURRENT_TRANSCRIPTION_TIME = "audio_current_transcription_time"

    const val KEY_WRITING_STYLE = "writing_style"
    const val KEY_PEER_COMPANION = "peer_companion"
    const val KEY_WORD_NUMBER = "word_number"

    const val KEY_ANALYSIS_PROGRESS = "analysis_progress"

    const val OPERATION_TYPE_ADD = "add"
    const val OPERATION_TYPE_REGENERATE = "regenerate"
    const val OPERATION_TYPE_SENSITIVE = "sensitive"

    // 是否是IFA展版本
    const val IS_IFA = true
}