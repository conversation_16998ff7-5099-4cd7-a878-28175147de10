package com.tcl.ai.note.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.colorResource

// 获取Color的Long值,负的 ARGB 值会发生符号扩展。使用位掩码 and 0xFFFFFFFFL 可以确保保留原始 32 位无符号值。
fun Color.toArgbLong() =
    (this.toArgb().toLong()) and 0xFFFFFFFFL

fun Long.toComposeColor(): Color {
    return Color((this and 0xFFFFFFFFL).toInt())
}
fun Int.toComposeColor(): Color = Color(this)


@Composable
fun Int.colorRes() = colorResource(id = this)

fun Long.alpha() = (this.toInt() shr 24) and 0xFF

fun Long.toArgbInt() = this.toInt()

fun Color.toHex(withAlpha: Boolean = true): String {
    val alpha = (alpha * 255).toInt()
    val red = (red * 255).toInt()
    val green = (green * 255).toInt()
    val blue = (blue * 255).toInt()

    return if (withAlpha) {
        // 输出 #AARRGGBB 格式
        String.format("#%02X%02X%02X%02X", alpha, red, green, blue)
    } else {
        // 输出 #RRGGBB 格式
        String.format("#%02X%02X%02X", red, green, blue)
    }
}

fun String?.toColor(): Color {
    if (this.isNullOrEmpty()) return Color.Transparent

    val colorStr = this.trim().removePrefix("#")

    return try {
        when (colorStr.length) {
            6 -> {
                val rgb = colorStr.toLong(16)
                Color(
                    red = ((rgb shr 16) and 0xFF).toFloat() / 255f,
                    green = ((rgb shr 8) and 0xFF).toFloat() / 255f,
                    blue = (rgb and 0xFF).toFloat() / 255f
                )
            }
            8 -> {
                val argb = colorStr.toLong(16)
                Color(
                    red = ((argb shr 16) and 0xFF).toFloat() / 255f,
                    green = ((argb shr 8) and 0xFF).toFloat() / 255f,
                    blue = (argb and 0xFF).toFloat() / 255f,
                    alpha = ((argb shr 24) and 0xFF).toFloat() / 255f
                )
            }
            else -> Color.Transparent
        }
    } catch (e: Exception) {
        Color.Transparent
    }
}