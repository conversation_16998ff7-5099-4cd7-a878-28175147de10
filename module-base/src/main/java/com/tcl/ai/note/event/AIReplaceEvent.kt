package com.tcl.ai.note.event

import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 * AI润色 中 替换文本事件
 */
sealed class AIEventType(open val text: String) {
    // AI润色 替换事件
    data class AIPolishReplace(override val text: String) : AIEventType(text)
    // 手写转文 替换事件 清除画布  插入文本光标处
    data class HandwritingToTextReplace(override val text: String) : AIEventType(text)
    // AI帮写 插入事件
    data class AIHelpWriteReplace(override val text: String) : AIEventType(text)
}

object AIReplaceEvent {
    private val _aiReplaceEvents = MutableSharedFlow<AIEventType>()
    val aiReplaceEvents = _aiReplaceEvents.asSharedFlow()
    val coroutineScope = MainScope()
    fun getAIReplaceEvent(): SharedFlow<AIEventType> {
        return aiReplaceEvents
    }
    private fun sendReplaceEvent(event: AIEventType) {
        coroutineScope.launch {
            _aiReplaceEvents.emit(event)
        }
    }

    /**
     * 润色 事件
     */
    fun sendAIPolishReplaceEvent(text: String) {
        sendReplaceEvent(AIEventType.AIPolishReplace(text))
    }

    /**
     * 手写 事件
     */
    fun sendHandwritingToTextEvent(text: String) {
        sendReplaceEvent(AIEventType.HandwritingToTextReplace(text))
    }

    /**
     * 帮写 事件
     */
    fun sendHelpWriteReplaceEvent(text: String) {
        sendReplaceEvent(AIEventType.AIHelpWriteReplace(text))
    }
}