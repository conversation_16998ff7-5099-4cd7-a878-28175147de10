package com.tcl.ai.note.widget

import androidx.compose.foundation.interaction.HoverInteraction
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow

/**
 * 悬浮笔的悬停处理
 */
class HoverPenInteractionSourceImpl : MutableInteractionSource {
    private var lastEnter: HoverInteraction.Enter? = null
    
    override val interactions = MutableSharedFlow<Interaction>(
        extraBufferCapacity = 16,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
    )
    
    override suspend fun emit(interaction: Interaction) {
        when (interaction) {
            is HoverInteraction.Enter -> {
                // 如果有之前的 hover，先退出
                lastEnter?.let {
                    interactions.emit(HoverInteraction.Exit(it))
                }
                lastEnter = interaction
                interactions.emit(interaction)
            }
            is HoverInteraction.Exit -> {
                lastEnter = null
                interactions.emit(interaction)
            }
            is PressInteraction.Press -> {
                // 按下时取消悬停
                lastEnter?.let {
                    interactions.emit(HoverInteraction.Exit(it))
                    lastEnter = null
                }
                interactions.emit(interaction)
            }
            else -> {
                interactions.emit(interaction)
            }
        }
    }
    
    override fun tryEmit(interaction: Interaction): Boolean {
        // 类似的逻辑，但使用 tryEmit
        return when (interaction) {
            is HoverInteraction.Enter -> {
                lastEnter?.let {
                    interactions.tryEmit(HoverInteraction.Exit(it))
                }
                lastEnter = interaction
                interactions.tryEmit(interaction)
            }
            is HoverInteraction.Exit -> {
                lastEnter = null
                interactions.tryEmit(interaction)
            }
            is PressInteraction.Press -> {
                lastEnter?.let {
                    interactions.tryEmit(HoverInteraction.Exit(it))
                    lastEnter = null
                }
                interactions.tryEmit(interaction)
            }
            else -> {
                interactions.tryEmit(interaction)
            }
        }
    }
}