package com.tcl.ai.note.utils

import android.app.Activity
import android.content.Context
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.content.res.Resources.getSystem
import android.util.TypedValue
import android.view.View
import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.tcl.ai.note.GlobalContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import java.util.Locale
import kotlin.math.roundToInt
import kotlin.properties.ReadWriteProperty
import kotlin.reflect.KProperty

fun View.tryRequestFocus(): Boolean {
    if (isFocused || !isAttachedToWindow) {
        return false
    }
    try {
       return requestFocus()
    } catch (e: Exception) {
        Logger.d("View", "tryRequestFocus fail! ${e.stackTraceToString()}")
    }
    return false
}

fun View.getNavigateBarHeight(): Int {
    val insets = ViewCompat.getRootWindowInsets(this)
    val imeInsets = insets?.getInsets(WindowInsetsCompat.Type.navigationBars())
    return imeInsets?.bottom ?: 0
}

fun Context.isAllGranted(permissions: Array<String>) =
    permissions.all {
        ContextCompat.checkSelfPermission(this, it) == PackageManager.PERMISSION_GRANTED
    }

fun String.isGranted() =
    ContextCompat.checkSelfPermission(
        GlobalContext.instance,
        this
    ) == PackageManager.PERMISSION_GRANTED

fun String.isNotGranted() = !this.isGranted()

fun String.shouldShowRationale(activity: Activity) =
    ActivityCompat.shouldShowRequestPermissionRationale(
        activity, this
    )

val Int.px2dp: Int get() = (this / getSystem().displayMetrics.density).toInt()
val Int.dp2px: Int get() = (this * getSystem().displayMetrics.density).toInt()

val Int.nonScaledSp
    get() = (this / GlobalContext.instance.resources.configuration.fontScale).sp
val Double.nonScaledSp
    get() = (this / GlobalContext.instance.resources.configuration.fontScale).sp
val Int.nonScaledSpCompose
    @Composable
    get() = (this / LocalConfiguration.current.fontScale).sp
val Double.nonScaledSpCompose
    @Composable
    get() = (this / LocalConfiguration.current.fontScale).sp


val TextUnit.px
    get() = (TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_SP,
        this.value,
        GlobalContext.instance.resources.displayMetrics
    )).toInt()
val Float.sp2px
    get() = (TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_SP,
        this,
        GlobalContext.instance.resources.displayMetrics
    ))
val Float.dp2px
    get() = (TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        this,
        GlobalContext.instance.resources.displayMetrics
    )).toInt()

private const val MIN_DB = -50.0
private const val MAX_DB = -12.0




fun ShortArray.toByteArray(): ByteArray = run {
    val byteArray = ByteArray(size * 2)
    forEachIndexed { index, short ->
        val byteIndex = index * 2
        byteArray[byteIndex] = (short.toInt() and 0xff).toByte()
        byteArray[byteIndex + 1] = (short.toInt() shr 8 and 0xff).toByte()
    }
    byteArray
}

fun ShortArray.plusWithPosition(other: ShortArray): ShortArray = run {
    ShortArray(this.size) { i ->
        val sum = this[i].toInt() + other[i].toInt()
        when {
            sum > Short.MAX_VALUE -> Short.MAX_VALUE
            sum < Short.MIN_VALUE -> Short.MIN_VALUE
            else -> sum.toShort()
        }
    }
}

fun Context.dpToPx(dp: Float): Int {
    return TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP, dp, this.resources.displayMetrics
    ).toInt()
}

/**
 * 根据langCode获取对应翻译
 * @param resourcesId 资源id
 * @param langCode 短code，e.g. en
 */
fun getLocaleStringResource(@StringRes resourcesId: Int, langCode: String): String {
    val context = GlobalContext.instance
    val config = Configuration(context.resources.configuration)
    config.setLocale(Locale(langCode))
    return context.createConfigurationContext(config).getString(resourcesId)
}

/**
 * dp转px
 * 50.dp.toPx
 */
val Dp.toPx
    get() = TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, value, getSystem().displayMetrics)


/**
 * 通过委托来修改或者读取MutableStateFlow的值
 */
fun <T> MutableStateFlow<T>.delegate(
    tag: String? = null,
    needPrintfCalledStack: Boolean = false,
    getter: ((T) -> T)? = null,
    setter: ((old: T, new: T) -> T)? = null,
) = object : ReadWriteProperty<Any?, T> {
    val mutableStateFlow = this@delegate
    override fun getValue(thisRef: Any?, property: KProperty<*>): T {
        val value = mutableStateFlow.value
        return getter?.invoke(value) ?: value
    }
    override fun setValue(thisRef: Any?, property: KProperty<*>, value: T) {
        Logger.v("MutableStateFlow", "delegate${tag?.let { "-$it" } ?: ""}, propertyName: ${thisRef?.javaClass?.simpleName?.let { "$it@${thisRef.hashCode()}::" } ?: "" }${property.name}, value: $value")
        if (needPrintfCalledStack) {
            Logger.printStackTrace("MutableStateFlow", "delegate${tag?.let { "-$it" } ?: ""}, propertyName: ${thisRef?.javaClass?.simpleName?.let { "$it@${thisRef.hashCode()}::" } ?: "" }${property.name}, calledStack:\n")
        }
        mutableStateFlow.update { oldValue ->
            setter?.invoke(oldValue, value) ?: value
        }
    }
}


@Stable
val Double.dp2px: Int
    get() = (this * getSystem().displayMetrics.density).toInt()


