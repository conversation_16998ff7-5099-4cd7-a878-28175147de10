package com.tcl.ai.note.utils

import android.view.View
import android.view.accessibility.AccessibilityManager
import android.widget.Toast
import androidx.annotation.StringRes
import com.tcl.ai.note.GlobalContext

object ToastUtils {

    private var currentToast: Toast? = null

    fun makeWithCancel(
        text: String,
        length: Int = Toast.LENGTH_SHORT,
        assertive: Boolean = false  // 添加参数控制是否立即打断其他播报
    ) {
        GlobalContext.applicationScope.launchMain {
            currentToast?.cancel()
            currentToast = Toast.makeText(
                GlobalContext.instance,
                text,
                if (isAccessibilityEnabled()) Toast.LENGTH_LONG else length
            ).apply {
                view?.accessibilityLiveRegion = View.ACCESSIBILITY_LIVE_REGION_NONE
            }
            currentToast?.show()
        }
    }

    /**
     * 这个方法弹出的toast 会带上APP的icon
     */
    fun makeWithCancel(
        @StringRes resId: Int,
        length: Int = Toast.LENGTH_SHORT,
        assertive: Boolean = false  // 添加参数控制是否立即打断其他播报
    ) {
        GlobalContext.applicationScope.launchMain {
            try {
                currentToast?.cancel()
                // 非强制打断时使用普通 Toast
                currentToast = Toast.makeText(
                    GlobalContext.instance,
                    resId,
                    if (isAccessibilityEnabled()) Toast.LENGTH_LONG else length
                ).apply {
                    view?.accessibilityLiveRegion = if (assertive) {
                        View.ACCESSIBILITY_LIVE_REGION_ASSERTIVE
                    } else {
                        View.ACCESSIBILITY_LIVE_REGION_POLITE
                    }
                }

                currentToast?.show()
            } catch (e: Exception) {
                Logger.e("ToastUtils", "Failed to show toast for resId: $resId, error: ${e.message}")
            }
        }
    }

    private fun isAccessibilityEnabled(): Boolean {
        val am = GlobalContext.instance.getSystemService(AccessibilityManager::class.java)
        return am?.isEnabled == true
    }
}