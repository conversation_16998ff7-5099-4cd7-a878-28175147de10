package com.tcl.ai.note.data

import com.tcl.ai.note.bean.DataStoreParam
import com.tcl.ai.note.utils.AppDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

/**
 * 获取用户设置的UseCase
 */
object GetUserSettingUseCase {

    /**
     *  获取用户保存的分类信息
      */
    fun getUserSavedCategoryInfoFlow(): Flow<UserSavedCategoryInfo> = flow {
        emit(getUserSavedCategoryInfo())
    }
    /**
     *  获取用户保存的分类信息
     */
     suspend fun getUserSavedCategoryInfo(): UserSavedCategoryInfo {
        return UserSavedCategoryInfo(
            id = AppDataStore.getData("currentCategoryId", ""),
            name = AppDataStore.getData("currentCategoryName", ""),
            colorIndex = AppDataStore.getData("currentColorIndex", ""),
            icon = AppDataStore.getData("currentCategoryIcon", "")
        )
    }
    /**
     * 保存当前选中的分类
     */
    suspend fun saveUserCategoryInfo(userSavedCategoryInfo: UserSavedCategoryInfo) {
        AppDataStore.putStringData("currentCategoryId", userSavedCategoryInfo.id)
        AppDataStore.putStringData("currentCategoryName", userSavedCategoryInfo.name)
        AppDataStore.putStringData("currentColorIndex", userSavedCategoryInfo.colorIndex)
        AppDataStore.putStringData("currentCategoryIcon", userSavedCategoryInfo.icon)
    }
    suspend fun saveDateSortType(isSortCreateTime:Boolean){
        AppDataStore.putBoolean(DataStoreParam.NOTE_LIST_IS_CREATE_TIME_SORT,isSortCreateTime)
    }

    /**
     * 获取排序类型
     */
    suspend fun getDateSortType(): Boolean {
        return AppDataStore.getBoolean(DataStoreParam.NOTE_LIST_IS_CREATE_TIME_SORT,true)
    }
}