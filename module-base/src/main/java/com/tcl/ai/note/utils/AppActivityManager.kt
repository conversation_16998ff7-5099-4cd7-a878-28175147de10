package com.tcl.ai.note.utils

import android.app.Activity
import android.app.Application.ActivityLifecycleCallbacks
import android.os.Bundle
import androidx.compose.ui.util.fastForEachReversed
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import java.lang.ref.WeakReference

object AppActivityManager : ActivityLifecycleCallbacks {
    private const val TAG = "AppActivityManager"
    private val activities = arrayListOf<WeakReference<Activity>>()
    private var activityStartedCount = 0
    //  是否在前台,息屏会触发
    private val _appInForegroundStateFlow = MutableStateFlow(false)
    val appInForegroundStateFlow = _appInForegroundStateFlow.asStateFlow()
    //  是否离开了App，回到了桌面时为true，息屏不触发
    private val _isLeaveHintStateFlow = MutableStateFlow(false)
    val isLeaveHintStateFlow = _isLeaveHintStateFlow.asStateFlow()

    var isAddingImage = MutableStateFlow(false)
    var isTakingPhoto = MutableStateFlow(false)

    
    fun isAppInForeground() = appInForegroundStateFlow.value

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        activities.add(WeakReference(activity))
    }

    override fun onActivityStarted(activity: Activity) {
        activityStartedCount++
        if (activityStartedCount == 1 && !isAppInForeground()) {
            _appInForegroundStateFlow.value = true
            Logger.v(TAG, "onActivityStarted, isAppInForeground: true")
        }
    }

    override fun onActivityResumed(activity: Activity) {
    }

    override fun onActivityPaused(activity: Activity) {
    }

    override fun onActivityStopped(activity: Activity) {
        activityStartedCount--
        if (activityStartedCount <= 0 && isAppInForeground()) {
            _appInForegroundStateFlow.value = false
            Logger.v(TAG, "onActivityStarted, isAppInForeground: false")
        }
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }

    override fun onActivityDestroyed(activity: Activity) {
        removeActivity(activity)
    }

    private fun removeActivity(activity: Activity) {
        activities.fastForEachReversed { activityRef ->
            if (activityRef == activity) {
                activities.remove(activityRef)
            }
        }
    }

    fun finishAll() {
        Logger.v(TAG, "try to finish all activity: $activities")
        activities.forEach { activityRef ->
            if (activityRef.get()?.isFinishing != true) {
                activityRef.get()?.finishAndRemoveTask()
            }
        }
    }

    /**
     * 获取当前所有活跃的 Activity
     * 发现切换小窗activities 会有多个 EditActivity 需判断是否isDestroyed
     * @return 活跃的 Activity 列表
     */
    fun getActiveActivities(): List<Activity> {
        return activities.mapNotNull { it.get() }.filter { !it.isDestroyed }
    }

    /**
     * 检查指定类型的 Activity 是否存在
     * @param activityClass Activity 的 Class
     * @return true 如果存在，false 否则
     */
    fun <T : Activity> isActivityExists(activityClass: Class<T>): Boolean {
        return getActiveActivities().any { it::class.java == activityClass }
    }

    /**
     * 检查指定类名的 Activity 是否存在
     * @param className Activity 的完整类名
     * @return true 如果存在，false 否则
     */
    fun isActivityExists(className: String): Boolean {
        val activeActivities = getActiveActivities()
        Logger.d(TAG, "isActivityExists: $className, activities: $activeActivities ")
        return activeActivities.any { it::class.java.name == className }
    }

    /**
     * 获取指定类型的 Activity 实例
     * @param activityClass Activity 的 Class
     * @return Activity 实例，如果不存在则返回 null
     */
    fun <T : Activity> getActivity(activityClass: Class<T>): T? {
        return getActiveActivities().find { it::class.java == activityClass } as? T
    }

    fun onResume() {
        if (_isLeaveHintStateFlow.value) {
            _isLeaveHintStateFlow.value = false
            Logger.v(TAG, "onResume, isAppInForeground: true")
        }
    }

    /**
     * 当用户按下 recent 键时调用此方法
     * 这个方法应该在 Activity 的 onUserLeaveHint() 中调用
     */
    fun onUserLeaveHint() {
        Logger.d(TAG, "onUserLeaveHint called, user pressed recent key")

        // 如果应用在前台，将其标记为后台
        if (isAppInForeground()) {
            _isLeaveHintStateFlow.value = true
            Logger.v(TAG, "onUserLeaveHint, isAppInForeground: false")
        }
    }

    /**
     * 记录 旅行日记 使用时长
     */
    var journalUsageTime: Long = 0L
    var journalUsageStartTime: Long = 0L
    private var currentPageIndex: Int = 0 // 当前页面索引，1表示旅行日记页面
    fun recordJournalUsage(pageIndex: Int) {
        currentPageIndex = pageIndex
        if (pageIndex == 1) { // 旅行日记页面
            if (journalUsageStartTime == 0L) {
                journalUsageStartTime = System.currentTimeMillis()
            }
        } else {
            if (journalUsageStartTime > 0L) {
                journalUsageTime += System.currentTimeMillis() - journalUsageStartTime
                journalUsageStartTime = 0L
            }
        }
    }

    fun checkJournalForeground() {
        journalUsageTime = 0L
        journalUsageStartTime = 0L
        if (currentPageIndex == 1) {
            journalUsageStartTime = System.currentTimeMillis()
        }
    }
}