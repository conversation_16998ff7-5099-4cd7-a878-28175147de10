package com.tcl.ai.note.theme

import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.utils.isLandScape
import com.tcl.ai.note.utils.isTablet


internal val LocalTclComposeDimens = staticCompositionLocalOf {
    getGlobalDimens()
}

// 不是私有是因为现在这个方法太多地方用到了, 等后续逐步替换过来
fun getGlobalDimens() = when {
    isTablet && isLandScape -> TabletLandscapeDimens   // 平板横屏
    isTablet && !isLandScape -> TabletPortraitDimens   // 平板竖屏
    else -> PhonePortraitDimens          // 手机竖屏，手机版只会有竖屏
}

private val PhoneDimens = GlobalDimens(
    btnSize = 32.dp,
    iconSize = 24.dp,
    penOptionHeight = 56.dp,
    popupWidth = 328.dp,
    sliderHeight = 26.dp,
    navigationBarHeight = 56.dp,
    menuBarHeight = 44.dp,
    richTextToolBarHeight = 44.dp,
    statusHeight = 24.dp,
    barHorizontalPadding = 24.dp,
    paletteCircleWidth = 20.dp,
    titleTopMargin = 16.dp,
    titleBottomMargin = 0.dp,
    colorPaletteWidth = 328.dp
)

private val TabletDimens = GlobalDimens(
    btnSize = 32.dp,
    iconSize = 24.dp,
    penOptionHeight = 42.dp,
    popupWidth = 328.dp,
    sliderHeight = 26.dp,
    navigationBarHeight = 56.dp,
    menuBarHeight = 44.dp,
    richTextToolBarHeight = 44.dp,
    statusHeight = 24.dp,
    barHorizontalPadding = 24.dp,
    paletteCircleWidth = 15.dp,
    titleTopMargin = 24.dp,
    titleBottomMargin = 16.dp,
    colorPaletteWidth = 328.dp
)

// 横竖屏适配定义
private val PhonePortraitDimens = PhoneDimens
private val TabletLandscapeDimens = TabletDimens

// 注意竖屏的区别在这改，以下列3个作为举例
private val TabletPortraitDimens = TabletDimens.copy(
    navigationBarHeight = 56.dp,
    menuBarHeight = 44.dp,
    richTextToolBarHeight = 44.dp
)


data class GlobalDimens(
    val btnSize: Dp,
    val iconSize: Dp,
    val penOptionHeight: Dp,
    val popupWidth: Dp,
    val sliderHeight: Dp,
    val navigationBarHeight: Dp,
    val menuBarHeight: Dp,
    val richTextToolBarHeight: Dp,
    val statusHeight: Dp,
    val barHorizontalPadding: Dp,
    val paletteCircleWidth: Dp,
    val titleTopMargin: Dp,
    val titleBottomMargin: Dp,
    val colorPaletteWidth: Dp,
)


// ================================================== 上边是控件大小，下边是字号大小 ==================================================


internal val LocalTclComposeTextSizes = staticCompositionLocalOf {
    getGlobalTextSizes()
}

fun getGlobalTextSizes() = when {
    isTablet && isLandScape -> TabletLandscapeTextSizes   // 平板横屏
    isTablet && !isLandScape -> TabletPortraitTextSizes   // 平板竖屏
    else -> PhoneTextSizes          // 手机
}

// 手机版字号，现在还是跟平板一样的
private val PhoneTextSizes = GlobalTextSizes(
    // 大标题 - 页面主标题
    titleLarge = 28.sp,
    titleMedium = 24.sp,
    titleSmall = 20.sp,

    // 正文内容
    bodyLarge = 18.sp,
    bodyMedium = 16.sp,
    bodySmall = 14.sp,

    // 辅助文本
    captionLarge = 16.sp,
    captionMedium = 14.sp,
    captionSmall = 12.sp,

    // 按钮文字
    buttonLarge = 18.sp,
    buttonMedium = 16.sp,
    buttonSmall = 14.sp,

    // 输入框文字
    inputText = 18.sp,
    inputHint = 16.sp
)

private val TabletTextSizes = GlobalTextSizes(
    // 大标题 - 页面主标题
    titleLarge = 28.sp,
    titleMedium = 24.sp,
    titleSmall = 20.sp,
    
    // 正文内容  
    bodyLarge = 18.sp,  // 平板
    bodyMedium = 16.sp,
    bodySmall = 14.sp,
    
    // 辅助文本
    captionLarge = 16.sp,
    captionMedium = 14.sp,
    captionSmall = 12.sp,
    
    // 按钮文字
    buttonLarge = 18.sp,
    buttonMedium = 16.sp,
    buttonSmall = 14.sp,
    
    // 输入框文字
    inputText = 18.sp,
    inputHint = 16.sp
)

// 平板字号，直径改上面的属性
private val TabletLandscapeTextSizes = TabletTextSizes

/**
 * 正常平板横竖屏下字号大小都应该一样，但是
 * 如果手机竖屏有什么不一样的，那么就这么修改
 */
private val TabletPortraitTextSizes = TabletLandscapeTextSizes.copy(
    titleLarge = 28.sp,
    titleMedium = 24.sp,
    titleSmall = 20.sp,
)

/**
 * 全局字号定义 - 列举了些现在项目常用的字号，待不断完善
 */
data class GlobalTextSizes(
    // 标题层级
    val titleLarge: TextUnit,    // 页面主标题
    val titleMedium: TextUnit,   // 次级标题  
    val titleSmall: TextUnit,    // 小标题
    
    // 正文层级
    val bodyLarge: TextUnit,     // 大正文
    val bodyMedium: TextUnit,    // 中等正文
    val bodySmall: TextUnit,     // 小正文
    
    // 辅助文本层级
    val captionLarge: TextUnit,  // 大辅助文本
    val captionMedium: TextUnit, // 中等辅助文本
    val captionSmall: TextUnit,  // 小辅助文本
    
    // 按钮文字层级
    val buttonLarge: TextUnit,   // 大按钮文字
    val buttonMedium: TextUnit,  // 中等按钮文字
    val buttonSmall: TextUnit,   // 小按钮文字
    
    // 输入框文字
    val inputText: TextUnit,     // 输入框内容文字
    val inputHint: TextUnit      // 输入框提示文字
)


