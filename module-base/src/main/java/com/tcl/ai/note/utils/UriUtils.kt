package com.tcl.ai.note.utils

import android.content.Context
import android.net.Uri
import android.os.ParcelFileDescriptor
import android.util.Log
import java.io.FileNotFoundException

/**
 * 安全地打开 Uri
 *
 */
fun Uri.openFileDescriptorSafely(context: Context): ParcelFileDescriptor? {
    val TAG = "UriUtils"
    return try {

        if (scheme.isNullOrBlank()) {
            Logger.w(TAG, "Invalid Uri: scheme is null or empty")
            return null
        }
        context.contentResolver.openFileDescriptor(this, "r")
    } catch (e: FileNotFoundException) {
        Log.e(TAG, "File not found for Uri: $this", e)
        null
    } catch (e: SecurityException) {
        Log.e(TAG, "Security exception for Uri: $this", e)
        null
    } catch (e: Exception) {
        Log.e(TAG, "Unexpected error opening Uri: $this", e)
        null
    }
}


