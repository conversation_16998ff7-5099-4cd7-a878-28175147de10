package com.tcl.ai.note.widget.components

import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.tcl.ai.note.base.R
import com.tcl.ai.note.controller.AIAppServiceHelper
import com.tcl.ai.note.controller.LoginErrorResult
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.utils.Logger
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton

/**
 * 显示 AI 应用服务对话框
 *isShow 是否显示
 * title 标题 当前是哪个模块
 */
@Composable
fun AIServiceDialog(isShow: <PERSON>ole<PERSON>,title: String, onDismiss: () -> Unit, onGoToSettings: () -> Unit) {
    val context = LocalContext.current
    val tip = stringResource(id = R.string.dialog_open_ai_service_title, title)
    TclDialog(
        onDismissRequest = { onDismiss.invoke() },
        show = isShow,
        content = {
            Text(
                text = tip,
            )
        },
        actions = {
            TclTextButton(onClick = { onDismiss.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_cancel)) }
            TclTextButton(
                onClick = {
                    onDismiss.invoke()
                    onGoToSettings.invoke()
                    AIAppServiceHelper.openAppSettings(context)
                }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_go_settings)) }
        })
}

@Composable
fun AIServiceSateHandler(content: @Composable () -> Unit) {
    val context = LocalContext.current
    val showAiServiceDialog = remember { mutableStateOf(false) }
    val loginHandler = rememberLoginHandler(onFailure = {
        if (it == LoginErrorResult.LunchError) {
            showAiServiceDialog.value = true
            Logger.d("AudioBlock", "loginHandler onFailure")
        }
    })
    AIServiceDialog(
        isShow = showAiServiceDialog.value,
        title = context.getString(R.string.audio_to_text),
        onDismiss = { showAiServiceDialog.value = false },
        onGoToSettings = {
            showAiServiceDialog.value = false
        }
    )
    content()
}