package com.tcl.ai.note.controller

import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.LifecycleResumeEffect
import com.tcl.ai.bridge.connector.contract.LOGIN_RESULT_NET_ERROR
import com.tcl.ai.bridge.connector.contract.LOGIN_RESULT_SUCCESS
import com.tcl.ai.bridge.connector.contract.LoginCheckContract
import com.tcl.ai.note.utils.Logger
import com.tcl.ff.component.core.http.core.exception.ApiException
import kotlinx.coroutines.launch
sealed class LoginErrorResult {
    data object  NetworkError : LoginErrorResult()
    data object  OtherError : LoginErrorResult()
    data object  LunchError : LoginErrorResult()
}
@Composable
fun rememberLoginHandler(onFailure: (LoginErrorResult) -> Unit = {}): (action: () -> Unit) -> Unit {
    val context = LocalContext.current
    var successCallback by remember { mutableStateOf({}) }
    var failureCallback by remember { mutableStateOf(onFailure) }
    val launcher = rememberLauncherForActivityResult(
        contract = LoginCheckContract(LoginCheckContract.ACTION_LOGIN_AI),
        onResult = { result ->

            when (result) {
                LOGIN_RESULT_SUCCESS -> {
                    Logger.v(TAG, "Login success")
                    successCallback.invoke()
                }
                LOGIN_RESULT_NET_ERROR -> {
                    //sdk中有toast提示，这里不需要再提示
                    //Toast.makeText(context, context.getString(R.string.network_response_failed), Toast.LENGTH_SHORT).show()
                    Logger.v(TAG, "Network error")
                    failureCallback.invoke(LoginErrorResult.NetworkError) // 网络错误时调用失败回调
                }
                else -> {
                    Logger.v(TAG, "Login failed")
                    failureCallback.invoke(LoginErrorResult.OtherError) // 其他失败情况也调用失败回调
                }
            }
        }
    )
    return { action ->
        try {
            successCallback = action
            //重新登录前，重置一下loginService
            AccountController.reconnect()
            launcher.launch(null)
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to launch login activity: ${e.message}")
            failureCallback.invoke(LoginErrorResult.LunchError) // 启动登录活动失败时也调用失败回调
        }
    }
}
const val TAG = "AccountLoginHandler"

/**
 * onResume 里监听登录状态，自动跳转到登录页面
 */
@Composable
fun OnResumeLoginStateEffect(onLoginFailure: () -> Unit) {
    val coroutineScope = rememberCoroutineScope()
    var isCancelResult by remember { mutableStateOf(false) }
    val launcher = rememberLauncherForActivityResult(
        contract = LoginCheckContract(LoginCheckContract.ACTION_LOGIN_AI),
        onResult = { result ->
            when (result) {
                LOGIN_RESULT_SUCCESS -> {
                    Logger.v(TAG, "Login success")
                }
                LOGIN_RESULT_NET_ERROR -> {
                    Logger.v(TAG, "Network error")
//                    failureCallback() // 调用失败回调
                }
                else -> {
                    Logger.v(TAG, "Login failed")
                    isCancelResult=true
                    onLoginFailure()
                }
            }
        }
    )
    LifecycleResumeEffect(Unit) {
        coroutineScope.launch {
            if (!AccountController.getLoginState()&&!isCancelResult) {
                try {
                    launcher.launch(null)
                } catch (e: Exception) {
                    Logger.e(TAG, "Failed to launch login activity: ${e.message}")
                }
            }
        }
        onPauseOrDispose {

        }
    }
}