package com.tcl.ai.note.track

import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.launch

abstract class AbsAnalyticsSubModel {
    // 扩展函数，方便使用
    fun <T> Flow<T>.collectWithScope(
        coroutineScope: CoroutineScope,
        dispatcher: CoroutineDispatcher = Dispatchers.IO,
        collector: (T) -> Unit
    ): Job {
        return coroutineScope.launch(dispatcher) {
            collect {
                collector(it)
            }
        }
    }
}
