package com.tcl.ai.note.state

data class ChatStreamingMsg(
    val status : StreamingStatus,
    val text : String,
    val threadId : String, // 服务器的对话ID，用于标识单轮对话
    val userMessageId : String = "", // 服务器返回的用户消息ID，用于retry时上传
    val errorCode : Int = 0 // 错误码
)
enum class StreamingStatus {
    IN_PROGRESS,
    COMPLETED,
    STOPPED
}
sealed interface Result<out T> {
    data class Success<T>(val data: T) : Result<T>
    data class Error(val exception: Throwable, val code: Int? = null) : Result<Nothing>
    data object Loading : Result<Nothing>
}
class NetError(message: String) : Throwable(message) {
    //用于没有网络的时候抛出异常 对应的是MsgStatus.NET_ERROR
}


