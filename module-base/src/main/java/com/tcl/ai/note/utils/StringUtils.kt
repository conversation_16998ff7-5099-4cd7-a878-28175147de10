package com.tcl.ai.note.utils

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.tcl.ai.note.base.R
import java.util.Collections

// 实化，避免泛型擦除后gson序列化时丢失类型
inline fun <reified T> stringToType(str: String?): List<T>? {
    if (str == null) {
        return Collections.emptyList()
    }
    val listType = object : TypeToken<ArrayList<T>>() {}.type
    return Gson().fromJson(str, listType)
}


@Composable
fun  Int.stringRes() = stringResource(id =this)

/**
 * 播报按钮文本时追加其类型
 */
@Composable
fun String.appendSemanticsButton() =buildString{

    append(this@appendSemanticsButton)
    append(R.string.button_type_semantics.stringRes())
}
