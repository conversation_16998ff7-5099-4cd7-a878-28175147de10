package com.tcl.ai.note.track

internal object AnalyticsConstant {
    // 埋点平台的唯一标示
    const val TYPE = "type"

    // 启动时间统计
    // 启动Note App时
    internal object LaunchTime {
        val EVENT_ID = Pair(TYPE, "3B2A65DB75")
        const val LAUNCH_TIME = "launchTime"
    }

    // 启动时长统计
    // 退出Note App时
    internal object LaunchDuration {
        val EVENT_ID = Pair(TYPE, "3BE6299669")
        const val LAUNCH_DURATION = "launchDuration"
    }

    // 笔记列表统计
    internal object NoteList {
        val EVENT_ID = Pair(TYPE, "397AD1982E")
        const val COUNT = "count"
        const val SKETCH_COUNT = "sketchCount"
        const val AVG_LENGTH = "avgLength"
        const val AVG_AUDIO_COUNT = "avgAudioCount"
        const val AVG_IMG_COUNT = "avgImgCount"
    }

    // 笔记内容统计
    internal object NoteContent {
        val EVENT_ID = Pair(TYPE, "3F039A0DAB")

        const val CONTENT_LENGTH = "contentLength"
        const val AUDIO_COUNT = "audioCount"
        const val AUDIO_TOTAL_LENGTH = "audioTotalLength"
        const val IMG_COUNT = "imgCount"
        const val BG_STYLE = "bgStyle"
    }

    // 笔记笔写内容统计
    internal object NoteHandWritingContent {
        val EVENT_ID = Pair(TYPE, "3A97F70362")

        const val PEN_ACTIONS = "penActions"
        const val PEN_DURATION = "penDuration"

        const val BALL_PEN_ACTIONS = "ballpenActions"
        const val BALL_PEN_DURATION = "ballpenDuration"

        const val MART_PEN_ACTIONS = "martpenActions"
        const val MARK_PEN_DURATION = "markpenDuration"
    }

    // 编辑统计
    internal object NoteEdit {
        val EVENT_ID = Pair(TYPE, "31177130CE")

        const val EDIT_DURATION = "editDuration"
    }

    // 录音转写统计
    internal object RecordToText {
        val EVENT_ID = Pair(TYPE, "3D2C5609DD")

        const val TRANSCRIPE_ID = "transcripeID"
        const val TRANSCRIPE_LENGTH = "transcripeLength"
        const val TRANSCRIPE_ACTION_STATE = "transcripeActionState"

        const val TRANSCRIPE_ACCOUNT_STATE = "transcripeAcountState"

        const val TRANSCRIPE_STATE = "transcripeState"
        const val TRANSCRIPE_DURATION = "transcripeDuration"

        const val TRANSCRIPE_PAUSE = "transcripePause"
        const val TRANSCRIPE_PAUSE_DURATION = "transcripePauseDuration"

        const val TRANSCRIPE_MAX = "transcripeMax"

        const val TRANSCRIPE_COPY = "transcripeCopy"

        const val TRANSCRIPE_RESET = "transcripeReset"
    }

    // 手写转文本统计
    internal object HandWritingToText {
        val EVENT_ID = Pair(TYPE, "3076C4D3F7")

        const val OCR_COUNT = "ocrCount"
        const val OCR_LANG = "ocrLang"

        const val OCR_DURATION = "ocrDuration"
        const val OCR_LENGTH = "ocrLength"
        const val OCR_STATE = "ocrState"

        const val OCR_STOP = "ocrStop"
        const val OCR_STOP_DURATION = "ocrStopDuration"

        const val OCR_RESET = "ocrReset"

        const val OCR_REPLACE = "ocrReplace"

        const val OCR_COPY = "ocrCopy"

        const val OCR_MAX = "ocrMax"
    }

    // AI概要
    internal object AiSummary {
        val EVENT_ID = Pair(TYPE, "3472C16D62")

        const val SUMMARY_START_STATE = "writeStartState"
        const val SUMMARY_RETURN_STATE = "summaryReturnState"
        const val SUMMARY_STOP = "summaryStop"
        const val SUMMARY_STOP_DURATION = "summaryStopDuration"
        const val SUMMARY_COPY = "summaryCopy"
        const val SUMMARY_RESET = "summaryReset"
        const val SUMMARY_MAX = "summaryMax"
    }

    // AI润色
    internal object AiRewrite {
        val EVENT_ID = Pair(TYPE, "39721BF5C0")

        const val REWRITE_START_STATE = "rewriteStartState"
        const val REWRITE_TOPIC = "rewriteTopic"
        const val REWRITE_RETURN_STATE = "rewriteReturnState"
        const val REWRITE_STOP = "rewriteStop"
        const val REWRITE_STOP_DURATION = "rewriteStopDuration"
        const val REWRITE_COPY = "rewriteCopy"
        const val REWRITE_RESET = "rewrite"
        const val REWRITE_MAX = "rewriteMax"
    }

    // AI帮写
    internal object AiWrite {
        val EVENT_ID = Pair(TYPE, "3DD26232B0")

        const val WRITE_START_STATE = "writeStartState"
        const val WRITE_TOPIC = "writeTopic"
        const val WRITE_RETURN_STATE = "writeReturnState"

        const val WRITE_STOP = "writeStop"
        const val WRITE_STOP_DURATION = "writeStopDuration"

        const val WRITE_COPY = "writeCopy"
        const val WRITE_RESET = "writeReset"
        const val WRITE_INSERT = "writeInsert"
        const val WRITE_MAX = "writeMax"
        const val WRITE_RESENT_TOPIC = "writeResentTopic"
    }

    internal object JournalUsageTime {
        val EVENT_ID = Pair(TYPE, "3C3BEF7553")

        const val USE_TRAVEL = "use_travel"
        const val USE_APP = "use_noteapp"
        const val START_PERIOD = "start_period"
    }

    internal object JournalCreation {
        val EVENT_ID = Pair(TYPE, "369B633651")

        const val TITLE_LENGTH = "title_length"
        const val COVER_PREFERENCE = "cover_preference"
        const val TYPE_PREFERENCE = "type_preference"
        const val OPERATION_TYPE = "operation_type"
        const val IS_SAVED_SUCCESSFULLY = "is_saved_successfully"
        const val LAST_EDITED_ITEM = "last_edited_item"
        const val USE_DURATION = "use_duration"
    }

    internal object JournalContentCreation{
        val EVENT_ID = Pair(TYPE, "3067B16E73")

        const val IS_INSPIRATION_RECOMMENDATION_ON = "is_inspiration_recommendation_on"
        const val CREATION_TYPE = "creation_type"
        const val TEMPLATE_INDEX = "template_index"
        const val IS_AI_USED = "is_ai_used"
        const val AI_REWRITE_COUNT = "ai_rewrite_count"
        const val PHOTO_ADD_COUNT = "photo_add_count"
        const val WORD_COUNT = "word_count"
        const val PAGE_STAY_DURATION = "page_stay_duration"
    }


    internal object JournalAiWrite {
        val EVENT_ID = Pair(TYPE, "3BF7777D31")

        const val CREATION_TYPE = "creation_type"
        const val WRITING_PREFERENCE_STYLE = "writing_preference_style"
        const val WRITING_PREFERENCE_COMPANION = "writing_preference_companion"
        const val WRITING_PREFERENCE_WORD_COUNT = "writing_preference_word_count"
        const val IS_SPECIAL_EVENT_FILLED = "is_special_event_filled"
        const val SPECIAL_EVENT_STRING_COUNT = "special_event_string_count"
        const val WRITING_OPERATION_DURATION = "writing_operation_duration"
        const val WRITING_GENERATION_DURATION = "writing_generation_duration"
        const val IS_ADOPTED = "is_adopted"
        const val IS_REWRITE_THIS_TIME = "is_rewrite_this_time"
    }

    internal object JournalUsage {
        val EVENT_ID = Pair(TYPE, "3C07B405E4")

        const val DIARY_COUNT = "diary_count"
        const val TODAY_CREATED_COUNT = "today_created_count"
        const val TODAY_DELETED_COUNT = "today_deleted_count"
        const val EXHIBITION_MODE = "exhibition_mode"
        const val IS_INSPIRATION_RECOMMENDATION_ON = "is_inspiration_recommendation_on"
    }
}