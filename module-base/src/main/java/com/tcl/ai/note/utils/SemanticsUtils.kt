package com.tcl.ai.note.utils

import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import com.tcl.ai.note.GlobalContext
import com.tcl.ai.note.base.R

/**
 * 无障碍语义工具类
 * 用于统一处理各种控件的无障碍描述
 * 谁要添加定制程度更高类型的，可以进行仿写
 */
object SemanticsUtils {

    /**
     * 为控件添加标准的无障碍语义
     * @param content 内容描述（如"红色"、"背景样式1"等）
     * @param controlType 控件类型（如"按钮"、"复选框"等）, 注意Button是没有选中态的
     * @param isSelected 是否选中状态, 什么都不填不会播报【点按两次即可激活】的后缀，只有填false才会
     * @param role 语义角色
     * @return 带有完整无障碍描述的 Modifier
     */
    private fun Modifier.accessibilitySemantics(
        content: String,
        controlType: String,
        isSelected: Boolean? = null,
        isPlaySuffix: Boolean? = null,
        role: Role? = null
    ): Modifier = this.semantics {
        val context = GlobalContext.appContext

        val separator = context.getString(R.string.accessibility_separator)

        // 构建完整的无障碍描述
        val stateDesc = if (role == null || role == Role.Button) {
            null
        } else {
            isSelected?.let { selected ->
                if (selected) {
                    context.getString(R.string.checked_status)
                } else {
                    context.getString(R.string.unchecked_status)
                }
            }
        }

        // 还是统一吧，与系统的保持一致
        val actionDesc = context.getString(R.string.double_tap_to_activate)

        // 使用格式化字符串构建描述
        this.contentDescription = when {
            isPlaySuffix == true -> {
                // isPlaySuffix为true时：拼接 类型+操作提示
                when {
                    stateDesc != null -> {
                        // 有状态且有操作提示：内容+类型+状态+操作
                        context.getString(
                            R.string.accessibility_description_format,
                            content,
                            controlType,
                            stateDesc,
                            actionDesc
                        )
                    }


                    else -> {
                        // 没有状态信息但有操作提示：内容+类型+操作提示
                        context.getString(
                            R.string.accessibility_description_format_3_parts,
                            content,
                            controlType,
                            actionDesc
                        )
                    }
                }
            }

            else -> {
                // isPlaySuffix为false或null时：只拼接 内容+状态
                when {
                    stateDesc != null -> {
                        // 有状态：内容+状态
                        "${content}${separator}${stateDesc}"
                    }

                    else -> {
                        // 无状态：仅内容
                        content
                    }
                }
            }
        }

        this.role = role ?: Role.Button
    }

    /**
     * 为按钮添加无障碍语义的便捷方法
     * 注意这个是没有选中态的
     */
    fun Modifier.buttonSemantics(
        content: String,
        isSelected: Boolean? = null,
        isPlaySuffix: Boolean = true
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.button_type_semantics),
            isSelected = isSelected,
            role = Role.Button,
            isPlaySuffix = isPlaySuffix
        )
    }

    /**
     * 为复选框添加无障碍语义的便捷方法
     */
    fun Modifier.checkboxSemantics(
        content: String,
        isSelected: Boolean,
        isPlaySuffix: Boolean = true
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.checkbox_type_semantics),
            isSelected = isSelected,
            role = Role.Checkbox,
            isPlaySuffix = isPlaySuffix
        )
    }

    /**
     * 为选项卡添加无障碍语义的便捷方法
     */
    fun Modifier.tabSemantics(
        content: String,
        isSelected: Boolean,
        isPlaySuffix: Boolean = true
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.tab_type_semantics),
            isSelected = isSelected,
            role = Role.Tab,
            isPlaySuffix = isPlaySuffix
        )
    }

    /**
     * 为单选按钮添加无障碍语义的便捷方法
     */
    fun Modifier.radioButtonSemantics(
        content: String,
        isSelected: Boolean,
        isPlaySuffix: Boolean = true
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.radio_button_type_semantics),
            isSelected = isSelected,
            role = Role.RadioButton,
            isPlaySuffix = isPlaySuffix
        )
    }

    /**
     * 为单选按钮添加无障碍语义的便捷方法（不设置Role角色）
     * 适用于某些特殊场景，避免TalkBack重复播报控件类型
     * 注意：这种场景的处理与标准radioButtonSemantics不同，不会设置Role.RadioButton
     */
    fun Modifier.radioButtonSemanticsWithoutRole(
        content: String,
        isSelected: Boolean,
        isPlaySuffix: Boolean = true
    ): Modifier = this.semantics {
        val context = GlobalContext.appContext
        
        val stateDesc = if (isSelected) {
            context.getString(R.string.checked_status)
        } else {
            context.getString(R.string.unchecked_status)
        }
        val actionDesc = context.getString(R.string.double_tap_to_activate)
        val controlType = context.getString(R.string.radio_button_type_semantics)
        
        this.contentDescription = if (isPlaySuffix) {
            context.getString(
                R.string.accessibility_description_format,
                content,
                controlType,
                stateDesc,
                actionDesc
            )
        } else {
            val separator = context.getString(R.string.accessibility_separator)
            "${content}${separator}${stateDesc}"
        }
    }

    /**
     * 为开关添加无障碍语义的便捷方法
     */
    fun Modifier.switchSemantics(
        content: String,
        isEnabled: Boolean,
        isPlaySuffix: Boolean = true
    ): Modifier {
        val context = GlobalContext.appContext
        return accessibilitySemantics(
            content = content,
            controlType = context.getString(R.string.switch_type_semantics),
            isSelected = isEnabled,
            role = Role.Switch,
            isPlaySuffix = isPlaySuffix
        )
    }

    /**
     * 为滑块添加无障碍语义的便捷方法
     * 注意：Compose没有专门的Slider Role，对于真正的滑块建议直接使用Compose的Slider组件
     * 这个方法主要用于自定义的类滑块控件
     */
    fun Modifier.sliderSemantics(
        content: String,
        value: Float? = null,
        valueRange: ClosedFloatingPointRange<Float>? = null
    ): Modifier {
        val context = GlobalContext.appContext
        val controlType = context.getString(R.string.slider_type_semantics)

        // 如果有数值信息，包含在描述中
        val fullContent = if (value != null && valueRange != null) {
            val percentage =
                ((value - valueRange.start) / (valueRange.endInclusive - valueRange.start) * 100).toInt()
            "$content, $percentage%"
        } else {
            content
        }

        // 滑块通常不需要"点按两次激活"的提示，因为它们有自己的交互方式（拖拽）
        return this.semantics {
            this.contentDescription =
                "${fullContent}${context.getString(R.string.accessibility_separator)}$controlType"
            // 不设置特定的role，让滑块组件自己处理语义
        }
    }
}

