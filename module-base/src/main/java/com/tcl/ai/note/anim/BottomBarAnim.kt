package com.tcl.ai.note.anim

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.AnimatedVisibilityScope
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import kotlinx.coroutines.delay

private const val ANIMATION_DURATION = 300

@Composable
fun BottomBarAnim(
    modifier: Modifier = Modifier,
    visibleA: Boolean,
    contentA: @Composable () -> Unit,
    contentB: @Composable () -> Unit,
) {
    var isAVisible by remember { mutableStateOf(visibleA) }
    var isBVisible by remember { mutableStateOf(!visibleA) }

    BottomAnimatedVisibility(modifier = modifier, visible = isAVisible) {
        // 操作按钮（仅在手机上显示）
        contentA()
    }
    BottomAnimatedVisibility(modifier = modifier,visible = isBVisible) {
        // 底部导航按钮（最底部，只在手机上显示）
        contentB()
    }

    LaunchedEffect(visibleA) {
        if (visibleA) {
            isBVisible = false
            delay(ANIMATION_DURATION.toLong())
            isAVisible = true
        } else {
            isAVisible = false
            delay(ANIMATION_DURATION.toLong())
            isBVisible = true
        }
    }

}

@Composable
internal fun BottomAnimatedVisibility(
    visible: Boolean,
    modifier: Modifier = Modifier,
    content: @Composable AnimatedVisibilityScope.() -> Unit
) {
    val slideEnter = slideInVertically(
        initialOffsetY = { it }, // 从下方滑入
        animationSpec = tween(ANIMATION_DURATION)
    )

    val slideExit = slideOutVertically(
        targetOffsetY = { it }, // 向下方滑出
        animationSpec = tween(ANIMATION_DURATION)
    )

    val enterAnimation = slideEnter + fadeIn(animationSpec = tween(ANIMATION_DURATION))

    val exitAnimation = slideExit + fadeOut(animationSpec = tween(ANIMATION_DURATION))
    AnimatedVisibility(
        modifier = modifier,
        visible = visible,
        enter = enterAnimation,
        exit = exitAnimation
    ) {
        content()
    }
}