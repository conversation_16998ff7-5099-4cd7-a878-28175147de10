package com.tcl.ai.note.utils

import android.app.ActivityManager
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import com.tcl.ai.note.GlobalContext

val APP_PACKAGE_NAME: String = GlobalContext.instance.packageName

fun Context.startAISummary(noteId: Long) {
    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.summary.view.SummaryActivity")
        putExtra("noteId", noteId)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

fun Context.startAIPolish(noteId: Long) {
    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.polish.view.ReWriteActivity")
        putExtra("noteId", noteId)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

fun Context.startAIHelpWriting(noteId: Long) {
    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.helpwriting.ui.HelpMeWriteActivity")
        putExtra("noteId", noteId)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

/**
 * activity跳转防抖 默认500L毫秒
 */
object ActivityDebounce {
    private var lastClickTime = 0L
    // 默认防抖时间 之前 给AI 帮写用的，因为点击AI帮写需要连AI服务比较耗时
    private const val DEBOUNCE_TIME = 500L

    fun checkDebounce(debounceTime: Long = DEBOUNCE_TIME): Boolean {
        val currentTime = System.currentTimeMillis()
        val timeDiff = currentTime - lastClickTime
        val result = timeDiff >= debounceTime

        Logger.d("ActivityDebounce", "currentTime=$currentTime, lastTime=$lastClickTime, diff=$timeDiff, result=$result")

        // 只有通过防抖检查时才更新lastClickTime
        // 这样实现前沿执行：首次点击立即执行，后续点击被忽略直到防抖时间过去
        if (result) {
            lastClickTime = currentTime
        }

        return result
    }
}


fun Context.startHandwritingToText(noteId: Long) {
    if (!ActivityDebounce.checkDebounce()) {
        return
    }
    Logger.d("startHandwritingToText","noteId = $noteId time = ${System.currentTimeMillis()}")
    val intent = Intent().apply {
        component =
            ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.hwtt.view.HandwritingToTextActivity")
        putExtra("NoteId", noteId)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

/**
 * 启动音频转文本结果展示
 * @param audioPath 音频文件的路径
 */
fun Context.startAudioToText(audioPath: String){
    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.voicetotext.view.AudioToTextActivity")
        putExtra("audioPath", audioPath)
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}

/**
 * 启动TCL AI 充值界面
 */
fun Context.startTCLAIChargeActivity() {
    val intent = Intent().apply {
        action = "com.tcl.ai.action.SETTINGS_MENU_GUIDE"
        `package` = "com.tcl.ai.app"
        putExtra("navigate_to", "vip_settings")
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }
    startActivity(intent)
}
/**
 * 检查指定的 Activity 是否正在运行
 * @param activityClassName Activity 的完整类名
 * @return true 如果 Activity 正在运行，false 否则
 */
fun Context.isActivityRunning(activityClassName: String): Boolean {
    return try {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val runningTasks = activityManager.getRunningTasks(10)

        runningTasks.any { taskInfo ->
            taskInfo.topActivity?.className == activityClassName ||
                    taskInfo.baseActivity?.className == activityClassName
        }
    } catch (e: Exception) {
        // 如果获取失败，默认返回 false，允许启动
        false
    }
}

/**
 * 检查 EditActivity 是否正在运行（方法1：使用 ActivityManager）
 * @return true 如果 EditActivity 正在运行，false 否则
 */
fun Context.isEditActivityRunning(): Boolean {
    return isActivityRunning("com.tcl.ai.note.dashboard.view.EditActivity")
}

/**
 * 检查 EditActivity 是否存在（方法2：使用 AppActivityManager，推荐）
 * @return true 如果 EditActivity 存在，false 否则
 */
fun Context.isEditActivityExists(): Boolean {
    return AppActivityManager.isActivityExists("com.tcl.ai.note.dashboard.view.EditActivity")
}
/**
 * 启动编辑界面
 * @param noteId 笔记ID，null表示新建笔记
 * @param isPen 是否启用画笔模式，默认false
 * @param checkIfExists 是否检查 EditActivity 是否已存在，如果存在则不启动，默认false
 */
fun Context.startEditActivity(
    noteId: String? = null,
    isPen: Boolean = false,
    checkIfExists: Boolean = false
) {
    Logger.d("ActivityIntentUtils", "startEditActivity called: noteId=$noteId, isPen=$isPen, checkIfExists=$checkIfExists")

    // 添加防抖机制，防止快速重复点击
    if (!ActivityDebounce.checkDebounce(250)) {
        Logger.d("ActivityIntentUtils", "startEditActivity blocked by debounce")
        return
    }

    // 如果需要检查是否已存在，且确实已存在，则不启动
    if (checkIfExists && isEditActivityExists()) {
        Logger.d("ActivityIntentUtils", "EditActivity already exists, skip launching")
        return
    }

    val intent = Intent().apply {
        component = ComponentName(APP_PACKAGE_NAME, "com.tcl.ai.note.dashboard.view.EditActivity")
        if (noteId != null) {
            putExtra("noteId", noteId)
        }
        putExtra("isPen", isPen)

        // 修复：使用简单的启动方式，避免任务栈问题
        // 只使用 NEW_TASK，因为从非 Activity 上下文启动需要这个 flag
        flags = Intent.FLAG_ACTIVITY_NEW_TASK
    }

    try {
        startActivity(intent)
        Logger.d("ActivityIntentUtils", "EditActivity started successfully: noteId=$noteId")
    } catch (e: Exception) {
        Logger.e("ActivityIntentUtils", "Failed to start EditActivity: ${e.message}")
    }
}

/**
 * 启动编辑界面（如果不存在的话）
 * 这是一个便捷方法，等同于 startEditActivity(checkIfExists = true)
 * @param noteId 笔记ID，null表示新建笔记
 * @param isPen 是否启用画笔模式，默认false
 */
fun Context.startEditActivityIfNotExists(
    noteId: String? = null,
    isPen: Boolean = false
) {
    startEditActivity(noteId = noteId, isPen = isPen, checkIfExists = true)
}
