package com.tcl.ai.note.utils

import android.app.Activity

@Deprecated("请用AppActivityManager替换")
object ActivityCollector {
    private val activities = mutableSetOf<Activity>()
    private const val TAG = "ActivityCollector"

    fun addActivity(activity: Activity) {
        activities.add(activity)
    }

    fun removeActivity(activity: Activity) {
        activities.remove(activity)
    }

    fun finishAll() {
        Logger.v(TAG, "try to finish all activity: $activities")
        activities.forEach {
            if (!it.isFinishing) {
                it.finishAndRemoveTask()
            }
        }
        activities.clear()
    }
}