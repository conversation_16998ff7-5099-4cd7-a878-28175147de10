package com.tcl.ai.note.utils

import android.util.Log
import java.util.Locale

// Lang Tag: "zh-CN"
val systemLangTag: String
    get() = Locale.getDefault().toLanguageTag()

// Lang Code: "zh"
val systemLang: String
    get() = Locale.getDefault().language

val systemLangCountry: String
    get() = Locale.getDefault().country


/**
 * 获取AI的LangCode，用于请求AI的API
 * 处理中文语言，请求不到AI数据问题
 */
fun getAIApiLangCode(): String {
    //                val languageCode = Locale.getDefault().toLanguageTag()
    // https://learn.microsoft.com/zh-cn/azure/ai-services/speech-service/language-support?tabs=language-identification&accessToken=ut_uqrOi0eCDnLK5DbbLKrD5XdAToscDwy31rt
    // 按照上面链接中的协议定义，zh-CN是中国大陆的语言代码，zh-HK是香港的语言代码，toLanguageTag会返回zh-Hans-CN，zh-Hant-HK
    val country = Locale.getDefault().country
    Log.d("TclCloudService", "country: $country")
    val  languageCode: String = if(country.isNotEmpty()){
        if (Locale.getDefault().language.equals("zh") && (country.equals("HK") || country.equals("MO"))){
            Locale.getDefault().toLanguageTag()
        }else{
            Locale.getDefault().language + "-" + country
        }
    }else{
        //zh-CN，卓易的country可以获取到CN
        //HK和US获取不到，为空，判断语言类型进行赋值
        if (Locale.getDefault().language.equals("zh")){
            "zh-Hant-HK"
        }else{
            "en-US"
        }
    }
    return languageCode
}
