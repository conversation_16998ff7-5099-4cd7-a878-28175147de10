package com.tcl.ai.note.net

import kotlinx.coroutines.flow.Flow

/**
 * Utility for reporting app connectivity status
 */
import android.content.Context
import android.net.ConnectivityManager
import android.net.ConnectivityManager.NetworkCallback
import android.net.Network
import android.net.NetworkCapabilities
import android.net.NetworkRequest
import android.net.NetworkRequest.Builder
import android.util.Log
import androidx.core.content.getSystemService
import com.tcl.ai.note.utils.Logger
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.conflate
import javax.inject.Inject

interface NetworkMonitor {
    val isOnline: Flow<Boolean>
}

class ConnectivityManagerNetworkMonitor @Inject constructor(
    @ApplicationContext private val context: Context,
) : NetworkMonitor {
    override val isOnline: Flow<Boolean> = callbackFlow {
        val connectivityManager = context.getSystemService<ConnectivityManager>()
        if (connectivityManager == null) {
            channel.trySend(false)
            channel.close()
            return@callbackFlow
        }

        /**
         * The callback's methods are invoked on changes to *any* network matching the [NetworkRequest],
         * not just the active network. So we can simply track the presence (or absence) of such [Network].
         */
        val callback = object : NetworkCallback() {

            private val networks = mutableSetOf<Network>()

            override fun onAvailable(network: Network) {
                networks += network
                channel.trySend(true)
                Logger.d("ConnectivityManager", "onAvailable: $network")
            }

            override fun onLost(network: Network) {
                networks -= network
                channel.trySend(networks.isNotEmpty())
            }
        }
        // 标记callback是否已注册
        var isCallbackRegistered = false
        try {
            val request = Builder()
                .addCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
                .addCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
                .build()
            connectivityManager.registerNetworkCallback(request, callback)
            isCallbackRegistered = true

            /**
             * Sends the latest connectivity status to the underlying channel.
             */
            Log.d("ConnectivityManager", "isCurrentlyConnected: ${connectivityManager.isCurrentlyConnected()}")
            channel.trySend(connectivityManager.isCurrentlyConnected())
        } catch (e: Exception) {
            e.printStackTrace()
            //出现这个问题的根本原因可能是内存泄漏，暂时没有看出是什么地方
            //此处可能会抛出异常，注册过多的网络状态监听器，会抛出异常
            channel.trySend(connectivityManager.isCurrentlyConnected())
            Log.d("ConnectivityManager", "isCurrentlyConnected: ${connectivityManager.isCurrentlyConnected()}")
        }
        awaitClose {
            // 只有在callback成功注册的情况下才尝试注销
            if (isCallbackRegistered) {
                try {
                    connectivityManager.unregisterNetworkCallback(callback)
                } catch (e: Exception) {
                    // 捕获可能的"NetworkCallback was not registered"异常
                    Logger.e("ConnectivityManager", "Error unregistering network callback$e")
                }
            }
        }
    }
        .conflate()

    private fun ConnectivityManager.isCurrentlyConnected(): Boolean {
        return run {
            val network = activeNetwork ?: return false
            val networkCapabilities = getNetworkCapabilities(network) ?: return false

            // 检查网络是否具有互联网连接能力
            val hasInternet = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
            val isValidated = networkCapabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)

            // 返回网络是否具有互联网能力且经过验证
            hasInternet && isValidated
        }
    }
}