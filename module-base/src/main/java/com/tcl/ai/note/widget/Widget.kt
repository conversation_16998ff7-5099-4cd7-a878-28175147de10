package com.tcl.ai.note.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tct.theme.core.designsystem.component.TclDialog
import com.tct.theme.core.designsystem.component.TclTextButton

/**
 * 通用按钮类控件
 * */
@Composable
fun CircleButton(
    onClick: () -> Unit,
    painter: Painter,
    modifier: Modifier = Modifier,
) {
    IconButton(
        onClick = onClick,
        modifier = modifier
    ) {
        Image(
            painter = painter,
            contentDescription = "image description",
            modifier = Modifier
                .fillMaxSize()
        )
    }
}

@Composable
fun UsageLimitDialog(onDismissRequest: () -> Unit, onLearnMoreClick: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Color(0X20000000)
            ),
        contentAlignment = Alignment.Center
    ) {
        Surface(
            modifier = Modifier.wrapContentSize(),
            color= Color(0X20000000),
            shape = RoundedCornerShape(16.dp),
            shadowElevation = 8.dp
        ) {
            TclDialog(
                onDismissRequest = { onDismissRequest.invoke() },
                show = true,
                content = {
                    Text(
                        text = stringResource(id = R.string.ai_message_limit),
                    )
                },
                actions = {
                    TclTextButton(onClick = { onLearnMoreClick.invoke() }) { Text(stringResource(id = com.tcl.ai.note.base.R.string.btn_learn_more)) }
                })
        }
    }
}

@Composable
fun HorizontalLine(modifier:Modifier = Modifier) {
    HorizontalLine(modifier = modifier, thickness = 1.dp) // 二期统一改成1dp了
}

/**
 * 水平分割线
 * @param thickness 线宽 (高度)
 * @param color 颜色
 * @param modifier 修饰符
 */
@Composable
fun HorizontalLine(
    thickness: Dp = 1.dp,
    color: Color = colorResource(R.color.bg_outline_10),
    modifier: Modifier = Modifier
) {

    Divider(
        color = color,
        thickness = thickness,
        modifier = modifier.fillMaxWidth()
    )
}
/**
 * 垂直分割线
 * @param thickness 线宽 (宽度)
 * @param color 颜色
 * @param modifier 修饰符
 */
@Composable
fun VerticalLine(
    thickness: Dp = 1.dp,
    color: Color = colorResource(R.color.bg_outline_10),
    modifier: Modifier = Modifier
) {
    Divider(
        modifier = modifier
            .width(thickness)
            .fillMaxHeight(),
        color = color
    )
}

