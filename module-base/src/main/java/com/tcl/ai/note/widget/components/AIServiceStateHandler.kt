package com.tcl.ai.note.widget.components

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import com.tcl.ai.note.controller.AccountController
import com.tcl.ai.note.controller.LoginErrorResult
import com.tcl.ai.note.controller.rememberLoginHandler
import com.tcl.ai.note.utils.Logger

/**
 * AI服务状态处理
 * 封装通用登录状态检测和打开AI服务
 * 对话框显示逻辑
 */
@Composable
fun AIServiceStateHandler(
    title: String = "",
    content: @Composable (loginHandler: (action: () -> Unit) -> Unit) -> Unit
) {
    // 是否显示AI服务对话框
    var showAiServiceDialog by remember { mutableStateOf(false) }

    // 登录处理器
    val loginHandler = rememberLoginHandler(onFailure = { result ->
        if (result == LoginErrorResult.LunchError) {
            showAiServiceDialog = true
            Logger.d("AIServiceStateHandler", "loginHandler onFailure")
        }
    })

    // 显示AI服务对话框
    if (showAiServiceDialog) {
        AIServiceDialog(
            isShow = true,
            title = title,
            onDismiss = { showAiServiceDialog = false },
            onGoToSettings = { showAiServiceDialog = false }
        )
    }

    // 调用内容组件，传递登录处理器
    content(loginHandler)
}

/**
 * 检查登录状态并执行操作
 */
suspend fun checkLoginAndExecute(
    loginHandler: (action: () -> Unit) -> Unit,
    action: () -> Unit
) {
    if (AccountController.getLoginState()) {
        action()
    } else {
        loginHandler(action)
    }
}
