package com.tcl.ai.note.state

import com.tcl.ai.note.exception.AppException

/**
 * 一次性事件
 */

sealed class ChatEffect {
    data class ShowToastRes(val resourceId: Int,val currentTime:Long=System.currentTimeMillis()) : ChatEffect()
    data class Exception(val exception: AppException) : ChatEffect()
    //使用次数限制
    data class ErrUsageLimited(val currentTime:Long=System.currentTimeMillis()) : ChatEffect()
}