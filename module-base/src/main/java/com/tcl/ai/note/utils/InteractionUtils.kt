package com.tcl.ai.note.utils

import android.util.Log
import androidx.compose.foundation.interaction.HoverInteraction
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.PressInteraction
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.launch


fun HoverMutableInteractionSource(): MutableInteractionSource = HoverCancelInteractionSourceImpl()


/**
 * 悬停事件管理器，用于处理多个悬停事件的取消逻辑
 */
object HoverInteractionManager {
    private val enterMap = mutableMapOf<Int, Pair<HoverCancelInteractionSourceImpl, HoverInteraction.Enter>>()


    fun onEnter(source: HoverCancelInteractionSourceImpl, enter: HoverInteraction.Enter) {
        if (enterMap.isNotEmpty()) {
            for ((_, pair) in enterMap) {
                val (src, ent) = pair
                CoroutineScope(Dispatchers.Main).launch {
                    src.emitExit(ent)
                }
            }
            enterMap.clear()
        }
        enterMap[source.hashCode()] = Pair(source, enter)
    }

    fun onExit(source: HoverCancelInteractionSourceImpl) {
        enterMap.remove(source.hashCode())

    }
}


class HoverCancelInteractionSourceImpl : MutableInteractionSource {
    private var lastEnter: HoverInteraction.Enter? = null

    suspend fun emitExit(enter: HoverInteraction.Enter) {
        interactions.emit(HoverInteraction.Exit(enter))
    }


    override val interactions = MutableSharedFlow<Interaction>(
        extraBufferCapacity = 16,
        onBufferOverflow = BufferOverflow.DROP_OLDEST,
    )

    override suspend fun emit(interaction: Interaction) {
        when (interaction) {
            is HoverInteraction.Enter -> {
                lastEnter = interaction
                HoverInteractionManager.onEnter(this, interaction)
            }
            is HoverInteraction.Exit -> {
                HoverInteractionManager.onExit(this)
                lastEnter = null
            }
            is PressInteraction.Press,is PressInteraction.Release ,is PressInteraction.Cancel -> {
                lastEnter?.let {
                    interactions.emit(HoverInteraction.Exit(it))
                }
                HoverInteractionManager.onExit(this)
                lastEnter = null
            }
        }
        interactions.emit(interaction)
    }

    override fun tryEmit(interaction: Interaction): Boolean {
        when (interaction) {
            is HoverInteraction.Enter -> {
                lastEnter = interaction
            }
            is HoverInteraction.Exit -> {
                HoverInteractionManager.onExit(this)
                lastEnter = null
            }
            is PressInteraction.Press,is PressInteraction.Release ,is PressInteraction.Cancel ->{
                lastEnter?.let {
                    interactions.tryEmit(HoverInteraction.Exit(it))
                }
                HoverInteractionManager.onExit(this)
                lastEnter = null
            }
        }
        return interactions.tryEmit(interaction)
    }

}