package com.tcl.ai.note.widget

import android.os.Build
import android.widget.TextView
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.TclTheme

@Composable
fun ExpandPanel(
    modifier: Modifier = Modifier,
    textExpanded: Boolean,
    originalText: String,
    onShowIconClick: ()-> Unit,
    paddingTop: Dp = 0.dp
){
    AnimatedVisibility(visible = textExpanded,
        enter = fadeIn(animationSpec = tween(durationMillis = 350)) +
                expandVertically(animationSpec = tween(durationMillis = 1000)),
        exit = shrinkVertically(animationSpec = tween(durationMillis = 1000)) +
                fadeOut(animationSpec = tween(durationMillis = 350))
    ) {

        Column(modifier = modifier
            .padding(start = 10.dp, end = 10.dp, top = paddingTop, bottom = 33.dp)
            .shadow(2.dp, RoundedCornerShape(20.dp))
            .background(TclTheme.colorScheme.reWriteExpandBg)
            .wrapContentHeight()  // Highly adaptive content
            .fillMaxWidth()
            .padding(start = 19.dp, end = 19.dp, bottom = 15.dp, top = 15.dp)
            , horizontalAlignment = Alignment.End) {
            val textColor =  TclTheme.colorScheme.tctStanderTextPrimary
            AndroidView(factory = {
                TextView(it).apply {
                    setTextIsSelectable(true)
                    setTextColor(textColor.toArgb())
                    text = originalText
                    textSize = 16f
                }
            }, modifier = Modifier
                .fillMaxWidth(),
                update = {
                    it.setTextColor(textColor.toArgb())
                })
        }
    }
}