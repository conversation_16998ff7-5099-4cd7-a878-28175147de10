package com.tcl.ai.note.utils

import androidx.compose.ui.geometry.Offset

fun Offset.coerceAtLeast(minimumValue: Float) =
    this.copy(
        x = x.coerceAtLeast(minimumValue),
        y = y.coerceAtLeast(minimumValue),
    )

fun Offset.coerceAtMost(maximumValue: Float) =
    this.copy(
        x = x.coerceAtMost(maximumValue),
        y = y.coerceAtMost(maximumValue),
    )

fun FloatArray.windowedPairIndex(action: (a: Float, b: Float, i: Int) -> Unit) {
    for (i in 0 until size - 1) {
        action(this[i], this[i + 1], i)
    }
}