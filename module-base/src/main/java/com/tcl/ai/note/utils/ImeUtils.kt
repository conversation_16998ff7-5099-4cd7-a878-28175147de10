package com.tcl.ai.note.utils

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.WindowInsetsAnimationController
import android.view.WindowInsetsController
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import androidx.core.view.OnApplyWindowInsetsListener
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsAnimationCompat
import androidx.core.view.WindowInsetsAnimationControllerCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import androidx.core.view.WindowInsetsControllerCompat.OnControllableInsetsChangedListener
import androidx.core.view.doOnAttach
import androidx.core.view.doOnDetach
import androidx.core.view.postDelayed
import com.tcl.ai.note.base.BaseActivity
import kotlinx.coroutines.Runnable
import org.w3c.dom.Text
import java.lang.ref.WeakReference
import java.util.LinkedList

/**
 * 获取软键盘高度
 */
fun EditText.getImeHeight(): Int {
    val insets = ViewCompat.getRootWindowInsets(this)
    val imeInsets = insets?.getInsets(WindowInsetsCompat.Type.ime())
    return imeInsets?.bottom ?: 0
}

/**
 * 获取软键盘高度
 */
fun EditText.isImeVisible(): Boolean {
    val insets = ViewCompat.getRootWindowInsets(this)
    val imeInsets = insets?.isVisible(WindowInsetsCompat.Type.ime())
    return imeInsets == true
}

fun EditText.showIme() {
    requestFocus()
    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.showSoftInput(this, InputMethodManager.SHOW_IMPLICIT)
}

fun EditText.hideIme() {
    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.hideSoftInputFromWindow(windowToken, 0)
}

fun EditText.doOnImeChanged(callback: (imeVisible: Boolean, imeHeight: Int) -> Unit) {
    val activityRef = WeakReference(context as Activity)
    activityRef.get()?.let { activity ->
        val windowInsetsControllerCompat = WindowInsetsControllerCompat(activity.window, this)
        val onControllableInsetsChangedListener =
            object : WindowInsetsControllerCompat.OnControllableInsetsChangedListener {
                override fun onControllableInsetsChanged(controller: WindowInsetsControllerCompat, typeMask: Int) {
                    val isKeyboard = (typeMask and WindowInsetsCompat.Type.ime()) != 0
                    Logger.v("EditText", "doOnImeChanged, isKeyboard: $isKeyboard, isImeVisible: ${isImeVisible()}")
                    if (!isKeyboard) {
                        return
                    }
                    callback.invoke(isImeVisible(), getImeHeight())
                }
            }
        callback.invoke(isImeVisible(), getImeHeight())
        doOnAttach {
            windowInsetsControllerCompat.addOnControllableInsetsChangedListener(onControllableInsetsChangedListener)
        }
        doOnDetach {
            windowInsetsControllerCompat.removeOnControllableInsetsChangedListener(onControllableInsetsChangedListener)
        }
    }
}

fun EditText.awaitShowIme(callback: (imeHeight: Int) -> Unit) {
    val activityRef = WeakReference(context as Activity)
    activityRef.get()?.let { activity ->
        val windowInsetsControllerCompat = WindowInsetsControllerCompat(activity.window, this)
        val onControllableInsetsChangedListener =
            object : WindowInsetsControllerCompat.OnControllableInsetsChangedListener {
                override fun onControllableInsetsChanged(controller: WindowInsetsControllerCompat, typeMask: Int) {
                    val isKeyboard = (typeMask and WindowInsetsCompat.Type.ime()) != 0
                    Logger.v("EditText", "awaitShowIme, isKeyboard: $isKeyboard, isImeVisible: ${isImeVisible()}")
                    if (!isKeyboard) {
                        return
                    }
                    if (isImeVisible()) {
                        // 等待键盘动画结束, 300ms时通过自测得出的。可能每个设备都不一样
                        doOnImeAnimEnd(this@awaitShowIme) {
                            callback.invoke(getImeHeight())
                        }
                        windowInsetsControllerCompat.removeOnControllableInsetsChangedListener(this)
                    }
                }
            }
        windowInsetsControllerCompat.addOnControllableInsetsChangedListener(onControllableInsetsChangedListener)
    }
}

fun EditText.awaitHideIme(callback: (imeHeight: Int) -> Unit) {
    val activityRef = WeakReference(context as Activity)
    val imeRealHeight = getImeHeight()
    activityRef.get()?.let { activity ->
        val windowInsetsControllerCompat = WindowInsetsControllerCompat(activity.window, this)
        val onControllableInsetsChangedListener =
            object : WindowInsetsControllerCompat.OnControllableInsetsChangedListener {
                override fun onControllableInsetsChanged(controller: WindowInsetsControllerCompat, typeMask: Int) {
                    val isKeyboard = (typeMask and WindowInsetsCompat.Type.ime()) != 0
                    Logger.v("EditText", "awaitHideIme, isKeyboard: $isKeyboard, isImeVisible: ${isImeVisible()}")
                    if (!isKeyboard) {
                        return
                    }
                    if (!isImeVisible()) {
                        callback.invoke(imeRealHeight)
                    }
                    windowInsetsControllerCompat.removeOnControllableInsetsChangedListener(this)
                }
            }
        windowInsetsControllerCompat.addOnControllableInsetsChangedListener(onControllableInsetsChangedListener)
    }
}

private val windowInsetsAnimationEndListeners = LinkedList<Runnable>()
fun OnControllableInsetsChangedListener.doOnImeAnimEnd(view: View, timeout: Long = 300L, onEnd: Runnable) {
    val activity = view.context as? Activity
    if (activity == null) {
        return
    }
    val activityRef = WeakReference(activity)
    windowInsetsAnimationEndListeners.remove(onEnd)
    windowInsetsAnimationEndListeners.add(onEnd)
    view.doOnDetach {
        windowInsetsAnimationEndListeners.remove(onEnd)
    }
    view.postDelayed(timeout) {
        windowInsetsAnimationEndListeners.remove(onEnd)
        onEnd.run()
    }
    ViewCompat.setWindowInsetsAnimationCallback(
        activity.window.decorView,
        object : WindowInsetsAnimationCompat.Callback(DISPATCH_MODE_CONTINUE_ON_SUBTREE) {
            override fun onProgress(
                windowInsetsCompat: WindowInsetsCompat,
                p1: List<WindowInsetsAnimationCompat?>
            ): WindowInsetsCompat {
                return windowInsetsCompat
            }

            override fun onEnd(animation: WindowInsetsAnimationCompat) {
                windowInsetsAnimationEndListeners.forEach { it.run() }
                windowInsetsAnimationEndListeners.clear()
                activityRef.get()?.let {
                    ViewCompat.setWindowInsetsAnimationCallback(it.window.decorView ,null)
                }
                Logger.v("EditText", "doOnImeAnimEnd, doEnd")
            }
        })
}