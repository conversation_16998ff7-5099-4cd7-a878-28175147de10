package com.tcl.ai.note.widget

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.invisibleSemantics
import com.tcl.ai.note.utils.judge
import com.tcl.ai.note.utils.stringRes
import com.tcl.ai.note.utils.tryToRequestFocus
import com.tcl.ai.note.widget.HoverProofIconButton
import com.tcl.ai.note.widget.IconThemeSwitcher

@SuppressLint("DesignSystem")
@Composable
fun SearchBox(
    text: String,
    darkTheme:Boolean = isSystemInDarkTheme(),
    onTextChange: (TextFieldValue) -> Unit,
    onClear: () -> Unit,
    onBack: () -> Unit,
    modifier: Modifier = Modifier
) {
    val focusRequester = remember { FocusRequester() }
    val context = LocalContext.current

    var searchTextFieldValue by remember { mutableStateOf(TextFieldValue(
        text = text,
        selection = TextRange(text.length)
    )) }

    /*LaunchedEffect(text){
        searchTextFieldValue = TextFieldValue(
            text = text,
            selection = TextRange(text.length)
        )
    }*/

    // 防止因为padding导致触摸事件穿透到下面的view
    Box(
        modifier = modifier
            .invisibleSemantics()
            .background(Color.Yellow)
            .fillMaxWidth()
            .height(64.dp)
            .pointerInput(Unit) { }
    )

    Box(
        modifier = modifier
            .invisibleSemantics()
            .background(TclTheme.colorScheme.tctGlobalBgColor)
            .height(64.dp)
            .padding(horizontal = 12.dp, vertical = 10.dp)
    ) {
        Box(
            modifier = Modifier
                .height(44.dp)
                .clip(RoundedCornerShape(32.dp))
                .background(colorResource(R.color.bg_search_box))
                .padding(end = 8.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxSize()
                    .height(44.dp)  // 保持固定高度
            ) {
                // 返回图标
                HoverProofIconButton(
                    modifier = Modifier.size(48.dp)
                        .clearAndSetSemantics {
                            contentDescription = context.getString(R.string.edit_top_menu_back_icon)
                            this.role = Role.Button
                        },
                    onClick = {
                        onBack()
                    }
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_menu_back),
                        contentDescription = "",
                        tint = Color.Gray,
                        modifier = Modifier
                            .size(16.dp)
                    )
                }


                // 输入框区域
                Box(
                    modifier = Modifier
                        .invisibleSemantics()
                        .weight(1f)
                        .fillMaxHeight()
                        .padding(end = 8.dp),
                    contentAlignment = Alignment.CenterStart  // 内容居中
                ) {
                    BasicTextField(
                        value = searchTextFieldValue,
                        onValueChange = {
                            searchTextFieldValue = it.copy(
                                text = it.text
                            )
                            onTextChange(it)
                        },
                        textStyle = TextStyle(
                            fontSize = 14.sp,
                            color = colorResource(R.color.text_title),
                            lineHeight = 24.sp  // 调整行高适配垂直居中
                        ),
                        cursorBrush = SolidColor(
                            colorResource(
                                darkTheme.judge(
                                    R.color.white,
                                    R.color.text_edit_color
                                )
                            )
                        ),
                        singleLine = true,
                        modifier = Modifier
                            .fillMaxWidth()
                            .semantics {
                                contentDescription =context.getString(R.string.search_notes)
                            }
                            .focusRequester(focusRequester)
                    )

                    // 占位符（保持垂直居中）
                    if (text.isEmpty()) {
                        Text(
                            text = stringResource(R.string.search_notes),
                            style = TextStyle(
                                fontSize = 14.sp,
                                color = colorResource(R.color.text_category_placeholder)
                            ),
                            modifier = Modifier.align(Alignment.CenterStart)
                                .invisibleSemantics()
                        )
                    }
                }

                // 清除按钮
                if (text.isNotEmpty()) {
                    Icon(
                        painter = painterResource(R.drawable.ic_close),
                        contentDescription = stringResource(R.string.delete),
                        tint = Color.Gray,
                        modifier = Modifier
                            .size(24.dp)
                            .clickable {
                                searchTextFieldValue = searchTextFieldValue.copy(text = "")
                                onClear()
                            }
                    )
                }
            }
        }
    }

    // 请求焦点
    DisposableEffect(Unit) {
        focusRequester.tryToRequestFocus()
        onDispose {}
    }
}