package com.tcl.ai.note.navigation

import androidx.navigation.NavController

/**
 * 导航工具类
 * 路由的统一管理
 */
object NavigationUtils {
    const val ROUTE_MAIN_SCREEN = "main_screen"
    const val ROUTE_EDIT_SCREEN = "edit_screen"
    const val ROUTE_EDIT_SCREEN_WITH_PARAMS = "$ROUTE_EDIT_SCREEN?noteId={noteId}&isPen={isPen}&isLandscape={isLandscape}"

    /**
     * 导航到编辑页面
     * @param navController 导航控制器
     * @param noteId 笔记ID，null表示新建笔记
     * @param isPen 是否启用画笔模式，默认false
     * @param isLandscape 是否横屏模式，默认false
     */
    fun navigateToEditScreen(
        navController: NavController,
        noteId: String? = null,
        isPen: Boolean = false,
        isLandscape: Boolean = false
    ) {
        val route = buildString {
            append(ROUTE_EDIT_SCREEN)

            val params = mutableListOf<String>()
            if (noteId != null) {
                params.add("noteId=$noteId")
            }
            if (isPen) {
                params.add("isPen=$isPen")
            }
            if (isLandscape) {
                params.add("isLandscape=$isLandscape")
            }

            if (params.isNotEmpty()) {
                append("?")
                append(params.joinToString("&"))
            }
        }

        navController.navigate(route)
    }


    /**
     * 导航到新建笔记页面
     * @param navController 导航控制器
     * @param isPen 是否启用画笔模式，默认false
     */
    fun navigateToNewNote(navController: NavController, isPen: Boolean = false) {
        navigateToEditScreen(navController, noteId = null, isPen = isPen)
    }

    /**
     * 导航到编辑页面（带画笔模式）
     * @param navController 导航控制器
     * @param noteId 笔记ID，null表示新建笔记
     * @param isPen 是否启用画笔模式
     */
    fun navigateToEditScreenWithPen(
        navController: NavController,
        noteId: String?,
        isPen: Boolean
    ) {
        navigateToEditScreen(navController, noteId = noteId, isPen = isPen)
    }
}


/**
 * 导航到指定笔记的编辑页面
 */
fun NavController.navigateToEdit(noteId: String) {
    NavigationUtils.navigateToEditScreen(this, noteId)
}

/**
 * 导航到新建笔记页面
 */
fun NavController.navigateToNewNote(isPen: Boolean = false) {
    NavigationUtils.navigateToNewNote(this, isPen)
}

/**
 * 导航到编辑页面（带画笔模式）
 */
fun NavController.navigateToEditWithPen(noteId: String?, isPen: Boolean) {
    NavigationUtils.navigateToEditScreenWithPen(this, noteId, isPen)
}
