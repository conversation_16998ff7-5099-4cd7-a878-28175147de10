package com.tcl.ai.note.utils

import android.app.ActivityManager
import androidx.compose.ui.focus.FocusRequester

private const val TAG = "FocusUtils"

/**
 * 安全地请求焦点，如果失败不会崩溃
 * 注意：如果在组件树完全构建之前调用，可能会失败
 */
fun FocusRequester.tryToRequestFocus() = try {
    if (!ActivityManager.isUserAMonkey()) {
        this.requestFocus()
    }
    true // 返回成功状态
} catch (ex: Exception) {
    Logger.e(TAG, "try to request focus fail: ${ex.message}")
    if (ex is IllegalArgumentException && ex.message?.contains("ActiveParent with no focused child") == true) {
        Logger.w(TAG, "Attempted to focus a parent with no focusable children")
    }
    false // 返回失败状态
}