package com.tcl.ai.note.widget

import android.content.Context.VIBRATOR_SERVICE
import android.os.Vibrator
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.AnimationVector1D
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.LinearOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.semantics.clearAndSetSemantics
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.isTraversalGroup
import androidx.compose.ui.semantics.role
import androidx.compose.ui.semantics.semantics
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.tcl.ai.note.base.R
import com.tcl.ai.note.theme.TclTheme
import com.tcl.ai.note.utils.Logger
import com.tcl.ai.note.utils.getNavBarHeight
import com.tcl.ai.note.utils.getNavigationBarHeight
import com.tcl.ai.note.utils.getStatusBarHeight
import com.tcl.ai.note.utils.isTablet
import com.tcl.ai.note.utils.isTabletLandscape
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 底部弹窗默认,初始高度
 * @param navBarHeight 导航栏高度
 * @param isTabletLandscape 是否平板横屏
 * @param customHeight 自定义初始高度，如果提供则使用此高度
 */
fun getOriginalHeight(navBarHeight: Dp, isTabletLandscape: Boolean, customHeight: Dp? = null): Dp {
    // 如果提供了自定义高度，则使用自定义高度
    if (customHeight != null) {
        val finalHeight = customHeight + navBarHeight
        Logger.d("BottomSheetDialog", "getOriginalHeight(custom):${finalHeight} navBarHeight:${navBarHeight}")
        return finalHeight
    }

    //初始高度设置为UI图总结和润色的初始高度
    var defaultHeight = 410.dp
    if (isTabletLandscape) {//平板横屏时的原始高度，测试建议不要那么高，和最大高度区别度大一点
        defaultHeight=380.dp
        if (navBarHeight == 0.dp) {//平板的导航栏虚拟导航栏高度，第一次获取会是0，第二次44dp
            defaultHeight = 400.dp
        }
    }

    defaultHeight+=navBarHeight
    Logger.d("BottomSheetDialog","getOriginalHeight:${defaultHeight} navBarHeight:${navBarHeight}")
    return defaultHeight
}

/**
 * 底部弹窗最大高度
 */
fun getMaxHeight(isTabletLandscape: Boolean,screenHeightDp: Dp, statusBarHeight: Dp, navBarHeight: Dp): Dp {
    //手机竖屏的最大高度
    var defaultMaxHeight: Dp
    var topHeight = 56.dp//手机的顶部高度
    //平板横屏的顶部高度
    val tabletTopHeight=50.dp + 45.dp + 25.dp
    if (isTablet) {//平板的顶部高度
        topHeight = tabletTopHeight
    }
    defaultMaxHeight = screenHeightDp - topHeight - statusBarHeight
    defaultMaxHeight+=navBarHeight
    defaultMaxHeight = maxOf(defaultMaxHeight, 368.dp)//最小值 分屏的时候 会放不下
    Logger.d("BottomSheetDialog","screenHeightDp:${screenHeightDp} defaultMaxHeight:${defaultMaxHeight}")
    return defaultMaxHeight
}

@Composable
fun BottomSheetDialog(
    modifier: Modifier = Modifier,
    visible: Boolean,
    customHeight: Dp? = null, // 自定义初始高度，如果提供则使用此高度
    cancelable: Boolean = true,//指定 BottomSheetDialog 是否可以被取消。如果设置为 true，用户可以通过按下返回键或者其他取消操作来关闭对话框；如果设置为 false，则对话框不能被取消。
    canceledOnTouchOutside: Boolean = true,//决定当用户点击对话框外部区域时，对话框是否会被取消。如果设置为 true，用户点击对话框外部区域时，对话框会关闭；如果设置为 false，点击外部区域不会产生任何效果
    onDismissRequest: () -> Unit,
    showIndicatorWhenFullScreen: Boolean = false,
    showFullScreenCallBack: (Boolean) -> Unit = {},
    backHandler: (suspend (suspend () -> Unit) -> Unit)? = null,
    extraTopPadding: Boolean = false,
    showAlphaAnimation: Boolean = true,
    isNavigationBarsPaddingNeeded: Boolean = false,//底部是否需要导航栏padding，录音转文本需求，不然是透明的
    isNeedAddNavigationBarsPadding: Boolean = true,//适配帮写导航栏高度
    onDismissCallback: (suspend () -> Unit) -> Unit,
    onHeightControllerReady: ((expandToFullScreen: () -> Unit) -> Unit)? = null,//提供一个回调函数，用于控制对话框高度，可以通过调用 expandToFullScreen 函数将对话框展开到全屏
    content: @Composable () -> Unit,
) {
    val backgroundAlpha = remember {
        Animatable(if (showAlphaAnimation) 0f else 1f)
    }
    var navigationBarHeight = getNavigationBarHeight()
    if (!isNeedAddNavigationBarsPadding) {
        navigationBarHeight=0.dp
    }
    val isTabletLandscape = isTabletLandscape
    val originalHeight = getOriginalHeight(navigationBarHeight, isTabletLandscape, customHeight)
    val statusBarHeight = getStatusBarHeight()
    val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp
    val maxHeight = getMaxHeight(isTabletLandscape,screenHeightDp, statusBarHeight,navigationBarHeight) // 设置最大高度 为UI图标注的高度
    val  isShowDialog = remember { mutableStateOf(visible) }
    if (isShowDialog.value) {
        BackHandler(visible, cancelable, backgroundAlpha, backHandler, onDismissRequest)
        Box(
            modifier = modifier
                .fillMaxSize()
//            .then(addNavigationBarPadding(isNavigationBarsPaddingNeeded))//加上底部导航栏的padding，才不会出现弹窗底部有蒙板的效果
//            .navigationBarsPadding()
//            .imePadding()
        ) {

            DialogBackgroundVisibility(
                visible,
                backgroundAlpha,
                isNavigationBarsPaddingNeeded,
                canceledOnTouchOutside,
                onDismissRequest,

                )
            InnerDialog(
                visible = visible,
                onDismissRequest = {
                    isShowDialog.value = false
                    onDismissRequest()
                },
                content = content,
                originalHeight = originalHeight,
                maxHeight = maxHeight,
                showFullScreenCallBack = showFullScreenCallBack,
                extraTopPadding = extraTopPadding,
                animationBeforeDropDown = {
                    backgroundAlpha.animateTo(
                        0f, animationSpec = tween(durationMillis = 50, easing = LinearEasing)
                    )
                },
                onDismissCallback = onDismissCallback,
                onHeightControllerReady = onHeightControllerReady
            )
        }
    }
}

@Composable
private fun BackHandler(
    visible: Boolean,
    cancelable: Boolean,
    backgroundAlpha: Animatable<Float, AnimationVector1D>,
    backHandler: (suspend (suspend () -> Unit) -> Unit)?,
    onDismissRequest: () -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    BackHandler(enabled = visible) {
        if (cancelable) {
            coroutineScope.launch {
                var beforeDismiss: suspend () -> Unit = {
                    backgroundAlpha.animateTo(
                        0f,
                        animationSpec = tween(durationMillis = 200, easing = LinearEasing)
                    )
                }
                if (backHandler != null) {
                    backHandler(beforeDismiss)
                } else {
                    beforeDismiss.invoke()
                    onDismissRequest()
                }
            }
        }
    }
}

/**
 * 背景层
 */
@Composable
private fun DialogBackgroundVisibility(
    visible: Boolean,
    backgroundAlpha: Animatable<Float, AnimationVector1D>,
    isNavigationBarsPaddingNeeded: Boolean,
    canceledOnTouchOutside: Boolean,
    onDismissRequest: () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    LaunchedEffect(key1 = null) {
        coroutineScope.launch {
            delay(300)
            if (backgroundAlpha.value == 1f) {
                return@launch
            }
            backgroundAlpha.animateTo(
                1f, animationSpec = tween(durationMillis = 100, easing = LinearEasing)
            )
        }
    }
    AnimatedVisibility(
        visible = visible,
        modifier = Modifier.clearAndSetSemantics {},
        enter = fadeIn(animationSpec = tween(durationMillis = 100, easing = LinearEasing)),
        exit = fadeOut(animationSpec = tween(durationMillis = 100, easing = LinearEasing))
    ) {
        Box(modifier = Modifier
            .fillMaxSize()
            .alpha(backgroundAlpha.value)
            .background(color = Color(color = 0x4D000000))
            .then(addNavigationBarPadding(isNavigationBarsPaddingNeeded))
            .clickableNoRipple {
                if (canceledOnTouchOutside) {
                    onDismissRequest()
                }
            })
    }
}

// 底部是否需要导航栏padding，录音转文本需求，不然是透明的
@Composable
private fun addNavigationBarPadding(isNavigationBarsPaddingNeeded: Boolean) =
    if (isNavigationBarsPaddingNeeded) {
        Modifier.navigationBarsPadding()
    } else {
        Modifier
    }


@Composable
private fun BoxScope.InnerDialog(
    visible: Boolean,
    onDismissRequest: () -> Unit,
    content: @Composable () -> Unit,
    originalHeight: Dp,
    maxHeight: Dp,
    showFullScreenCallBack: (Boolean) -> Unit,
    extraTopPadding: Boolean = false,
    animationBeforeDropDown: suspend () -> Unit = {},
    onDismissCallback: (suspend () -> Unit) -> Unit,
    onHeightControllerReady: ((expandToFullScreen: () -> Unit) -> Unit)? = null
) {
    val screenHeightDp = LocalConfiguration.current.screenHeightDp.dp
    val topPadding = 20.dp
    val corner = if (isTablet) 18.dp else 24.dp//横屏24dp 太大 UX说 统一改成18dp
    // 加上顶部padding高度 全屏才能刚好到Title  maxHeight += topPadding
    var innerDialogHeight = remember {
        Animatable(originalHeight.value)
    }
    var currentHeight = remember {
        mutableFloatStateOf(originalHeight.value)
    }
    val originalHeightPaddingTop = originalHeight.value + 20
    val originalHeightPaddingBottom = originalHeight.value - 20

    val fullHeightPaddingTop = maxHeight - topPadding

    val coroutineScope = rememberCoroutineScope()
    val context = LocalContext.current
    // 修改点1：新增状态变量，控制小横线显示
//    var shouldShowIndicator by remember(innerDialogHeight.value) {
//        mutableStateOf(innerDialogHeight.value != fullHeightPaddingTop.value)
//    }
    //指示器 handler 始终显示
    val shouldShowIndicator = true
    val rounderModifier = Modifier.clip(
        shape = RoundedCornerShape(
            topStart = corner, topEnd = corner
        )
    )
    val isFullScreen = remember {
        mutableStateOf(false)
    }

    // 封装的方法
    suspend fun dismissDialog() {
        // 等待高度动画完成
        innerDialogHeight.animateTo(0f)
        // 高度动画完成后立即调用onDismissRequest
        onDismissRequest()
//        // 在后台开始背景淡出动画
        coroutineScope.launch {
            animationBeforeDropDown()
        }
        Logger.d("BoxScope.InnerDialog", "dismissDialog-----")

    }

    onDismissCallback(::dismissDialog)

    // 定义展开到全屏的函数
    fun expandToFullScreen() {
        coroutineScope.launch {
            currentHeight.floatValue = maxHeight.value
            innerDialogHeight.animateTo(maxHeight.value)
            showFullScreenCallBack(true)
            isFullScreen.value = true
            Logger.d("BoxScope.InnerDialog", "expandToFullScreen: 展开到全屏")
        }
    }

    // 如果提供了高度控制器回调，则传递展开到全屏的函数
    onHeightControllerReady?.invoke(::expandToFullScreen)

    AnimatedVisibility(
        visible = visible,
        modifier = Modifier
            .then(
                if (!isTablet) {
                    //平板 横屏下 软键盘弹出的情况 弹窗会上下闪动
                    Modifier.imePadding()
                } else {
                    Modifier
                }
            )
            .align(alignment = Alignment.BottomCenter)
            // 修改点2：限制高度不超过最大高度
            .height(innerDialogHeight.value.dp.coerceAtMost(maxHeight))
            .semantics() {
//                contentDescription = description
//                isTraversalGroup = true
            },
        enter = slideInVertically(animationSpec = tween(
            durationMillis = 400,
            easing = LinearOutSlowInEasing
        ),
            initialOffsetY = {
                it
            }),
        exit = slideOutVertically(animationSpec = tween(
            durationMillis = 400,
            easing = LinearOutSlowInEasing
        ),
            targetOffsetY = {
                it
            })
    ) {
        Box(
            modifier = (if (shouldShowIndicator) rounderModifier else Modifier)
                .background(color = TclTheme.colorScheme.tctGlobalBgColor)
        ) {
            val bottomBgResId = if (isTablet&& !isSystemInDarkTheme()) R.drawable.bottom_dialog_bg_tablet else R.drawable.bottom_dialog_bg
            Image(
                painter = painterResource(id = bottomBgResId),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,  // 改用 FillBounds 确保图片填充整个空间
                alignment = Alignment.TopCenter // 确保顶部对齐，优先显示顶部内容
            )
            Box {
                val draggableState = rememberDraggableState(onDelta = {
                    val scale = context.resources.displayMetrics.density
                    var newHeight = innerDialogHeight.value - it * 1.2f / scale
                    // 修改点3：限制高度范围在0到最大高度之间
                    newHeight = newHeight.coerceIn(0f, maxHeight.value)
                    var shouldVibrate = false
                    //向上滑动：从下往上滑动有2次震动--小于1/2到1/2，1/2到大于1/2

                    if (it < 0) {
                        if (innerDialogHeight.value < originalHeightPaddingBottom && newHeight > originalHeightPaddingBottom) {

                            shouldVibrate = true
                        }
                        if (innerDialogHeight.value > originalHeightPaddingBottom && innerDialogHeight.value < originalHeightPaddingTop && newHeight > originalHeightPaddingTop) {

                            shouldVibrate = true
                        }
                    }
                    //向下滑动：从上往下滑动1次震动--大于1/2到1/2
                    if (it > 0 && innerDialogHeight.value > originalHeightPaddingTop && newHeight < originalHeightPaddingTop) {

                        shouldVibrate = true
                    }
                    Logger.d(
                        "BoxScope.InnerDialog",
                        "onDelta:${it}--${innerDialogHeight.value} --${newHeight}"
                    )
                    coroutineScope.launch {
                        innerDialogHeight.snapTo(newHeight)
                    }
                    if (shouldVibrate) {
                        //vibratorController.vibrate(75)
                    }

                    // 修改点4：根据新高度动态更新小横线显示状态
//                        shouldShowIndicator = newHeight > 0
                })/*
                * 这里如果是全屏模式，则补充相应高度，note里设定最高上限的情况下不需要
                * *//*                    AnimatedVisibility(visible = extraTopPadding && isFullScreen.value) {
                                        Spacer(modifier = Modifier.height(14.dp))
                                    }*/
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .draggable(
                            state = draggableState,
                            orientation = Orientation.Vertical,
                            enabled = true,
                            onDragStarted = {},
                            onDragStopped = {
                                coroutineScope.launch {
                                    // 新增条件：定义屏幕的三个区域
                                    val screenTopThird = screenHeightDp.value * 0.33f
                                    var screenMiddleThird = screenHeightDp.value * 0.66f
                                    // 定义半屏高度
                                    val halfScreenHeight = screenHeightDp.value * 0.5f
                                    // 根据设备类型调整全屏阈值
                                    if (isTablet) {
                                        screenMiddleThird = maxHeight.value * 0.66f
                                    }
                                    val targetHeight = when {
                                        // 全屏状态下的逻辑
                                        currentHeight.floatValue == maxHeight.value -> {
                                            Logger.d(
                                                "BoxScope.InnerDialog",
                                                "全屏状态下的逻辑 " + innerDialogHeight.value + "-- maxHeight:" + maxHeight.value
                                                        + "-- screenTopThird:" + screenTopThird + "-- screenMiddleThird:" + screenMiddleThird
                                            )
                                            when {
                                                // 下拉至屏幕2/3位置以上，保持全屏
                                                innerDialogHeight.value > screenMiddleThird -> maxHeight.value
                                                // 下拉至屏幕1/3到2/3之间，变为半屏
                                                innerDialogHeight.value > screenTopThird -> originalHeight.value
                                                // 下拉至屏幕1/3以下，关闭弹窗
                                                else -> 0f
                                            }
                                        }
                                        // 初始状态下的逻辑
                                        else -> when {
                                            // 拖拽超过半屏则全屏
                                            innerDialogHeight.value >= halfScreenHeight -> maxHeight.value
                                            // 低于半屏则关闭
                                            else -> 0f
                                        }
                                    }
                                    Logger.d(
                                        "BottomSheetDialog",
                                        "targetHeight -----  ***  $targetHeight   originalHeight.value   ${originalHeight.value}  innerDialogHeight.value : ${innerDialogHeight.value}"
                                    )
                                    currentHeight.floatValue = targetHeight
                                    innerDialogHeight.animateTo(targetHeight)

                                    if (targetHeight > originalHeight.value) {
                                        showFullScreenCallBack(true)
                                        isFullScreen.value = true
                                    } else if (targetHeight == originalHeight.value) {
                                        showFullScreenCallBack(false)
                                        isFullScreen.value = false
                                    } else if (targetHeight == 0f) {
                                        animationBeforeDropDown()
                                        onDismissRequest()
                                    }
                                    // 动画完成后更新小横线显示状态
//                                    shouldShowIndicator = targetHeight > 0
                                }
                            })
                        .padding(top = 12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    DragHandleBar(shouldShowIndicator)
                    Spacer(modifier = Modifier.height(20.dp).fillMaxWidth())
                }
                //滑动条下边的高度 手机20dp 平板1dp 所以AI弹窗标题不要再设置padding
                val bottomBarPadding = if (isTablet) 1.dp else 20.dp
                Box(modifier = Modifier.padding(top = bottomBarPadding+4.dp+12.dp)) {
                    content()
                }
            }
        }
    }
}
// 根据当前高度判断是否显示小横线
@Composable
private fun DragHandleBar(shouldShowIndicator: Boolean) {
    val width = if (isTablet) 72.dp else 48.dp
    if (shouldShowIndicator) {
        HorizontalDivider(
            thickness = 4.dp,
            color = colorResource(id = R.color.bottom_sheet_dialog_drag_bar_color),
            modifier = Modifier
                .clip(RoundedCornerShape(10.dp))
                .width(width)
        )
    } else {
        Spacer(modifier = Modifier.height(4.dp))
    }
}
