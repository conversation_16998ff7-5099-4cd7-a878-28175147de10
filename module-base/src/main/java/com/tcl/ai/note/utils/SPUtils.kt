package com.tcl.ai.note.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.collection.SimpleArrayMap
import com.tcl.ai.note.GlobalContext

/**
 * Utility class for SharedPreferences, which caches each Editor object
 * for each sp file in a map.
 *
 * key-value pairs:
 * key: SharedPreferences 文件名称
 * value: 与key所代表的 SharedPreferences文件所对应的 SharedPreferences.Editor的对象
 */
object SPUtils {
    private const val TAG = "SPUtils"
    private const val SP_NAME = "Note_SP"

    /**
     * key: SharedPreferences 文件名称
     * value: 与key所代表的 SharedPreferences文件所对应的 SharedPreferences.Editor的对象
     */
    private val sCachedSpNameEditorMap = SimpleArrayMap<String, SharedPreferences.Editor>()

    @JvmStatic
    @Synchronized
    fun getString(
        context: Context? = GlobalContext.instance,
        spFileName: String = SP_NAME,
        key: String,
        defValue: String?
    ): String? {
        Logger.v(TAG, "getString, sp file name: $spFileName, key: $key, def value: $defValue")
        if (context == null) {
            return defValue
        }
        var result = defValue
        try {
            result = getSP(context, spFileName).getString(key, defValue)
        } catch (e: Exception) {
            Logger.d(TAG, " getString e = " + e.message)
        }
        return result
    }

    @JvmStatic
    @Synchronized
    fun getInt(
        context: Context = GlobalContext.instance,
        spFileName: String = SP_NAME,
        key: String,
        defValue: Int
    ): Int {
        var result = defValue
        try {
            result = getSP(context, spFileName).getInt(key, defValue)
        } catch (e: Exception) {
            Logger.d(TAG, " getInt e = " + e.message)
        }
        return result
    }

    @JvmStatic
    @Synchronized
    fun getBoolean(
        context: Context? = GlobalContext.instance,
        spFileName: String = SP_NAME,
        key: String,
        defValue: Boolean
    ): Boolean {
        if (context == null) {
            return defValue
        }
        var result = defValue
        try {
            result = getSP(context, spFileName).getBoolean(key, defValue)
        } catch (e: Exception) {
            Logger.d(TAG, "getBoolean e = " + e.message)
        }
        return result
    }

    /**
     * Save the given key-value data into the give sp file whose name is spFileName.
     *
     * 建议放在子线程里调用以防止ANR
     *
     * @param spFileName
     * @param key
     * @param value
     */
    @JvmStatic
    @Synchronized
    fun setSync(
        context: Context = GlobalContext.instance,
        spFileName: String = SP_NAME,
        key: String,
        value: Any
    ): Boolean {
        return try {
            Logger.v(TAG, "setSync, sp file name: $spFileName, key: $key, value: $value")
            val editor = getEditor(context, spFileName)
            when (value) {
                is String -> editor.putString(key, value)
                is Int -> editor.putInt(key, value)
                is Boolean -> editor.putBoolean(key, value)
                is Float -> editor.putFloat(key, value)
                is Long -> editor.putLong(key, value)
            }
            editor.commit()
        } catch (e: Exception) {
            Logger.d(TAG, "setSync e = " + e.message)
            false
        }
    }

    private fun getSP(
        context: Context,
        spFileName: String
    ): SharedPreferences {
        return context
            .getSharedPreferences(spFileName, Context.MODE_PRIVATE)
    }

    private fun getEditor(context: Context, spFileName: String): SharedPreferences.Editor {
        val editor: SharedPreferences.Editor
        // the lock will take effect for this sp file only, that is, the lock will
        // make threads that handle the same sp file to hold or wait for it, but
        // takes no effects for threads that handle other sp files.
        val cachedEditor = getFromCache(spFileName)
        if (cachedEditor != null) {
            editor = cachedEditor
        } else {
            editor = getSP(context, spFileName).edit()
            putToCache(spFileName, editor)
        }
        return editor
    }

    private fun putToCache(spFileName: String, editor: SharedPreferences.Editor) {
        sCachedSpNameEditorMap.put(spFileName, editor)
    }

    private fun getFromCache(spFileName: String): SharedPreferences.Editor? {
        return if (sCachedSpNameEditorMap.containsKey(spFileName)) {
            sCachedSpNameEditorMap[spFileName]
        } else null
    }
}
