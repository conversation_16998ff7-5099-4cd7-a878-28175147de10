<?xml version="1.0" encoding="utf-8" standalone="no"?>
<resources xmlns:xliff="urn:oasis:names:tc:xliff:document:1.2">
    <string name="app_name">"Notes"</string>
    <string name="audio_title">"Audio"</string>
    <string name="image_title">"Image"</string>
    <string name="image_audio_title">"Audio &amp; image"</string>
    <string name="doodle_title">"Doodle"</string>
    <string name="handwritten_title">"Handwritten note"</string>
    <string name="audio_doodle_title">"Audio &amp; doodle"</string>
    <string name="audio_image_title">"Audio &amp; image"</string>

    <string name="database_preset_category_all">"All notes"</string>
    <string name="database_preset_category_none">"Uncategorised"</string>
    <string name="database_preset_shopping_list">"Shopping list"</string>
    <string name="database_preset_category_travel">"Travel"</string>
    <string name="database_preset_category_work">"Work"</string>
    <string name="database_preset_category_learning">"Learning"</string>
    <string name="database_preset_category_education">"Education"</string>
    <string name="dialog_category_name_title">"New category"</string>
    <string name="dialog_category_rename_title">"Rename category"</string>

    <string name="search_notes">"Search for notes"</string>
    <string name="search_image">"Search for images"</string>
    <string name="category_name">"Category name"</string>
    <string name="category_add">"Create"</string>
    <string name="category_cancel">"Cancel"</string>
    <string name="category_delete">"Delete"</string>
    <string name="category_add_fail">"The maximum number of folders has been reached"</string>
    <string name="category_add_success">"Category created"</string>
    <string name="category_name_exists">"Category name already exists"</string>
    <string name="category_name_maximum_character">"Category name cannot exceed 50 characters"</string>
    <string name="category_moveto_already">"Can\'t move to the current category"</string>
    <string name="category_moveto_success">"Move to "%s"."</string>
    <string name="category_moveto_success_new">"The selected %s notes have been moved to %s"</string>
    <string name="category_rename">"Renamed to %s"</string>
    <string name="not_content_share">"No content to share"</string>
    <string name="not_content_text_function_unavailable">"Function is unavailable without text content"</string>

    <string name="sharing_pdf_failure">"Couldn\'t generate PDF file"</string>
    <string name="show_content_share">"Share"</string>
    <string name="image_loading_failed">"Couldn\'t load image"</string>
    <string name="failed_to_create_image">"Couldn\'t create image"</string>

    <string name="choose_category">"Choose a category"</string>
    <string name="select_items">"Select items"</string>
    <string name="sort">"Sort"</string>
    <string name="move_to">"Move to"</string>
    <string name="rename_category">"Rename"</string>
    <string name="delete_category">"Delete"</string>
    <string name="dialog_category_delete_title">"Delete the category?"</string>
    <string name="dialog_category_delete_content">"Also delete all notes in this category"</string>
    <string name="create_date">"Creation date"</string>
    <string name="modify_date">"Modification date"</string>


    <string name="content_prompt">"AI-generated content"</string>
    <string name="writing_stop">"Stop"</string>
    <string name="writing_copy">"Copy"</string>
    <string name="stopped_answer">"AI generation has stopped"</string>
    <string name="network_error">"Network error. Check the connection."</string>
    <string name="network_response_failed">"Network error. Try again."</string>

    <string name="stroke_to_text_title">"Handwriting to Text"</string>
    <string name="downloading_tip">"Downloading language pack…"</string>
    <string name="net_download_model_tip">"Connect to the network to download the language pack…"</string>
    <string name="quick_function_rewrite">"AI refinement"</string>
    <string name="ai_function_summary">"AI summary"</string>

    <string name="text_polishing">"Improve writing"</string>
    <string name="fix_pelling">"Fix spelling &amp; grammar"</string>
    <string name="make_shorter">"Make shorter"</string>
    <string name="make_longer">"Make longer"</string>
    <string name="professional">"Professional"</string>
    <string name="casual">"Casual"</string>
    <string name="dialog_title_delete_one_items">"Permanently delete this note?"</string>
    <string name="dialog_title_delete_multiple_items">"Permanently delete %s notes?"</string>
    <string name="title">"Title"</string>
    <string name="enter_your_text">"Enter your text"</string>
    <string name="edit_note_saved">"Note is saved"</string>
    <string name="action_share_image_file">"Image"</string>
    <string name="action_share_text_file">"Text"</string>
    <string name="action_share_pdf_file">"PDF file"</string>
    <string name="cancel">"Cancel"</string>
    <string name="confirm">"Confirm"</string>
    <string name="dialog_positive_text_ok">"OK"</string>
    <string name="tips_content_too_short">"Features are unavailable due to note text being under 20 characters."</string>
    <string name="tips_content_too_long">"Can\'t process text over 10,000 characters"</string>
    <string name="dialog_title_delete_photo">"Delete the image?"</string>
    <string name="ai_generate_failed">"Couldn\'t generate"</string>
    <string name="ai_summary">"AI summary"</string>
    <string name="ai_writing_assistant">"AI writing assistant"</string>
    <string name="ai_refinement">"AI refinement"</string>
    <string name="copied">"Copied"</string>
    <string name="replaced">"Replaced"</string>
    <string name="replace">"Replace"</string>

    <string name="select_photos">"Select photos"</string>
    <string name="take_photos">"Take photos"</string>
    <!--Audio begin-->
    <string name="edit_audio_record_shortest_time">"Can\'t save recording \nRecording too short"</string>
    <string name="recording_fail">"Couldn\'t record"</string>
    <string name="retry">"Retry"</string>
    <string name="tips_notification_generating">"Summary is being generated"</string>
    <string name="tips_notification_generated">"Summary has been generated"</string>
    <string name="invalid_duration_for_audio_to_text">"Unable to transcribe. Audio duration must be 2 minutes to 2 hours."</string>
    <string name="menu_delete_recording">"Delete"</string>
    <string name="summary_loading">"Loading..."</string>
    <string name="text_try_again">"Try again"</string>
    <string name="text_audio_to_text_failed">"Couldn\'t convert to text"</string>
    <string name="text_max_transcribe_duration">"Voice memos can be transcribed for up to 2 hours"</string>
    <string name="text_reach_2_hour_message">"Approaching 2-hour limit. Exceeding this will stop transcription. Save and create a new one?"</string>
    <string name="btn_save_and_create_new">"Save and create new (%s)s"</string>
    <string name="btn_continue">"Continue"</string>
    <string name="btn_finish">"Finish"</string>
    <string name="dialog_grant_microphone_permission">"To record audio, grant the permission of microphone in App info &gt; Permissions."</string>
    <string name="dialog_grant_camera_permission">"To take photos, grant the permission of camera in App info &gt; Permissions."</string>
    <string name="dialog_grant_storage_permission">"To select images, grant the permission of storage in App info &gt; Permissions."</string>
    <string name="btn_go_settings">"Go to settings"</string>
    <string name="btn_cancel">"Cancel"</string>
    <string name="title_audio_transcribe">"Transcribe"</string>
    <string name="message_transcription_limit">"Insufficient transcription time. Upgrade to VIP for more features."</string>
    <string name="btn_learn_more">"Learn more"</string>
    <string name="text_speaker">"Speaker %s"</string>
    <string name="message_generate_failed_since_sensitive_words">"Unable to generate. Inappropriate language detected. Revise and resubmit."</string>
    <string name="toast_in_generating">"Transcription in progress. Try again later."</string>
    <string name="message_audio_too_long_to_generate">"Can\'t convert recording: exceeds 2-hour limit."</string>
    <string name="message_delete_audio">"Delete this recording?"</string>
    <string name="message_note_is_recording">"Notes is recording audio"</string>
    <string name="note_recording_channel_name">"Recording notification"</string>
    <!--Audio end-->

    <string name="help_writing_prompt">"Hello, I can help you write:"</string>
    <string name="help_writing_email">"Email"</string>
    <string name="help_writing_work_summary">"Work summary"</string>
    <string name="help_writing_event_program">"Event plan"</string>
    <string name="help_writing_invitation">"Invitation"</string>
    <string name="help_writing_outLine">"Outline"</string>
    <string name="help_writing_meeting_notice">"Meeting notification"</string>
    <string name="help_writing_shorter">"Shorter"</string>
    <string name="help_writing_longer">"Longer"</string>
    <string name="help_writing_default_prompt">"Help me write a letter about…"</string>
    <string name="help_writing_work_summary_description">"Write a summary of key achievements for this quarter and objectives for the next quarter."</string>
    <string name="help_writing_event_program_description">"Write a proposal for our annual gala."</string>
    <string name="help_writing_invitation_description">"Help me write an invitation for the launch event on the 25th and emphasise its significance."</string>
    <string name="help_writing_outLine_description">"Help me create an outline for a keynote speech, starting with a problem statement."</string>
    <string name="help_writing_meeting_notice_description">"Draft a brief meeting notice for the team to discuss Q4 targets this Friday."</string>
    <string name="help_writing_tag_prompt">"Enter your topic..."</string>
    <string name="help_writing_continue_ask">"Go ahead and ask questions"</string>
    <string name="exceed_limit">"The allowed character limit has been exceeded"</string>
    <string name="ai_send">"Send"</string>
    <string name="ai_stop">"Stop"</string>
    <string name="ai_error_code">"Sorry, the current input cannot be processed, please try sending something else and try again"</string>
    <string name="operate_text_override">"Do not support processing more than 10000 characters, please choose again"</string>
    <string name="login_failed_token_expired">"Login expired. Login again."</string>
    <string name="more">"more"</string>
    <string name="ai_writing_loading">"AI is writing..."</string>
    <string name="help_writing_title">"Writing Assist"</string>
    <string name="close_writing_dialog">"Close the writing dialog"</string>
    <string name="close">"Close"</string>


    <!--新增-->
    <string name="enter">"Enter"</string>
    <string name="show_content_quick_share">"Quick share"</string>
    <string name="edit">"Edit"</string>
    <string name="no_results_found">"No results found"</string>
    <string name="no_recommended_sharing_recipients_string">"No recommended sharing recipients"</string>
    <string name="bluetooth">"Bluetooth"</string>
    <string name="search_for_images">"Search for images"</string>
    <string name="messages">"Messages"</string>
    <string name="print">"Print"</string>
    <string name="file_geek">"File Geek"</string>
    <string name="download">"Download"</string>
    <string name="save">"SAVE"</string>
    <string name="audio_to_text">"Audio to Text"</string>
    <string name="delete">"Delete"</string>
    <string name="notes">"Notes"</string>
    <string name="handwriting">"Handwriting"</string>
    <string name="done">"Done"</string>
    <string name="no_notes">"No notes"</string>
    <string name="could_not_convert_to_text">"Couldn\'t convert to text"</string>
    <string name="generating">"Generating..."</string>
    <string name="could_not_generate">"Couldn\'t generate"</string>
    <string name="log_in">"Log in to use this feature"</string>
    <string name="no_connection">"No connection. Features can\'t be accessed."</string>
    <string name="settings">"Settings"</string>
    <string name="text_refinement">"Text refinement"</string>
    <string name="grammar_correction">"Grammar correction"</string>
    <string name="more_concise">"More concise"</string>
    <string name="more_detailed">"More detailed"</string>
    <string name="more_professional">"More professional"</string>
    <string name="more_casual">"More casual"</string>
    <string name="grammar_correction2">"Grammar correction"</string>
    <string name="here_for_ai_writing_assistance">"Here for AI writing assistance:"</string>
    <string name="enter_your_topic">"Enter your topic..."</string>
    <string name="ai_is_working_on_it">"AI is working on it..."</string>
    <string name="benefits_comparison">"Benefits comparison"</string>
    <string name="select_and_drag">"Select and drag"</string>
    <string name="voice_memos">"Voice memos"</string>
    <string name="unlimited">"Unlimited"</string>
    <string name="upgrade_to_a_membership">"Upgrade to a membership"</string>
    <string name="transcription_quota_exceeded">"Unable to transcribe. Transcription quota exceeded."</string>
    <string name="exceeds_time_limit">"Can\'t convert recording: exceeds 2-hour limit."</string>
    <string name="converting">"Converting"</string>
    <string name="could_not_convert">"Couldn\'t convert"</string>
    <string name="binding_failed">"Couldn\'t bind"</string>
    <string name="ai_message_limit">"Insufficient available time. Upgrade to VIP for more features."</string>
    <string name="clear_all">"Erase all handwriting"</string>
    <string name="clean_all">"Clean all"</string>
    <string name="click_to_create_note">"Touch + button to create your first note"</string>
    <string name="inserted">"Inserted"</string>
    <string name="insert">"Insert"</string>

    <string name="list_item_is_a_note">"This is a note"</string>
    <string name="item_top_check_all">"Select all"</string>
    <string name="list_top_delete">"Delete"</string>
    <string name="menu_add_to_do">"Add"</string>
    <string name="menu_set_format">"Set format"</string>
    <string name="menu_brush">"Brush"</string>
    <string name="menu_skin">"Theme"</string>
    <string name="menu_image">"Image"</string>
    <string name="menu_start_recording">"Start Recording"</string>
    <string name="menu_stop_recording">"Stop Recording"</string>
    <string name="edit_top_menu_back_icon">"Back"</string>
    <string name="edit_top_menu_undo">"Undo"</string>
    <string name="edit_top_menu_redo">"Redo"</string>
    <string name="edit_top_menu_save_note">"Save note"</string>
    <string name="edit_top_menu_category">"Category"</string>
    <string name="edit_top_menu_share">"Share"</string>
    <string name="edit_bottom_menu_color_select">"Select colour"</string>
    <string name="edit_bottom_menu_fold">"Fold"</string>
    <string name="edit_bottom_menu_color_range">"Colour 1 to N"</string>
    <string name="edit_bottom_menu_skin_def">"Default"</string>
    <string name="edit_bottom_menu_skin_book">"Line"</string>
    <string name="edit_bottom_menu_skin_grid">"Grid"</string>

    <string name="edit_bottom_menu_skin_white">"White"</string>
    <string name="edit_bottom_menu_skin_grey">"Grey"</string>
    <string name="edit_bottom_menu_skin_green">"Green"</string>
    <string name="edit_bottom_menu_skin_pink">"Pink"</string>
    <string name="edit_bottom_menu_skin_yellow">"Yellow"</string>
    <string name="edit_bottom_menu_skin_purple">"Purple"</string>
    <string name="pause_play">"Stop playing"</string>
    <string name="start_play">"Start playing"</string>
    <string name="checked_status">"Selected"</string>
    <string name="unchecked_status">"Unselected"</string>
    <string name="sort_by_create_date_selected">"Currently sorted by creation date"</string>
    <string name="sort_by_modify_date_selected">"Currently sorted by modification date"</string>
    <string name="search_icon">"Search"</string>
    <string name="view_type_staggered_grid">"Staggered grid"</string>
    <string name="view_type_list">"List"</string>
    <string name="more_actions">"More actions"</string>
    <string name="stop_recording">"Stop recording"</string>
    <string name="audio_pause">"Pause"</string>
    <string name="audio_play">"Play"</string>
    <string name="audio_more">"More"</string>
    <string name="bulleted_list">"Bulleted list"</string>
    <string name="numbered_list">"Numbered list"</string>
    <string name="font_bold">"Bold"</string>
    <string name="font_underline">"Underline"</string>
    <string name="font_italic">"Italic"</string>
    <string name="recognition_failed">"Not recognised"</string>
    <string name="creating_in_progress">"Creating…"</string>


    <!--笔刷选择模块-->
    <string name="pen_selector_fountain_pen">"Fountain pen"</string>
    <string name="pen_selector_marker_pen">"Marker pen"</string>
    <string name="pen_selector_ball_pen">"Ball pen"</string>
    <string name="pen_selector_pencil">"Pencil"</string>

    <!--字迹工具模块-->
    <string name="edit_bottom_tool_beautify">"Handwriting beautification"</string>
    <string name="edit_bottom_tool_brush">"Select the brush tool"</string>
    <string name="edit_bottom_tool_eraser">"Eraser"</string>
    <string name="edit_bottom_tool_convert_text">"Text conversion"</string>
    <string name="edit_bottom_tool_expand">"Expand"</string>

    <!--新建分类-->
    <string name="dialog_category_expand_colour_swatches">"Expand classified colour swatches"</string>
    <string name="dialog_category_close_colour_swatches">"Close classified colour swatches"</string>
    <string name="dialog_category_colour_yellow">"Yellow"</string>
    <string name="dialog_category_colour_orange">"Orange"</string>
    <string name="dialog_category_colour_pink">"Pink"</string>
    <string name="dialog_category_colour_purple">"Purple"</string>
    <string name="dialog_category_colour_azure">"Azure"</string>
    <string name="dialog_category_colour_bluish">"Bluish"</string>
    <string name="dialog_category_colour_green">"Green"</string>
    <string name="dialog_category_colour_gray">"Gray"</string>

    <!--底部菜单新增-->
    <string name="edit_bottom_menu_keyboard">"Keyboard"</string>
    <string name="edit_bottom_menu_handwriting">"Handwriting"</string>
    <string name="edit_bottom_menu_pen">"Pen"</string>

    <!--滑块-->
    <string name="color_slider_status">"%s colour slider. Slide left or right to adjust."</string>
    <string name="brush_slider_status">"%s brush slider. Slide left or right to adjust."</string>
    <string name="eraser_slider_status">"%s eraser slider. Slide left or right to adjust."</string>

    <!--Ai润色-->
    <string name="edit_bottom_menu_ai_assistant">"AI assistant"</string>
    <string name="accessibility_scroll_to_see_more">"Slide down to see more"</string>

    <!--首页新增便签-->
    <string name="btn_add_sticky_note">"Add a sticky note"</string>

    <!---画笔颜色-->
    <string name="brush_color_black">"Brush colour, black"</string>
    <string name="brush_color_white">"Brush colour, white"</string>
    <string name="brush_color_red">"Brush colour, red"</string>
    <string name="brush_color_orange">"Brush colour, orange"</string>
    <string name="brush_color_yellow">"Brush colour, yellow"</string>
    <string name="brush_color_green">"Brush colour, green"</string>
    <string name="brush_color_blue">"Brush colour, blue"</string>
    <string name="btn_close_hand_drawn_mode">"Disable hand-drawn mode"</string>
    <string name="btn_open_hand_drawn_mode">"Enable hand-drawn mode"</string>

    <string name="max_content_length">"Up to 20,000 words can be entered"</string>
    <string name="max_image_limit">"Insert up to 50 images"</string>


    <!--控件类型播报-->
    <string name="dialog_type_semantics">"Popup window"</string>
    <string name="button_type_semantics">"Button"</string>
    <string name="dialog_semantics_description">"%1$s: %2$s. Actions: %3$s; %4$s."</string>

    <string name="preview_note">"Note preview window. Double tap to enter."</string>
    <string name="draw_panel">"Drawing board. Swipe with two fingers to draw."</string>
    <string name="dialog_open_ai_service_title">"The %1$s feature requires TCL AI service. Enable the service in App info."</string>

    <string name="saving">"Saving..."</string>
    <string name="no_more_content">"You\'ve reached the bottom"</string>

    <string name="image_large">"Large"</string>
    <string name="image_original">"Original"</string>
    <string name="image_small">"Small"</string>

    <string name="edit_menu_lasso">"Lasso"</string>
    <string name="edit_menu_ruler">"Ruler"</string>
    <string name="edit_menu_handwriting_to_text">"Handwriting to Text"</string>
    <string name="edit_menu_beautify">"Beautify"</string>

    <string name="color_panle_colorpicker">Color Picker</string>
    <string name="color_alpha_slider_status">"%s colour transparency slider. Slide left or right to adjust."</string>

    <string name="color_swatch_white">"White"</string>
    <string name="color_swatch_light_gray">"Light grey"</string>
    <string name="color_swatch_dark_gray">"Dark grey"</string>
    <string name="color_swatch_black">"Black"</string>
    <string name="color_swatch_dark_orange_red">"Dark orange red"</string>
    <string name="color_swatch_orange_red">"Orange red"</string>
    <string name="color_swatch_light_orange_red">"Light orange red"</string>
    <string name="color_swatch_dark_blue_violet">"Dark blue violet"</string>
    <string name="color_swatch_blue_violet">"Blue violet"</string>
    <string name="color_swatch_light_blue_violet">"Light blue violet"</string>
    <string name="color_swatch_dark_red">"Dark red"</string>
    <string name="color_swatch_red">"Red"</string>
    <string name="color_swatch_light_red">"Light red"</string>
    <string name="color_swatch_dark_orange">"Dark orange"</string>
    <string name="color_swatch_orange">Orange</string>
    <string name="color_swatch_light_orange">Light Orange</string>
    <string name="color_swatch_dark_yellow_green">"Dark yellow green"</string>
    <string name="color_swatch_yellow_green">"Yellow green"</string>
    <string name="color_swatch_light_yellow_green">"Light yellow green"</string>
    <string name="color_swatch_dark_yellow">"Dark yellow"</string>
    <string name="color_swatch_yellow">"Yellow"</string>
    <string name="color_swatch_light_yellow">"Light yellow"</string>
    <string name="color_swatch_dark_green">"Dark green"</string>
    <string name="color_swatch_green">"Green"</string>
    <string name="color_swatch_light_green">"Light green"</string>
    <string name="color_swatch_dark_cyan">"Dark cyan"</string>
    <string name="color_swatch_cyan">"Cyan"</string>
    <string name="color_swatch_light_cyan">"Light cyan"</string>
    <string name="color_swatch_dark_blue">"Dark blue"</string>
    <string name="color_swatch_blue">"Blue"</string>
    <string name="color_swatch_light_blue">"Light blue"</string>
    <string name="color_swatch_dark_purple">"Dark purple"</string>
    <string name="color_swatch_purple">"Purple"</string>
    <string name="color_swatch_light_purple">"Light purple"</string>
    <string name="color_swatch_dark_magenta">"Dark magenta"</string>
    <string name="color_swatch_magenta">"Magenta"</string>
    <string name="color_swatch_light_magenta">"Light magenta"</string>
    <string name="color_swatch_collection">"Personal collection"</string>
    <!--旅行日记 begin-->
    <string name="title_tab_note">"Note"</string>
    <string name="title_tab_journal">"Memo craft"</string>
    <!--旅行日记 end-->

    <string name="selected_audios">"%d selected"</string>
    <string name="delete_these_audios">"Delete these recordings?"</string>
    <string name="text_same_audio_file">"An audio file with the same name already exists"</string>
    <string name="audio_name_format">"Audio %03d"</string>

    <!-- Accessibility descriptions for audio recording features -->
    <string name="accessibility_select_all_recordings">"Select all recordings"</string>
    <string name="accessibility_edit_recordings">"Edit recordings"</string>
    <string name="accessibility_recording_selected">"Recording selected"</string>
    <string name="accessibility_recording_not_selected">"Recording not selected"</string>
    <string name="accessibility_rename_recording">"Rename recording"</string>
    <string name="accessibility_start_new_recording">"Start new recording"</string>
    <string name="accessibility_show_recording_list">"Show recording list"</string>
    <string name="accessibility_stop_recording">"Stop recording"</string>
    <string name="accessibility_show_audio_panel">"Show audio panel"</string>
    <string name="accessibility_hide_audio_panel">"Hide audio panel"</string>
    <string name="accessibility_audio_seek_bar">"Audio playback progress"</string>
    <string name="accessibility_recording_duration">"Recording duration: %s"</string>
    <string name="accessibility_recording_block">"Recording in progress. Touch to stop."</string>

    <string name="stylus_only_enable">"Stylus-only mode is enabled"</string>
    <string name="handwriting_limit_message">"The Handwriting limit has been reached"</string>

    <string name="pen_popup_brush_tool">"Brush tool"</string>

    <string name="exceeds_maximum_characters">"Exceeds the maximum of 50 characters"</string>
    <string name="selected_count">"%d selected"</string>
    <string name="areyou_sure_delete">"Are you sure to delete?"</string>
    <string name="move">"Move"</string>
    <string name="invalid_duration_for_audio_to_text_new">"Unable to transcribe. Audio duration must be 15 seconds to 2 hours."</string>

    <string name="swatches_popup_tool">"Swatches"</string>

    <string name="more_colors">More colors</string>

    <string name="navigation_import_button">"Import"</string>
    <string name="navigation_more_button">"More"</string>
    <string name="import_menu_record_audio">"Record audio"</string>
    <string name="import_menu_choose_photo">"Select images"</string>
    <string name="import_menu_take_photo">"Take photos"</string>
    <string name="more_menu_page_settings">"Page settings"</string>
    <string name="more_menu_move_note">"Move note"</string>
    <string name="more_menu_share_note">"Share note"</string>

    <string name="rich_text_todo">"Todo"</string>
    <string name="rich_text_bold">"Bold"</string>
    <string name="rich_text_italic">"Italic"</string>
    <string name="rich_text_underline">"Underline"</string>
    <string name="rich_text_strikethrough">"Strikethrough"</string>
    <string name="rich_text_text_color">"Text colour"</string>
    <string name="rich_text_text_bg_color">"Text background colour"</string>
    <string name="rich_text_font_size">"Font size"</string>
    <string name="rich_text_font_family">"Font family"</string>
    <string name="rich_text_bullet_list">"Bullet list"</string>
    <string name="rich_text_number_list">"Number list"</string>
    <string name="rich_text_paragraph_style">"Paragraph style"</string>
    <string name="rich_text_align_left">"Align left"</string>
    <string name="rich_text_align_center">"Align centre"</string>
    <string name="rich_text_align_right">"Align right"</string>
    <string name="rich_text_indent_increase">"Increase indent"</string>
    <string name="rich_text_indent_decrease">"Decrease indent"</string>
    <string name="rich_text_insert_link">"Insert link"</string>
    <string name="rich_text_undo">"Undo"</string>
    <string name="rich_text_redo">"Redo"</string>

    <string name="menu_keyboard_button">"Keyboard"</string>
    <string name="menu_brush_button">"Brush"</string>
    <string name="menu_eraser_button">"Eraser"</string>
    <string name="menu_undo_button">"Undo"</string>
    <string name="menu_redo_button">"Redo"</string>
    <string name="menu_color_button">"Colour"</string>
    <string name="menu_todo_button">"Todo"</string>
    <string name="menu_text_style_button">"Text Style"</string>
    <string name="menu_one_stroke_forming_button">"One Stroke Forming"</string>

    <string name="action_expand">"Expand"</string>
    <string name="action_collapse">"Collapse"</string>

    <string name="state_selected">"Selected"</string>

    <string name="note">Note</string>
    <string name="journal">Journal</string>

    <string name="title_input_field">"Note title input field"</string>

    <string name="background_mode">"Background mode"</string>

    <string name="eraser_toolbar_title">"Handwriting eraser"</string>
    <string name="eraser_stroke_mode">"Stroke eraser"</string>
    <string name="eraser_area_mode">"Area eraser"</string>
    <string name="eraser_clear_all">"Erase all handwriting"</string>

    <string name="text_style_options">"Text options"</string>
    <string name="action_share_off_finger_drawing">"Turn off finger drawing"</string>
    <string name="action_share_on_finger_drawing">"Turn on finger drawing"</string>
    <string name="record_completed">"Recording completed and saved."</string>

    <string name="brush_size_less">"Touch or hold to decrease brush size"</string>
    <string name="brush_size_add">"Touch or hold to increase brush size"</string>

    <string name="color_alpha_size_less">"Touch or hold to decrease colour transparency"</string>
    <string name="color_alpha_size_add">"Touch or hold to increase colour transparency"</string>

    <string name="eraser_value_decrease">"Touch or long press to decrease the eraser size"</string>
    <string name="eraser_value_increase">"Touch or long press to increase the eraser size"</string>

    <string name="rename_audio">"Rename"</string>
    <string name="recording_audio">"Recording audio"</string>
    <string name="dialog_title_delete_multiple_audio">"%s recordings will be deleted and cannot be recovered."</string>
    <string name="dialog_title_delete_all_audio">"All recordings will be deleted and cannot be recovered."</string>
    <string name="dialog_title_delete_audio">"Delete"</string>

    <!-- Accessibility strings for audio recording -->
    <string name="recording_stop_description">"Stop recording"</string>
    <string name="select_all_audios">"Select all recordings"</string>
    <string name="deselect_all_audios">"Deselect all recordings"</string>
    <string name="delete_selected_audios">"Delete selected recordings"</string>
    <string name="edit_audio_list">"Edit recording list"</string>
    <string name="audio_item_description">"%1$s, duration %2$s"</string>
    <string name="audio_item_selected_description">"%1$s selected"</string>
    <string name="audio_item_unselected_description">"%1$s not selected"</string>
    <string name="rename_audio_button">"Rename recording"</string>
    <string name="audio_to_text_button">"Convert recording to text"</string>

    <string name="menu_lasso_delete">"Delete"</string>
    <string name="aota_not_exist">"No upgradeable apps found"</string>
    <string name="lastest_version_tips">"The lastest version is installed"</string>
    <string name="updates">"Updates"</string>
    <string name="cannot_check_updates">"Couldn\'t check for updates"</string>

    <string name="enable_log">"sunia log is enabled, please restart"</string>
    <string name="disable_log">"sunia log is disabled, please restart"</string>
    <string name="other_apps_recording">"The recording feature is currently in use by other apps, so the current app can\'t enable it."</string>
    <string name="recording_over_limit">"You can save up to 100 recordings"</string>

    <string name="double_tap_to_activate">"double tap to activate"</string>
    <string name="checkbox_type_semantics">"Checkbox"</string>
    <string name="tab_type_semantics">"Tab"</string>
    <string name="radio_button_type_semantics">"Radio button"</string>
    <string name="switch_type_semantics">"Switch"</string>
    <string name="slider_type_semantics">"Slider"</string>
    <string name="accessibility_description_format">"%1$s, %2$s, %3$s, %4$s"</string>
    <string name="accessibility_description_format_3_parts">"%1$s, %2$s, %3$s"</string>
    <string name="accessibility_separator">", "</string>
    <string name="color_collection_format">"%1$s %2$s"</string>

    <string name="share_content_empty">"No content to share"</string>
    <string name="select_notes">"Select notes"</string>
    <string name="no_result">"No results"</string>
</resources>