<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="97dp" android:viewportHeight="97" android:viewportWidth="96" android:width="96dp">
      
    <path android:fillColor="#1B1B1B" android:pathData="M48,96.462C74.51,96.462 96,74.972 96,48.462C96,21.952 74.51,0.462 48,0.462C21.49,0.462 0,21.952 0,48.462C0,74.972 21.49,96.462 48,96.462Z"/>
      
    <path android:pathData="M48,96.462C74.51,96.462 96,74.972 96,48.462C96,21.952 74.51,0.462 48,0.462C21.49,0.462 0,21.952 0,48.462C0,74.972 21.49,96.462 48,96.462Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="48" android:endY="96.462" android:startX="48" android:startY="0.462" android:type="linear">
                        
                <item android:color="#FF313538" android:offset="0"/>
                        
                <item android:color="#FF28282C" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:pathData="M53.795,28.384C59.373,28.944 63.849,32.303 64.804,38.064H61.575L66.786,45.822L72,38.064H68.404C67.449,30.306 61.578,25.263 54.162,24.544C53.135,24.462 52.184,25.342 52.184,26.462C52.252,27.425 52.912,28.302 53.795,28.384ZM45.723,24.462H27.596C26.569,24.462 25.762,25.342 25.762,26.462V43.742C25.762,44.863 26.569,45.743 27.596,45.743H45.723C46.75,45.743 47.557,44.863 47.557,43.742V26.462C47.557,25.342 46.75,24.462 45.723,24.462ZM43.96,41.903H29.358V28.302H43.964V41.903H43.96ZM42.198,68.464C35.96,67.904 31.189,63.745 30.969,56.863H34.418L29.211,49.105L24,56.863H27.449C27.593,65.745 33.831,71.503 41.834,72.304C42.861,72.383 43.816,71.503 43.816,70.382C43.823,69.921 43.662,69.473 43.365,69.121C43.068,68.769 42.654,68.535 42.198,68.464ZM68.476,51.102H50.346C49.316,51.102 48.512,51.985 48.512,53.102V70.465C48.512,71.582 49.316,72.462 50.346,72.462H68.472C69.499,72.462 70.303,71.582 70.303,70.465V53.102C70.307,51.985 69.427,51.102 68.476,51.102ZM66.71,68.543H52.108V55.021H66.71V68.543Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="72" android:endY="72.462" android:startX="27" android:startY="27.462" android:type="linear">
                        
                <item android:color="#FF4D68EE" android:offset="0"/>
                        
                <item android:color="#FF7A65F6" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
    
</vector>
