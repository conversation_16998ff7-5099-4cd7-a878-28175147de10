<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="96dp" android:viewportHeight="96" android:viewportWidth="96" android:width="96dp">
      
    <path android:fillColor="#1B1B1B" android:pathData="M48,96C74.51,96 96,74.51 96,48C96,21.49 74.51,0 48,0C21.49,0 0,21.49 0,48C0,74.51 21.49,96 48,96Z"/>
      
    <path android:pathData="M48,96C74.51,96 96,74.51 96,48C96,21.49 74.51,0 48,0C21.49,0 0,21.49 0,48C0,74.51 21.49,96 48,96Z">
            
        <aapt:attr name="android:fillColor">
                  
            <gradient android:endX="48" android:endY="96" android:startX="48" android:startY="0" android:type="linear">
                        
                <item android:color="#FF313538" android:offset="0"/>
                        
                <item android:color="#FF28282C" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M67.999,50.365V24.115C67.999,22.735 66.88,21.615 65.499,21.615H30.499C29.118,21.615 27.999,22.735 27.999,24.115V69.115C27.999,70.496 29.118,71.615 30.499,71.615H44.249" android:strokeWidth="5">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="47.999" android:endY="71.615" android:startX="47.999" android:startY="21.615" android:type="linear">
                        
                <item android:color="#FF4968EE" android:offset="0"/>
                        
                <item android:color="#FF7E65F7" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M39.249,31.614H56.749" android:strokeWidth="5">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="47.999" android:endY="32.614" android:startX="47.999" android:startY="31.614" android:type="linear">
                        
                <item android:color="#FF4968EE" android:offset="0"/>
                        
                <item android:color="#FF7E65F7" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M39.249,41.613H56.749" android:strokeWidth="5">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="47.999" android:endY="42.613" android:startX="47.999" android:startY="41.613" android:type="linear">
                        
                <item android:color="#FF4968EE" android:offset="0"/>
                        
                <item android:color="#FF7E65F7" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M39.249,51.615H46.749" android:strokeWidth="5">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="42.999" android:endY="52.615" android:startX="42.999" android:startY="51.615" android:type="linear">
                        
                <item android:color="#FF4968EE" android:offset="0"/>
                        
                <item android:color="#FF7E65F7" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M55.623,61.989L49.998,67.614L55.623,73.239" android:strokeWidth="4.5">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="52.81" android:endY="73.239" android:startX="52.81" android:startY="61.989" android:type="linear">
                        
                <item android:color="#FF4968EE" android:offset="0"/>
                        
                <item android:color="#FF7E65F7" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
      
    <path android:fillColor="#00000000" android:pathData="M49.998,67.614H67.998V58.614" android:strokeWidth="4.5">
            
        <aapt:attr name="android:strokeColor">
                  
            <gradient android:endX="58.998" android:endY="67.614" android:startX="58.998" android:startY="58.614" android:type="linear">
                        
                <item android:color="#FF4968EE" android:offset="0"/>
                        
                <item android:color="#FF7E65F7" android:offset="1"/>
                      
            </gradient>
                
        </aapt:attr>
          
    </path>
    
</vector>
