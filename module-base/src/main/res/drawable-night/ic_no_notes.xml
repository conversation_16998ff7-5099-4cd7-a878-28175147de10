<vector xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android" android:autoMirrored="true" android:height="120dp" android:viewportHeight="120" android:viewportWidth="120" android:width="120dp">
      
    <group>
            
        <clip-path android:pathData="M0,0h120v120h-120z"/>
            
        <path android:pathData="M26.98,29.88L70.47,23.21A8,8 126.24,0 1,79.59 29.9L88.09,85.25A8,8 125.78,0 1,81.4 94.37L37.91,101.05A8,8 126.24,0 1,28.79 94.36L20.29,39.01A8,8 126.24,0 1,26.98 29.88z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="54.19" android:endY="101.14" android:startX="54.19" android:startY="23.12" android:type="linear">
                              
                    <item android:color="#FF472C00" android:offset="0"/>
                              
                    <item android:color="#FF6B4200" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:fillAlpha="0" android:fillColor="#00000000" android:pathData="M27.05,30.38L70.55,23.7A7.5,7.5 127.1,0 1,79.1 29.98L87.6,85.33A7.5,7.5 121.51,0 1,81.32 93.88L37.83,100.56A7.5,7.5 128.62,0 1,29.28 94.28L20.78,38.93A7.5,7.5 128.62,0 1,27.05 30.38z" android:strokeAlpha="0.02" android:strokeColor="#000000" android:strokeWidth="1"/>
            
        <path android:fillAlpha="0.6" android:fillColor="#FFD09E" android:pathData="M56,36L76,36A8,8 0,0 1,84 44L84,72A8,8 0,0 1,76 80L56,80A8,8 0,0 1,48 72L48,44A8,8 0,0 1,56 36z"/>
            
        <path android:pathData="M39,22L81,22A8,8 0,0 1,89 30L89,86A8,8 0,0 1,81 94L39,94A8,8 0,0 1,31 86L31,30A8,8 0,0 1,39 22z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="73.1" android:endY="13.37" android:startX="33.65" android:startY="91.24" android:type="linear">
                              
                    <item android:color="#68FF9E00" android:offset="0"/>
                              
                    <item android:color="#33FF9E00" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:fillAlpha="0" android:fillColor="#00000000" android:pathData="M39,22.5L81,22.5A7.5,7.5 0,0 1,88.5 30L88.5,86A7.5,7.5 0,0 1,81 93.5L39,93.5A7.5,7.5 0,0 1,31.5 86L31.5,30A7.5,7.5 0,0 1,39 22.5z" android:strokeColor="#8C8C8C" android:strokeWidth="1"/>
            
        <path android:fillAlpha="0" android:fillColor="#00000000" android:pathData="M39,22.5L81,22.5A7.5,7.5 0,0 1,88.5 30L88.5,86A7.5,7.5 0,0 1,81 93.5L39,93.5A7.5,7.5 0,0 1,31.5 86L31.5,30A7.5,7.5 0,0 1,39 22.5z" android:strokeWidth="1">
                  
            <aapt:attr name="android:strokeColor">
                        
                <gradient android:centerX="102.39" android:centerY="14.36" android:gradientRadius="92.44" android:type="radial">
                              
                    <item android:color="#00FFFFFF" android:offset="0"/>
                              
                    <item android:color="#00FFFFFF" android:offset="0"/>
                              
                    <item android:color="#5BFFFFFF" android:offset="0.44"/>
                              
                    <item android:color="#00FFFFFF" android:offset="0.47"/>
                              
                    <item android:color="#00FFFFFF" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:fillColor="#D4A354" android:pathData="M45.5,38L74.5,38A2.5,2.5 0,0 1,77 40.5L77,40.5A2.5,2.5 0,0 1,74.5 43L45.5,43A2.5,2.5 0,0 1,43 40.5L43,40.5A2.5,2.5 0,0 1,45.5 38z"/>
            
        <path android:fillColor="#D4A354" android:pathData="M45.5,52L66.5,52A2.5,2.5 0,0 1,69 54.5L69,54.5A2.5,2.5 0,0 1,66.5 57L45.5,57A2.5,2.5 0,0 1,43 54.5L43,54.5A2.5,2.5 0,0 1,45.5 52z"/>
            
        <path android:pathData="M67,86L80,84L108.5,57.5L95,56L70.5,80L67,86Z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="89.31" android:endY="70.14" android:startX="68.68" android:startY="84.91" android:type="linear">
                              
                    <item android:color="#FFFE8B10" android:offset="0"/>
                              
                    <item android:color="#00FFDDA3" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:fillType="evenOdd" android:pathData="M65.01,84.72L65.98,77.87C66.25,75.93 67.15,74.14 68.53,72.76L94,47.32C95.76,45.56 98.6,45.56 100.36,47.32L103.68,50.63C105.44,52.38 105.44,55.23 103.68,56.99L78.24,82.44C76.87,83.81 75.09,84.71 73.17,84.99L66.29,85.99C65.54,86.1 64.91,85.46 65.01,84.72Z">
                  
            <aapt:attr name="android:fillColor">
                        
                <gradient android:endX="103.17" android:endY="46.95" android:startX="66.83" android:startY="84.47" android:type="linear">
                              
                    <item android:color="#AFD6A75C" android:offset="0"/>
                              
                    <item android:color="#FFC08930" android:offset="1"/>
                            
                </gradient>
                      
            </aapt:attr>
                
        </path>
            
        <path android:fillColor="#989898" android:fillType="evenOdd" android:pathData="M65.01,84.72L65.98,77.87C66.25,75.93 67.15,74.14 68.53,72.76L94,47.32C95.76,45.56 98.6,45.56 100.36,47.32L103.68,50.63C105.44,52.38 105.44,55.23 103.68,56.99L78.24,82.44C76.87,83.81 75.09,84.71 73.17,84.99L66.29,85.99C65.54,86.1 64.91,85.46 65.01,84.72ZM66,84.86Q65.99,84.92 66.04,84.96Q66.08,85.01 66.14,85L73.03,84Q75.66,83.61 77.53,81.73L102.97,56.29Q104,55.26 104,53.81Q104,52.36 102.97,51.34L99.66,48.02Q98.63,47 97.18,47Q95.73,47 94.71,48.02L69.24,73.47Q67.35,75.36 66.97,78.01L66,84.86Z"/>
          
    </group>
    
</vector>
