<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="32dp"
    android:height="33dp"
    android:viewportWidth="32"
    android:viewportHeight="33">
  <group>
    <clip-path
        android:pathData="M0,0.5h32v32h-32z"/>
    <path
        android:pathData="M16,32.962C24.837,32.962 32,25.799 32,16.962C32,8.125 24.837,0.962 16,0.962C7.163,0.962 0,8.125 0,16.962C0,25.799 7.163,32.962 16,32.962Z"
        android:fillColor="#1B1B1B"/>
    <path
        android:pathData="M16,32.962C24.837,32.962 32,25.799 32,16.962C32,8.125 24.837,0.962 16,0.962C7.163,0.962 0,8.125 0,16.962C0,25.799 7.163,32.962 16,32.962Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="16"
            android:startY="0.962"
            android:endX="16"
            android:endY="32.962"
            android:type="linear">
          <item android:offset="0" android:color="#FF313538"/>
          <item android:offset="1" android:color="#FF28282C"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M18.769,13.192H10C9.448,13.192 9,13.64 9,14.192V22.962C9,23.514 9.448,23.962 10,23.962H18.769C19.322,23.962 19.769,23.514 19.769,22.962V14.192C19.769,13.64 19.322,13.192 18.769,13.192Z"
        android:strokeWidth="1.5"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="14.385"
            android:startY="13.192"
            android:endX="14.385"
            android:endY="23.962"
            android:type="linear">
          <item android:offset="0" android:color="#FF4968EE"/>
          <item android:offset="1" android:color="#FF7E65F7"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M12.231,11.847V11.039C12.231,10.444 12.713,9.962 13.308,9.962H21.923C22.518,9.962 23,10.444 23,11.039V19.654C23,20.249 22.518,20.731 21.923,20.731H21.116"
        android:strokeWidth="1.5"
        android:fillColor="#00000000">
      <aapt:attr name="android:strokeColor">
        <gradient 
            android:startX="17.616"
            android:startY="9.962"
            android:endX="17.616"
            android:endY="20.731"
            android:type="linear">
          <item android:offset="0" android:color="#FF4968EE"/>
          <item android:offset="1" android:color="#FF7E65F7"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
